<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\UserBook;
use App\Models\UserReadingLog;
use App\Models\User;
use App\Models\Book;

try {
    echo "=== TESTING AUTOMATIC SESSION COMPLETION ===\n\n";
    
    // Get a test user and book
    $user = User::first();
    $book = Book::first();
    
    if (!$user || !$book) {
        echo "❌ No users or books found in database\n";
        exit;
    }
    
    echo "Test User: {$user->name}\n";
    echo "Test Book: {$book->name}\n\n";
    
    // Clean up any existing test data
    UserBook::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserReadingLog::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // Test Case 1: Create an active reading session
    echo "TEST CASE 1: Create active reading session\n";
    $session = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now()->subDays(5)->toDateString(),
        'end_date' => null, // Active session
    ]);
    
    echo "Session created:\n";
    echo "- Session ID: {$session->id}\n";
    echo "- Start Date: {$session->start_date->format('Y-m-d')}\n";
    echo "- End Date: " . ($session->end_date ? $session->end_date->format('Y-m-d') : 'NULL (Active)') . "\n";
    echo "- Status: {$session->localized_reading_status}\n\n";
    
    // Test Case 2: Create reading log without completion
    echo "TEST CASE 2: Create reading log without completion\n";
    $readingLog1 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->subDays(3)->toDateString(),
        'pages_read' => 25,
        'book_completed' => false,
    ]);
    
    // Refresh session to check if it was updated
    $session->refresh();
    echo "Reading log created (not completed):\n";
    echo "- Log Date: {$readingLog1->log_date->format('Y-m-d')}\n";
    echo "- Book Completed: " . ($readingLog1->book_completed ? 'YES' : 'NO') . "\n";
    echo "- Session End Date: " . ($session->end_date ? $session->end_date->format('Y-m-d') : 'NULL (Still Active)') . "\n";
    echo "✅ " . (is_null($session->end_date) ? "PASS" : "FAIL") . " - Session should remain active\n\n";
    
    // Test Case 3: Create reading log with completion
    echo "TEST CASE 3: Create reading log with completion (should auto-complete session)\n";
    $completionDate = now()->subDays(1)->toDateString();
    $readingLog2 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => $completionDate,
        'pages_read' => 30,
        'book_completed' => true,
    ]);
    
    // Refresh session to check if it was updated
    $session->refresh();
    echo "Reading log created (completed):\n";
    echo "- Log Date: {$readingLog2->log_date->format('Y-m-d')}\n";
    echo "- Book Completed: " . ($readingLog2->book_completed ? 'YES' : 'NO') . "\n";
    echo "- Session End Date: " . ($session->end_date ? $session->end_date->format('Y-m-d') : 'NULL') . "\n";
    echo "- Expected End Date: {$completionDate}\n";
    
    $isCorrectlyCompleted = $session->end_date && $session->end_date->format('Y-m-d') === $completionDate;
    echo "✅ " . ($isCorrectlyCompleted ? "PASS" : "FAIL") . " - Session should be auto-completed with correct date\n\n";
    
    // Test Case 4: Try to update existing reading log to completed
    echo "TEST CASE 4: Update existing reading log to completed\n";
    $updateDate = now()->toDateString();
    
    // Create new session first
    $session2 = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now()->subDays(2)->toDateString(),
        'end_date' => null,
    ]);
    
    // Create reading log without completion
    $readingLog3 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => $updateDate,
        'pages_read' => 40,
        'book_completed' => false,
    ]);
    
    echo "Before update:\n";
    echo "- Session 2 End Date: " . ($session2->end_date ? $session2->end_date->format('Y-m-d') : 'NULL (Active)') . "\n";
    
    // Update the reading log to completed
    $readingLog3->update(['book_completed' => true]);
    
    // Refresh session to check if it was updated
    $session2->refresh();
    echo "After updating reading log to completed:\n";
    echo "- Session 2 End Date: " . ($session2->end_date ? $session2->end_date->format('Y-m-d') : 'NULL') . "\n";
    echo "- Expected End Date: {$updateDate}\n";
    
    $isUpdateCorrect = $session2->end_date && $session2->end_date->format('Y-m-d') === $updateDate;
    echo "✅ " . ($isUpdateCorrect ? "PASS" : "FAIL") . " - Session should be auto-completed when log is updated\n\n";
    
    // Test Case 5: Test date validation (reading log before session start)
    echo "TEST CASE 5: Test date validation (reading log before session start)\n";
    $session3 = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now()->toDateString(),
        'end_date' => null,
    ]);
    
    // Try to create reading log with date before session start
    $invalidDate = now()->subDays(1)->toDateString();
    $readingLog4 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => $invalidDate,
        'pages_read' => 20,
        'book_completed' => true,
    ]);
    
    $session3->refresh();
    echo "Reading log with invalid date (before session start):\n";
    echo "- Session Start: {$session3->start_date->format('Y-m-d')}\n";
    echo "- Log Date: {$readingLog4->log_date->format('Y-m-d')}\n";
    echo "- Session End Date: " . ($session3->end_date ? $session3->end_date->format('Y-m-d') : 'NULL (Should remain active)') . "\n";
    
    $validationWorked = is_null($session3->end_date);
    echo "✅ " . ($validationWorked ? "PASS" : "FAIL") . " - Session should not be completed due to invalid date\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    UserBook::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserReadingLog::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Automatic session completion tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
