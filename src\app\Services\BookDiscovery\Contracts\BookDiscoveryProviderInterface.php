<?php

namespace App\Services\BookDiscovery\Contracts;

interface BookDiscoveryProviderInterface
{
    /**
     * Search for a book by ISBN.
     *
     * @param string $isbn
     * @return array|null Book data or null if not found
     */
    public function searchByIsbn(string $isbn): ?array;

    /**
     * Get the provider name.
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Get the provider priority.
     *
     * @return int
     */
    public function getPriority(): int;

    /**
     * Check if the provider is enabled.
     *
     * @return bool
     */
    public function isEnabled(): bool;

    /**
     * Validate extracted book data.
     *
     * @param array $data
     * @return bool
     */
    public function validateBookData(array $data): bool;
}
