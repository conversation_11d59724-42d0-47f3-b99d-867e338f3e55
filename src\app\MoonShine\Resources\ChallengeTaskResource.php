<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ChallengeTask;
use App\Models\Challenge;
use App\Models\Task;
use App\Models\Reward;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use Illuminate\Database\Eloquent\Builder;

#[Icon('clipboard-document-check')]
class ChallengeTaskResource extends BaseResource
{
    protected string $model = ChallengeTask::class;

    protected string $column = 'id';

    protected array $with = ['challenge', 'task.taskType', 'task.taskCycle'];

    public function getTitle(): string
    {
        return __('admin.challenge_tasks');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.challenge'),
                'challenge',
                formatted: fn(Challenge $challenge) => $challenge->name,
                resource: ChallengeResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name,
                resource: TaskResource::class
            )
                ->sortable(),

            Text::make(__('admin.task_type'), 'task.taskType.name')
                ->sortable(),

            Text::make(__('admin.task_cycle'), 'task.taskCycle.name')
                ->sortable(),

            Date::make(__('admin.start_date'), 'start_date')
                ->sortable(),

            Date::make(__('admin.end_date'), 'end_date')
                ->sortable(),

            Text::make(__('admin.date_range'), 'date_range_display'),

            Number::make(__('admin.participants_count'), 'participants_count')
                ->sortable(),

            Number::make(__('admin.completion_count'), 'completion_count')
                ->sortable(),

            Text::make(__('admin.completion_rate'), 'completion_rate_display'),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(?Reward $reward) => $reward ? $reward->name . ' (' . $reward->reward_type_display . ')' : '-',
                resource: RewardResource::class
            )
                ->sortable(),

            Text::make(__('admin.status'), 'status_display'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.challenge'),
                'challenge',
                formatted: fn(Challenge $challenge) => $challenge->name,
                resource: ChallengeResource::class
            )
                ->required()
                ->searchable(),

            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                resource: TaskResource::class
            )
                ->required()
                ->searchable(),

            Date::make(__('admin.start_date'), 'start_date')
                ->required()
                ->default(now()->format('Y-m-d')),

            Date::make(__('admin.end_date'), 'end_date')
                ->required()
                ->default(now()->addDays(30)->format('Y-m-d')),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(?Reward $reward) => $reward ? $reward->name . ' (' . $reward->reward_type_display . ')' : null,
                resource: RewardResource::class
            )
            // filter active rewards which tasks are empty
                ->valuesQuery(function (Builder $query) { return $query->active()->whereDoesntHave('tasks'); })                          
                ->nullable()
                ->searchable()
                ->hint(__('admin.optional_reward_for_task_completion')),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.challenge'),
                'challenge',
                formatted: fn(Challenge $challenge) => $challenge->name,
                resource: ChallengeResource::class
            ),

            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name,
                resource: TaskResource::class
            ),

            Text::make(__('admin.task_type'), 'task.taskType.name'),
            Text::make(__('admin.task_cycle'), 'task.taskCycle.name'),
            Text::make(__('admin.task_value'), 'task.task_value_with_unit'),

            Date::make(__('admin.start_date'), 'start_date'),
            Date::make(__('admin.end_date'), 'end_date'),
            Text::make(__('admin.date_range'), 'date_range_display'),
            Text::make(__('admin.duration'), 'duration'),
            Text::make(__('admin.status'), 'status_display'),

            Number::make(__('admin.participants_count'), 'participants_count'),
            Number::make(__('admin.completion_count'), 'completion_count'),
            Text::make(__('admin.completion_rate'), 'completion_rate_display'),
            Text::make(__('admin.summary'), 'summary'),

            HasMany::make(__('admin.user_tasks'), 'userTasks', UserTaskResource::class),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'challenge_id' => ['required', 'exists:challenges,id'],
            'task_id' => ['required', 'exists:tasks,id'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            ...parent::getCommonRules($item),
        ];

        // Add validation to ensure dates are within challenge date range
        if ($item && $item->challenge) {
            $rules['start_date'][] = 'after_or_equal:' . $item->challenge->start_date->format('Y-m-d');
            $rules['end_date'][] = 'before_or_equal:' . $item->challenge->end_date->format('Y-m-d');
        }

        return $rules;
    }

    protected function search(): array
    {
        return ['challenge.name', 'task.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['start_date' => 'asc'];
    }
}
