<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\BookDiscovery\Providers\DRBookProvider;

class DRBookProviderTest extends TestCase
{
    protected DRBookProvider $provider;

    protected function setUp(): void
    {
        parent::setUp();

        // Create provider with test configuration
        $config = [
            'name' => 'D&R',
            'enabled' => true,
            'search_url' => 'https://www.dr.com.tr/search?q={isbn}',
            'timeout' => 15,
            'parsing' => [],
            'not_found_indicators' => [
                'status_codes' => [404, 500],
                'text_patterns' => [
                    'ürün bulunamadı',
                    'Sonuç bulunamadı',
                    'No results found',
                    'Ürün bulunamadı'
                ],
                'empty_selectors' => []
            ]
        ];

        $this->provider = new DRBookProvider('dr', $config);
    }

    /** @test */
    public function it_can_parse_dr_product_properties_structure()
    {
        $html = '
        <div class="product-property">
            <ul class="nav js-list-prd-property">
                <li>
                    <strong> Kitap Adı:</strong>
                    <span> Alp Efe ile Hayatı Öğreniyorum-1.Sın<PERSON>f Okuma Seti-10 Kitap Takım </span>
                </li>
                <li>
                    <strong> Yazar:</strong>
                    <span> Nazife Burcu Takıl </span>
                </li>
                <li>
                    <strong> Yayınevi:</strong>
                    <span> Martı Yayınları  </span>
                </li>
                <li>
                    <strong> Hamur Tipi: </strong>
                    <span> 2. Hamur </span>
                </li>
                <li>
                    <strong> Sayfa Sayısı: </strong>
                    <span> 160 </span>
                </li>
                <li>
                    <strong> Ebat: </strong>
                    <span> 16 x 21 </span>
                </li>
                <li>
                    <strong> İlk Baskı Yılı: </strong>
                    <span> 2020 </span>
                </li>
                <li>
                    <strong> Baskı Sayısı: </strong>
                    <span> 1. Basım </span>
                </li>
                <li>
                    <strong> Dil: </strong>
                    <span> Türkçe </span>
                </li>
                <li>
                    <strong> Kitap Seti: </strong>
                    <span> Var </span>
                </li>
                <li>
                    <strong> Barkod:</strong>
                    <span> 9786050322033 </span>
                </li>
            </ul>
        </div>';

        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseDRProductProperties');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertEquals('Alp Efe ile Hayatı Öğreniyorum-1.Sınıf Okuma Seti-10 Kitap Takım', $result['name']);
        $this->assertEquals(['Nazife Burcu Takıl'], $result['author']);
        $this->assertEquals('Martı Yayınları', $result['publisher']);
        $this->assertEquals(160, $result['page_count']);
        $this->assertEquals(2020, $result['year']);
        $this->assertEquals('9786050322033', $result['isbn']);
        $this->assertEquals('D&R', $result['source']);
    }

    /** @test */
    public function it_handles_missing_product_properties_gracefully()
    {
        $html = '<div>No product properties here</div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseDRProductProperties');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNull($result);
    }

    /** @test */
    public function it_handles_incomplete_product_properties()
    {
        $html = '
        <div class="product-property">
            <ul class="nav js-list-prd-property">
                <li>
                    <strong> Kitap Adı:</strong>
                    <span> Test Book Title </span>
                </li>
                <li>
                    <strong> Yazar:</strong>
                    <span> Test Author </span>
                </li>
                <!-- Missing other properties -->
            </ul>
        </div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseDRProductProperties');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertEquals('Test Book Title', $result['name']);
        $this->assertEquals(['Test Author'], $result['author']);
        $this->assertArrayNotHasKey('publisher', $result);
        $this->assertArrayNotHasKey('page_count', $result);
        $this->assertArrayNotHasKey('year', $result);
    }

    /** @test */
    public function it_cleans_author_names_correctly()
    {
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('cleanAuthorName');
        $method->setAccessible(true);

        $this->assertEquals('John Doe', $method->invoke($this->provider, 'Yazar: John Doe'));
        $this->assertEquals('Jane Smith', $method->invoke($this->provider, 'Jane Smith (Editor)'));
        $this->assertEquals('Test Author', $method->invoke($this->provider, '  Test Author  '));
    }

    /** @test */
    public function it_cleans_publisher_names_correctly()
    {
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('cleanPublisherName');
        $method->setAccessible(true);

        $this->assertEquals('Test Publisher', $method->invoke($this->provider, 'Yayınevi: Test Publisher'));
        $this->assertEquals('Another Publisher', $method->invoke($this->provider, '  Another Publisher  '));
    }

    /** @test */
    public function it_extracts_page_count_correctly()
    {
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('extractPageCount');
        $method->setAccessible(true);

        $this->assertEquals(160, $method->invoke($this->provider, '160'));
        $this->assertEquals(250, $method->invoke($this->provider, '250 sayfa'));
        $this->assertEquals(300, $method->invoke($this->provider, '300 pages'));
        $this->assertEquals(180, $method->invoke($this->provider, '180 sf'));
        $this->assertNull($method->invoke($this->provider, 'no numbers here'));
        $this->assertNull($method->invoke($this->provider, '99999')); // Too high
    }

    /** @test */
    public function it_detects_book_not_found_correctly()
    {
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('isBookNotFound');
        $method->setAccessible(true);

        $this->assertTrue($method->invoke($this->provider, 'ürün bulunamadı'));
        $this->assertTrue($method->invoke($this->provider, 'Sonuç bulunamadı'));
        $this->assertTrue($method->invoke($this->provider, 'Ürün bulunamadı'));
        $this->assertTrue($method->invoke($this->provider, 'Arama sonucu bulunamadı'));
        $this->assertFalse($method->invoke($this->provider, 'Book found successfully'));
    }

    /** @test */
    public function it_handles_alternative_year_labels()
    {
        $html = '
        <div class="product-property">
            <ul class="nav js-list-prd-property">
                <li>
                    <strong> Kitap Adı:</strong>
                    <span> Test Book </span>
                </li>
                <li>
                    <strong> Baskı Yılı: </strong>
                    <span> 2021 </span>
                </li>
            </ul>
        </div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseDRProductProperties');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertEquals('Test Book', $result['name']);
        $this->assertEquals(2021, $result['year']);
    }
}
