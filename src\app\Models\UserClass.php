<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserClass extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'class_id',
        'school_id',
        'active',
        'default',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
            'default' => 'boolean',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Handle default class logic
        static::creating(function ($userClass) {
            $userClass->handleDefaultLogicOnCreate();
        });

        static::updating(function ($userClass) {
            $userClass->handleDefaultLogicOnUpdate();
        });

        static::deleting(function ($userClass) {
            $userClass->handleDefaultLogicOnDelete();
        });
    }

    /**
     * Get the user.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the class.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the school.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the user who created this class assignment.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by class.
     */
    public function scopeByClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to filter by school.
     */
    public function scopeBySchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get default assignments.
     */
    public function scopeDefault($query)
    {
        return $query->where('default', true);
    }

    /**
     * Scope to get non-default assignments.
     */
    public function scopeNonDefault($query)
    {
        return $query->where('default', false);
    }

    /**
     * Get the display name for this assignment.
     */
    public function getDisplayNameAttribute(): string
    {
        $parts = [
            $this->user->name,
            $this->school->name,
        ];

        return implode(' - ', array_filter($parts));
    }

    /**
     * Get assignment summary.
     */
    public function getSummaryAttribute(): string
    {
        $summary = $this->user->name;
        
        if ($this->school) {
            $summary .= ' at ' . $this->school->name;
        }
        
        return $summary;
    }

    /**
     * Activate this assignment.
     */
    public function activate(): bool
    {
        return $this->update(['active' => true]);
    }

    /**
     * Deactivate this assignment.
     */
    public function deactivate(): bool
    {
        return $this->update(['active' => false]);
    }

    /**
     * Check if this assignment is active.
     */
    public function isActive(): bool
    {
        return $this->active;
    }

    /**
     * Check if this assignment is the default.
     */
    public function isDefault(): bool
    {
        return $this->default;
    }

    /**
     * Set this assignment as default.
     */
    public function setAsDefault(): bool
    {
        // First, unset any existing default for this user
        static::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->update(['default' => false]);

        // Set this as default
        return $this->update(['default' => true]);
    }

    /**
     * Handle default logic when creating a new assignment.
     */
    public function handleDefaultLogicOnCreate(): void
    {
        // If this is the user's first class assignment, make it default
        $existingCount = static::where('user_id', $this->user_id)->count();

        if ($existingCount === 0) {
            $this->default = true;
        } elseif ($this->default) {
            // If explicitly setting as default, unset others
            static::where('user_id', $this->user_id)
                ->update(['default' => false]);
        }
    }

    /**
     * Handle default logic when updating an assignment.
     */
    public function handleDefaultLogicOnUpdate(): void
    {
        if ($this->isDirty('default') && $this->default) {
            // If setting as default, unset others
            static::where('user_id', $this->user_id)
                ->where('id', '!=', $this->id)
                ->update(['default' => false]);
        }

        // Prevent deactivating the default class
        if ($this->isDirty('active') && !$this->active && $this->default) {
            throw new \Exception(__('admin.cannot_deactivate_default_class'));
        }
    }

    /**
     * Handle default logic when deleting an assignment.
     */
    public function handleDefaultLogicOnDelete(): void
    {
        // Prevent deleting the default class
        if ($this->default) {
            throw new \Exception(__('admin.cannot_delete_default_class'));
        }
    }

    /**
     * Get the user's default class assignment.
     */
    public static function getDefaultForUser(int $userId): ?self
    {
        return static::where('user_id', $userId)
            ->where('default', true)
            ->first();
    }

    /**
     * Ensure user has a default class assignment.
     */
    public static function ensureUserHasDefault(int $userId): void
    {
        $defaultExists = static::where('user_id', $userId)
            ->where('default', true)
            ->exists();

        if (!$defaultExists) {
            // Set the first active assignment as default
            $firstActive = static::where('user_id', $userId)
                ->where('active', true)
                ->first();

            if ($firstActive) {
                $firstActive->update(['default' => true]);
            }
        }
    }
}
