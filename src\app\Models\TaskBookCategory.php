<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TaskBookCategory extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'task_id',
        'category_id',
        'created_by',
    ];

    /**
     * Get the task for this task book category.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Get the category for this task book category.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the user who created this task book category.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by task.
     */
    public function scopeByTask($query, $taskId)
    {
        return $query->where('task_id', $taskId);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Get the display name for this task book category.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->task->name . ' - ' . $this->category->name;
    }

    /**
     * Add a category to a task.
     */
    public static function addCategoryToTask($taskId, $categoryId): ?self
    {
        // Check if category is already in task
        if (self::where('task_id', $taskId)->where('category_id', $categoryId)->exists()) {
            return null;
        }

        return self::create([
            'task_id' => $taskId,
            'category_id' => $categoryId,
        ]);
    }

    /**
     * Remove a category from a task.
     */
    public static function removeCategoryFromTask($taskId, $categoryId): bool
    {
        return self::where('task_id', $taskId)->where('category_id', $categoryId)->delete() > 0;
    }

    /**
     * Get categories for a task.
     */
    public static function getCategoriesForTask($taskId)
    {
        return self::where('task_id', $taskId)
            ->with(['category'])
            ->get();
    }

    /**
     * Check if a category is in a task.
     */
    public static function isCategoryInTask($taskId, $categoryId): bool
    {
        return self::where('task_id', $taskId)->where('category_id', $categoryId)->exists();
    }

    /**
     * Get tasks that include a specific category.
     */
    public static function getTasksForCategory($categoryId)
    {
        return self::where('category_id', $categoryId)
            ->with(['task'])
            ->get();
    }

    /**
     * Get books in the specified categories for a task.
     */
    public static function getBooksInTaskCategories($taskId)
    {
        $categoryIds = self::where('task_id', $taskId)->pluck('category_id');
        
        if ($categoryIds->isEmpty()) {
            return collect();
        }
        
        return Book::whereHas('categories', function ($query) use ($categoryIds) {
            $query->whereIn('categories.id', $categoryIds);
        })->get();
    }

    /**
     * Get the summary of this task book category.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            'Category "%s" is part of task "%s"',
            $this->category->name,
            $this->task->name
        );
    }
}
