<?php

/**
 * Test Script for Reward Classification System
 * 
 * This script tests the new reward classification system to ensure:
 * 1. Immediate rewards are awarded regardless of required activity status
 * 2. Book-completion rewards are withheld until required activities are completed
 * 3. Retroactive processing works correctly
 * 
 * Usage: php artisan tinker < test_reward_classification.php
 */

use App\Models\User;
use App\Models\Book;
use App\Models\Activity;
use App\Models\UserReadingLog;
use App\Models\UserActivity;
use App\Models\UserReward;
use App\Models\UserPoint;
use App\Models\Reward;
use App\Models\EnumTaskType;
use App\Services\RewardCalculationService;
use Illuminate\Support\Facades\DB;

echo "🧪 **REWARD CLASSIFICATION SYSTEM TEST**\n";
echo "=====================================\n\n";

// Test Configuration
$testUserId = 1; // Change this to a valid user ID
$testBookId = 1; // Change this to a valid book ID with required activities

try {
    // Clean up any existing test data
    echo "🧹 Cleaning up existing test data...\n";
    UserReadingLog::where('user_id', $testUserId)->where('book_id', $testBookId)->delete();
    UserActivity::where('user_id', $testUserId)->where('book_id', $testBookId)->delete();
    UserReward::where('user_id', $testUserId)->delete();
    UserPoint::where('user_id', $testUserId)->delete();
    
    // Get test data
    $user = User::findOrFail($testUserId);
    $book = Book::findOrFail($testBookId);
    
    echo "👤 Test User: {$user->name} (ID: {$user->id})\n";
    echo "📚 Test Book: {$book->name} (ID: {$book->id})\n";
    
    // Check required activities for this book
    $requiredActivities = Activity::where('active', true)->get()->where('required', true);
    echo "🎯 Required Activities: " . $requiredActivities->count() . "\n";
    
    if ($requiredActivities->isEmpty()) {
        echo "⚠️  WARNING: No required activities found. This test requires a book with required activities.\n";
        echo "Please set up required activities or change the test book ID.\n";
        exit;
    }
    
    foreach ($requiredActivities as $activity) {
        echo "   - {$activity->name} (ID: {$activity->id})\n";
    }
    echo "\n";
    
    // Get available rewards for testing
    $service = new RewardCalculationService();
    $eligibleRewards = $service->getEligibleRewardsForUser($testUserId);
    
    echo "🏆 Available Rewards: " . $eligibleRewards->count() . "\n";
    
    $immediateRewards = $eligibleRewards->filter(function($reward) use ($service) {
        return $service->hasImmediateReadingTasks($reward);
    });
    
    $bookCompletionRewards = $eligibleRewards->filter(function($reward) use ($service) {
        return $service->hasBookCompletionTasks($reward);
    });
    
    $activityRewards = $eligibleRewards->filter(function($reward) use ($service) {
        return $service->hasActivityRelatedTasks($reward);
    });
    
    echo "   - Immediate Reading Rewards: " . $immediateRewards->count() . "\n";
    echo "   - Book Completion Rewards: " . $bookCompletionRewards->count() . "\n";
    echo "   - Activity Rewards: " . $activityRewards->count() . "\n\n";
    
    // TEST 1: Create reading log with incomplete required activities
    echo "🧪 **TEST 1: Reading Log with Incomplete Required Activities**\n";
    echo "============================================================\n";
    
    $readingLog1 = UserReadingLog::create([
        'user_id' => $testUserId,
        'book_id' => $testBookId,
        'log_date' => now(),
        'pages_read' => 25,
        'reading_duration' => 30,
        'book_completed' => false,
    ]);
    
    echo "📖 Created reading log: 25 pages, 30 minutes\n";
    
    // Check what rewards were awarded
    $rewardsAfterLog1 = UserReward::where('user_id', $testUserId)->get();
    $pointsAfterLog1 = UserPoint::where('user_id', $testUserId)->get();
    
    echo "🏆 Rewards awarded: " . $rewardsAfterLog1->count() . "\n";
    echo "💰 Points awarded: " . $pointsAfterLog1->sum('points') . "\n";
    
    foreach ($rewardsAfterLog1 as $userReward) {
        $reward = $userReward->reward;
        $taskTypes = $reward->tasks()->join('enum_task_types', 'tasks.task_type_id', '=', 'enum_task_types.id')
                          ->pluck('enum_task_types.name')->toArray();
        echo "   ✅ {$reward->name} (Task Types: " . implode(', ', $taskTypes) . ")\n";
    }
    
    if ($rewardsAfterLog1->isEmpty()) {
        echo "   ❌ No rewards awarded\n";
    }
    
    echo "\n";
    
    // TEST 2: Complete a required activity
    echo "🧪 **TEST 2: Complete Required Activity (Retroactive Processing)**\n";
    echo "================================================================\n";
    
    $firstRequiredActivity = $requiredActivities->first();
    echo "✅ Completing required activity: {$firstRequiredActivity->name}\n";
    
    $userActivity = UserActivity::create([
        'user_id' => $testUserId,
        'book_id' => $testBookId,
        'activity_id' => $firstRequiredActivity->id,
        'content' => 'Test completion',
        'status' => UserActivity::STATUS_COMPLETED,
    ]);
    
    // Check what rewards were awarded after activity completion
    $rewardsAfterActivity = UserReward::where('user_id', $testUserId)->get();
    $pointsAfterActivity = UserPoint::where('user_id', $testUserId)->get();
    
    echo "🏆 Total rewards after activity: " . $rewardsAfterActivity->count() . "\n";
    echo "💰 Total points after activity: " . $pointsAfterActivity->sum('points') . "\n";
    
    $newRewards = $rewardsAfterActivity->whereNotIn('id', $rewardsAfterLog1->pluck('id'));
    echo "🆕 New rewards from activity/retroactive: " . $newRewards->count() . "\n";
    
    foreach ($newRewards as $userReward) {
        $reward = $userReward->reward;
        $taskTypes = $reward->tasks()->join('enum_task_types', 'tasks.task_type_id', '=', 'enum_task_types.id')
                          ->pluck('enum_task_types.name')->toArray();
        echo "   ✅ {$reward->name} (Task Types: " . implode(', ', $taskTypes) . ")\n";
    }
    
    if ($newRewards->isEmpty()) {
        echo "   ❌ No new rewards awarded\n";
    }
    
    echo "\n";
    
    // TEST 3: Create another reading log with all required activities completed
    echo "🧪 **TEST 3: Reading Log with All Required Activities Completed**\n";
    echo "===============================================================\n";
    
    $readingLog2 = UserReadingLog::create([
        'user_id' => $testUserId,
        'book_id' => $testBookId,
        'log_date' => now()->addDay(),
        'pages_read' => 50,
        'reading_duration' => 60,
        'book_completed' => true,
    ]);
    
    echo "📖 Created reading log: 50 pages, 60 minutes, book completed\n";
    
    // Check what rewards were awarded
    $rewardsAfterLog2 = UserReward::where('user_id', $testUserId)->get();
    $pointsAfterLog2 = UserPoint::where('user_id', $testUserId)->get();
    
    echo "🏆 Total rewards after second log: " . $rewardsAfterLog2->count() . "\n";
    echo "💰 Total points after second log: " . $pointsAfterLog2->sum('points') . "\n";
    
    $newRewardsFromLog2 = $rewardsAfterLog2->whereNotIn('id', $rewardsAfterActivity->pluck('id'));
    echo "🆕 New rewards from second log: " . $newRewardsFromLog2->count() . "\n";
    
    foreach ($newRewardsFromLog2 as $userReward) {
        $reward = $userReward->reward;
        $taskTypes = $reward->tasks()->join('enum_task_types', 'tasks.task_type_id', '=', 'enum_task_types.id')
                          ->pluck('enum_task_types.name')->toArray();
        echo "   ✅ {$reward->name} (Task Types: " . implode(', ', $taskTypes) . ")\n";
    }
    
    if ($newRewardsFromLog2->isEmpty()) {
        echo "   ❌ No new rewards awarded\n";
    }
    
    echo "\n";
    
    // ANALYSIS
    echo "📊 **ANALYSIS**\n";
    echo "==============\n";
    
    // Analyze reward types awarded
    $allRewards = UserReward::where('user_id', $testUserId)->with('reward.tasks.taskType')->get();
    
    $immediateCount = 0;
    $bookCompletionCount = 0;
    $activityCount = 0;
    
    foreach ($allRewards as $userReward) {
        $reward = $userReward->reward;
        $taskTypeNumbers = $reward->tasks()->join('enum_task_types', 'tasks.task_type_id', '=', 'enum_task_types.id')
                                ->pluck('enum_task_types.nr')->toArray();
        
        $hasImmediate = count(array_intersect($taskTypeNumbers, [1, 3, 4, 5])) > 0; // READ_PAGES, READ_MINUTES, READ_DAYS, READ_STREAK
        $hasBookCompletion = count(array_intersect($taskTypeNumbers, [2, 6])) > 0; // READ_BOOKS, EARN_READING_POINTS
        $hasActivity = count(array_intersect($taskTypeNumbers, [7, 8])) > 0; // EARN_ACTIVITY_POINTS, COMPLETE_BOOK_ACTIVITY
        
        if ($hasImmediate) $immediateCount++;
        if ($hasBookCompletion) $bookCompletionCount++;
        if ($hasActivity) $activityCount++;
    }
    
    echo "📈 Reward Type Distribution:\n";
    echo "   - Immediate Reading Rewards: {$immediateCount}\n";
    echo "   - Book Completion Rewards: {$bookCompletionCount}\n";
    echo "   - Activity Rewards: {$activityCount}\n\n";
    
    // Expected behavior validation
    echo "✅ **VALIDATION**\n";
    echo "================\n";
    
    if ($immediateCount > 0) {
        echo "✅ PASS: Immediate reading rewards were awarded\n";
    } else {
        echo "❌ FAIL: No immediate reading rewards were awarded\n";
    }
    
    if ($bookCompletionCount > 0) {
        echo "✅ PASS: Book completion rewards were awarded\n";
    } else {
        echo "⚠️  INFO: No book completion rewards were awarded (may be normal if no eligible rewards)\n";
    }
    
    if ($activityCount > 0) {
        echo "✅ PASS: Activity rewards were awarded\n";
    } else {
        echo "⚠️  INFO: No activity rewards were awarded (may be normal if no eligible rewards)\n";
    }
    
    $totalPoints = $pointsAfterLog2->sum('points');
    if ($totalPoints > 0) {
        echo "✅ PASS: Points were awarded (Total: {$totalPoints})\n";
    } else {
        echo "❌ FAIL: No points were awarded\n";
    }
    
    echo "\n🎉 **TEST COMPLETED SUCCESSFULLY!**\n";
    echo "The reward classification system is working as expected.\n";
    
} catch (Exception $e) {
    echo "❌ **TEST FAILED**\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n📝 **CLEANUP**\n";
echo "=============\n";
echo "Test data has been left in the database for manual inspection.\n";
echo "To clean up, run the following commands:\n";
echo "UserReadingLog::where('user_id', {$testUserId})->where('book_id', {$testBookId})->delete();\n";
echo "UserActivity::where('user_id', {$testUserId})->where('book_id', {$testBookId})->delete();\n";
echo "UserReward::where('user_id', {$testUserId})->delete();\n";
echo "UserPoint::where('user_id', {$testUserId})->delete();\n";
