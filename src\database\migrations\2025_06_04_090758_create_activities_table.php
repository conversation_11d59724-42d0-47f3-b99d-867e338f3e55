<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('activity_categories')->onDelete('cascade');
            $table->integer('activity_type')->default(1)->comment('1-Writing, 2-Rating, 3-Media, 4-Physical, 5-Game');
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('question_count')->nullable()->comment('Number of questions for quiz/vocabulary tests (1-10)');
            $table->integer('choices_count')->nullable()->comment('Number of answer choices per question (1-6)');
            $table->integer('min_grade')->nullable()->comment('Minimum passing grade out of 100');
            $table->boolean('required')->default(false)->comment('Whether activity is mandatory for book completion');
            $table->integer('allowed_tries')->default(1)->comment('Number of retry attempts allowed');
            $table->integer('min_word_count')->nullable();
            $table->integer('min_rating')->nullable();
            $table->integer('max_rating')->nullable();
            $table->string('media_url')->nullable()->comment('Playable game URL or media content');
            $table->integer('points')->default(0);
            $table->boolean('need_approval')->default(false);
            $table->integer('media_type')->nullable()->comment('1-image, 2-audio');
            $table->boolean('active')->default(true);
            
            // Add indexes for performance
            $table->index(['category_id', 'active']);
            $table->index('activity_type');
            $table->index('need_approval');
            $table->index('active');
            $table->index('points');
            $table->index('required');
            $table->index('allowed_tries');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activities');
    }
};
