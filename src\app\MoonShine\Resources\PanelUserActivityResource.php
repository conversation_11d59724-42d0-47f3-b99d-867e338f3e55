<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Contracts\UI\ActionButtonContract;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\QueryTags\QueryTag;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use App\Models\{Activity, Book, User, UserActivity};
use Illuminate\Support\Facades\Storage;
use MoonShine\Contracts\Core\DependencyInjection\FieldsContract;
use MoonShine\Support\{Attributes\Icon, ListOf};
use MoonShine\UI\Components\ActionButton;
use MoonShine\UI\Components\Components;
use MoonShine\UI\Fields\Preview;
use MoonShine\UI\Components\Layout\{Box, Flex, LineBreak};
use MoonShine\UI\Fields\{Date, File, Image, Number, Select, Text, Textarea};

#[Icon('clipboard-document-check')]
class PanelUserActivityResource extends UserActivityResource
{
    use WithRolePermissions;

    public function getTitle(): string
    {
        return __('admin.book_activities');
    }

    public function queryTags(): array
    {
        // add query tags for pending and completed activities
        return [
            QueryTag::make(__('admin.all'), static fn(Builder $query) => $query)            
                ->icon('arrows-pointing-in'),
            QueryTag::make( __('admin.status_pending'), static fn(Builder $query) => $query->where('status', UserActivity::STATUS_PENDING))
                ->icon('clock')
                ->default(),
            QueryTag::make( __('admin.status_completed'), static fn(Builder $query) => $query->where('status', UserActivity::STATUS_COMPLETED) )
                ->icon('check'),
            QueryTag::make( __('admin.status_approved'), static fn(Builder $query) => $query->where('status', UserActivity::STATUS_APPROVED) )
                ->icon('check-circle'),
            QueryTag::make( __('admin.status_rejected'), static fn(Builder $query) => $query->where('status', UserActivity::STATUS_REJECTED) )
                ->icon('x-mark'),
            QueryTag::make( __('admin.status_failed'), static fn(Builder $query) => $query->where('status', UserActivity::STATUS_FAILED) )
                ->icon('x-circle'),
        ];
    }

    protected function indexFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.student'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            )
                ->sortable(),

            Preview::make(__('admin.cover_image'), formatted:fn(UserActivity $userActivity) => asset('storage/' . $userActivity['book']['cover_image']))
                ->image(),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            )
                ->unescape()
                ->sortable(),

            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            )
                ->link(link: fn() => '#')
                ->sortable(),

            Date::make(__('admin.activity_date'), 'activity_date')
                ->format('d.m.Y')
                ->sortable(),

            Select::make(__('admin.status'), 'status')
                ->options(UserActivity::getStatusOptions())
                ->sortable(),

            Number::make(__('admin.points'), 'activity.points')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.student'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: StudentResource::class
                    )                   
                        ->valuesQuery(function (Builder $query) { return $query->forCurrentUser(); })
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.book'),
                        'book',
                        formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                        resource: PanelBookResource::class
                    )                    
                        ->required()
                        ->asyncSearch()
                        ->searchable(),
                ]),

                Flex::make([
                    BelongsTo::make(
                        __('admin.activity'),
                        'activity',
                        formatted: fn(Activity $activity) => $activity->name . ' (' . $activity->points . ' ' . __('admin.points') . ')',
                        resource: ActivityResource::class
                    )
                        // list activities for teachers default class only from class activities
                        ->valuesQuery(function (Builder $query) { 
                            return $query->whereHas('classActivities', function ($q) {
                                $q->where('class_id', auth('moonshine')->user()->getDefaultClass()->class_id ?? 0);
                            });
                        })
                        ->reactive(function (FieldsContract $fields, $value, $field, $values) {
                            // find activity type and show/hide fields accordingly
                            $activity = Activity::find($value);
                            $fields->findByColumn('rating')->disabled($activity->activity_type !== Activity::ACTIVITY_TYPE_RATING);
                            $fields->findByColumn('media_url')->disabled($activity->activity_type !== Activity::ACTIVITY_TYPE_MEDIA);
                            return $fields;
                        })
                        ->nullable()
                        ->required()
                        ->searchable(),

                    Date::make(__('admin.activity_date'), 'activity_date')
                        ->required()
                        ->default(now()->format('Y-m-d')),
                ]),

                Textarea::make(__('admin.content'), 'content')
                    ->hint(__('admin.activity_content_hint')),

                Flex::make([
                    Number::make(__('admin.rating'), 'rating')
                        ->reactive()
                        ->min(1)
                        ->max(10)
                        ->hint(__('admin.activity_rating_hint')),

                    File::make(__('admin.media_url'), 'media_url')
                        ->dir('user_activities')
                        ->removable()                        
                        ->reactive()
                        ->hint(__('admin.activity_media_url_hint')),
                ]),

                Select::make(__('admin.status'), 'status')
                    ->options(UserActivity::getStatusOptions())
                    ->default(UserActivity::STATUS_PENDING),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        $ratingField = $this->item->activity->activity_type === Activity::ACTIVITY_TYPE_RATING ?  [ Number::make(__('admin.rating'), 'rating') ] : [];
        $testFields = $this->item->isTestActivity() ? [
            Text::make(__('admin.test_score'), 'test_score'),
            Text::make(__('admin.test_passed'), 'test_passed'),
            Text::make(__('admin.attempt_count'), 'attempt_count'),
        ] : [
            Text::make(__('admin.content'), 'content'),
        ];

        $mediaField = [];
        if($this->item->activity->media_type === Activity::MEDIA_TYPE_IMAGE) {
            $mediaField = [ Image::make(__('admin.media_content'), 'media_url') ];
        } elseif ($this->item->activity->media_type === Activity::MEDIA_TYPE_AUDIO) {
            $mediaField = [ File::make(__('admin.media_content'), 'media_url')
                                ->changePreview(fn(?string $value) => "<audio width='300px' controls><source src='" . asset('storage/' . $value) . "' type='audio/mpeg'></audio>")
                                ->changeRender(fn(?string $value, File $ctx) => (string) Components::make([
                                    File::make($ctx->getLabel())->withoutWrapper(),
                                    LineBreak::make(),
                                    $ctx->preview()
                                ])),
            ];
        }

        return [
            BelongsTo::make(
                __('admin.student'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name)   . ' - ' . $book->isbn,
                resource: BookResource::class
            ),

            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            ),

            Date::make(__('admin.activity_date'), 'activity_date')
                ->format('d.m.Y H:i'),            
            ...$ratingField,
                
            ...$mediaField,
            Text::make(__('admin.status'), 'localized_status_name'),
            ...$testFields,
            Number::make(__('admin.points'), 'activity.points'),
        ];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        // Only show users of the current user's default class
        return $builder->whereHas('user.activeUserClasses', function ($q) {
            $q->where('class_id', auth('moonshine')->user()->getDefaultClass()->class_id ?? 0);
        });

    }    
    protected function indexButtons(): ListOf
    {
        return new ListOf(ActionButtonContract::class, [
            $this->getDetailButton(),
        ]);
    }

    protected function topButtons(): ListOf
    {
        $resource = app(PanelClassActivityResource::class);
        return parent::topButtons()
            ->add(
                ActionButton::make(__('admin.activity_types'), fn($review) => $resource->getIndexPageUrl())
                    ->info()
                    ->icon('academic-cap')                
            );
    }
}
