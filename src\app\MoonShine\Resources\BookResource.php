<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Services\BookDiscovery\BookDiscoveryService;
use Illuminate\Support\Facades\Log;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;

use App\Models\{Book, BookType, Publisher};
use MoonShine\Laravel\Fields\Relationships\{BelongsTo, BelongsToMany, HasMany};
use MoonShine\Laravel\MoonShineRequest;
use MoonShine\Support\{Attributes\Icon, Enums\JsEvent, Enums\ToastType, AlpineJs};
use MoonShine\UI\Components\{Layout\Box, Layout\Flex, ActionButton, Badge, CardsBuilder};
use MoonShine\UI\Fields\{Image, Number, Switcher, Text};

#[Icon('book-open')]

class BookResource extends BaseResource
{
    protected string $model = Book::class;

    protected string $column = 'name';

    protected array $with = ['publisher', 'authors'];

    public function getTitle(): string
    {
        return __('admin.books');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Image::make(__('admin.cover_image'), 'cover_image'),

            Text::make(__('admin.name'), 'name')
                ->unescape()
                ->sortable(),

            Text::make(__('admin.isbn'), 'isbn')
                ->sortable(),

            BelongsTo::make(
                __('admin.publisher'),
                'publisher',
                formatted: fn(Publisher $publisher) => $publisher->name
            )
                ->sortable(),

            Text::make(__('admin.author_names'), 'author_names'),

            Number::make(__('admin.page_count'), 'page_count')
                ->sortable(),

            Number::make(__('admin.year_of_publish'), 'year_of_publish')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.isbn'), 'isbn')
                        ->required()
                        ->placeholder(__('admin.enter_isbn'))
                        ->hint(__('admin.isbn_hint')),

                        
                    ActionButton::make(__('admin.search_import_book'))
                            ->method('searchAndImportBook') 
                            ->withSelectorsParams(['isbn' => 'input[name="isbn"]'])
                            ->secondary()
                            ->icon('magnifying-glass')
                ]),

                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),

                Flex::make([
                    BelongsTo::make(
                        __('admin.publisher'),
                        'publisher',
                        formatted: fn(Publisher $publisher) => $publisher->name,
                        resource: PublisherResource::class
                    )
                        ->creatable()
                        ->searchable()
                        ->placeholder(__('admin.select_publisher'))
                        ->hint(__('admin.publisher_auto_filled')),

                    BelongsTo::make(
                        __('admin.book_type'),
                        'bookType',
                        formatted: fn(BookType $bookType) => $bookType->name,
                        resource: BookTypeResource::class)
                        ->withImage('thumbnail')
                        ->required()
                        ->placeholder(__('admin.select_book_type')),
                ]),

                Flex::make([
                    Number::make(__('admin.page_count'), 'page_count')
                        ->required()
                        ->min(1)
                        ->placeholder(__('admin.enter_page_count')),

                    Number::make(__('admin.year_of_publish'), 'year_of_publish')
                        ->required()
                        ->min(1900)
                        ->max(intval(date('Y')))
                        ->placeholder(__('admin.enter_year')),
                ]),
                BelongsToMany::make(__('admin.authors'), 'authors', resource: AuthorResource::class, formatted: 'name')
                    ->creatable()
                    ->selectMode(),
                BelongsToMany::make(__('admin.categories'), 'categories', resource: CategoryResource::class, formatted: 'name')
                    ->selectMode(),
                Image::make(__('admin.cover_image'), 'cover_image')
                    ->dir('books/covers')
                    ->removable()
                    ->keepOriginalFileName(),
                Switcher::make(__('admin.active'), 'active')
                    ->onValue(1)
                    ->offValue(0),
            ]),


        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Image::make(__('admin.cover_image'), 'cover_image'),
            Text::make(__('admin.name'), 'name')->unescape(),
            Text::make(__('admin.isbn'), 'isbn'),
            Text::make(__('admin.publisher'), 'publisher.name'),
            Text::make(__('admin.book_type'), 'bookType.name'),
            Number::make(__('admin.page_count'), 'page_count'),
            Number::make(__('admin.year_of_publish'), 'year_of_publish'),
            BelongsToMany::make(__('admin.authors'), 'authors', resource: AuthorResource::class, formatted: 'name')
                ->inLine(separator: ' ', badge: fn($model, $value) => Badge::make((string) $value, 'primary')),
            BelongsToMany::make(__('admin.categories'), 'categories', resource: CategoryResource::class, formatted: 'name')
                ->inLine(separator: ' ', badge: fn($model, $value) => Badge::make((string) $value, 'primary')),
            Switcher::make(__('admin.active'), 'active')
                ->onValue(1)
                ->offValue(0),
            ...parent::getCommonDetailFields(),
            HasMany::make(__('admin.book_questions'), 'questions', resource: BookQuestionResource::class)
                ->creatable()
                ->searchable(false),
            HasMany::make(__('admin.book_words'), 'words', resource: BookWordResource::class)
                ->creatable()
                ->searchable(false),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'isbn' => ['required', 'string', 'max:255', 'unique:books,isbn,' . $item?->id],
            'publisher_id' => ['nullable', 'exists:publishers,id'],
            'book_type_id' => ['nullable', 'exists:book_types,id'],
            'page_count' => ['nullable', 'integer', 'min:1'],
            'year_of_publish' => ['nullable', 'integer', 'min:1900', 'max:' . date('Y')],
            'cover_image' => ['image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    /**
     * Search and import book data using the Book Discovery Service
     */
    public function searchAndImportBook(MoonShineRequest $request): MoonShineJsonResponse
    {
        try {
            $isbn = $request->get('isbn');
            if (empty($isbn)) {
                return MoonShineJsonResponse::make()->toast(__('admin.enter_isbn_to_search'), ToastType::ERROR);
            }

            // Clean and validate ISBN
            $cleanIsbn = preg_replace('/[^0-9X]/i', '', strtoupper($isbn));

            if (!$this->isValidIsbn($cleanIsbn)) {
                return MoonShineJsonResponse::make()->toast(__('admin.invalid_isbn_format'), ToastType::ERROR);
            }

            // Check if book already exists
            $existingBook = Book::where('isbn', $cleanIsbn)->first();
            if ($existingBook) {
                return MoonShineJsonResponse::make()
                    ->toast(__('admin.book_already_exists'), ToastType::ERROR)
                    ->redirect($this->getFormPageUrl($existingBook->id));
            }

            // Use discovery service to search for book
            $discoveryService = app(BookDiscoveryService::class);
            $bookData = $discoveryService->searchByIsbn($cleanIsbn);

            if (!$bookData) {
                Log::info('Book not found in any external sources', ['isbn' => $cleanIsbn]);
                return MoonShineJsonResponse::make()->toast(__('admin.book_not_found_sources'), ToastType::ERROR);
            }

            if (isset($bookData['exists_locally']) && $bookData['exists_locally']) {
                return MoonShineJsonResponse::make()
                    ->toast(__('admin.book_exists_locally'), ToastType::ERROR)
                    ->redirect($this->getFormPageUrl($bookData['id']));
            }

            // Create book from data
            $book = $discoveryService->createBookFromData($bookData, auth()->id());

            if ($book) {
                Log::info('Book created successfully from discovery service', [
                    'book_id' => $book->id,
                    'isbn' => $cleanIsbn,
                    'source' => $bookData['source'] ?? 'unknown'
                ]);

                return MoonShineJsonResponse::make()
                    ->toast(__('admin.book_found_auto_populated'), ToastType::SUCCESS)
                    ->redirect($this->getFormPageUrl($book->id));
            } else {
                return MoonShineJsonResponse::make()->toast(__('admin.book_search_error'), ToastType::ERROR);
            }

        } catch (\Exception $e) {
            Log::error('Book discovery error in BookResource', [
                'isbn' => $isbn ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return MoonShineJsonResponse::make()->toast(__('admin.book_search_error'), ToastType::ERROR);
        }
    }

    /**
     * Validate ISBN format
     */
    protected function isValidIsbn(string $isbn): bool
    {
        $length = strlen($isbn);

        if ($length !== 10 && $length !== 13) {
            return false;
        }

        if ($length === 10) {
            return (bool) preg_match('/^[0-9]{9}[0-9X]$/i', $isbn);
        } else {
            return (bool) preg_match('/^[0-9]{13}$/', $isbn);
        }
    }

    protected function search(): array
    {
        return ['name', 'isbn'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }

    public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();

        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }

    public function getQuestionsAndWordsCountBadge(Book $book): ?Badge
    {
        return $book->questions_and_words_count ? Badge::make($book->questions_and_words_count, 'success') : null;
    }

    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), []) //$this->getIndexFields())
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
            ->header(fn($book) => $this->getQuestionsAndWordsCountBadge($book) )
            ->title( fn($book) => html_entity_decode($book->name))
            ->subtitle('author_names')
            ->url(fn($book) => $this->getFormPageUrl($book->getKey()))
            ->thumbnail(fn($book) => asset('storage/' . $book->cover_image))
            ->buttons($this->getIndexButtons())
            ->fields([
                Text::make(__('admin.isbn'), 'isbn'),
                // Text::make(__('admin.publisher'), 'publisher.name'),
                // Text::make(__('admin.book_type'), 'bookType.name'),
                // Number::make(__('admin.page_count'), 'page_count'),
                // Number::make(__('admin.year_of_publish'), 'year_of_publish'),
                    ]);
    }
}
