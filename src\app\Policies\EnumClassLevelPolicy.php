<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\EnumClassLevel;
use App\Models\User;

class EnumClassLevelPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // All users can view enum class levels for selection purposes
        return true;
    }

    public function view(User $user, EnumClassLevel $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return $user->isSystemAdmin();
    }

    public function update(User $user, EnumClassLevel $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function delete(User $user, EnumClassLevel $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function restore(User $user, EnumClassLevel $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function forceDelete(User $user, EnumClassLevel $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin();
    }
}
