<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Task extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'task_type_id',
        'task_cycle_id',
        'task_value',
        'activity_id',
        'active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'task_value' => 'integer',
            'active' => 'boolean',
        ];
    }

    /**
     * Get the task type for this task.
     */
    public function taskType(): BelongsTo
    {
        return $this->belongsTo(EnumTaskType::class, 'task_type_id');
    }

    /**
     * Get the task cycle for this task.
     */
    public function taskCycle(): BelongsTo
    {
        return $this->belongsTo(EnumTaskCycle::class, 'task_cycle_id');
    }

    /**
     * Get the activity for this task (if applicable).
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class);
    }

    /**
     * Get the user who created this task.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the task books for this task.
     */
    public function taskBooks(): HasMany
    {
        return $this->hasMany(TaskBook::class);
    }

    /**
     * Get the task book categories for this task.
     */
    public function taskBookCategories(): HasMany
    {
        return $this->hasMany(TaskBookCategory::class);
    }

    /**
     * Get the books associated with this task.
     */
    public function books(): BelongsToMany
    {
        return $this->belongsToMany(Book::class, 'task_books');
    }

    /**
     * Get the categories associated with this task.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'task_book_categories');
    }

    // get user tasks
    public function userTasks(): HasMany
    {
        return $this->hasMany(UserTask::class);
    }

    /**
     * Scope to get active tasks.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to get tasks by type.
     */
    public function scopeByType($query, $taskTypeId)
    {
        return $query->where('task_type_id', $taskTypeId);
    }

    /**
     * Scope to get tasks by cycle.
     */
    public function scopeByCycle($query, $taskCycleId)
    {
        return $query->where('task_cycle_id', $taskCycleId);
    }

    /**
     * Scope to get quantitative tasks.
     */
    public function scopeQuantitative($query)
    {
        return $query->whereHas('taskType', function ($q) {
            $q->whereIn('nr', EnumTaskType::getQuantitativeTypes());
        });
    }

    /**
     * Scope to get qualitative tasks.
     */
    public function scopeQualitative($query)
    {
        return $query->whereHas('taskType', function ($q) {
            $q->whereIn('nr', EnumTaskType::getQualitativeTypes());
        });
    }

    /**
     * Check if this task is quantitative (requires task_value).
     */
    public function isQuantitative(): bool
    {
        return $this->taskType ? $this->taskType->isQuantitative() : false;
    }

    /**
     * Check if this task is qualitative (book list completion).
     */
    public function isQualitative(): bool
    {
        return $this->taskType ? $this->taskType->isQualitative() : false;
    }

    /**
     * Check if this task requires book list selection.
     */
    public function requiresBookList(): bool
    {
        return $this->taskType ? $this->taskType->requiresBookList() : false;
    }

    /**
     * Check if this task requires book categories selection.
     */
    public function requiresBookCategories(): bool
    {
        return $this->taskType ? $this->taskType->requiresBookCategories() : false;
    }

    /**
     * Check if this task requires activity selection.
     */
    public function requiresActivity(): bool
    {
        return $this->taskType ? $this->taskType->requiresActivity() : false;
    }

    /**
     * Check if this task is measurable (has numeric progress).
     */
    public function isMeasurable(): bool
    {
        return $this->taskType ? $this->taskType->isMeasurable() : false;
    }

    /**
     * Check if this task is binary (achieved/not achieved only).
     */
    public function isBinary(): bool
    {
        return $this->taskType ? $this->taskType->isBinary() : false;
    }

    /**
     * Get the task type name.
     */
    public function getTaskTypeNameAttribute(): string
    {
        return $this->taskType ? $this->taskType->name : '';
    }

    /**
     * Get the task cycle name.
     */
    public function getTaskCycleNameAttribute(): string
    {
        return $this->taskCycle ? $this->taskCycle->name : '';
    }

    /**
     * Get the task value with unit.
     */
    public function getTaskValueWithUnitAttribute(): string
    {
        if (!$this->isQuantitative() || is_null($this->task_value)) {
            return '';
        }

        $unit = $this->taskType ? $this->taskType->unit : '';
        return $this->task_value . ' ' . $unit;
    }

    /**
     * Get the activity name (if applicable).
     */
    public function getActivityNameAttribute(): string
    {
        return $this->activity ? $this->activity->name : '';
    }

    /**
     * Get the book count for this task.
     */
    public function getBooksCountAttribute(): int
    {
        return $this->books()->count();
    }

    /**
     * Get the category count for this task.
     */
    public function getCategoriesCountAttribute(): int
    {
        return $this->categories()->count();
    }

    /**
     * Get the task configuration summary.
     */
    public function getConfigurationSummaryAttribute(): string
    {
        $parts = [];
        
        $parts[] = $this->task_cycle_name;
        if ($this->isQuantitative() && $this->task_value) {
            $parts[] = $this->task_value;
        }
        $parts[] = $this->task_type_name;        
        
        if ($this->requiresActivity() && $this->activity) {
            $parts[] = __('admin.activity') . ': ' . $this->activity_name;
        }
        
        if ($this->requiresBookList() && $this->books_count > 0) {
            $parts[] = $this->books_count . ' ' . __('admin.books_localizable');
        }
        
        if ($this->categories_count > 0) {
            $parts[] = $this->categories_count . ' ' . __('admin.categories_localizable');
        }
        
        return implode(' | ', $parts);
    }

    /**
     * Get the display name for this task.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' (' . $this->task_type_name . ')';
    }

    /**
     * Get the summary of this task.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            '%s - %s',
            $this->name,
            $this->configuration_summary
        );
    }
}
