<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('book_words', function (Blueprint $table) {
            $table->id();
            
            // Core word information
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->string('word')->comment('The vocabulary word');
            $table->text('definition')->nullable()->comment('Word definition');
            $table->string('synonym')->nullable()->comment('Synonym of the word');
            $table->string('antonym')->nullable()->comment('Antonym of the word');
            
            // Page reference
            $table->integer('page_reference')->nullable()->comment('Page where word appears');
            
            // Word metadata
            $table->enum('difficulty_level', ['easy', 'medium', 'hard'])->default('medium');
            $table->boolean('is_active')->default(true);
            
                                    
            // Indexes for performance
            $table->index(['book_id', 'is_active']);
            $table->index(['book_id', 'difficulty_level']);
            $table->index(['word', 'book_id']);
            $table->index('page_reference');
            $table->index('difficulty_level');
            
            // Unique constraint to prevent duplicate words per book
            $table->unique(['book_id', 'word'], 'unique_book_word');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('book_words');
    }
};
