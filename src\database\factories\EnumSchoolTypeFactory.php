<?php

namespace Database\Factories;

use App\Models\EnumSchoolType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EnumSchoolType>
 */
class EnumSchoolTypeFactory extends Factory
{
    protected $model = EnumSchoolType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->unique()->randomElement(['İlkokul', 'Ortaokul', 'Lise', 'Anaokulu']) . ' ' . fake()->unique()->numberBetween(1, 1000),
        ];
    }
}
