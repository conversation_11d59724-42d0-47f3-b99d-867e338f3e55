<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PagePoint extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'book_type_id',
        'class_level_id',
        'point',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'point' => 'decimal:2',            
        ];
    }

    /**
     * Get the book type.
     */
    public function bookType(): BelongsTo
    {
        return $this->belongsTo(BookType::class);
    }

    /**
     * Get the class level.
     */
    public function classLevel(): BelongsTo
    {
        return $this->belongsTo(EnumClassLevel::class);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBookType($query, $bookTypeId)
    {
        return $query->where('book_type_id', $bookTypeId);
    }

    /**
     * Scope to filter by class level.
     */
    public function scopeByClassLevel($query, $classLevelId)
    {
        return $query->where('class_level_id', $classLevelId);
    }
}
