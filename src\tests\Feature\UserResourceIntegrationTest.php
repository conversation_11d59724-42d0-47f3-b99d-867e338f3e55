<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\UserClass;
use App\Models\UserSchool;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserResourceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'system_admin', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'school_admin', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'teacher', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'student', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'parent', 'guard_name' => 'moonshine']);
    }

    public function test_user_resource_applies_local_scope_filtering_for_system_admin()
    {
        $admin = User::factory()->create();
        $admin->assignRole('system_admin');

        $student = User::factory()->create();
        $student->assignRole('student');

        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        // Authenticate as system admin
        $this->actingAs($admin, 'moonshine');

        // Test the local scope directly
        $users = User::forCurrentUser()->get();

        // System admin should see all users
        $this->assertCount(3, $users);

        $userIds = $users->pluck('id')->toArray();
        $this->assertContains($admin->id, $userIds);
        $this->assertContains($student->id, $userIds);
        $this->assertContains($teacher->id, $userIds);
    }

    public function test_user_resource_applies_local_scope_filtering_for_school_admin()
    {
        $school1 = School::factory()->create();
        $school2 = School::factory()->create();
        
        $class1 = SchoolClass::factory()->create(['school_id' => $school1->id]);
        $class2 = SchoolClass::factory()->create(['school_id' => $school2->id]);
        
        $schoolAdmin = User::factory()->create();
        $schoolAdmin->assignRole('school_admin');
        
        $teacher1 = User::factory()->create();
        $teacher1->assignRole('teacher');
        
        $student1 = User::factory()->create();
        $student1->assignRole('student');
        
        $student2 = User::factory()->create();
        $student2->assignRole('student');

        // Assign school admin to school1
        $schoolAdminRole = Role::where('name', 'school_admin')->first();
        UserSchool::create([
            'user_id' => $schoolAdmin->id,
            'school_id' => $school1->id,
            'role_id' => $schoolAdminRole->id,
            'active' => true,
        ]);

        // Assign teacher1 to school1
        UserClass::create([
            'user_id' => $teacher1->id,
            'class_id' => $class1->id,
            'school_id' => $school1->id,
            'active' => true,
        ]);

        // Assign student1 to school1
        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class1->id,
            'school_id' => $school1->id,
            'active' => true,
        ]);

        // Assign student2 to school2 (different school)
        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class2->id,
            'school_id' => $school2->id,
            'active' => true,
        ]);

        // Authenticate as school admin
        $this->actingAs($schoolAdmin, 'moonshine');

        // Test the local scope directly
        $users = User::forCurrentUser()->get();
        
        // School admin should see themselves, teacher1, and student1 (all in school1)
        $this->assertCount(3, $users);
        
        $userIds = $users->pluck('id')->toArray();
        $this->assertContains($schoolAdmin->id, $userIds);
        $this->assertContains($teacher1->id, $userIds);
        $this->assertContains($student1->id, $userIds);
        $this->assertNotContains($student2->id, $userIds);
    }

    public function test_user_resource_applies_local_scope_filtering_for_teacher()
    {
        $school = School::factory()->create();
        $class1 = SchoolClass::factory()->create(['school_id' => $school->id]);
        $class2 = SchoolClass::factory()->create(['school_id' => $school->id]);
        
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');
        
        $student1 = User::factory()->create();
        $student1->assignRole('student');
        
        $student2 = User::factory()->create();
        $student2->assignRole('student');
        
        $student3 = User::factory()->create();
        $student3->assignRole('student');

        // Assign teacher to class1
        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class1->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Assign student1 and student2 to class1 (teacher's class)
        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class1->id,
            'school_id' => $school->id,
            'active' => true,
        ]);
        
        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class1->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Assign student3 to class2 (different class)
        UserClass::create([
            'user_id' => $student3->id,
            'class_id' => $class2->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Authenticate as teacher
        $this->actingAs($teacher, 'moonshine');

        // Test the local scope directly
        $users = User::forCurrentUser()->get();

        // Teacher should see themselves and students in their class (student1, student2)
        $this->assertCount(3, $users);

        $userIds = $users->pluck('id')->toArray();
        $this->assertContains($teacher->id, $userIds);
        $this->assertContains($student1->id, $userIds);
        $this->assertContains($student2->id, $userIds);
        $this->assertNotContains($student3->id, $userIds);
    }

    public function test_user_resource_applies_local_scope_filtering_for_student()
    {
        $student1 = User::factory()->create();
        $student1->assignRole('student');

        $student2 = User::factory()->create();
        $student2->assignRole('student');

        // Authenticate as student1
        $this->actingAs($student1, 'moonshine');

        // Test the local scope directly
        $users = User::forCurrentUser()->get();

        // Student should only see themselves
        $this->assertCount(1, $users);
        $this->assertEquals($student1->id, $users->first()->id);
    }

    public function test_user_resource_denies_access_for_unauthenticated_user()
    {
        $student = User::factory()->create();
        $student->assignRole('student');

        // No authentication

        // Test the local scope directly
        $users = User::forCurrentUser()->get();

        // Unauthenticated user should see no users
        $this->assertCount(0, $users);
    }

    public function test_user_resource_denies_access_for_user_without_role()
    {
        $user = User::factory()->create();
        // No role assigned

        // Authenticate as user without role
        $this->actingAs($user, 'moonshine');

        // Test the local scope directly
        $users = User::forCurrentUser()->get();

        // User without role should see no users
        $this->assertCount(0, $users);
    }

    public function test_user_social_methods_work_without_interference()
    {
        $school = School::factory()->create();
        $class = SchoolClass::factory()->create(['school_id' => $school->id]);
        
        $teacher = User::factory()->create([
            'username' => 'teacher',
            'password' => Hash::make('password123'),
        ]);
        $teacher->assignRole('teacher');
        
        $student1 = User::factory()->create();
        $student1->assignRole('student');
        
        $student2 = User::factory()->create();
        $student2->assignRole('student');

        // Set up class relationships
        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Authenticate as teacher
        $this->actingAs($teacher, 'moonshine');

        // User social methods should work without circular dependency issues
        $friends = $teacher->getFriends();
        $classmates = $teacher->getClassmates();
        $teammates = $teacher->getTeammates();
        
        $this->assertIsIterable($friends);
        $this->assertIsIterable($classmates);
        $this->assertIsIterable($teammates);
        
        // These methods should not be affected by local scopes
        // They should work based on the user's own relationships
        $this->assertCount(2, $classmates); // Should see student1 and student2
    }
}
