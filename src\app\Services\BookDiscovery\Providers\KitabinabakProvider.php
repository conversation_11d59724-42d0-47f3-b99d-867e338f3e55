<?php

namespace App\Services\BookDiscovery\Providers;

use App\Services\BookDiscovery\AbstractBookDiscoveryProvider;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use DOMXPath;

class KitabinabakProvider extends AbstractBookDiscoveryProvider
{
    /**
     * Build search URL for the given ISBN.
     */
    protected function buildSearchUrl(string $isbn): string
    {
        return "https://www.kitabinabak.com/sorgu/Kitap?bak={$isbn}";
    }

    /**
     * Check if the page indicates book not found.
     */
    protected function isBookNotFound(string $html): bool
    {
        // Check for "Sonuç bulunamadı" text pattern
        if (stripos($html, 'Sonuç bulunamadı') !== false) {
            return true;
        }

        // If "sonuç listeleniyor" is found, books are available
        if (stripos($html, 'sonuç listeleniyor') !== false) {
            return false;
        }

        // Also check parent's generic logic
        return parent::isBookNotFound($html);
    }

    /**
     * Parse book data from HTML.
     */
    protected function parseBookData(string $html): ?array
    {
        // First, try to extract detail URL from search results
        $detailUrl = $this->extractDetailUrl($html);
        
        if (!$detailUrl) {
            Log::warning("Kitabinabak: Could not extract detail URL from search results");
            return null;
        }

        // Fetch the detail page
        $detailHtml = $this->fetchDetailPage($detailUrl);
        
        if (!$detailHtml) {
            Log::warning("Kitabinabak: Could not fetch detail page", ['url' => $detailUrl]);
            return null;
        }

        // Parse book data from detail page
        $bookData = $this->parseDetailPage($detailHtml);

        if (!$bookData) {
            return null;
        }

        // Post-process the data
        $bookData = $this->postProcessKitabinabakData($bookData);

        return $bookData;
    }

    /**
     * Extract detail URL from search results page.
     */
    protected function extractDetailUrl(string $html): ?string
    {
        $dom = $this->createDomDocument($html);
        if (!$dom) {
            return null;
        }

        $xpath = new DOMXPath($dom);
        
        // Look for the first div.single-product
        $productNodes = $xpath->query('//div[contains(@class, "single-product")]');
        
        if ($productNodes->length === 0) {
            return null;
        }

        // Find the product link within the first product
        $linkNodes = $xpath->query('.//a[contains(@class, "product-in-list-link")]', $productNodes->item(0));
        
        if ($linkNodes->length === 0) {
            return null;
        }

        $href = $linkNodes->item(0)->getAttribute('href');
        
        if (empty($href)) {
            return null;
        }

        // If it's a relative URL, make it absolute
        if (strpos($href, 'http') !== 0) {
            $href = 'https://www.kitabinabak.com' . $href;
        }

        return $href;
    }

    /**
     * Fetch detail page content.
     */
    protected function fetchDetailPage(string $url): ?string
    {
        try {
            $response = Http::timeout($this->config['timeout'])
                ->withHeaders([
                    'User-Agent' => $this->getRandomUserAgent(),
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language' => 'tr-TR,tr;q=0.9,en;q=0.8',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                    'Upgrade-Insecure-Requests' => '1',
                ])
                ->withoutVerifying()
                ->get($url);

            if ($response->successful()) {
                return $response->body();
            }

            Log::warning("Kitabinabak: HTTP error fetching detail page", [
                'url' => $url,
                'status' => $response->status()
            ]);

        } catch (\Exception $e) {
            Log::error("Kitabinabak: Exception fetching detail page", [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Parse book data from detail page HTML.
     */
    protected function parseDetailPage(string $html): ?array
    {
        $dom = $this->createDomDocument($html);
        if (!$dom) {
            return null;
        }

        $xpath = new DOMXPath($dom);
        $bookData = [];

        // Extract book title from h1.bookDescTitle
        $titleNodes = $xpath->query('//h1[contains(@class, "bookDescTitle")]');
        if ($titleNodes->length > 0) {
            $title = trim($titleNodes->item(0)->textContent);
            if (!empty($title)) {
                $bookData['name'] = html_entity_decode($title, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
        }

// kitabinabak cover images are missing for now
$bookData['cover_image'] = '';
/*        
        // Extract cover image from img.large-img
        $imageNodes = $xpath->query('//img[contains(@class, "large-img")]');
        if ($imageNodes->length > 0) {
            $src = $imageNodes->item(0)->getAttribute('src');
            if (!empty($src)) {
                // Make absolute URL if needed
                if (strpos($src, 'http') !== 0) {
                    $src = 'https://www.kitabinabak.com' . $src;
                }
                $bookData['cover_image'] = $src;
            }
        }
*/

        // Extract ISBN from meta tag
        $isbnNodes = $xpath->query('//meta[@itemprop="isbn"]');
        if ($isbnNodes->length > 0) {
            $isbn = $isbnNodes->item(0)->getAttribute('content');
            if (!empty($isbn)) {
                $bookData['isbn'] = trim($isbn);
            }
        }

        // Extract authors
        $this->extractAuthors($xpath, $bookData);

        // Extract publisher
        $this->extractPublisher($xpath, $bookData);

        // Extract page count
        $this->extractPageCount($xpath, $bookData);

        // Extract publication year
        $this->extractPublicationYear($xpath, $bookData);

        return !empty($bookData) ? $bookData : null;
    }

    /**
     * Extract authors from detail page.
     */
    protected function extractAuthors(DOMXPath $xpath, array &$bookData): void
    {
        // Look for the "Yazar" label and extract authors from adjacent div
        $authorNodes = $xpath->query('//div[contains(@class, "col-lg-3")]/b[text()="Yazar"]/../../following-sibling::div[contains(@class, "col-lg-9")]//a');
        
        if ($authorNodes->length > 0) {
            $authors = [];
            foreach ($authorNodes as $authorNode) {
                $authorName = trim($authorNode->textContent);
                if (!empty($authorName)) {
                    $authors[] = html_entity_decode($authorName, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                }
            }
            
            if (!empty($authors)) {
                $bookData['author'] = $authors;
            }
        }
    }

    /**
     * Extract publisher from detail page.
     */
    protected function extractPublisher(DOMXPath $xpath, array &$bookData): void
    {
        // Look for publisher in div with itemprop="publisher"
        $publisherNodes = $xpath->query('//div[@itemprop="publisher"]//div[@itemprop="name"]');
        
        if ($publisherNodes->length > 0) {
            $publisher = trim($publisherNodes->item(0)->textContent);
            if (!empty($publisher)) {
                $bookData['publisher'] = html_entity_decode($publisher, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
        }
    }

    /**
     * Extract page count from detail page.
     */
    protected function extractPageCount(DOMXPath $xpath, array &$bookData): void
    {
        // Look for page count in div with itemprop="numberOfPages"
        $pageNodes = $xpath->query('//div[@itemprop="numberOfPages"]');
        
        if ($pageNodes->length > 0) {
            $pageText = trim($pageNodes->item(0)->textContent);
            if (!empty($pageText) && is_numeric($pageText)) {
                $bookData['page_count'] = (int) $pageText;
            }
        }
    }

    /**
     * Extract publication year from detail page.
     */
    protected function extractPublicationYear(DOMXPath $xpath, array &$bookData): void
    {
        // Look for "Yayın" label and extract year from adjacent div
        $yearNodes = $xpath->query('//div/b[text()="Yayın"]/../../following-sibling::div');
        if ($yearNodes->length > 0) {
            $yearText = trim($yearNodes->item(0)->textContent);
            if (!empty($yearText)) {
                // Extract 4-digit year from the text
                if (preg_match('/(\d{4})/', $yearText, $matches)) {
                    $year = (int) $matches[1];
                    if ($year >= 1000 && $year <= (date('Y') + 1)) {
                        $bookData['year'] = $year;
                    }
                }
            }
        }
    }

    /**
     * Post-process Kitabinabak specific data.
     */
    protected function postProcessKitabinabakData(array $bookData): array
    {
        // Clean and validate book title
        if (isset($bookData['name'])) {
            $bookData['name'] = $this->cleanText($bookData['name']);
        }

        // Clean author names
        if (isset($bookData['author'])) {
            if (is_array($bookData['author'])) {
                $bookData['author'] = array_map([$this, 'cleanText'], $bookData['author']);
                $bookData['author'] = array_filter($bookData['author']); // Remove empty values
            } else {
                $bookData['author'] = [$this->cleanText($bookData['author'])];
            }
        }

        // Clean publisher name
        if (isset($bookData['publisher'])) {
            $bookData['publisher'] = $this->cleanText($bookData['publisher']);
        }

        // Add source information
        $bookData['source'] = 'Kitabinabak';

        return $bookData;
    }

    /**
     * Clean text content.
     */
    protected function cleanText(string $text): string
    {
        // Remove extra whitespace and decode HTML entities
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * Create DOM document from HTML with proper error handling.
     */
    protected function createDomDocument(string $html): ?DOMDocument
    {
        $dom = new DOMDocument();
        
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
        
        // Load HTML with UTF-8 encoding
        $success = @$dom->loadHTML('<?xml encoding="UTF-8">' . $html);
        
        // Clear libxml errors
        libxml_clear_errors();
        
        return $success ? $dom : null;
    }

    /**
     * Get random user agent from config.
     */
    protected function getRandomUserAgent(): string
    {
        $userAgents = $this->config['user_agents'] ?? [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ];
        
        return $userAgents[array_rand($userAgents)];
    }
}
