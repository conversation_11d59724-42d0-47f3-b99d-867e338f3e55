-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Anamakine: 127.0.0.1:3306
-- <PERSON><PERSON><PERSON>: 24 Tem 2025, 14:48:04
-- <PERSON><PERSON><PERSON>: 10.11.8-MariaDB
-- P<PERSON>ümü: 8.3.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Veritabanı: `moondb`
--

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `authors`
--

CREATE TABLE `authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `authors`
--

INSERT INTO `authors` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Nazim Hikmet', NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(2, 'Yaşar Kemal', NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(3, 'Orhan Pamuk', NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `books`
--

CREATE TABLE `books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `isbn` varchar(255) NOT NULL,
  `publisher_id` bigint(20) UNSIGNED NOT NULL,
  `page_count` int(11) NOT NULL,
  `year_of_publish` year(4) NOT NULL,
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `books`
--

INSERT INTO `books` (`id`, `name`, `isbn`, `publisher_id`, `page_count`, `year_of_publish`, `cover_image`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Memleketimden İnsan Manzaraları', '9789750802737', 1, 320, '2019', NULL, NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(2, 'İnce Memed', '9789750718045', 2, 456, '2018', NULL, NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(3, 'Kar', '9789750718052', 1, 512, '2020', 'K8F10DXMrNyW0YzMzazZpU9SdsC2YFH8fCtwuD4o.jpg', NULL, 1, '2025-06-14 01:21:52', '2025-07-05 11:31:50'),
(4, 'Kitap 4', '12', 1, 11, '2000', NULL, 1, 1, '2025-07-05 12:18:30', '2025-07-05 12:18:30');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `book_activity_types`
--

CREATE TABLE `book_activity_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `category` enum('vocabulary','spelling','writing','comprehension','creative') NOT NULL COMMENT 'Activity category',
  `name` varchar(255) NOT NULL COMMENT 'Activity type name',
  `description` text DEFAULT NULL COMMENT 'Activity description',
  `min_word_count` int(11) DEFAULT NULL COMMENT 'Minimum word count for writing activities',
  `max_word_count` int(11) DEFAULT NULL COMMENT 'Maximum word count for writing activities',
  `points_base` int(11) NOT NULL DEFAULT 10 COMMENT 'Base points for completing this activity type',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `book_activity_types`
--

INSERT INTO `book_activity_types` (`id`, `category`, `name`, `description`, `min_word_count`, `max_word_count`, `points_base`, `is_active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 'vocabulary', 'Word Definition Match', 'Match vocabulary words with their correct definitions.', NULL, NULL, 15, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(2, 'vocabulary', 'Synonym and Antonym Exercise', 'Find synonyms and antonyms for given vocabulary words.', NULL, NULL, 20, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(3, 'writing', 'Character Analysis Essay', 'Write a detailed analysis of a main character from the book.', 200, 500, 50, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(4, 'writing', 'Book Summary', 'Write a comprehensive summary of the book.', 150, 300, 40, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(5, 'comprehension', 'Plot Analysis', 'Analyze the main plot points and story structure.', NULL, NULL, 30, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(6, 'creative', 'Alternative Ending', 'Write an alternative ending for the story.', 100, 250, 35, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(7, 'spelling', 'Vocabulary Spelling Test', 'Spell vocabulary words correctly from audio prompts.', NULL, NULL, 25, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `book_authors`
--

CREATE TABLE `book_authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `book_authors`
--

INSERT INTO `book_authors` (`id`, `book_id`, `author_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, NULL),
(2, 2, 2, NULL, NULL),
(3, 3, 3, NULL, NULL),
(4, 4, 2, NULL, NULL),
(5, 4, 3, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `book_questions`
--

CREATE TABLE `book_questions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `question_text` text NOT NULL COMMENT 'The question content',
  `question_image_url` varchar(255) DEFAULT NULL COMMENT 'Optional image URL',
  `question_audio_url` varchar(255) DEFAULT NULL COMMENT 'Optional audio URL',
  `question_video_url` varchar(255) DEFAULT NULL COMMENT 'Optional video URL',
  `correct_answer` varchar(255) NOT NULL COMMENT 'The correct answer text',
  `incorrect_answer_1` varchar(255) DEFAULT NULL COMMENT 'First incorrect option',
  `incorrect_answer_2` varchar(255) DEFAULT NULL COMMENT 'Second incorrect option',
  `incorrect_answer_3` varchar(255) DEFAULT NULL COMMENT 'Third incorrect option',
  `incorrect_answer_4` varchar(255) DEFAULT NULL COMMENT 'Fourth incorrect option',
  `incorrect_answer_5` varchar(255) DEFAULT NULL COMMENT 'Fifth incorrect option',
  `page_start` int(11) DEFAULT NULL COMMENT 'Starting page reference',
  `page_end` int(11) DEFAULT NULL COMMENT 'Ending page reference',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether question is available for use',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `book_questions`
--

INSERT INTO `book_questions` (`id`, `book_id`, `question_text`, `question_image_url`, `question_audio_url`, `question_video_url`, `correct_answer`, `incorrect_answer_1`, `incorrect_answer_2`, `incorrect_answer_3`, `incorrect_answer_4`, `incorrect_answer_5`, `page_start`, `page_end`, `difficulty_level`, `is_active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 'Who is the main character of the story?', NULL, NULL, NULL, 'Alice', 'Bob', 'Charlie', 'Diana', NULL, NULL, 1, 10, 'easy', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(2, 1, 'What is the main theme of the book?', NULL, NULL, NULL, 'Friendship and courage', 'Love and romance', 'War and conflict', 'Mystery and suspense', NULL, NULL, NULL, NULL, 'medium', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(3, 1, 'In which setting does most of the story take place?', NULL, NULL, NULL, 'A magical forest', 'A modern city', 'An ancient castle', 'A space station', NULL, NULL, 15, 25, 'easy', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(4, 1, 'What motivates the protagonist\'s journey?', NULL, NULL, NULL, 'To save their village from danger', 'To find treasure', 'To become famous', 'To learn magic', NULL, NULL, 30, 50, 'medium', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(5, 1, 'How does the conflict in the story get resolved?', NULL, NULL, NULL, 'Through teamwork and sacrifice', 'Through violence and force', 'Through magic spells', 'Through negotiation', NULL, NULL, 80, 100, 'hard', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(6, 1, 'What lesson does the main character learn?', NULL, NULL, NULL, 'The importance of believing in yourself', 'Money is the most important thing', 'Never trust anyone', 'Power corrupts everyone', NULL, NULL, NULL, NULL, 'hard', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(7, 1, 'Which character serves as the mentor figure?', NULL, NULL, NULL, 'The wise old wizard', 'The young prince', 'The evil queen', 'The talking animal', NULL, NULL, 20, 30, 'medium', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(8, 1, 'What is the climax of the story?', NULL, NULL, NULL, 'The final battle against the dark forces', 'The discovery of the magic sword', 'The meeting with the wizard', 'The return to the village', NULL, NULL, 75, 85, 'hard', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `book_words`
--

CREATE TABLE `book_words` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `word` varchar(255) NOT NULL COMMENT 'The vocabulary word',
  `definition` text DEFAULT NULL COMMENT 'Word definition',
  `synonym` varchar(255) DEFAULT NULL COMMENT 'Synonym of the word',
  `antonym` varchar(255) DEFAULT NULL COMMENT 'Antonym of the word',
  `page_reference` int(11) DEFAULT NULL COMMENT 'Page where word appears',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `book_words`
--

INSERT INTO `book_words` (`id`, `book_id`, `word`, `definition`, `synonym`, `antonym`, `page_reference`, `difficulty_level`, `is_active`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 'Adventure', 'An exciting or unusual experience or activity', 'Journey', 'Routine', 5, 'easy', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(2, 1, 'Courage', 'The ability to do something that frightens one; bravery', 'Bravery', 'Cowardice', 12, 'medium', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(3, 1, 'Mysterious', 'Difficult or impossible to understand, explain, or identify', 'Enigmatic', 'Clear', 18, 'medium', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(4, 1, 'Enchanted', 'Under a spell; having magical properties', 'Magical', 'Ordinary', 25, 'easy', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(5, 1, 'Perilous', 'Full of danger or risk', 'Dangerous', 'Safe', 35, 'hard', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(6, 1, 'Wisdom', 'The quality of having experience, knowledge, and good judgment', 'Intelligence', 'Ignorance', 42, 'medium', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(7, 1, 'Treacherous', 'Guilty of or involving betrayal or deception', 'Deceitful', 'Loyal', 55, 'hard', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(8, 1, 'Triumph', 'A great victory or achievement', 'Victory', 'Defeat', 78, 'medium', 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `cache`
--

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('laravel_cache_spatie.permission.cache', 'a:3:{s:5:\"alias\";a:6:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";s:1:\"j\";s:11:\"description\";s:1:\"k\";s:13:\"role_priority\";}s:11:\"permissions\";a:24:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:20:\"UserResource.viewAny\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:17:\"UserResource.view\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:19:\"UserResource.create\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:19:\"UserResource.update\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:19:\"UserResource.delete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:23:\"UserResource.massDelete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:20:\"UserResource.restore\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:24:\"UserResource.forceDelete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:20:\"RoleResource.viewAny\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:17:\"RoleResource.view\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:19:\"RoleResource.create\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:19:\"RoleResource.update\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:19:\"RoleResource.delete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:23:\"RoleResource.massDelete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:20:\"RoleResource.restore\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:24:\"RoleResource.forceDelete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:26:\"PermissionResource.viewAny\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:23:\"PermissionResource.view\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:25:\"PermissionResource.create\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:25:\"PermissionResource.update\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:25:\"PermissionResource.delete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:29:\"PermissionResource.massDelete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:26:\"PermissionResource.restore\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:30:\"PermissionResource.forceDelete\";s:1:\"c\";s:9:\"moonshine\";s:1:\"r\";a:1:{i:0;i:1;}}}s:5:\"roles\";a:1:{i:0;a:5:{s:1:\"a\";i:1;s:1:\"b\";s:12:\"system_admin\";s:1:\"j\";s:18:\"Sistem Yöneticisi\";s:1:\"c\";s:9:\"moonshine\";s:1:\"k\";N;}}}', 1753258250);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `enum_organization_types`
--

CREATE TABLE `enum_organization_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `enum_organization_types`
--

INSERT INTO `enum_organization_types` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'İlkokul', 1, 1, '2025-07-09 11:10:07', '2025-07-09 11:10:07'),
(2, 'Ortaokul', 1, 1, '2025-07-09 11:10:15', '2025-07-09 11:10:15'),
(3, 'Lise', 1, 1, '2025-07-09 11:10:21', '2025-07-09 11:10:21');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2020_10_04_115514_create_moonshine_roles_table', 1),
(5, '2020_10_05_173148_create_moonshine_tables', 1),
(6, '2024_01_16_000003_create_book_activity_types_table', 1),
(7, '2024_12_19_000001_create_user_agreements_table', 1),
(8, '2025_03_17_073436_create_notifications_table', 1),
(9, '2025_06_04_090557_create_roles_table', 1),
(10, '2025_06_04_090634_create_terms_table', 1),
(11, '2025_06_04_090643_create_organizations_table', 1),
(12, '2025_06_04_090706_create_grade_levels_table', 1),
(13, '2025_06_04_090706_create_organization_grade_levels_table', 1),
(14, '2025_06_04_090706_create_school_classes_table', 1),
(15, '2025_06_04_090706_create_tags_table', 1),
(16, '2025_06_04_090708_create_tag_values_table', 1),
(17, '2025_06_04_090749_create_publishers_table', 1),
(18, '2025_06_04_090750_create_authors_table', 1),
(19, '2025_06_04_090751_create_books_table', 1),
(20, '2025_06_04_090752_create_book_authors_table', 1),
(21, '2025_06_04_090753_create_term_users_table', 1),
(22, '2025_06_04_090754_update_users_table', 1),
(23, '2025_06_04_090800_create_stories_table', 1),
(24, '2025_06_04_090801_create_story_rules_table', 1),
(25, '2025_06_04_090802_create_story_rule_details_table', 1),
(26, '2025_06_04_090803_create_story_chapters_table', 1),
(27, '2025_06_04_090804_create_story_characters_table', 1),
(28, '2025_06_04_090805_create_story_character_stages_table', 1),
(29, '2025_06_04_090806_create_story_achievements_table', 1),
(30, '2025_06_04_090900_create_programs_table', 1),
(31, '2025_06_04_090901_create_program_schools_table', 1),
(32, '2025_06_04_090902_create_program_classes_table', 1),
(33, '2025_06_04_090903_create_program_books_table', 1),
(34, '2025_06_04_090904_create_program_teams_table', 1),
(35, '2025_06_04_090905_create_program_team_members_table', 1),
(36, '2025_06_04_090906_create_program_user_levels_table', 1),
(37, '2025_06_04_090907_create_program_user_achievements_table', 1),
(38, '2025_06_04_090908_create_program_user_characters_table', 1),
(39, '2025_06_04_090909_create_program_user_maps_table', 1),
(40, '2025_06_04_090910_create_program_user_points_table', 1),
(41, '2025_06_04_091000_create_story_books_table', 1),
(42, '2025_06_04_091001_create_program_tasks_table', 1),
(43, '2025_06_04_091002_create_program_task_instances_table', 1),
(44, '2025_06_04_091003_create_program_task_actions_table', 1),
(45, '2025_06_04_091004_create_program_book_quizzes_table', 1),
(46, '2025_06_04_091006_create_program_book_activities_table', 1),
(47, '2025_06_04_091007_create_program_reading_logs_table', 1),
(48, '2025_06_04_091008_create_book_questions_table', 1),
(49, '2025_06_04_091009_create_book_words_table', 1),
(50, '2025_06_04_091010_create_program_book_quiz_questions_table', 1),
(51, '2025_06_04_091100_create_program_user_books_table', 1),
(52, '2025_06_16_000001_remove_organization_hierarchy', 2),
(53, '2025_06_16_000002_add_title_to_users_table', 2),
(54, '2025_06_16_000003_remove_group_school_admin_role', 2),
(55, '2025_07_03_123155_create_permission_tables', 3),
(56, 'create_or_supplement_users_table', 4),
(57, 'add_role_priority_to_roles_table', 5),
(58, '2025_07_03_203155_update_role_table', 6),
(59, '2025_01_04_119999_migrate_term_users_data', 7),
(60, '2025_01_04_120000_create_organization_users_table', 7),
(62, '2025_01_04_120001_create_user_classes_table', 8),
(63, '2025_01_04_120002_update_program_tables_remove_term_users', 9),
(64, '2025_01_04_120003_drop_term_related_tables', 10),
(65, '2025_07_09_131154_create_enum_organization_types_table', 11),
(66, '2025_07_09_132420_add_organization_type_id_to_organizations_table', 12);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `notifications`
--

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `organizations`
--

CREATE TABLE `organizations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `organization_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `organizations`
--

INSERT INTO `organizations` (`id`, `name`, `organization_type_id`, `active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Örnek Eğitim Grubu', NULL, 1, NULL, NULL, '2025-06-14 01:21:49', '2025-06-14 01:21:49'),
(2, 'Örnek İlkokulu', 1, 1, NULL, 1, '2025-06-14 01:21:49', '2025-07-09 11:10:31'),
(3, 'Örnek Ortaokulu', 2, 1, NULL, 1, '2025-06-14 01:21:49', '2025-07-09 11:10:37');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `organization_users`
--

CREATE TABLE `organization_users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `organization_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `organization_users`
--

INSERT INTO `organization_users` (`id`, `user_id`, `organization_id`, `role_id`, `active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 14, 2, 4, 1, 1, 1, '2025-07-04 17:48:24', '2025-07-04 17:52:30'),
(2, 14, 3, 4, 1, 1, 1, '2025-07-04 17:50:25', '2025-07-04 17:52:30'),
(4, 3, 3, 3, 1, 1, 1, '2025-07-09 09:11:21', '2025-07-09 09:11:21');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'UserResource.viewAny', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(2, 'UserResource.view', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(3, 'UserResource.create', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(4, 'UserResource.update', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(5, 'UserResource.delete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(6, 'UserResource.massDelete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(7, 'UserResource.restore', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(8, 'UserResource.forceDelete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(9, 'RoleResource.viewAny', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(10, 'RoleResource.view', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(11, 'RoleResource.create', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(12, 'RoleResource.update', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(13, 'RoleResource.delete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(14, 'RoleResource.massDelete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(15, 'RoleResource.restore', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(16, 'RoleResource.forceDelete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(17, 'PermissionResource.viewAny', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(18, 'PermissionResource.view', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(19, 'PermissionResource.create', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(20, 'PermissionResource.update', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(21, 'PermissionResource.delete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(22, 'PermissionResource.massDelete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(23, 'PermissionResource.restore', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28'),
(24, 'PermissionResource.forceDelete', 'moonshine', '2025-07-03 09:50:28', '2025-07-03 09:50:28');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `programs`
--

CREATE TABLE `programs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `story_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `programs`
--

INSERT INTO `programs` (`id`, `name`, `description`, `story_id`, `start_date`, `end_date`, `is_active`, `created_by`, `updated_by`, `deleted_by`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Spring Reading Challenge 2024', 'A comprehensive reading program for spring semester focusing on adventure stories and character development.', 1, '2025-06-21', '2025-09-12', 1, 1, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL),
(2, 'Summer Reading Adventure', 'An exciting summer reading program with gamification elements to keep students engaged during vacation.', 1, '2025-09-22', '2025-11-21', 1, 1, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL),
(3, 'Fall Literacy Program', 'A structured fall program focusing on reading comprehension and vocabulary building.', 1, '2025-05-15', '2025-07-14', 1, 1, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL),
(4, 'Winter Reading Club', 'A cozy winter reading program with indoor activities and storytelling sessions.', 1, '2025-12-31', '2026-03-21', 0, 1, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_books`
--

CREATE TABLE `program_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_book_activities`
--

CREATE TABLE `program_book_activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `activity_type_id` bigint(20) UNSIGNED NOT NULL,
  `program_task_instance_id` bigint(20) UNSIGNED DEFAULT NULL,
  `activity_content` text DEFAULT NULL COMMENT 'Student''s work/response',
  `word_count` int(11) DEFAULT NULL COMMENT 'For writing activities',
  `points_earned` int(11) NOT NULL DEFAULT 0 COMMENT 'Points awarded',
  `is_completed` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Completion status',
  `completed_at` datetime DEFAULT NULL COMMENT 'When activity was completed',
  `reviewed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL COMMENT 'When activity was reviewed',
  `feedback` text DEFAULT NULL COMMENT 'Teacher feedback',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `program_book_activities`
--

INSERT INTO `program_book_activities` (`id`, `program_id`, `book_id`, `user_id`, `activity_type_id`, `program_task_instance_id`, `activity_content`, `word_count`, `points_earned`, `is_completed`, `completed_at`, `reviewed_by`, `reviewed_at`, `feedback`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 1, 5, 1, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(2, 1, 1, 5, 2, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(3, 1, 1, 6, 1, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(4, 1, 1, 6, 2, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(5, 1, 1, 7, 1, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(6, 1, 1, 7, 2, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_book_quizzes`
--

CREATE TABLE `program_book_quizzes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `quiz_type` enum('completion','daily_reading','practice') NOT NULL COMMENT 'Type of quiz',
  `total_questions` int(11) NOT NULL COMMENT 'Number of questions in this quiz',
  `correct_answers` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of correct answers',
  `score_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Score as percentage (0.00-100.00)',
  `passing_score` decimal(5,2) NOT NULL COMMENT 'Required score to pass',
  `is_passed` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether student passed',
  `attempt_number` int(11) NOT NULL DEFAULT 1 COMMENT 'Which attempt this is',
  `started_at` datetime NOT NULL COMMENT 'When quiz was started',
  `completed_at` datetime DEFAULT NULL COMMENT 'When quiz was completed',
  `time_limit_minutes` int(11) DEFAULT NULL COMMENT 'Time limit for quiz',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `program_book_quizzes`
--

INSERT INTO `program_book_quizzes` (`id`, `program_id`, `book_id`, `user_id`, `quiz_type`, `total_questions`, `correct_answers`, `score_percentage`, `passing_score`, `is_passed`, `attempt_number`, `started_at`, `completed_at`, `time_limit_minutes`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 1, 5, 'completion', 5, 0, 0.00, 60.00, 0, 1, '2025-06-14 04:21:53', NULL, 30, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(2, 1, 1, 5, 'daily_reading', 3, 0, 0.00, 70.00, 0, 1, '2025-06-14 04:21:53', NULL, 15, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(3, 1, 1, 6, 'completion', 5, 0, 0.00, 60.00, 0, 1, '2025-06-14 04:21:53', NULL, 30, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(4, 1, 1, 6, 'daily_reading', 3, 0, 0.00, 70.00, 0, 1, '2025-06-14 04:21:53', NULL, 15, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(5, 1, 1, 7, 'completion', 5, 0, 0.00, 60.00, 0, 1, '2025-06-14 04:21:53', NULL, 30, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(6, 1, 1, 7, 'daily_reading', 3, 0, 0.00, 70.00, 0, 1, '2025-06-14 04:21:53', NULL, 15, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_book_quiz_questions`
--

CREATE TABLE `program_book_quiz_questions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_book_quiz_id` bigint(20) UNSIGNED NOT NULL,
  `book_question_id` bigint(20) UNSIGNED NOT NULL,
  `question_order` int(11) NOT NULL COMMENT 'Order of question in quiz',
  `student_answer` varchar(255) DEFAULT NULL COMMENT 'Student''s selected answer',
  `is_correct` tinyint(1) DEFAULT NULL COMMENT 'Whether answer was correct',
  `answered_at` datetime DEFAULT NULL COMMENT 'When question was answered',
  `points_earned` int(11) NOT NULL DEFAULT 0 COMMENT 'Points earned for this question',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `program_book_quiz_questions`
--

INSERT INTO `program_book_quiz_questions` (`id`, `program_book_quiz_id`, `book_question_id`, `question_order`, `student_answer`, `is_correct`, `answered_at`, `points_earned`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 7, 1, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(2, 1, 2, 2, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(3, 1, 4, 3, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(4, 1, 3, 4, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(5, 1, 8, 5, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(6, 2, 3, 1, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(7, 2, 1, 2, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(8, 2, 7, 3, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(9, 3, 5, 1, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(10, 3, 8, 2, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(11, 3, 7, 3, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(12, 3, 3, 4, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(13, 3, 6, 5, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(14, 4, 1, 1, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(15, 4, 2, 2, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(16, 4, 7, 3, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(17, 5, 4, 1, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(18, 5, 7, 2, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(19, 5, 6, 3, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(20, 5, 1, 4, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(21, 5, 8, 5, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(22, 6, 2, 1, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(23, 6, 1, 2, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(24, 6, 3, 3, NULL, NULL, NULL, 0, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_classes`
--

CREATE TABLE `program_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `school_class_id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_reading_logs`
--

CREATE TABLE `program_reading_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `program_task_instance_id` bigint(20) UNSIGNED DEFAULT NULL,
  `reading_date` date NOT NULL COMMENT 'The date of reading session',
  `start_page` int(11) NOT NULL COMMENT 'Page where reading session started',
  `end_page` int(11) NOT NULL COMMENT 'Page where reading session ended',
  `pages_read` int(11) GENERATED ALWAYS AS (`end_page` - `start_page` + 1) STORED COMMENT 'Calculated as end_page - start_page + 1',
  `reading_duration_minutes` int(11) DEFAULT NULL COMMENT 'Time spent reading in minutes',
  `reading_speed_pages_per_minute` decimal(5,2) DEFAULT NULL COMMENT 'Calculated if duration provided',
  `reading_notes` text DEFAULT NULL COMMENT 'Student''s thoughts or summary',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Teacher verification status',
  `verified_by` bigint(20) UNSIGNED DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL COMMENT 'When verification occurred',
  `points_awarded` int(11) NOT NULL DEFAULT 0 COMMENT 'Points earned for this log entry',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `program_reading_logs`
--

INSERT INTO `program_reading_logs` (`id`, `program_id`, `book_id`, `user_id`, `program_task_instance_id`, `reading_date`, `start_page`, `end_page`, `reading_duration_minutes`, `reading_speed_pages_per_minute`, `reading_notes`, `is_verified`, `verified_by`, `verified_at`, `points_awarded`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 1, 5, NULL, '2025-06-04', 73, 79, 15, 0.00, 'This was a slower chapter but important for character development.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(2, 1, 1, 5, NULL, '2025-06-06', 57, 71, 45, 0.00, 'The dialogue feels very natural and realistic.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(3, 1, 1, 5, NULL, '2025-06-07', 49, 57, 15, 0.00, 'This chapter answered some of my questions from earlier.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(4, 1, 1, 5, NULL, '2025-06-09', 33, 48, 15, 0.00, 'The author\'s description of the setting is very vivid.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(5, 1, 1, 5, NULL, '2025-06-10', 25, 37, 33, 0.00, 'Great action scene! Very exciting to read.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(6, 1, 1, 5, NULL, '2025-06-12', 9, 22, 39, 0.00, 'The plot is getting more interesting. Can\'t wait to see what happens next.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(7, 1, 1, 5, NULL, '2025-06-13', 1, 9, 35, 0.00, 'Really enjoyed this chapter! The characters are developing well.', 1, 3, '2025-06-14 04:21:54', 5, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(8, 1, 1, 6, NULL, '2025-06-04', 73, 78, 41, 0.00, 'This was a slower chapter but important for character development.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(9, 1, 1, 6, NULL, '2025-06-06', 57, 67, 25, 0.00, 'The dialogue feels very natural and realistic.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(10, 1, 1, 6, NULL, '2025-06-07', 49, 62, 20, 0.00, 'This chapter answered some of my questions from earlier.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(11, 1, 1, 6, NULL, '2025-06-09', 33, 39, 36, 0.00, 'The author\'s description of the setting is very vivid.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(12, 1, 1, 6, NULL, '2025-06-10', 25, 31, 45, 0.00, 'Great action scene! Very exciting to read.', 1, 3, '2025-06-14 04:21:54', 5, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(13, 1, 1, 6, NULL, '2025-06-12', 9, 16, 21, 0.00, 'The plot is getting more interesting. Can\'t wait to see what happens next.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(14, 1, 1, 6, NULL, '2025-06-13', 1, 8, 22, 0.00, 'Really enjoyed this chapter! The characters are developing well.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(15, 1, 1, 7, NULL, '2025-06-04', 73, 88, 17, 0.00, 'This was a slower chapter but important for character development.', 1, 3, '2025-06-14 04:21:54', 5, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(16, 1, 1, 7, NULL, '2025-06-06', 57, 69, 31, 0.00, 'The dialogue feels very natural and realistic.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(17, 1, 1, 7, NULL, '2025-06-07', 49, 54, 20, 0.00, 'This chapter answered some of my questions from earlier.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(18, 1, 1, 7, NULL, '2025-06-09', 33, 44, 27, 0.00, 'The author\'s description of the setting is very vivid.', 1, 3, '2025-06-14 04:21:54', 5, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(19, 1, 1, 7, NULL, '2025-06-10', 25, 35, 44, 0.00, 'Great action scene! Very exciting to read.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(20, 1, 1, 7, NULL, '2025-06-12', 9, 24, 23, 0.00, 'The plot is getting more interesting. Can\'t wait to see what happens next.', 1, 3, '2025-06-14 04:21:54', 5, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(21, 1, 1, 7, NULL, '2025-06-13', 1, 16, 20, 0.00, 'Really enjoyed this chapter! The characters are developing well.', 0, NULL, NULL, 0, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_schools`
--

CREATE TABLE `program_schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `organization_id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_tasks`
--

CREATE TABLE `program_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL COMMENT 'Task title',
  `description` text DEFAULT NULL COMMENT 'Detailed task instructions',
  `task_type` enum('reading_log','activity','question','physical') NOT NULL COMMENT 'Type of task',
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'One-time vs recurring task',
  `recurrence_pattern` enum('daily','weekly') DEFAULT NULL COMMENT 'For recurring tasks only',
  `start_date` date NOT NULL COMMENT 'When task becomes available',
  `end_date` date NOT NULL COMMENT 'Final deadline for task completion',
  `points` int(11) DEFAULT NULL COMMENT 'Optional points awarded for completion',
  `book_id` bigint(20) UNSIGNED DEFAULT NULL,
  `page_start` int(11) DEFAULT NULL COMMENT 'For reading tasks: starting page',
  `page_end` int(11) DEFAULT NULL COMMENT 'For reading tasks: ending page',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `program_tasks`
--

INSERT INTO `program_tasks` (`id`, `program_id`, `name`, `description`, `task_type`, `is_recurring`, `recurrence_pattern`, `start_date`, `end_date`, `points`, `book_id`, `page_start`, `page_end`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 'Daily Reading Log', 'Complete daily reading log entries', 'reading_log', 1, 'daily', '2025-05-31', '2025-06-21', 15, NULL, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(2, 1, 'Daily Reading Log', 'Record your daily reading progress and thoughts.', 'reading_log', 1, 'daily', '2025-06-14', '2025-07-14', 10, 1, 1, 50, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(3, 1, 'Weekly Reading Comprehension', 'Answer comprehension questions about your weekly reading.', 'question', 1, 'weekly', '2025-06-14', '2025-07-12', 25, 1, NULL, NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(4, 1, 'Book Review Activity', 'Write a detailed review of the assigned book.', 'activity', 0, NULL, '2025-06-14', '2025-06-28', 50, 2, NULL, NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(5, 1, 'Reading Corner Setup', 'Create a comfortable reading space at home and share a photo.', 'physical', 0, NULL, '2025-06-14', '2025-06-21', 30, NULL, NULL, NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(6, 1, 'Character Analysis', 'Analyze the main character of your current book.', 'activity', 0, NULL, '2025-06-17', '2025-06-24', 40, 3, 1, 100, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_task_actions`
--

CREATE TABLE `program_task_actions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_task_instance_id` bigint(20) UNSIGNED NOT NULL,
  `action_type` enum('completed','missed','excused','reassigned') NOT NULL COMMENT 'Type of action',
  `action_date` datetime NOT NULL COMMENT 'When action occurred',
  `notes` text DEFAULT NULL COMMENT 'Optional notes from teacher or system',
  `points_awarded` int(11) DEFAULT NULL COMMENT 'Points given for this action',
  `completed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `program_task_actions`
--

INSERT INTO `program_task_actions` (`id`, `program_task_instance_id`, `action_type`, `action_date`, `notes`, `points_awarded`, `completed_by`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 'completed', '2025-06-14 04:21:55', 'Sample completion', 15, 5, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(2, 2, 'completed', '2025-06-14 04:21:55', 'Sample completion', 15, 5, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(3, 3, 'completed', '2025-06-14 04:21:55', 'Sample completion', 15, 5, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_task_instances`
--

CREATE TABLE `program_task_instances` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_task_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL COMMENT 'When this instance becomes available',
  `end_date` date NOT NULL COMMENT 'Deadline for this specific instance',
  `status` enum('pending','completed','missed','excused') NOT NULL DEFAULT 'pending' COMMENT 'Current status',
  `assigned_via` enum('individual','team') NOT NULL COMMENT 'How task was assigned',
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `program_task_instances`
--

INSERT INTO `program_task_instances` (`id`, `program_task_id`, `user_id`, `start_date`, `end_date`, `status`, `assigned_via`, `team_id`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
(1, 1, 5, '2025-05-31', '2025-05-31', 'completed', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(2, 1, 6, '2025-05-31', '2025-05-31', 'completed', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(3, 1, 7, '2025-05-31', '2025-05-31', 'completed', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(4, 1, 5, '2025-06-01', '2025-06-01', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(5, 1, 6, '2025-06-01', '2025-06-01', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(6, 1, 7, '2025-06-01', '2025-06-01', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(7, 1, 5, '2025-06-02', '2025-06-02', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(8, 1, 6, '2025-06-02', '2025-06-02', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(9, 1, 7, '2025-06-02', '2025-06-02', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(10, 1, 5, '2025-06-03', '2025-06-03', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(11, 1, 6, '2025-06-03', '2025-06-03', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(12, 1, 7, '2025-06-03', '2025-06-03', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(13, 1, 5, '2025-06-04', '2025-06-04', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(14, 1, 6, '2025-06-04', '2025-06-04', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(15, 1, 7, '2025-06-04', '2025-06-04', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(16, 1, 5, '2025-06-05', '2025-06-05', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(17, 1, 6, '2025-06-05', '2025-06-05', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(18, 1, 7, '2025-06-05', '2025-06-05', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(19, 1, 5, '2025-06-06', '2025-06-06', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(20, 1, 6, '2025-06-06', '2025-06-06', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(21, 1, 7, '2025-06-06', '2025-06-06', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(22, 1, 5, '2025-06-07', '2025-06-07', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(23, 1, 6, '2025-06-07', '2025-06-07', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(24, 1, 7, '2025-06-07', '2025-06-07', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(25, 1, 5, '2025-06-08', '2025-06-08', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(26, 1, 6, '2025-06-08', '2025-06-08', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(27, 1, 7, '2025-06-08', '2025-06-08', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(28, 1, 5, '2025-06-09', '2025-06-09', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(29, 1, 6, '2025-06-09', '2025-06-09', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(30, 1, 7, '2025-06-09', '2025-06-09', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(31, 1, 5, '2025-06-10', '2025-06-10', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(32, 1, 6, '2025-06-10', '2025-06-10', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(33, 1, 7, '2025-06-10', '2025-06-10', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(34, 1, 5, '2025-06-11', '2025-06-11', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(35, 1, 6, '2025-06-11', '2025-06-11', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(36, 1, 7, '2025-06-11', '2025-06-11', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(37, 1, 5, '2025-06-12', '2025-06-12', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(38, 1, 6, '2025-06-12', '2025-06-12', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(39, 1, 7, '2025-06-12', '2025-06-12', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(40, 1, 5, '2025-06-13', '2025-06-13', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(41, 1, 6, '2025-06-13', '2025-06-13', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(42, 1, 7, '2025-06-13', '2025-06-13', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(43, 1, 5, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(44, 1, 6, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(45, 1, 7, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(46, 1, 5, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(47, 1, 6, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(48, 1, 7, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(49, 1, 5, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(50, 1, 6, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(51, 1, 7, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(52, 1, 5, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(53, 1, 6, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(54, 1, 7, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(55, 1, 5, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(56, 1, 6, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(57, 1, 7, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL, NULL, NULL, NULL),
(58, 1, 5, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(59, 1, 6, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(60, 1, 7, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(61, 1, 5, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(62, 1, 6, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(63, 1, 7, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(64, 1, 5, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(65, 1, 6, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(66, 1, 7, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(67, 2, 5, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(68, 2, 6, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(69, 2, 7, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(70, 2, 8, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(71, 2, 9, '2025-06-14', '2025-06-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(72, 2, 5, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(73, 2, 6, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(74, 2, 7, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(75, 2, 8, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(76, 2, 9, '2025-06-15', '2025-06-15', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(77, 2, 5, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(78, 2, 6, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(79, 2, 7, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(80, 2, 8, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(81, 2, 9, '2025-06-16', '2025-06-16', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(82, 2, 5, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(83, 2, 6, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(84, 2, 7, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(85, 2, 8, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(86, 2, 9, '2025-06-17', '2025-06-17', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(87, 2, 5, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(88, 2, 6, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(89, 2, 7, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(90, 2, 8, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(91, 2, 9, '2025-06-18', '2025-06-18', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(92, 2, 5, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(93, 2, 6, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(94, 2, 7, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(95, 2, 8, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(96, 2, 9, '2025-06-19', '2025-06-19', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(97, 2, 5, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(98, 2, 6, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(99, 2, 7, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(100, 2, 8, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(101, 2, 9, '2025-06-20', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(102, 2, 5, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(103, 2, 6, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(104, 2, 7, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(105, 2, 8, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(106, 2, 9, '2025-06-21', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(107, 2, 5, '2025-06-22', '2025-06-22', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(108, 2, 6, '2025-06-22', '2025-06-22', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(109, 2, 7, '2025-06-22', '2025-06-22', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(110, 2, 8, '2025-06-22', '2025-06-22', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(111, 2, 9, '2025-06-22', '2025-06-22', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(112, 2, 5, '2025-06-23', '2025-06-23', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(113, 2, 6, '2025-06-23', '2025-06-23', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(114, 2, 7, '2025-06-23', '2025-06-23', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(115, 2, 8, '2025-06-23', '2025-06-23', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(116, 2, 9, '2025-06-23', '2025-06-23', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(117, 2, 5, '2025-06-24', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(118, 2, 6, '2025-06-24', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(119, 2, 7, '2025-06-24', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(120, 2, 8, '2025-06-24', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(121, 2, 9, '2025-06-24', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(122, 2, 5, '2025-06-25', '2025-06-25', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(123, 2, 6, '2025-06-25', '2025-06-25', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(124, 2, 7, '2025-06-25', '2025-06-25', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(125, 2, 8, '2025-06-25', '2025-06-25', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(126, 2, 9, '2025-06-25', '2025-06-25', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(127, 2, 5, '2025-06-26', '2025-06-26', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(128, 2, 6, '2025-06-26', '2025-06-26', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(129, 2, 7, '2025-06-26', '2025-06-26', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(130, 2, 8, '2025-06-26', '2025-06-26', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(131, 2, 9, '2025-06-26', '2025-06-26', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(132, 2, 5, '2025-06-27', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(133, 2, 6, '2025-06-27', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(134, 2, 7, '2025-06-27', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(135, 2, 8, '2025-06-27', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(136, 2, 9, '2025-06-27', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(137, 2, 5, '2025-06-28', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(138, 2, 6, '2025-06-28', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(139, 2, 7, '2025-06-28', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(140, 2, 8, '2025-06-28', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(141, 2, 9, '2025-06-28', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(142, 2, 5, '2025-06-29', '2025-06-29', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(143, 2, 6, '2025-06-29', '2025-06-29', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(144, 2, 7, '2025-06-29', '2025-06-29', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(145, 2, 8, '2025-06-29', '2025-06-29', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(146, 2, 9, '2025-06-29', '2025-06-29', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(147, 2, 5, '2025-06-30', '2025-06-30', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(148, 2, 6, '2025-06-30', '2025-06-30', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(149, 2, 7, '2025-06-30', '2025-06-30', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(150, 2, 8, '2025-06-30', '2025-06-30', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(151, 2, 9, '2025-06-30', '2025-06-30', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(152, 2, 5, '2025-07-01', '2025-07-01', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(153, 2, 6, '2025-07-01', '2025-07-01', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(154, 2, 7, '2025-07-01', '2025-07-01', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(155, 2, 8, '2025-07-01', '2025-07-01', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(156, 2, 9, '2025-07-01', '2025-07-01', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(157, 2, 5, '2025-07-02', '2025-07-02', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(158, 2, 6, '2025-07-02', '2025-07-02', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(159, 2, 7, '2025-07-02', '2025-07-02', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(160, 2, 8, '2025-07-02', '2025-07-02', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(161, 2, 9, '2025-07-02', '2025-07-02', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(162, 2, 5, '2025-07-03', '2025-07-03', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(163, 2, 6, '2025-07-03', '2025-07-03', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(164, 2, 7, '2025-07-03', '2025-07-03', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(165, 2, 8, '2025-07-03', '2025-07-03', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(166, 2, 9, '2025-07-03', '2025-07-03', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(167, 2, 5, '2025-07-04', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(168, 2, 6, '2025-07-04', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(169, 2, 7, '2025-07-04', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(170, 2, 8, '2025-07-04', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(171, 2, 9, '2025-07-04', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(172, 2, 5, '2025-07-05', '2025-07-05', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(173, 2, 6, '2025-07-05', '2025-07-05', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(174, 2, 7, '2025-07-05', '2025-07-05', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(175, 2, 8, '2025-07-05', '2025-07-05', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(176, 2, 9, '2025-07-05', '2025-07-05', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(177, 2, 5, '2025-07-06', '2025-07-06', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(178, 2, 6, '2025-07-06', '2025-07-06', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(179, 2, 7, '2025-07-06', '2025-07-06', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(180, 2, 8, '2025-07-06', '2025-07-06', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(181, 2, 9, '2025-07-06', '2025-07-06', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(182, 2, 5, '2025-07-07', '2025-07-07', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(183, 2, 6, '2025-07-07', '2025-07-07', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(184, 2, 7, '2025-07-07', '2025-07-07', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(185, 2, 8, '2025-07-07', '2025-07-07', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(186, 2, 9, '2025-07-07', '2025-07-07', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(187, 2, 5, '2025-07-08', '2025-07-08', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(188, 2, 6, '2025-07-08', '2025-07-08', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(189, 2, 7, '2025-07-08', '2025-07-08', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(190, 2, 8, '2025-07-08', '2025-07-08', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(191, 2, 9, '2025-07-08', '2025-07-08', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(192, 2, 5, '2025-07-09', '2025-07-09', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(193, 2, 6, '2025-07-09', '2025-07-09', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(194, 2, 7, '2025-07-09', '2025-07-09', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(195, 2, 8, '2025-07-09', '2025-07-09', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(196, 2, 9, '2025-07-09', '2025-07-09', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(197, 2, 5, '2025-07-10', '2025-07-10', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(198, 2, 6, '2025-07-10', '2025-07-10', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(199, 2, 7, '2025-07-10', '2025-07-10', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(200, 2, 8, '2025-07-10', '2025-07-10', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(201, 2, 9, '2025-07-10', '2025-07-10', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(202, 2, 5, '2025-07-11', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(203, 2, 6, '2025-07-11', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(204, 2, 7, '2025-07-11', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(205, 2, 8, '2025-07-11', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(206, 2, 9, '2025-07-11', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(207, 2, 5, '2025-07-12', '2025-07-12', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(208, 2, 6, '2025-07-12', '2025-07-12', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(209, 2, 7, '2025-07-12', '2025-07-12', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(210, 2, 8, '2025-07-12', '2025-07-12', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(211, 2, 9, '2025-07-12', '2025-07-12', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(212, 2, 5, '2025-07-13', '2025-07-13', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(213, 2, 6, '2025-07-13', '2025-07-13', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(214, 2, 7, '2025-07-13', '2025-07-13', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(215, 2, 8, '2025-07-13', '2025-07-13', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(216, 2, 9, '2025-07-13', '2025-07-13', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(217, 2, 5, '2025-07-14', '2025-07-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(218, 2, 6, '2025-07-14', '2025-07-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(219, 2, 7, '2025-07-14', '2025-07-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(220, 2, 8, '2025-07-14', '2025-07-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(221, 2, 9, '2025-07-14', '2025-07-14', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(222, 3, 5, '2025-06-14', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(223, 3, 6, '2025-06-14', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(224, 3, 7, '2025-06-14', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(225, 3, 8, '2025-06-14', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(226, 3, 9, '2025-06-14', '2025-06-20', 'pending', 'individual', NULL, '2025-06-14 01:21:54', '2025-06-14 01:21:54', NULL, NULL, NULL, NULL),
(227, 3, 5, '2025-06-21', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(228, 3, 6, '2025-06-21', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(229, 3, 7, '2025-06-21', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(230, 3, 8, '2025-06-21', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(231, 3, 9, '2025-06-21', '2025-06-27', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(232, 3, 5, '2025-06-28', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(233, 3, 6, '2025-06-28', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(234, 3, 7, '2025-06-28', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(235, 3, 8, '2025-06-28', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(236, 3, 9, '2025-06-28', '2025-07-04', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(237, 3, 5, '2025-07-05', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(238, 3, 6, '2025-07-05', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(239, 3, 7, '2025-07-05', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(240, 3, 8, '2025-07-05', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(241, 3, 9, '2025-07-05', '2025-07-11', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(242, 3, 5, '2025-07-12', '2025-07-18', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(243, 3, 6, '2025-07-12', '2025-07-18', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(244, 3, 7, '2025-07-12', '2025-07-18', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(245, 3, 8, '2025-07-12', '2025-07-18', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(246, 3, 9, '2025-07-12', '2025-07-18', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(247, 4, 5, '2025-06-14', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(248, 4, 6, '2025-06-14', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(249, 4, 7, '2025-06-14', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(250, 4, 8, '2025-06-14', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(251, 4, 9, '2025-06-14', '2025-06-28', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(252, 5, 5, '2025-06-14', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(253, 5, 6, '2025-06-14', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(254, 5, 7, '2025-06-14', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(255, 5, 8, '2025-06-14', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(256, 5, 9, '2025-06-14', '2025-06-21', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(257, 6, 5, '2025-06-17', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(258, 6, 6, '2025-06-17', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(259, 6, 7, '2025-06-17', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(260, 6, 8, '2025-06-17', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL),
(261, 6, 9, '2025-06-17', '2025-06-24', 'pending', 'individual', NULL, '2025-06-14 01:21:55', '2025-06-14 01:21:55', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_teams`
--

CREATE TABLE `program_teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_team_members`
--

CREATE TABLE `program_team_members` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_team_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_user_achievements`
--

CREATE TABLE `program_user_achievements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `story_achievement_id` bigint(20) UNSIGNED NOT NULL,
  `earned_at` timestamp NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_user_books`
--

CREATE TABLE `program_user_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_user_characters`
--

CREATE TABLE `program_user_characters` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `story_character_id` bigint(20) UNSIGNED NOT NULL,
  `current_stage` int(11) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_user_levels`
--

CREATE TABLE `program_user_levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `story_chapter_id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_user_maps`
--

CREATE TABLE `program_user_maps` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `item_type` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `x_coordinate` decimal(8,2) NOT NULL,
  `y_coordinate` decimal(8,2) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `program_user_points`
--

CREATE TABLE `program_user_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `program_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `point_source` int(11) NOT NULL,
  `points` int(11) NOT NULL,
  `earned_at` timestamp NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `publishers`
--

CREATE TABLE `publishers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `publishers`
--

INSERT INTO `publishers` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Yapı Kredi Yayınları', NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(2, 'İş Bankası Kültür Yayınları', NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `guard_name` varchar(255) DEFAULT NULL,
  `role_priority` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`role_priority`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `roles`
--

INSERT INTO `roles` (`id`, `name`, `description`, `guard_name`, `role_priority`, `created_at`, `updated_at`) VALUES
(1, 'system_admin', 'Sistem Yöneticisi', 'moonshine', NULL, NULL, '2025-07-03 17:23:20'),
(2, 'school_admin', 'Okul Sorumlusu', 'moonshine', '[\"3\",\"4\",\"7\"]', '2025-07-03 17:22:47', '2025-07-03 17:39:47'),
(3, 'teacher', 'Öğretmen', 'moonshine', '[\"4\"]', '2025-07-03 17:23:33', '2025-07-03 17:40:18'),
(4, 'student', 'Öğrenci', 'moonshine', NULL, '2025-07-03 17:23:44', '2025-07-03 17:23:44'),
(7, 'report_user', 'Raporlama Kullanıcısı', NULL, NULL, '2025-07-03 17:38:12', '2025-07-03 17:38:12');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `rolesx`
--

CREATE TABLE `rolesx` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `level` int(11) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `rolesx`
--

INSERT INTO `rolesx` (`id`, `name`, `description`, `level`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'System Administrator', 'Full system access and management', 1, NULL, NULL, '2025-06-14 01:21:48', '2025-06-14 01:21:48'),
(3, 'School Administrator', 'Manages a single school', 2, NULL, 1, '2025-06-14 01:21:48', '2025-06-16 16:15:23'),
(4, 'Teacher', 'Teaches classes and manages students', 2, NULL, 1, '2025-06-14 01:21:48', '2025-06-16 16:15:12'),
(5, 'Student', 'Participates in reading activities', 3, NULL, 1, '2025-06-14 01:21:48', '2025-06-16 16:14:59');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(2, 1),
(3, 1),
(4, 1),
(5, 1),
(6, 1),
(7, 1),
(8, 1),
(9, 1),
(10, 1),
(11, 1),
(12, 1),
(13, 1),
(14, 1),
(15, 1),
(16, 1),
(17, 1),
(18, 1),
(19, 1),
(20, 1),
(21, 1),
(22, 1),
(23, 1),
(24, 1);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `school_classes`
--

CREATE TABLE `school_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `organization_id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `school_classes`
--

INSERT INTO `school_classes` (`id`, `name`, `organization_id`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(21, '3-A', 2, 1, 1, '2025-07-09 11:21:38', '2025-07-09 11:21:38');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('0PvD4vb1KbMib6sXEBXhA3YVeGjYpw09FEteSaiX', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWXdBY3VWa3JiNVpCbllabVdyQWFmSHliYVU3V0Y3ZmhESFRjWVF6SSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTg6Imh0dHA6Ly9tb29uLnNpdGUvP1ZTQ09ERT0maWQ9OGJmZDIxZGEtYzE2Yi00YzFhLThhNGItNTRjMjhmMDQ4OGJjJnZzY29kZUJyb3dzZXJSZXFJZD0xNzUzMzYwNDUzNTA3Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1753360453),
('akOAMmxX4gNs36whOKp1uuQpdgqdV4mbXSpJduea', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZ0xSTjFmUzJDMGVFTVhiMnJyTGRnYlVKa1BocjFJRHJ5OWRwZVJVdSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTA2OiJodHRwOi8vbW9vbi5zaXRlLz9YREVCVUdfVFJJR0dFUj0maWQ9OGJmZDIxZGEtYzE2Yi00YzFhLThhNGItNTRjMjhmMDQ4OGJjJnZzY29kZUJyb3dzZXJSZXFJZD0xNzUzMzU5Njk5MDEzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1753359699),
('cW8nLPvBtuuYrhafvHqanRxGsX8tD46wB86eMOtF', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaXZ1c3FZZjljWmZaTWRyNzFEZmZuZHNrTU5GNURLbXlGUFZuWWs1SCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTA6Imh0dHA6Ly9tb29uLnNpdGUvP2lkPThiZmQyMWRhLWMxNmItNGMxYS04YTRiLTU0YzI4ZjA0ODhiYyZ2c2NvZGVCcm93c2VyUmVxSWQ9MTc1MzM1OTY1MTc2NiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1753359652),
('cXmG8uRQPBN5fCK4vUrHBFjLgIC1c4eH2wsMLCYh', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiS1pQYnpWZFpEUEdwVE5ITm1tWW9OWXE0OGVFRnRlMTJqRHdqNlBsUyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTY6Imh0dHA6Ly9tb29uLnNpdGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1753359359),
('e5sYGUuSuv1f8WUL4acFbodzYvAOznPlOZe5p0r5', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiV2tESFY1elJUTmFxemwweUQzQU9ORVVUUmVIbVBMdXVwM2syUGJhViI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTY6Imh0dHA6Ly9tb29uLnNpdGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1753358655),
('fA9omm3K722JgfmlRX0r5CUNi3uFtQ67cKwHcsAR', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiejNxcGZ1MjhHOVB1cFZJcFg5NThRQkJma0piWHVHa1JtVWNJVTVmQSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTA2OiJodHRwOi8vbW9vbi5zaXRlLz9YREVCVUdfVFJJR0dFUj0maWQ9OGJmZDIxZGEtYzE2Yi00YzFhLThhNGItNTRjMjhmMDQ4OGJjJnZzY29kZUJyb3dzZXJSZXFJZD0xNzUzMzU5NjU5Nzk5Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1753359660),
('gpMOz72f66iaMOIeez1GoHzWVotiJBQ6pkS0HyC5', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZWJxWVpjakt4ZGlVbnBiNkNRR1R0bERzVlM2eERCVXBkbzlDUGVlZiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTY6Imh0dHA6Ly9tb29uLnNpdGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1753358529),
('krri7P7Z1qaKmi19HnWAXSCMFBYnCVP6v7set14T', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQ28zRTlVSUNaQ3Jyb0pienBmc2ZSOGVCVFN6d3o0cmRGZHJ2ZTNOUiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTA6Imh0dHA6Ly9tb29uLnNpdGUvP2lkPTRjNTZmYzc3LWVmMzItNDRmMS05YTk4LTE1YTNkMjUxNWUzOSZ2c2NvZGVCcm93c2VyUmVxSWQ9MTc1MzM1OTM0NTc4MyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1753359346),
('LAxhNXRYBeZAoktWDfE3wpxEjjHM71egQp0jrGmk', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaTRaMGdyY3oyWTZMZmt0OExIYlVwU2ZMZkt0cGN4dlhqdFdzSWphQiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzI6Imh0dHA6Ly9tb29uLnNpdGUvP1hDT0RFX1RSSUdHRVI9Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1753362098),
('lvGVSVFfrhCIKjDa9qKQAQSojiHkWJzDdvbLa0UH', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiNHNqSXBWZWpLVnNwcFJkclVnUXFiSWtrb3hOeDZKU0pSRXFmNmR2RyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTA6Imh0dHA6Ly9tb29uLnNpdGUvP2lkPThiZmQyMWRhLWMxNmItNGMxYS04YTRiLTU0YzI4ZjA0ODhiYyZ2c2NvZGVCcm93c2VyUmVxSWQ9MTc1MzM2MDQ3MjYwNyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1753360472),
('nA45FHUAbb2Lx9j7P31w37fxD7K2S4V9rZQlMUWr', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWUV5OEZLc2dDdHdCMnRXZjZKTWdGNGNDYW56b2VFVDU4NWE1THZ3QiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTA6Imh0dHA6Ly9tb29uLnNpdGUvP2lkPTRjNTZmYzc3LWVmMzItNDRmMS05YTk4LTE1YTNkMjUxNWUzOSZ2c2NvZGVCcm93c2VyUmVxSWQ9MTc1MzM1OTMxNTM5MSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', **********),
('nn675PMtNxmban1EHxLknPiZqWC3qRokigfFylY9', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiRUJvVE01aVBrd240SEpVOE1aUVJVeXpGUjJjSUVlS21CYnc0WWx6cSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTg6Imh0dHA6Ly9tb29uLnNpdGUvP1ZTQ09ERT0maWQ9OGJmZDIxZGEtYzE2Yi00YzFhLThhNGItNTRjMjhmMDQ4OGJjJnZzY29kZUJyb3dzZXJSZXFJZD0xNzUzMzYwNDY4NzU1Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', **********),
('nOFyQztZBAvCHrJDYbZX9BEWwNHLF8gKYhoo1yMg', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', '********************************************************************************************************************************************************************************************************************************************************************************************************************************************', 1753359365),
('NyfoWF6aJDR8QvZYtzG7KsUhqVAoo8ZRMu95xcZa', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiOTVCbjRPaDh1dXdoZUlQN0RYY2NCTkNVZ29PQ01UWDdTS3pNOTNXYiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9tb29uLnNpdGUvYWRtaW4vbG9naW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1753304214),
('seLCaLcqqkSa1wKrlOHNXcPBfPXmvmUzumz74bH3', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoibVFvZHVwM2VyeUhCMUhnUmY4c1pLeTh3TUxaVzU0aTNaY0gxUXhHRCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjU4OiJodHRwOi8vbW9vbi5zaXRlL2FkbWluL3Jlc291cmNlL3VzZXItcmVzb3VyY2UvZm9ybS1wYWdlLzE0Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1NjoibG9naW5fbW9vbnNoaW5lXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjIzOiJwYXNzd29yZF9oYXNoX21vb25zaGluZSI7czo2MDoiJDJ5JDEyJEtvbmhSUEt6LmphcWxhZ2dVSTRXMHVNY1c4bko5djFVVHgxWDMzTnB2TWsyMVpIcGw1cDNXIjt9', 1753306019),
('sJEQbECa9aOzB0a4bvXA0eorpXsLtZ8ZjFlW7cfM', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiam1jQjlWYkt0M1lhRWVzTDRTZGdoYUN1cXBrRnRuc0hsNkljb29vZiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjIyOiJodHRwOi8vbW9vbi5zaXRlL2FkbWluIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1NjoibG9naW5fbW9vbnNoaW5lXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjIzOiJwYXNzd29yZF9oYXNoX21vb25zaGluZSI7czo2MDoiJDJ5JDEyJEtvbmhSUEt6LmphcWxhZ2dVSTRXMHVNY1c4bko5djFVVHgxWDMzTnB2TWsyMVpIcGw1cDNXIjt9', 1753364001),
('T3YSMnWfcVX0hDJE8XOcsiyjhA1WiZtmfxnk9oqX', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQ0FYa0lrVTVmRFhVdUFzbmMxMkdVMGpra0o1b2xnOEZXNDZYZzRMdiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTg6Imh0dHA6Ly9tb29uLnNpdGUvP1ZTQ09ERT0maWQ9OGJmZDIxZGEtYzE2Yi00YzFhLThhNGItNTRjMjhmMDQ4OGJjJnZzY29kZUJyb3dzZXJSZXFJZD0xNzUzMzYwMjc3ODk1Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1753360278),
('vEhkqi1QYI3zMe346YBFlqtdgnxvITJLjcrnsk4X', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWXJPQVdpNmxVSW42S3pVaDBFNjl6TTBPdUFaM0dwdEVIdFIzeVU4eiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTY6Imh0dHA6Ly9tb29uLnNpdGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1753358654),
('Vldyf2XFQ5dgXzzuumCT0fKV1FKTYHNuy4RLURzy', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiYWwyd0FSeEwydE5MeldzbndzNFB3cWRidXVFTHZCZ3d3Y3JTS2NRaSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTA1OiJodHRwOi8vbW9vbi5zaXRlLz9YREVCVUdfVlNDT0RFPSZpZD04YmZkMjFkYS1jMTZiLTRjMWEtOGE0Yi01NGMyOGYwNDg4YmMmdnNjb2RlQnJvd3NlclJlcUlkPTE3NTMzNjAyNzMxMTEiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1753360273),
('VX9qu0TVeuGLuAOx0kvOW4ZRoZ4QzXBrKIciblq4', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiaVNXZ3BoM0U5N2lWWnNsRk83TXJvTmVuMVUyMEZ1S1JiWHNsT2ZYbyI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo2NDoiaHR0cDovL21vb24uc2l0ZS9hZG1pbi9yZXNvdXJjZS9zY2hvb2wtY2xhc3MtcmVzb3VyY2UvaW5kZXgtcGFnZSI7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjY0OiJodHRwOi8vbW9vbi5zaXRlL2FkbWluL3Jlc291cmNlL3NjaG9vbC1jbGFzcy1yZXNvdXJjZS9pbmRleC1wYWdlIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1753304214),
('YczOdo2M6aMTbumMnEQ675IIet0yqoERQWToOJVr', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidGUwbk0yNndaZTdhc2YwN2hxWDR5UXgyTHJLb0hZaU1SWW1PUDJPeCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTY6Imh0dHA6Ly9tb29uLnNpdGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1753358565),
('zVW2xWX04BySEUy3v36slwOlefmIhWsqjGYdOO0v', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiOTl2aktnZEJ1VGdkSGRUaWZ6NmtDQ2lXaU1yY1dOUjh1c1IwT1JZUSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTA2OiJodHRwOi8vbW9vbi5zaXRlLz9YREVCVUdfVFJJR0dFUj0maWQ9OGJmZDIxZGEtYzE2Yi00YzFhLThhNGItNTRjMjhmMDQ4OGJjJnZzY29kZUJyb3dzZXJSZXFJZD0xNzUzMzYwMjY3NTY3Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1753360267);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `stories`
--

CREATE TABLE `stories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `cover_image` varchar(255) NOT NULL,
  `map_grid_rows` int(11) NOT NULL,
  `map_grid_columns` int(11) NOT NULL,
  `map_background_image` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `stories`
--

INSERT INTO `stories` (`id`, `title`, `description`, `cover_image`, `map_grid_rows`, `map_grid_columns`, `map_background_image`, `active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'The Magical Library Adventure', 'Join our heroes as they explore the enchanted library, discovering magical books and solving ancient puzzles to unlock the secrets of reading.', 'stories/magical-library-cover.jpg', 12, 15, 'stories/magical-library-map.jpg', 1, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `story_achievements`
--

CREATE TABLE `story_achievements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `story_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `type` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `unlock_rule_id` bigint(20) UNSIGNED DEFAULT NULL,
  `map_start_x` int(11) DEFAULT NULL,
  `map_start_y` int(11) DEFAULT NULL,
  `map_end_x` int(11) DEFAULT NULL,
  `map_end_y` int(11) DEFAULT NULL,
  `is_dynamic_position` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `story_achievements`
--

INSERT INTO `story_achievements` (`id`, `story_id`, `name`, `description`, `type`, `image`, `unlock_rule_id`, `map_start_x`, `map_start_y`, `map_end_x`, `map_end_y`, `is_dynamic_position`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 1, 'First Book Badge', 'Awarded for reading your first book in the magical library.', 'badge', '', NULL, 2, 2, NULL, NULL, 0, 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(2, 1, 'Reading Streak Trophy', 'Earned by maintaining a consistent reading habit.', 'trophy', '', 1, 5, 3, NULL, NULL, 0, 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(3, 1, 'Knowledge Crystal', 'A magical crystal that appears when you master comprehension skills.', 'collectible', '', 2, 9, 7, NULL, NULL, 1, 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(4, 1, 'Master Reader Crown', 'The ultimate achievement for completing the entire reading adventure.', 'reward', 'STwWcrwSUTwOAYE6CetqDch21pFpoUJIX3W3TAAU.jpg', 3, 12, 12, NULL, NULL, 0, 1, 1, '2025-06-14 01:21:53', '2025-07-05 11:21:14');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `story_books`
--

CREATE TABLE `story_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `story_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `sequence` int(11) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `story_books`
--

INSERT INTO `story_books` (`id`, `story_id`, `book_id`, `sequence`, `created_by`, `updated_by`, `deleted_by`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 1, 1, 1, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL),
(2, 1, 2, 2, 1, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL),
(3, 1, 3, 3, 1, NULL, NULL, '2025-06-14 01:21:53', '2025-06-14 01:21:53', NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `story_chapters`
--

CREATE TABLE `story_chapters` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `story_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `sequence` int(11) NOT NULL,
  `unlock_rule_id` bigint(20) UNSIGNED DEFAULT NULL,
  `map_start_x` int(11) DEFAULT NULL,
  `map_start_y` int(11) DEFAULT NULL,
  `map_end_x` int(11) DEFAULT NULL,
  `map_end_y` int(11) DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `story_chapters`
--

INSERT INTO `story_chapters` (`id`, `story_id`, `title`, `description`, `sequence`, `unlock_rule_id`, `map_start_x`, `map_start_y`, `map_end_x`, `map_end_y`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 1, 'The Entrance Hall', 'Welcome to the magical library! Learn the basics of navigation and discover your first magical book.', 1, NULL, 1, 1, 3, 3, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(2, 1, 'The Fiction Wing', 'Explore the fiction section and meet the story characters who will guide your journey.', 2, 1, 4, 2, 7, 5, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(3, 1, 'The Knowledge Tower', 'Climb the tower of knowledge and unlock advanced reading skills.', 3, 2, 8, 6, 12, 10, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(4, 1, 'The Secret Archive', 'Discover the hidden archive and unlock the ultimate reading treasures.', 4, 3, 10, 11, 14, 14, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `story_characters`
--

CREATE TABLE `story_characters` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `story_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `base_image` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `story_characters`
--

INSERT INTO `story_characters` (`id`, `story_id`, `name`, `description`, `base_image`, `active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 1, 'Luna the Librarian', 'A wise and magical librarian who guides students through their reading journey.', 'characters/luna-base.jpg', 1, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(2, 1, 'Bookworm the Dragon', 'A friendly dragon who loves books and helps students discover new stories.', 'characters/bookworm-base.jpg', 1, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(3, 1, 'Sage the Owl', 'An ancient owl with vast knowledge who teaches reading comprehension.', 'characters/sage-base.jpg', 1, 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `story_character_stages`
--

CREATE TABLE `story_character_stages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `character_id` bigint(20) UNSIGNED NOT NULL,
  `stage_number` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `unlock_rule_id` bigint(20) UNSIGNED DEFAULT NULL,
  `abilities` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`abilities`)),
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `story_character_stages`
--

INSERT INTO `story_character_stages` (`id`, `character_id`, `stage_number`, `name`, `image`, `unlock_rule_id`, `abilities`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 'Apprentice', 'characters/1-apprentice.jpg', NULL, '[\"basic_reading\"]', 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(2, 1, 2, 'Scholar', 'characters/1-scholar.jpg', 1, '[\"basic_reading\",\"comprehension\"]', 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(3, 1, 3, 'Master', 'characters/1-master.jpg', 2, '[\"basic_reading\",\"comprehension\",\"speed_reading\"]', 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(4, 2, 1, 'Apprentice', 'characters/2-apprentice.jpg', NULL, '[\"basic_reading\"]', 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(5, 2, 2, 'Scholar', 'characters/2-scholar.jpg', 1, '[\"basic_reading\",\"comprehension\"]', 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(6, 2, 3, 'Master', 'characters/2-master.jpg', 2, '[\"basic_reading\",\"comprehension\",\"speed_reading\"]', 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(7, 3, 1, 'Apprentice', 'characters/3-apprentice.jpg', NULL, '[\"basic_reading\"]', 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(8, 3, 2, 'Scholar', 'characters/3-scholar.jpg', 1, '[\"basic_reading\",\"comprehension\"]', 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53'),
(9, 3, 3, 'Master', 'characters/3-master.jpg', 2, '[\"basic_reading\",\"comprehension\",\"speed_reading\"]', 1, 1, '2025-06-14 01:21:53', '2025-06-14 01:21:53');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `story_rules`
--

CREATE TABLE `story_rules` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `story_id` bigint(20) UNSIGNED NOT NULL,
  `rule_type` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `story_rules`
--

INSERT INTO `story_rules` (`id`, `story_id`, `rule_type`, `quantity`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 100, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(2, 1, 3, 3, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52'),
(3, 1, 2, 2, 1, 1, '2025-06-14 01:21:52', '2025-06-14 01:21:52');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `story_rule_details`
--

CREATE TABLE `story_rule_details` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `rule_id` bigint(20) UNSIGNED NOT NULL,
  `required_type` int(11) NOT NULL,
  `required_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `tags`
--

CREATE TABLE `tags` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `tag_type` tinyint(4) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `tag_values`
--

CREATE TABLE `tag_values` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `taggable_id` bigint(20) UNSIGNED NOT NULL,
  `taggable_type` tinyint(4) NOT NULL,
  `tag_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Tablo döküm verisi `users`
--

INSERT INTO `users` (`id`, `username`, `name`, `title`, `email`, `email_verified_at`, `password`, `created_by`, `updated_by`, `remember_token`, `created_at`, `updated_at`, `avatar`) VALUES
(1, '11', 'System Administrator', NULL, '<EMAIL>', '2025-06-14 01:21:49', '$2y$12$KonhRPKz.jaqlaggUI4W0uMcW8nJ9v1UTx1X33NpvMk21ZHpl5p3W', NULL, NULL, NULL, '2025-06-14 01:21:49', '2025-07-09 06:19:57', 'moonshine_users/9kg9K5UpfZleQkuLyrkzYUM2OfVcxZqPYRqwwx1b.jpg'),
(2, '22', 'Okul Müdürü', NULL, '<EMAIL>', NULL, '$2y$12$VehL.APcrgJWCvM9eK8dWeWmnFEbFw1MYpWoX5yliEkyLTB3L9Ck6', NULL, NULL, NULL, '2025-06-14 01:21:49', '2025-06-14 01:21:49', NULL),
(3, '33', 'Öğretmen Ayşe', NULL, '<EMAIL>', NULL, '$2y$12$u9PyoXpqBKxWBLTTJR44LeOuZ07UhyLSSLbFtquw9gc/gwIs5x4Lq', NULL, NULL, NULL, '2025-06-14 01:21:49', '2025-06-14 01:21:49', NULL),
(4, '44', 'Öğretmen Mehmet', NULL, '<EMAIL>', NULL, '$2y$12$2.gpsB1BlW3WGQUSxWv5fe1cOy7BzD35BAAX6CwLmX.FWgKdcXTTW', NULL, NULL, NULL, '2025-06-14 01:21:50', '2025-06-14 01:21:50', NULL),
(5, '55', 'Öğrenci 1', NULL, '<EMAIL>', NULL, '$2y$12$RBSY1QcWj6.84qtlM/EwsO4Riwft0my07IQIzvaqY.f2fh29Srlna', NULL, NULL, NULL, '2025-06-14 01:21:50', '2025-06-14 01:21:50', NULL),
(6, '66', 'Öğrenci 2', NULL, '<EMAIL>', NULL, '$2y$12$iN/nxLcCGnDM5m/cG9IPbe.Yevjurh87M0FF2F6GowMOHVvuVMk8i', NULL, NULL, NULL, '2025-06-14 01:21:50', '2025-06-14 01:21:50', NULL),
(7, '77', 'Öğrenci 3', NULL, '<EMAIL>', NULL, '$2y$12$8kbI.tlbg2/Rzep5EAhdMuecJFf1HT6V8cFZYCgr.kXI3Vp/y4dGu', NULL, NULL, NULL, '2025-06-14 01:21:50', '2025-06-14 01:21:50', NULL),
(8, '88', 'Öğrenci 4', NULL, '<EMAIL>', NULL, '$2y$12$HNmsabByaie.7M3Nr678be3Jnjh2F20H1x3xrHCfp1Up0kFzA.Vw6', NULL, NULL, NULL, '2025-06-14 01:21:51', '2025-06-14 01:21:51', NULL),
(9, '99', 'Öğrenci 5', NULL, '<EMAIL>', NULL, '$2y$12$DFvRghlifFf/CHcr3U12O.7XvtKQsJ737cnISVcXqv1wEsBZtWJEK', NULL, NULL, NULL, '2025-06-14 01:21:51', '2025-06-14 01:21:51', NULL),
(10, '110', 'Öğrenci 6', NULL, '<EMAIL>', NULL, '$2y$12$YXeYIhXnC6iETJ4MdehXaeziNhZgp2PGlI.Avqnxc74vyJ0/bQLc2', NULL, NULL, NULL, '2025-06-14 01:21:51', '2025-06-14 01:21:51', NULL),
(11, '111', 'Öğrenci 7', NULL, '<EMAIL>', NULL, '$2y$12$g.4Ba/ej.F4.Al7BFpgtqevro7BBXog1aMUkAWeQ6Ee1us1ukXtWm', NULL, NULL, NULL, '2025-06-14 01:21:51', '2025-06-14 01:21:51', NULL),
(12, '112', 'Öğrenci 8', NULL, '<EMAIL>', NULL, '$2y$12$qn5hXa.MmNUWZ4TmcwlhGeues7Cz.1ruc9LHGWNaLy7ZW0YuXiTl6', NULL, NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52', NULL),
(13, '113', 'Öğrenci 9', NULL, '<EMAIL>', NULL, '$2y$12$65ex8ky/JFULsS7PqSxyWOUULgm6fKhCZQEz4HauSIwrD11dV0d1a', NULL, NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52', NULL),
(14, '114', 'Öğrenci 10', NULL, '<EMAIL>', NULL, '$2y$12$CofaxRSm0YgDLLhWgCr/9eFOMQUZg8vl.6StpM1mkjAosyV6.gUZu', NULL, NULL, NULL, '2025-06-14 01:21:52', '2025-06-14 01:21:52', NULL);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `user_agreements`
--

CREATE TABLE `user_agreements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `agreement_type` varchar(255) NOT NULL DEFAULT 'privacy_policy' COMMENT 'Type of agreement (privacy_policy, terms_of_service, etc.)',
  `version` varchar(255) NOT NULL DEFAULT '1.0' COMMENT 'Version of the agreement accepted',
  `accepted_at` timestamp NOT NULL COMMENT 'When the user accepted the agreement',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the user when accepting',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `user_classes`
--

CREATE TABLE `user_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `organization_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dökümü yapılmış tablolar için indeksler
--

--
-- Tablo için indeksler `authors`
--
ALTER TABLE `authors`
  ADD PRIMARY KEY (`id`),
  ADD KEY `authors_created_by_foreign` (`created_by`),
  ADD KEY `authors_updated_by_foreign` (`updated_by`),
  ADD KEY `authors_name_index` (`name`);

--
-- Tablo için indeksler `books`
--
ALTER TABLE `books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `books_isbn_unique` (`isbn`),
  ADD KEY `books_created_by_foreign` (`created_by`),
  ADD KEY `books_updated_by_foreign` (`updated_by`),
  ADD KEY `books_name_index` (`name`),
  ADD KEY `books_publisher_id_index` (`publisher_id`),
  ADD KEY `books_year_of_publish_index` (`year_of_publish`);

--
-- Tablo için indeksler `book_activity_types`
--
ALTER TABLE `book_activity_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_category_name` (`category`,`name`),
  ADD KEY `book_activity_types_created_by_foreign` (`created_by`),
  ADD KEY `book_activity_types_updated_by_foreign` (`updated_by`),
  ADD KEY `book_activity_types_deleted_by_foreign` (`deleted_by`),
  ADD KEY `book_activity_types_category_is_active_index` (`category`,`is_active`),
  ADD KEY `book_activity_types_is_active_index` (`is_active`),
  ADD KEY `book_activity_types_category_index` (`category`);

--
-- Tablo için indeksler `book_authors`
--
ALTER TABLE `book_authors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_authors_book_id_author_id_unique` (`book_id`,`author_id`),
  ADD KEY `book_authors_author_id_index` (`author_id`);

--
-- Tablo için indeksler `book_questions`
--
ALTER TABLE `book_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `book_questions_created_by_foreign` (`created_by`),
  ADD KEY `book_questions_updated_by_foreign` (`updated_by`),
  ADD KEY `book_questions_deleted_by_foreign` (`deleted_by`),
  ADD KEY `book_questions_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_questions_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_questions_page_start_page_end_index` (`page_start`,`page_end`),
  ADD KEY `book_questions_difficulty_level_index` (`difficulty_level`);

--
-- Tablo için indeksler `book_words`
--
ALTER TABLE `book_words`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_book_word` (`book_id`,`word`),
  ADD KEY `book_words_created_by_foreign` (`created_by`),
  ADD KEY `book_words_updated_by_foreign` (`updated_by`),
  ADD KEY `book_words_deleted_by_foreign` (`deleted_by`),
  ADD KEY `book_words_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_words_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_words_word_book_id_index` (`word`,`book_id`),
  ADD KEY `book_words_page_reference_index` (`page_reference`),
  ADD KEY `book_words_difficulty_level_index` (`difficulty_level`);

--
-- Tablo için indeksler `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Tablo için indeksler `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Tablo için indeksler `enum_organization_types`
--
ALTER TABLE `enum_organization_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_organization_types_name_unique` (`name`),
  ADD KEY `enum_organization_types_created_by_foreign` (`created_by`),
  ADD KEY `enum_organization_types_updated_by_foreign` (`updated_by`),
  ADD KEY `enum_organization_types_name_index` (`name`);

--
-- Tablo için indeksler `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Tablo için indeksler `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Tablo için indeksler `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Tablo için indeksler `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Tablo için indeksler `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

--
-- Tablo için indeksler `organizations`
--
ALTER TABLE `organizations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `organizations_created_by_foreign` (`created_by`),
  ADD KEY `organizations_updated_by_foreign` (`updated_by`),
  ADD KEY `organizations_organization_type_id_index` (`organization_type_id`),
  ADD KEY `active` (`active`);

--
-- Tablo için indeksler `organization_users`
--
ALTER TABLE `organization_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `organization_users_unique` (`user_id`,`organization_id`,`role_id`),
  ADD KEY `organization_users_role_id_foreign` (`role_id`),
  ADD KEY `organization_users_created_by_foreign` (`created_by`),
  ADD KEY `organization_users_updated_by_foreign` (`updated_by`),
  ADD KEY `organization_users_organization_id_role_id_index` (`organization_id`,`role_id`),
  ADD KEY `organization_users_user_id_active_index` (`user_id`,`active`),
  ADD KEY `organization_users_active_index` (`active`);

--
-- Tablo için indeksler `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Tablo için indeksler `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Tablo için indeksler `programs`
--
ALTER TABLE `programs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `programs_created_by_foreign` (`created_by`),
  ADD KEY `programs_updated_by_foreign` (`updated_by`),
  ADD KEY `programs_deleted_by_foreign` (`deleted_by`),
  ADD KEY `programs_name_is_active_index` (`name`,`is_active`),
  ADD KEY `programs_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `programs_story_id_index` (`story_id`);

--
-- Tablo için indeksler `program_books`
--
ALTER TABLE `program_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_books_program_id_book_id_unique` (`program_id`,`book_id`),
  ADD KEY `program_books_created_by_foreign` (`created_by`),
  ADD KEY `program_books_updated_by_foreign` (`updated_by`),
  ADD KEY `program_books_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_books_program_id_index` (`program_id`),
  ADD KEY `program_books_book_id_index` (`book_id`);

--
-- Tablo için indeksler `program_book_activities`
--
ALTER TABLE `program_book_activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `program_book_activities_activity_type_id_foreign` (`activity_type_id`),
  ADD KEY `program_book_activities_created_by_foreign` (`created_by`),
  ADD KEY `program_book_activities_updated_by_foreign` (`updated_by`),
  ADD KEY `program_book_activities_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_book_user_idx` (`program_id`,`book_id`,`user_id`),
  ADD KEY `user_activity_type_idx` (`user_id`,`activity_type_id`),
  ADD KEY `book_activity_type_idx` (`book_id`,`activity_type_id`),
  ADD KEY `completed_status_idx` (`is_completed`,`completed_at`),
  ADD KEY `review_status_idx` (`reviewed_by`,`reviewed_at`),
  ADD KEY `program_book_activities_program_task_instance_id_index` (`program_task_instance_id`),
  ADD KEY `program_book_activities_points_earned_index` (`points_earned`),
  ADD KEY `user_book_activity_idx` (`user_id`,`book_id`,`activity_type_id`);

--
-- Tablo için indeksler `program_book_quizzes`
--
ALTER TABLE `program_book_quizzes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `program_book_quizzes_created_by_foreign` (`created_by`),
  ADD KEY `program_book_quizzes_updated_by_foreign` (`updated_by`),
  ADD KEY `program_book_quizzes_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_book_quizzes_program_id_book_id_user_id_index` (`program_id`,`book_id`,`user_id`),
  ADD KEY `program_book_quizzes_user_id_quiz_type_index` (`user_id`,`quiz_type`),
  ADD KEY `program_book_quizzes_book_id_quiz_type_index` (`book_id`,`quiz_type`),
  ADD KEY `program_book_quizzes_is_passed_quiz_type_index` (`is_passed`,`quiz_type`),
  ADD KEY `program_book_quizzes_started_at_completed_at_index` (`started_at`,`completed_at`),
  ADD KEY `program_book_quizzes_attempt_number_index` (`attempt_number`),
  ADD KEY `user_book_attempt_idx` (`user_id`,`book_id`,`attempt_number`);

--
-- Tablo için indeksler `program_book_quiz_questions`
--
ALTER TABLE `program_book_quiz_questions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_quiz_question` (`program_book_quiz_id`,`book_question_id`),
  ADD UNIQUE KEY `unique_quiz_order` (`program_book_quiz_id`,`question_order`),
  ADD KEY `program_book_quiz_questions_created_by_foreign` (`created_by`),
  ADD KEY `program_book_quiz_questions_updated_by_foreign` (`updated_by`),
  ADD KEY `program_book_quiz_questions_deleted_by_foreign` (`deleted_by`),
  ADD KEY `quiz_question_order_idx` (`program_book_quiz_id`,`question_order`),
  ADD KEY `question_correct_idx` (`book_question_id`,`is_correct`),
  ADD KEY `correct_points_idx` (`is_correct`,`points_earned`),
  ADD KEY `program_book_quiz_questions_answered_at_index` (`answered_at`);

--
-- Tablo için indeksler `program_classes`
--
ALTER TABLE `program_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_classes_program_id_school_class_id_unique` (`program_id`,`school_class_id`),
  ADD KEY `program_classes_created_by_foreign` (`created_by`),
  ADD KEY `program_classes_updated_by_foreign` (`updated_by`),
  ADD KEY `program_classes_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_classes_program_id_index` (`program_id`),
  ADD KEY `program_classes_school_class_id_index` (`school_class_id`);

--
-- Tablo için indeksler `program_reading_logs`
--
ALTER TABLE `program_reading_logs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_daily_log` (`program_id`,`book_id`,`user_id`,`reading_date`),
  ADD KEY `program_reading_logs_book_id_foreign` (`book_id`),
  ADD KEY `program_reading_logs_verified_by_foreign` (`verified_by`),
  ADD KEY `program_reading_logs_created_by_foreign` (`created_by`),
  ADD KEY `program_reading_logs_updated_by_foreign` (`updated_by`),
  ADD KEY `program_reading_logs_deleted_by_foreign` (`deleted_by`),
  ADD KEY `user_date_idx` (`user_id`,`reading_date`),
  ADD KEY `program_book_idx` (`program_id`,`book_id`),
  ADD KEY `verification_idx` (`is_verified`,`verified_by`),
  ADD KEY `date_program_idx` (`reading_date`,`program_id`),
  ADD KEY `program_reading_logs_program_task_instance_id_index` (`program_task_instance_id`),
  ADD KEY `program_reading_logs_points_awarded_index` (`points_awarded`);

--
-- Tablo için indeksler `program_schools`
--
ALTER TABLE `program_schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_schools_program_id_organization_id_unique` (`program_id`,`organization_id`),
  ADD KEY `program_schools_created_by_foreign` (`created_by`),
  ADD KEY `program_schools_updated_by_foreign` (`updated_by`),
  ADD KEY `program_schools_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_schools_program_id_index` (`program_id`),
  ADD KEY `program_schools_organization_id_index` (`organization_id`);

--
-- Tablo için indeksler `program_tasks`
--
ALTER TABLE `program_tasks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `program_tasks_created_by_foreign` (`created_by`),
  ADD KEY `program_tasks_updated_by_foreign` (`updated_by`),
  ADD KEY `program_tasks_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_tasks_program_id_task_type_index` (`program_id`,`task_type`),
  ADD KEY `program_tasks_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `program_tasks_is_recurring_recurrence_pattern_index` (`is_recurring`,`recurrence_pattern`),
  ADD KEY `program_tasks_book_id_index` (`book_id`);

--
-- Tablo için indeksler `program_task_actions`
--
ALTER TABLE `program_task_actions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `program_task_actions_created_by_foreign` (`created_by`),
  ADD KEY `program_task_actions_updated_by_foreign` (`updated_by`),
  ADD KEY `program_task_actions_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_task_actions_program_task_instance_id_action_type_index` (`program_task_instance_id`,`action_type`),
  ADD KEY `program_task_actions_action_date_action_type_index` (`action_date`,`action_type`),
  ADD KEY `program_task_actions_completed_by_index` (`completed_by`),
  ADD KEY `program_task_actions_points_awarded_index` (`points_awarded`);

--
-- Tablo için indeksler `program_task_instances`
--
ALTER TABLE `program_task_instances`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_task_user_date` (`program_task_id`,`user_id`,`start_date`),
  ADD KEY `program_task_instances_created_by_foreign` (`created_by`),
  ADD KEY `program_task_instances_updated_by_foreign` (`updated_by`),
  ADD KEY `program_task_instances_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_task_instances_program_task_id_user_id_index` (`program_task_id`,`user_id`),
  ADD KEY `program_task_instances_user_id_status_index` (`user_id`,`status`),
  ADD KEY `program_task_instances_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `program_task_instances_status_end_date_index` (`status`,`end_date`),
  ADD KEY `program_task_instances_team_id_index` (`team_id`);

--
-- Tablo için indeksler `program_teams`
--
ALTER TABLE `program_teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `program_teams_created_by_foreign` (`created_by`),
  ADD KEY `program_teams_updated_by_foreign` (`updated_by`),
  ADD KEY `program_teams_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_teams_program_id_name_index` (`program_id`,`name`),
  ADD KEY `program_teams_program_id_index` (`program_id`);

--
-- Tablo için indeksler `program_team_members`
--
ALTER TABLE `program_team_members`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_team_members_program_team_id_user_id_unique` (`program_team_id`,`user_id`),
  ADD KEY `program_team_members_created_by_foreign` (`created_by`),
  ADD KEY `program_team_members_updated_by_foreign` (`updated_by`),
  ADD KEY `program_team_members_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_team_members_program_team_id_index` (`program_team_id`),
  ADD KEY `program_team_members_user_id_index` (`user_id`);

--
-- Tablo için indeksler `program_user_achievements`
--
ALTER TABLE `program_user_achievements`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_user_achievement_unique` (`program_id`,`user_id`,`story_achievement_id`) USING BTREE,
  ADD KEY `program_user_achievements_created_by_foreign` (`created_by`),
  ADD KEY `program_user_achievements_updated_by_foreign` (`updated_by`),
  ADD KEY `program_user_achievements_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_user_achievements_program_id_index` (`program_id`),
  ADD KEY `program_user_achievements_user_id_index` (`user_id`),
  ADD KEY `program_user_achievements_story_achievement_id_index` (`story_achievement_id`),
  ADD KEY `program_user_achievements_earned_at_index` (`earned_at`);

--
-- Tablo için indeksler `program_user_books`
--
ALTER TABLE `program_user_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_user_book_unique` (`program_id`,`user_id`,`book_id`) USING BTREE,
  ADD KEY `program_user_books_created_by_foreign` (`created_by`),
  ADD KEY `program_user_books_updated_by_foreign` (`updated_by`),
  ADD KEY `program_user_books_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_user_books_program_id_index` (`program_id`),
  ADD KEY `program_user_books_user_id_index` (`user_id`),
  ADD KEY `program_user_books_book_id_index` (`book_id`),
  ADD KEY `program_user_books_start_date_index` (`start_date`),
  ADD KEY `program_user_books_end_date_index` (`end_date`),
  ADD KEY `program_user_books_program_id_user_id_index` (`program_id`,`user_id`);

--
-- Tablo için indeksler `program_user_characters`
--
ALTER TABLE `program_user_characters`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_user_character_unique` (`program_id`,`user_id`,`story_character_id`) USING BTREE,
  ADD KEY `program_user_characters_created_by_foreign` (`created_by`),
  ADD KEY `program_user_characters_updated_by_foreign` (`updated_by`),
  ADD KEY `program_user_characters_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_user_characters_program_id_index` (`program_id`),
  ADD KEY `program_user_characters_user_id_index` (`user_id`),
  ADD KEY `program_user_characters_story_character_id_index` (`story_character_id`),
  ADD KEY `program_user_characters_current_stage_index` (`current_stage`);

--
-- Tablo için indeksler `program_user_levels`
--
ALTER TABLE `program_user_levels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_user_levels_program_id_user_id_unique` (`program_id`,`user_id`) USING BTREE,
  ADD KEY `program_user_levels_created_by_foreign` (`created_by`),
  ADD KEY `program_user_levels_updated_by_foreign` (`updated_by`),
  ADD KEY `program_user_levels_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_user_levels_program_id_index` (`program_id`),
  ADD KEY `program_user_levels_story_chapter_id_index` (`story_chapter_id`),
  ADD KEY `program_user_levels_user_id_index` (`user_id`) USING BTREE;

--
-- Tablo için indeksler `program_user_maps`
--
ALTER TABLE `program_user_maps`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `program_user_map_unique` (`program_id`,`user_id`,`item_type`,`item_id`) USING BTREE,
  ADD KEY `program_user_maps_created_by_foreign` (`created_by`),
  ADD KEY `program_user_maps_updated_by_foreign` (`updated_by`),
  ADD KEY `program_user_maps_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_user_maps_program_id_index` (`program_id`),
  ADD KEY `program_user_maps_user_id_index` (`user_id`),
  ADD KEY `program_user_maps_item_type_item_id_index` (`item_type`,`item_id`),
  ADD KEY `program_user_maps_x_coordinate_y_coordinate_index` (`x_coordinate`,`y_coordinate`);

--
-- Tablo için indeksler `program_user_points`
--
ALTER TABLE `program_user_points`
  ADD PRIMARY KEY (`id`),
  ADD KEY `program_user_points_created_by_foreign` (`created_by`),
  ADD KEY `program_user_points_updated_by_foreign` (`updated_by`),
  ADD KEY `program_user_points_deleted_by_foreign` (`deleted_by`),
  ADD KEY `program_user_points_program_id_index` (`program_id`),
  ADD KEY `program_user_points_point_source_index` (`point_source`),
  ADD KEY `program_user_points_earned_at_index` (`earned_at`),
  ADD KEY `program_user_points_program_id_user_id_earned_at_index` (`program_id`,`user_id`,`earned_at`),
  ADD KEY `program_user_points_user_id_index` (`user_id`) USING BTREE;

--
-- Tablo için indeksler `publishers`
--
ALTER TABLE `publishers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `publishers_created_by_foreign` (`created_by`),
  ADD KEY `publishers_updated_by_foreign` (`updated_by`),
  ADD KEY `publishers_name_index` (`name`);

--
-- Tablo için indeksler `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Tablo için indeksler `rolesx`
--
ALTER TABLE `rolesx`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_level_unique` (`name`,`level`),
  ADD KEY `roles_created_by_foreign` (`created_by`),
  ADD KEY `roles_updated_by_foreign` (`updated_by`),
  ADD KEY `roles_level_index` (`level`);

--
-- Tablo için indeksler `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Tablo için indeksler `school_classes`
--
ALTER TABLE `school_classes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `school_classes_created_by_foreign` (`created_by`),
  ADD KEY `school_classes_updated_by_foreign` (`updated_by`);

--
-- Tablo için indeksler `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Tablo için indeksler `stories`
--
ALTER TABLE `stories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `stories_created_by_foreign` (`created_by`),
  ADD KEY `stories_updated_by_foreign` (`updated_by`),
  ADD KEY `stories_title_index` (`title`),
  ADD KEY `stories_active_index` (`active`);

--
-- Tablo için indeksler `story_achievements`
--
ALTER TABLE `story_achievements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `story_achievements_unlock_rule_id_foreign` (`unlock_rule_id`),
  ADD KEY `story_achievements_created_by_foreign` (`created_by`),
  ADD KEY `story_achievements_updated_by_foreign` (`updated_by`),
  ADD KEY `story_achievements_story_id_type_index` (`story_id`,`type`),
  ADD KEY `story_achievements_name_index` (`name`);

--
-- Tablo için indeksler `story_books`
--
ALTER TABLE `story_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `story_books_story_id_book_id_unique` (`story_id`,`book_id`),
  ADD UNIQUE KEY `story_books_story_id_sequence_unique` (`story_id`,`sequence`),
  ADD KEY `story_books_created_by_foreign` (`created_by`),
  ADD KEY `story_books_updated_by_foreign` (`updated_by`),
  ADD KEY `story_books_deleted_by_foreign` (`deleted_by`),
  ADD KEY `story_books_story_id_index` (`story_id`),
  ADD KEY `story_books_book_id_index` (`book_id`),
  ADD KEY `story_books_sequence_index` (`sequence`);

--
-- Tablo için indeksler `story_chapters`
--
ALTER TABLE `story_chapters`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `story_chapters_story_id_sequence_unique` (`story_id`,`sequence`),
  ADD KEY `story_chapters_unlock_rule_id_foreign` (`unlock_rule_id`),
  ADD KEY `story_chapters_created_by_foreign` (`created_by`),
  ADD KEY `story_chapters_updated_by_foreign` (`updated_by`),
  ADD KEY `story_chapters_story_id_sequence_index` (`story_id`,`sequence`);

--
-- Tablo için indeksler `story_characters`
--
ALTER TABLE `story_characters`
  ADD PRIMARY KEY (`id`),
  ADD KEY `story_characters_created_by_foreign` (`created_by`),
  ADD KEY `story_characters_updated_by_foreign` (`updated_by`),
  ADD KEY `story_characters_story_id_active_index` (`story_id`,`active`),
  ADD KEY `story_characters_name_index` (`name`);

--
-- Tablo için indeksler `story_character_stages`
--
ALTER TABLE `story_character_stages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `story_character_stages_character_id_stage_number_unique` (`character_id`,`stage_number`),
  ADD KEY `story_character_stages_unlock_rule_id_foreign` (`unlock_rule_id`),
  ADD KEY `story_character_stages_created_by_foreign` (`created_by`),
  ADD KEY `story_character_stages_updated_by_foreign` (`updated_by`),
  ADD KEY `story_character_stages_character_id_stage_number_index` (`character_id`,`stage_number`);

--
-- Tablo için indeksler `story_rules`
--
ALTER TABLE `story_rules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `story_rules_created_by_foreign` (`created_by`),
  ADD KEY `story_rules_updated_by_foreign` (`updated_by`),
  ADD KEY `story_rules_story_id_rule_type_index` (`story_id`,`rule_type`);

--
-- Tablo için indeksler `story_rule_details`
--
ALTER TABLE `story_rule_details`
  ADD PRIMARY KEY (`id`),
  ADD KEY `story_rule_details_created_by_foreign` (`created_by`),
  ADD KEY `story_rule_details_updated_by_foreign` (`updated_by`),
  ADD KEY `story_rule_details_rule_id_required_type_index` (`rule_id`,`required_type`),
  ADD KEY `story_rule_details_required_type_required_id_index` (`required_type`,`required_id`);

--
-- Tablo için indeksler `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tags_name_tag_type_unique` (`name`,`tag_type`),
  ADD KEY `tags_created_by_foreign` (`created_by`),
  ADD KEY `tags_updated_by_foreign` (`updated_by`),
  ADD KEY `tags_tag_type_index` (`tag_type`);

--
-- Tablo için indeksler `tag_values`
--
ALTER TABLE `tag_values`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tag_values_unique` (`taggable_id`,`tag_id`,`taggable_type`),
  ADD KEY `tag_values_taggable_type_taggable_id_index` (`taggable_type`,`taggable_id`),
  ADD KEY `tag_values_tag_id_index` (`tag_id`);

--
-- Tablo için indeksler `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD UNIQUE KEY `users_username_unique` (`username`) USING BTREE,
  ADD KEY `users_created_by_foreign` (`created_by`),
  ADD KEY `users_updated_by_foreign` (`updated_by`);

--
-- Tablo için indeksler `user_agreements`
--
ALTER TABLE `user_agreements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_agreements_created_by_foreign` (`created_by`),
  ADD KEY `user_agreements_updated_by_foreign` (`updated_by`),
  ADD KEY `user_agreements_user_id_agreement_type_index` (`user_id`,`agreement_type`),
  ADD KEY `user_agreements_agreement_type_version_index` (`agreement_type`,`version`),
  ADD KEY `user_agreements_accepted_at_index` (`accepted_at`);

--
-- Tablo için indeksler `user_classes`
--
ALTER TABLE `user_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_classes_unique` (`user_id`,`class_id`,`organization_id`),
  ADD KEY `user_classes_created_by_foreign` (`created_by`),
  ADD KEY `user_classes_updated_by_foreign` (`updated_by`),
  ADD KEY `user_classes_class_id_active_index` (`class_id`,`active`),
  ADD KEY `user_classes_organization_id_active_index` (`organization_id`,`active`),
  ADD KEY `user_classes_user_id_active_index` (`user_id`,`active`),
  ADD KEY `user_classes_active_index` (`active`);

--
-- Dökümü yapılmış tablolar için AUTO_INCREMENT değeri
--

--
-- Tablo için AUTO_INCREMENT değeri `authors`
--
ALTER TABLE `authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Tablo için AUTO_INCREMENT değeri `books`
--
ALTER TABLE `books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Tablo için AUTO_INCREMENT değeri `book_activity_types`
--
ALTER TABLE `book_activity_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- Tablo için AUTO_INCREMENT değeri `book_authors`
--
ALTER TABLE `book_authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Tablo için AUTO_INCREMENT değeri `book_questions`
--
ALTER TABLE `book_questions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Tablo için AUTO_INCREMENT değeri `book_words`
--
ALTER TABLE `book_words`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Tablo için AUTO_INCREMENT değeri `enum_organization_types`
--
ALTER TABLE `enum_organization_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Tablo için AUTO_INCREMENT değeri `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- Tablo için AUTO_INCREMENT değeri `organizations`
--
ALTER TABLE `organizations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Tablo için AUTO_INCREMENT değeri `organization_users`
--
ALTER TABLE `organization_users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Tablo için AUTO_INCREMENT değeri `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- Tablo için AUTO_INCREMENT değeri `programs`
--
ALTER TABLE `programs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Tablo için AUTO_INCREMENT değeri `program_books`
--
ALTER TABLE `program_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_book_activities`
--
ALTER TABLE `program_book_activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Tablo için AUTO_INCREMENT değeri `program_book_quizzes`
--
ALTER TABLE `program_book_quizzes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Tablo için AUTO_INCREMENT değeri `program_book_quiz_questions`
--
ALTER TABLE `program_book_quiz_questions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- Tablo için AUTO_INCREMENT değeri `program_classes`
--
ALTER TABLE `program_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_reading_logs`
--
ALTER TABLE `program_reading_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- Tablo için AUTO_INCREMENT değeri `program_schools`
--
ALTER TABLE `program_schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_tasks`
--
ALTER TABLE `program_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Tablo için AUTO_INCREMENT değeri `program_task_actions`
--
ALTER TABLE `program_task_actions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Tablo için AUTO_INCREMENT değeri `program_task_instances`
--
ALTER TABLE `program_task_instances`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=262;

--
-- Tablo için AUTO_INCREMENT değeri `program_teams`
--
ALTER TABLE `program_teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_team_members`
--
ALTER TABLE `program_team_members`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_user_achievements`
--
ALTER TABLE `program_user_achievements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_user_books`
--
ALTER TABLE `program_user_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Tablo için AUTO_INCREMENT değeri `program_user_characters`
--
ALTER TABLE `program_user_characters`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_user_levels`
--
ALTER TABLE `program_user_levels`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_user_maps`
--
ALTER TABLE `program_user_maps`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `program_user_points`
--
ALTER TABLE `program_user_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Tablo için AUTO_INCREMENT değeri `publishers`
--
ALTER TABLE `publishers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Tablo için AUTO_INCREMENT değeri `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- Tablo için AUTO_INCREMENT değeri `rolesx`
--
ALTER TABLE `rolesx`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Tablo için AUTO_INCREMENT değeri `school_classes`
--
ALTER TABLE `school_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- Tablo için AUTO_INCREMENT değeri `stories`
--
ALTER TABLE `stories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Tablo için AUTO_INCREMENT değeri `story_achievements`
--
ALTER TABLE `story_achievements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Tablo için AUTO_INCREMENT değeri `story_books`
--
ALTER TABLE `story_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Tablo için AUTO_INCREMENT değeri `story_chapters`
--
ALTER TABLE `story_chapters`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Tablo için AUTO_INCREMENT değeri `story_characters`
--
ALTER TABLE `story_characters`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Tablo için AUTO_INCREMENT değeri `story_character_stages`
--
ALTER TABLE `story_character_stages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- Tablo için AUTO_INCREMENT değeri `story_rules`
--
ALTER TABLE `story_rules`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Tablo için AUTO_INCREMENT değeri `story_rule_details`
--
ALTER TABLE `story_rule_details`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `tags`
--
ALTER TABLE `tags`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `tag_values`
--
ALTER TABLE `tag_values`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- Tablo için AUTO_INCREMENT değeri `user_agreements`
--
ALTER TABLE `user_agreements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `user_classes`
--
ALTER TABLE `user_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Dökümü yapılmış tablolar için kısıtlamalar
--

--
-- Tablo kısıtlamaları `authors`
--
ALTER TABLE `authors`
  ADD CONSTRAINT `authors_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `authors_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `books`
--
ALTER TABLE `books`
  ADD CONSTRAINT `books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `books_publisher_id_foreign` FOREIGN KEY (`publisher_id`) REFERENCES `publishers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `books_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `book_activity_types`
--
ALTER TABLE `book_activity_types`
  ADD CONSTRAINT `book_activity_types_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `book_activity_types_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `book_activity_types_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `book_authors`
--
ALTER TABLE `book_authors`
  ADD CONSTRAINT `book_authors_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_authors_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `book_questions`
--
ALTER TABLE `book_questions`
  ADD CONSTRAINT `book_questions_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_questions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `book_questions_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `book_questions_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `book_words`
--
ALTER TABLE `book_words`
  ADD CONSTRAINT `book_words_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_words_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `book_words_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `book_words_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `enum_organization_types`
--
ALTER TABLE `enum_organization_types`
  ADD CONSTRAINT `enum_organization_types_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `enum_organization_types_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `organizations`
--
ALTER TABLE `organizations`
  ADD CONSTRAINT `organizations_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `organizations_organization_type_id_foreign` FOREIGN KEY (`organization_type_id`) REFERENCES `enum_organization_types` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `organizations_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `organization_users`
--
ALTER TABLE `organization_users`
  ADD CONSTRAINT `organization_users_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `organization_users_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `organization_users_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `organization_users_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `organization_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `programs`
--
ALTER TABLE `programs`
  ADD CONSTRAINT `programs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `programs_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `programs_story_id_foreign` FOREIGN KEY (`story_id`) REFERENCES `stories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `programs_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_books`
--
ALTER TABLE `program_books`
  ADD CONSTRAINT `program_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_books_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_books_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_books_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_book_activities`
--
ALTER TABLE `program_book_activities`
  ADD CONSTRAINT `program_book_activities_activity_type_id_foreign` FOREIGN KEY (`activity_type_id`) REFERENCES `book_activity_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_book_activities_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_book_activities_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_activities_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_activities_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_book_activities_program_task_instance_id_foreign` FOREIGN KEY (`program_task_instance_id`) REFERENCES `program_task_instances` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_activities_reviewed_by_foreign` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_activities_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_activities_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_book_quizzes`
--
ALTER TABLE `program_book_quizzes`
  ADD CONSTRAINT `program_book_quizzes_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_book_quizzes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_quizzes_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_quizzes_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_book_quizzes_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_quizzes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_book_quiz_questions`
--
ALTER TABLE `program_book_quiz_questions`
  ADD CONSTRAINT `program_book_quiz_questions_book_question_id_foreign` FOREIGN KEY (`book_question_id`) REFERENCES `book_questions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_book_quiz_questions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_quiz_questions_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_book_quiz_questions_program_book_quiz_id_foreign` FOREIGN KEY (`program_book_quiz_id`) REFERENCES `program_book_quizzes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_book_quiz_questions_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_classes`
--
ALTER TABLE `program_classes`
  ADD CONSTRAINT `program_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_classes_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_classes_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_classes_school_class_id_foreign` FOREIGN KEY (`school_class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_classes_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_reading_logs`
--
ALTER TABLE `program_reading_logs`
  ADD CONSTRAINT `program_reading_logs_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_reading_logs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_reading_logs_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_reading_logs_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_reading_logs_program_task_instance_id_foreign` FOREIGN KEY (`program_task_instance_id`) REFERENCES `program_task_instances` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_reading_logs_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_reading_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_reading_logs_verified_by_foreign` FOREIGN KEY (`verified_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_schools`
--
ALTER TABLE `program_schools`
  ADD CONSTRAINT `program_schools_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_schools_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_schools_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_schools_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_schools_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_tasks`
--
ALTER TABLE `program_tasks`
  ADD CONSTRAINT `program_tasks_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_tasks_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_tasks_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_tasks_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_task_actions`
--
ALTER TABLE `program_task_actions`
  ADD CONSTRAINT `program_task_actions_completed_by_foreign` FOREIGN KEY (`completed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_task_actions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_task_actions_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_task_actions_program_task_instance_id_foreign` FOREIGN KEY (`program_task_instance_id`) REFERENCES `program_task_instances` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_task_actions_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_task_instances`
--
ALTER TABLE `program_task_instances`
  ADD CONSTRAINT `program_task_instances_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_task_instances_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_task_instances_program_task_id_foreign` FOREIGN KEY (`program_task_id`) REFERENCES `program_tasks` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_task_instances_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `program_teams` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_task_instances_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_task_instances_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_teams`
--
ALTER TABLE `program_teams`
  ADD CONSTRAINT `program_teams_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_teams_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_teams_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_teams_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `program_team_members`
--
ALTER TABLE `program_team_members`
  ADD CONSTRAINT `program_team_members_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_team_members_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_team_members_program_team_id_foreign` FOREIGN KEY (`program_team_id`) REFERENCES `program_teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_team_members_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_team_members_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_user_achievements`
--
ALTER TABLE `program_user_achievements`
  ADD CONSTRAINT `program_user_achievements_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_achievements_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_achievements_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_achievements_story_achievement_id_foreign` FOREIGN KEY (`story_achievement_id`) REFERENCES `story_achievements` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_achievements_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_achievements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_user_books`
--
ALTER TABLE `program_user_books`
  ADD CONSTRAINT `program_user_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_books_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_books_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_books_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_books_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_user_characters`
--
ALTER TABLE `program_user_characters`
  ADD CONSTRAINT `program_user_characters_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_characters_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_characters_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_characters_story_character_id_foreign` FOREIGN KEY (`story_character_id`) REFERENCES `story_characters` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_characters_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_characters_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_user_levels`
--
ALTER TABLE `program_user_levels`
  ADD CONSTRAINT `program_user_levels_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_levels_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_levels_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_levels_story_chapter_id_foreign` FOREIGN KEY (`story_chapter_id`) REFERENCES `story_chapters` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_levels_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_levels_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_user_maps`
--
ALTER TABLE `program_user_maps`
  ADD CONSTRAINT `program_user_maps_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_maps_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_maps_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_maps_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_maps_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `program_user_points`
--
ALTER TABLE `program_user_points`
  ADD CONSTRAINT `program_user_points_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_points_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_points_program_id_foreign` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `program_user_points_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `program_user_term_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `publishers`
--
ALTER TABLE `publishers`
  ADD CONSTRAINT `publishers_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `publishers_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `rolesx`
--
ALTER TABLE `rolesx`
  ADD CONSTRAINT `roles_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `roles_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `school_classes`
--
ALTER TABLE `school_classes`
  ADD CONSTRAINT `school_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `school_classes_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `school_classes_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `stories`
--
ALTER TABLE `stories`
  ADD CONSTRAINT `stories_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `stories_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `story_achievements`
--
ALTER TABLE `story_achievements`
  ADD CONSTRAINT `story_achievements_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_achievements_story_id_foreign` FOREIGN KEY (`story_id`) REFERENCES `stories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_achievements_unlock_rule_id_foreign` FOREIGN KEY (`unlock_rule_id`) REFERENCES `story_rules` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_achievements_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `story_books`
--
ALTER TABLE `story_books`
  ADD CONSTRAINT `story_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_books_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_books_story_id_foreign` FOREIGN KEY (`story_id`) REFERENCES `stories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_books_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `story_chapters`
--
ALTER TABLE `story_chapters`
  ADD CONSTRAINT `story_chapters_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_chapters_story_id_foreign` FOREIGN KEY (`story_id`) REFERENCES `stories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_chapters_unlock_rule_id_foreign` FOREIGN KEY (`unlock_rule_id`) REFERENCES `story_rules` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_chapters_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `story_characters`
--
ALTER TABLE `story_characters`
  ADD CONSTRAINT `story_characters_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_characters_story_id_foreign` FOREIGN KEY (`story_id`) REFERENCES `stories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_characters_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `story_character_stages`
--
ALTER TABLE `story_character_stages`
  ADD CONSTRAINT `story_character_stages_character_id_foreign` FOREIGN KEY (`character_id`) REFERENCES `story_characters` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_character_stages_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_character_stages_unlock_rule_id_foreign` FOREIGN KEY (`unlock_rule_id`) REFERENCES `story_rules` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_character_stages_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `story_rules`
--
ALTER TABLE `story_rules`
  ADD CONSTRAINT `story_rules_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_rules_story_id_foreign` FOREIGN KEY (`story_id`) REFERENCES `stories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_rules_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `story_rule_details`
--
ALTER TABLE `story_rule_details`
  ADD CONSTRAINT `story_rule_details_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `story_rule_details_rule_id_foreign` FOREIGN KEY (`rule_id`) REFERENCES `story_rules` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `story_rule_details_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `tags`
--
ALTER TABLE `tags`
  ADD CONSTRAINT `tags_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `tags_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `tag_values`
--
ALTER TABLE `tag_values`
  ADD CONSTRAINT `tag_values_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `users_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Tablo kısıtlamaları `user_agreements`
--
ALTER TABLE `user_agreements`
  ADD CONSTRAINT `user_agreements_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_agreements_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_agreements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Tablo kısıtlamaları `user_classes`
--
ALTER TABLE `user_classes`
  ADD CONSTRAINT `user_classes_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_classes_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_classes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
