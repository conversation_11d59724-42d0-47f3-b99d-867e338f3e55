<?php

namespace App\Services\BookDiscovery;

use App\Services\BookDiscovery\Contracts\BookDiscoveryProviderInterface;
use App\Services\BookDiscovery\Providers\DRBookProvider;
use App\Services\BookDiscovery\Providers\FidanKitapProvider;
use App\Services\BookDiscovery\Providers\KitabinabakProvider;
use App\Models\Book;
use App\Models\Author;
use App\Models\Publisher;
use App\Models\Category;
use App\Models\BookType;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;

class BookDiscoveryService
{
    protected array $providers = [];
    protected array $discoveredData = [];

    public function __construct()
    {
        $this->initializeProviders();
    }

    /**
     * Initialize all book discovery providers.
     */
    protected function initializeProviders(): void
    {
        $sources = config('book_discovery.sources', []);

        foreach ($sources as $key => $config) {
            if (!($config['enabled'] ?? true)) {
                continue;
            }

            $provider = $this->createProvider($key, $config);
            if ($provider) {
                $this->providers[] = $provider;
            }
        }

        // Sort providers by priority
        usort($this->providers, function($a, $b) {
            return $a->getPriority() <=> $b->getPriority();
        });
    }

    /**
     * Create provider instance based on key.
     */
    protected function createProvider(string $key, array $config): ?BookDiscoveryProviderInterface
    {
        switch ($key) {
            case 'dr':
                return new DRBookProvider($key, $config);
            case 'fidankitap':
                return new FidanKitapProvider($key, $config);
            case 'kitabinabak':
                return new KitabinabakProvider($key, $config);
            default:
                Log::warning("Unknown book discovery provider: {$key}");
                return null;
        }
    }

    /**
     * Search for a book by ISBN across all providers.
     * Priority: Database → Local Repository → Online Providers
     */
    public function searchByIsbn(string $isbn): ?array
    {
        $isbn = $this->cleanIsbn($isbn);

        if (!$this->isValidIsbn($isbn)) {
            return null;
        }

        // First check local database
        $localBook = $this->searchLocalDatabase($isbn);
        if ($localBook) {
            return $this->formatLocalBookData($localBook);
        }

        // Second, check local repository
        $repositoryBook = $this->searchLocalRepository($isbn);
        if ($repositoryBook) {
            Log::info("Book found in local repository", [
                'isbn' => $isbn,
                'name' => $repositoryBook['name']
            ]);
            return $repositoryBook;
        }

        // Third, search across external providers
        foreach ($this->providers as $provider) {
            try {
                Log::info("Searching with provider: {$provider->getName()}", ['isbn' => $isbn]);
                
                $bookData = $provider->searchByIsbn($isbn);
                
                if ($bookData && !empty($bookData['name'])) {
                    $bookData['source'] = $provider->getName();
                    $bookData['isbn'] = $isbn;
                    
                    Log::info("Book found with provider: {$provider->getName()}", [
                        'isbn' => $isbn,
                        'name' => $bookData['name']
                    ]);
                    
                    return $bookData;
                }
                
                // Add delay between providers to be respectful
                $delay = config('book_discovery.settings.rate_limit_delay', 1);
                if ($delay > 0) {
                    sleep($delay);
                }
                
            } catch (\Exception $e) {
                Log::error("Provider {$provider->getName()} failed", [
                    'isbn' => $isbn,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // Fallback to Google Books if enabled
        if (config('book_discovery.settings.fallback_to_google_books', true)) {
            return $this->searchGoogleBooks($isbn);
        }

        return null;
    }

    /**
     * Create a book record from discovered data.
     */
    public function createBookFromData(array $bookData, ?int $userId = null): ?Book
    {
        try {
            // Validate required data
            if (empty($bookData['name'])) {
                throw new \InvalidArgumentException('Book name is required');
            }

            // Create or find related entities
            $publisherId = $this->createOrFindPublisher($bookData['publisher'] ?? null);

            // Use provided book_type_id or get default
            $bookTypeId = $bookData['book_type_id'] ?? $this->getDefaultBookTypeId();

            // Extract year from various formats
            $year = $this->extractYear( isset($bookData['year_of_publish']) && $bookData['year_of_publish'] ? $bookData['year_of_publish'] : $bookData['year'] ?? null);

            // Extract page count
            $pageCount = $this->extractPageCount($bookData['page_count'] ?? null);

            // Create book record
            $book = Book::create([
                'name' => trim($bookData['name']),
                'isbn' => $bookData['isbn'],
                'publisher_id' => $publisherId,
                'book_type_id' => $bookTypeId,
                'page_count' => $pageCount,
                'year_of_publish' => $year,
                'created_by' => $userId,
            ]);

            // Create and attach authors
            if (!empty($bookData['author'])) {
                $this->attachAuthors($book, $bookData['author']);
            }

            // Create and attach categories
            if (!empty($bookData['category'])) {
                $this->attachCategories($book, $bookData['category']);
            }

            // Handle cover image based on source
            if (!empty($bookData['cover_image'])) {
                // Check if this is from local repository
                if (isset($bookData['source']) && $bookData['source'] === 'local_repository' && isset($bookData['local_cover_path'])) {
                    // Copy from local repository
                    $this->copyLocalCoverImage($book, $bookData['local_cover_path']);
                } else {
                    // Download from URL
                    $this->downloadAndAttachCoverImage($book, $bookData['cover_image']);
                }
            }

            // Log successful creation
            Log::info('Book created from discovery', [
                'book_id' => $book->id,
                'name' => $book->name,
                'isbn' => $book->isbn,
                'source' => $bookData['source'] ?? 'unknown'
            ]);

            return $book->load(['authors', 'publisher', 'bookType', 'categories']);

        } catch (\Exception $e) {
            Log::error('Failed to create book from discovery data', [
                'data' => $bookData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return null;
        }
    }

    /**
     * Search local database for existing book.
     */
    protected function searchLocalDatabase(string $isbn): ?Book
    {
        return Book::where('isbn', $isbn)
            ->with(['authors', 'publisher', 'bookType', 'categories'])
            ->first();
    }

    /**
     * Search local repository for book metadata.
     * Looks for JSON file in storage/app/private/book_repository/json/{isbn}.json
     */
    protected function searchLocalRepository(string $isbn): ?array
    {
        try {
            $jsonPath = "book_repository/json/{$isbn}.json";

            // Check if JSON file exists
            if (!Storage::exists($jsonPath)) {
                Log::debug("Local repository: JSON file not found", ['isbn' => $isbn, 'path' => $jsonPath]);
                return null;
            }

            // Read and parse JSON file
            $jsonContent = Storage::get($jsonPath);
            $bookData = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Local repository: Invalid JSON format", [
                    'isbn' => $isbn,
                    'error' => json_last_error_msg()
                ]);
                return null;
            }

            // Validate required fields
            if (empty($bookData['name'])) {
                Log::error("Local repository: Missing required field 'name'", ['isbn' => $isbn]);
                return null;
            }

            // Extract authors from the author array
            $authors = [];
            if (isset($bookData['author']) && is_array($bookData['author'])) {
                $authors = array_values($bookData['author']);
            }

            // Extract categories from the category array
            $categories = [];
            if (isset($bookData['category']) && is_array($bookData['category'])) {
                $categories = array_values($bookData['category']);
            }

            // Check if cover image exists in local repository
            $coverImagePath = null;
            $localCoverPath = "book_repository/covers/{$isbn}.jpg";
            if (Storage::exists($localCoverPath)) {
                // Encode the image content to base64 and Prepare the data URL to embed in an image tag
                $imageData = Storage::get($localCoverPath);
                $extension = $this->getImageExtension($imageData);
                $coverImagePath = "data:image/{$extension};base64," . base64_encode($imageData);                
            } else {
                Log::warning("Local repository: Cover image not found", [
                    'isbn' => $isbn,
                    'expected_path' => $localCoverPath
                ]);
            }

            // Format book data for consistency with other providers
            $formattedData = [
                'name' => trim($bookData['name']),
                'isbn' => $isbn,
                'author' => $authors,
                'publisher' => $bookData['publisher'] ?? null,
                'year' => $bookData['year_of_publish'] ?? null,
                'page_count' => $bookData['page_count'] ?? null,
                'cover_image' => $coverImagePath, // Will be processed by copyLocalCoverImage
                'category' => $categories,
                'source' => 'local_repository',
                'local_cover_path' => $localCoverPath, // Store for later copying
            ];

            Log::info("Local repository: Book data loaded successfully", [
                'isbn' => $isbn,
                'name' => $formattedData['name'],
                'authors' => count($authors),
                'categories' => count($categories),
                'has_cover' => !is_null($coverImagePath)
            ]);

            return $formattedData;

        } catch (\Exception $e) {
            Log::error("Local repository: Error searching for book", [
                'isbn' => $isbn,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Format local book data for consistency.
     */
    protected function formatLocalBookData(Book $book): array
    {
        return [
            'id' => $book->id,
            'name' => $book->name,
            'isbn' => $book->isbn,
            'author' => $book->authors->pluck('name')->toArray(),
            'publisher' => $book->publisher?->name,
            'year' => $book->year_of_publish,
            'page_count' => $book->page_count,
            'cover_image' => $book->cover_image,
            'category' => $book->categories->pluck('name')->toArray(),
            'source' => 'local',
            'exists_locally' => true,
        ];
    }

    /**
     * Fallback to Google Books API.
     */
    protected function searchGoogleBooks(string $isbn): ?array
    {
        try {
            $response = Http::timeout(10)->withoutVerifying()->get("https://www.googleapis.com/books/v1/volumes", [
                'q' => "isbn:{$isbn}"
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['items']) && count($data['items']) > 0) {
                    $volumeInfo = $data['items'][0]['volumeInfo'];

                    if (isset($volumeInfo['title']) && !empty($volumeInfo['title'])) {
                        return [
                            'name' => $volumeInfo['title'],
                            'author' => $volumeInfo['authors'] ?? [],
                            'publisher' => $volumeInfo['publisher'] ?? null,
                            'year' => $this->extractYear($volumeInfo['publishedDate'] ?? null),
                            'page_count' => $volumeInfo['pageCount'] ?? null,
                            'cover_image' => $volumeInfo['imageLinks']['thumbnail'] ?? null,
                            'isbn' => $isbn,
                            'source' => 'Google Books',
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Google Books API error', [
                'isbn' => $isbn,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Create or find publisher.
     */
    protected function createOrFindPublisher(?string $publisherName): ?int
    {
        if (empty($publisherName)) {
            return null;
        }

        $publisher = Publisher::firstOrCreate(
            ['name' => trim($publisherName)]
        );

        return $publisher->id;
    }

    /**
     * Get default book type ID.
     */
    protected function getDefaultBookTypeId(): ?int
    {
        $bookType = BookType::first();
        return $bookType ? $bookType->id : null;
    }

    /**
     * Attach authors to book.
     */
    protected function attachAuthors(Book $book, $authors): void
    {
        if (!is_array($authors)) {
            $authors = [$authors];
        }

        foreach ($authors as $authorName) {
            $authorName = trim($authorName);
            if (!empty($authorName)) {
                $author = Author::firstOrCreate(
                    ['name' => $authorName]
                );
                $book->authors()->attach($author->id);
            }
        }
    }

    /**
     * Attach categories to book.
     */
    protected function attachCategories(Book $book, $categories): void
    {
        if (!is_array($categories)) {
            $categories = [$categories];
        }

        foreach ($categories as $categoryName) {
            $categoryName = trim($categoryName);
            if (!empty($categoryName)) {
                $category = Category::firstOrCreate(
                    ['name' => $categoryName]
                );
                $book->categories()->attach($category->id);
            }
        }
    }

    /**
     * Download and attach cover image.
     */
    protected function downloadAndAttachCoverImage(Book $book, string $imageUrl): void
    {
        try {
            $response = Http::timeout(config('book_discovery.settings.image_download_timeout', 30))
                ->withoutVerifying()
                ->get($imageUrl);

            if ($response->successful()) {
                $imageData = $response->body();
                $maxSize = config('book_discovery.settings.max_image_size', 5 * 1024 * 1024);

                if (strlen($imageData) <= $maxSize) {
                    $extension = $this->getImageExtension($imageData, $imageUrl);
                    $allowedTypes = config('book_discovery.settings.allowed_image_types', ['jpg', 'jpeg', 'png', 'webp']);

                    if (in_array($extension, $allowedTypes)) {
                        $filename = 'book_' . $book->id . '_' . time() . '.' . $extension;
                        $path = config('book_discovery.settings.image_storage_path', 'books/covers');

                        Storage::disk('public')->put($path . '/' . $filename, $imageData);

                        $book->update(['cover_image' => $path . '/' . $filename]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to download cover image', [
                'book_id' => $book->id,
                'image_url' => $imageUrl,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Copy cover image from local repository to public storage.
     *
     * @param Book $book The book model instance
     * @param string $localCoverPath Path to the cover image in local repository (e.g., "private/book_repository/covers/{isbn}.jpg")
     */
    protected function copyLocalCoverImage(Book $book, string $localCoverPath): void
    {
        try {
            // Check if source file exists
            if (!Storage::exists($localCoverPath)) {
                Log::warning('Local repository cover image not found', [
                    'book_id' => $book->id,
                    'local_path' => $localCoverPath
                ]);
                return;
            }

            // Read the image data from local repository
            $imageData = Storage::get($localCoverPath);

            // Validate image size
            $maxSize = config('book_discovery.settings.max_image_size', 5 * 1024 * 1024);
            if (strlen($imageData) > $maxSize) {
                Log::warning('Local repository cover image too large', [
                    'book_id' => $book->id,
                    'size' => strlen($imageData),
                    'max_size' => $maxSize
                ]);
                return;
            }

            // Determine file extension
            $extension = $this->getImageExtension($imageData);
            $allowedTypes = config('book_discovery.settings.allowed_image_types', ['jpg', 'jpeg', 'png', 'webp']);

            if (!in_array($extension, $allowedTypes)) {
                Log::warning('Local repository cover image type not allowed', [
                    'book_id' => $book->id,
                    'extension' => $extension,
                    'allowed_types' => $allowedTypes
                ]);
                return;
            }

            // Generate filename and path for public storage
            $filename = 'book_' . $book->id . '_' . time() . '.' . $extension;
            $path = config('book_discovery.settings.image_storage_path', 'books/covers');
            $publicPath = $path . '/' . $filename;

            // Copy to public storage
            Storage::disk('public')->put($publicPath, $imageData);

            // Update book record with cover image path
            $book->update(['cover_image' => $publicPath]);

            Log::info('Local repository cover image copied successfully', [
                'book_id' => $book->id,
                'isbn' => $book->isbn,
                'source_path' => $localCoverPath,
                'public_path' => $publicPath
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to copy local repository cover image', [
                'book_id' => $book->id,
                'local_path' => $localCoverPath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get image extension from data or URL.
     */
    protected function getImageExtension(string $imageData, string $url = ''): string
    {
        // Try to detect from image data
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_buffer($finfo, $imageData);
        finfo_close($finfo);

        $extensions = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/webp' => 'webp',
            'image/gif' => 'gif',
        ];

        if (isset($extensions[$mimeType])) {
            return $extensions[$mimeType];
        }

        // Fallback to URL extension
        if (!empty($url)) {
            $pathInfo = pathinfo(parse_url($url, PHP_URL_PATH));
            return strtolower($pathInfo['extension'] ?? 'jpg');
        }
        return 'jpg';
    }

    /**
     * Extract year from various formats.
     */
    protected function extractYear($yearData): ?int
    {
        if (empty($yearData)) {
            return null;
        }

        if (is_numeric($yearData)) {
            $year = (int) $yearData;
            return ($year >= 1000 && $year <= (date('Y') + 1)) ? $year : null;
        }

        if (preg_match('/(\d{4})/', $yearData, $matches)) {
            $year = (int) $matches[1];
            return ($year >= 1000 && $year <= (date('Y') + 1)) ? $year : null;
        }

        return null;
    }

    /**
     * Extract page count from various formats.
     */
    protected function extractPageCount($pageData): ?int
    {
        if (empty($pageData)) {
            return null;
        }

        if (is_numeric($pageData)) {
            $count = (int) $pageData;
            return ($count > 0 && $count <= 10000) ? $count : null;
        }

        if (preg_match('/(\d+)/', $pageData, $matches)) {
            $count = (int) $matches[1];
            return ($count > 0 && $count <= 10000) ? $count : null;
        }

        return null;
    }

    /**
     * Clean ISBN format.
     */
    protected function cleanIsbn(string $isbn): string
    {
        return preg_replace('/[^0-9X]/i', '', strtoupper($isbn));
    }

    /**
     * Validate ISBN format.
     */
    protected function isValidIsbn(string $isbn): bool
    {
        $length = strlen($isbn);
        
        if ($length !== 10 && $length !== 13) {
            return false;
        }

        if ($length === 10) {
            return preg_match('/^[0-9]{9}[0-9X]$/i', $isbn);
        } else {
            return preg_match('/^[0-9]{13}$/', $isbn);
        }
    }

    /**
     * Get all available providers.
     */
    public function getProviders(): array
    {
        return $this->providers;
    }

    /**
     * Get provider by name.
     */
    public function getProvider(string $name): ?BookDiscoveryProviderInterface
    {
        foreach ($this->providers as $provider) {
            if ($provider->getName() === $name) {
                return $provider;
            }
        }

        return null;
    }
}
