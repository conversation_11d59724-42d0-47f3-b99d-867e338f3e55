<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\BookType;
use App\Models\EnumClassLevel;
use App\Models\PagePoint;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\Number;

#[Icon('building-office')]
class PagePointResource extends BaseResource
{
    protected string $model = PagePoint::class;

    protected array $with = ['bookType', 'classLevel'];

    public function getTitle(): string
    {
        return __('admin.page_points');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.book_type'),
                'bookType',
                formatted: fn(BookType $bookType) => $bookType->name,
                resource: BookTypeResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.class_level'),
                'classLevel',
                formatted: fn(EnumClassLevel $classLevel) => $classLevel->name,
                resource: EnumClassLevelResource::class
            )
                ->sortable(),

            Number::make(__('admin.point'), 'point'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
            BelongsTo::make(
                __('admin.book_type'),
                'bookType',
                formatted: fn(BookType $bookType) => $bookType->name,
                resource: BookTypeResource::class
            )
            ->required(),

            BelongsTo::make(
                __('admin.class_level'),
                'classLevel',
                formatted: fn(EnumClassLevel $classLevel) => $classLevel->name,
                resource: EnumClassLevelResource::class
            )
            ->required(),

            Number::make(__('admin.point'), 'point')
            ->required(),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(
                __('admin.book_type'),
                'bookType',
                formatted: fn(BookType $bookType) => $bookType->name,
                resource: BookTypeResource::class
            ),

            BelongsTo::make(
                __('admin.class_level'),
                'classLevel',
                formatted: fn(EnumClassLevel $classLevel) => $classLevel->name,
                resource: EnumClassLevelResource::class
            ),

            Number::make(__('admin.point'), 'point'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'book_type_id' => ['required', 'exists:book_types,id'],
            'class_level_id' => ['required', 'exists:enum_class_levels,id'],
            'point' => ['required', 'numeric'],
            ...parent::getCommonRules($item),
        ];
    }
}
