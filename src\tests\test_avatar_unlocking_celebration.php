<?php

/**
 * Test Avatar Unlocking Celebration Functionality
 * 
 * This script tests the avatar unlocking celebration feature that displays
 * newly unlocked avatars in the mobile badge unlock screen.
 */

// Set up Laravel environment
$_ENV['APP_ENV'] = 'testing';
require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Avatar;
use App\Models\Activity;
use App\Models\UserActivity;
use App\Models\UserPoint;
use App\Models\Book;
use App\Services\MobileRewardDisplayService;
use Illuminate\Support\Facades\Auth;

try {
    echo "🧪 AVATAR UNLOCKING CELEBRATION TEST\n";
    echo "=====================================\n\n";

    // Test Case 1: Setup test data
    echo "TEST CASE 1: Setup test data\n";
    
    // Find a test user
    $user = User::where('email', 'like', '%test%')->first();
    if (!$user) {
        $user = User::first();
    }
    
    if (!$user) {
        throw new Exception("No users found in database");
    }
    
    echo "- Using test user: {$user->name} (ID: {$user->id})\n";
    
    // Find a test book
    $book = Book::where('active', true)->first();
    if (!$book) {
        throw new Exception("No active books found in database");
    }
    
    echo "- Using test book: {$book->title} (ID: {$book->id})\n";
    
    // Find a test activity
    $activity = Activity::where('active', true)->where('points', '>', 0)->first();
    if (!$activity) {
        throw new Exception("No active activities with points found in database");
    }
    
    echo "- Using test activity: {$activity->name} (Points: {$activity->points})\n";
    
    // Check existing avatars
    $avatars = Avatar::where('active', true)->orderBy('required_points', 'asc')->get();
    echo "- Found {$avatars->count()} active avatars in database\n";
    
    foreach ($avatars->take(5) as $avatar) {
        echo "  - {$avatar->name}: {$avatar->required_points} points required\n";
    }
    
    echo "✅ PASS - Test data setup complete\n\n";
    
    // Test Case 2: Check user's current activity points
    echo "TEST CASE 2: Check user's current activity points\n";
    
    $currentActivityPoints = $user->getActivityPoints();
    $availableAvatars = $user->getAvailableAvatars();
    $lockedAvatars = $user->getLockedAvatars();
    
    echo "- User's current activity points: {$currentActivityPoints}\n";
    echo "- Available avatars: {$availableAvatars->count()}\n";
    echo "- Locked avatars: {$lockedAvatars->count()}\n";
    
    echo "✅ PASS - Current status checked\n\n";
    
    // Test Case 3: Create a user activity that will unlock avatars
    echo "TEST CASE 3: Create user activity to unlock avatars\n";

    // Clean up any existing test activities and points
    UserActivity::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();

    // Find an avatar that can be unlocked with the activity points
    $targetAvatar = $avatars->where('required_points', '<=', $activity->points)->where('required_points', '>', 0)->first();

    if (!$targetAvatar) {
        // Create some existing points to make avatar unlocking possible
        UserPoint::create([
            'point_date' => now()->subDay(),
            'user_id' => $user->id,
            'book_id' => $book->id,
            'source_id' => 999, // Simulate existing activity
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
            'points' => 15, // Add 15 points so 15 + 10 = 25 points total
        ]);

        echo "- Added 15 existing activity points to enable avatar unlocking\n";
        $targetAvatar = $avatars->where('required_points', '<=', 25)->where('required_points', '>', 15)->first();
    }

    if ($targetAvatar) {
        echo "- Target avatar to unlock: {$targetAvatar->name} ({$targetAvatar->required_points} pts)\n";
    }

    // Create a new user activity
    $userActivity = UserActivity::create([
        'user_id' => $user->id,
        'activity_id' => $activity->id,
        'book_id' => $book->id,
        'activity_date' => now(),
        'status' => UserActivity::STATUS_COMPLETED, // Completed status
        'content' => 'Test activity for avatar unlocking',
        'created_by' => $user->id,
    ]);

    echo "- Created user activity (ID: {$userActivity->id})\n";
    echo "- Activity status: {$userActivity->status} (COMPLETED)\n";
    echo "- Activity points value: {$userActivity->getActivityPointsValue()}\n";

    echo "✅ PASS - User activity created\n\n";
    
    // Test Case 4: Test MobileRewardDisplayService avatar checking
    echo "TEST CASE 4: Test MobileRewardDisplayService avatar checking\n";
    
    // Authenticate as the test user
    Auth::login($user);
    
    $rewardService = new MobileRewardDisplayService();
    
    // Check for rewards (including avatars)
    $rewardResult = $rewardService->checkForRewards(null, $userActivity->id, false);
    
    if ($rewardResult) {
        echo "- Reward service returned celebration data\n";
        echo "- Redirect to celebration: " . ($rewardResult['redirect_to_celebration'] ? 'YES' : 'NO') . "\n";
        echo "- User activity ID: {$rewardResult['user_activity_id']}\n";
    } else {
        echo "- No celebration data returned (no new unlocks)\n";
    }
    
    // Check session data
    $unlockedAvatars = session('unlocked_avatars', []);
    echo "- Unlocked avatars in session: " . count($unlockedAvatars) . "\n";
    
    if (!empty($unlockedAvatars)) {
        $sessionAvatars = Avatar::whereIn('id', $unlockedAvatars)->get();
        foreach ($sessionAvatars as $avatar) {
            echo "  - Unlocked: {$avatar->name} ({$avatar->required_points} pts)\n";
        }
    }
    
    echo "✅ PASS - MobileRewardDisplayService tested\n\n";
    
    // Test Case 5: Test avatar unlocking logic directly
    echo "TEST CASE 5: Test avatar unlocking logic directly\n";
    
    $newlyUnlockedAvatars = $userActivity->getNewlyUnlockedAvatars();
    echo "- Newly unlocked avatars from activity: {$newlyUnlockedAvatars->count()}\n";
    
    foreach ($newlyUnlockedAvatars as $avatar) {
        echo "  - {$avatar->name}: {$avatar->required_points} points required\n";
    }
    
    $hasUnlockedAvatars = $userActivity->hasUnlockedAvatarsForUser();
    echo "- Has unlocked avatars: " . ($hasUnlockedAvatars ? 'YES' : 'NO') . "\n";
    
    echo "✅ PASS - Avatar unlocking logic tested\n\n";
    
    // Test Case 6: Test updated user status after activity
    echo "TEST CASE 6: Test updated user status after activity\n";
    
    $user->refresh();
    $newActivityPoints = $user->getActivityPoints();
    $newAvailableAvatars = $user->getAvailableAvatars();
    $newLockedAvatars = $user->getLockedAvatars();
    
    echo "- User's new activity points: {$newActivityPoints}\n";
    echo "- New available avatars: {$newAvailableAvatars->count()}\n";
    echo "- New locked avatars: {$newLockedAvatars->count()}\n";
    
    $pointsGained = $newActivityPoints - $currentActivityPoints;
    $avatarsUnlocked = $newAvailableAvatars->count() - $availableAvatars->count();
    
    echo "- Points gained: {$pointsGained}\n";
    echo "- Avatars unlocked: {$avatarsUnlocked}\n";
    
    echo "✅ PASS - User status updated correctly\n\n";
    
    // Test Case 7: Test BadgeUnlocked component data
    echo "TEST CASE 7: Test BadgeUnlocked component data\n";
    
    if (!empty($unlockedAvatars)) {
        // Simulate what BadgeUnlocked component would do
        $avatarData = Avatar::whereIn('id', $unlockedAvatars)->get()->toArray();
        
        echo "- Avatar data for component: " . count($avatarData) . " avatars\n";
        
        foreach ($avatarData as $avatar) {
            echo "  - Avatar: {$avatar['name']}\n";
            echo "    - Description: {$avatar['description']}\n";
            echo "    - Base image: {$avatar['base_image']}\n";
            echo "    - Required points: {$avatar['required_points']}\n";
        }
        
        // Test item structure for view
        $allItems = array_map(fn($avatar) => ['type' => 'avatar', 'data' => $avatar], $avatarData);
        echo "- Component items structure: " . count($allItems) . " items\n";
        
        foreach ($allItems as $item) {
            echo "  - Item type: {$item['type']}, Name: {$item['data']['name']}\n";
        }
    } else {
        echo "- No avatar data to test (no avatars unlocked)\n";
    }
    
    echo "✅ PASS - BadgeUnlocked component data tested\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    UserActivity::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    session()->forget(['unlocked_avatars']);
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ AVATAR UNLOCKING CELEBRATION TESTS COMPLETED SUCCESSFULLY!\n";
    echo "🎉 All functionality is working correctly!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
