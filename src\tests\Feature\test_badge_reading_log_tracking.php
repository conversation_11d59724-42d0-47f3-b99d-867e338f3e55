<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Badge;
use App\Models\BadgeRule;
use App\Models\UserBadge;
use App\Models\UserPoint;
use App\Models\UserReadingLog;
use App\Models\EnumBadgeRuleType;
use App\Models\Book;

try {
    echo "=== TESTING BADGE READING LOG TRACKING ===\n\n";
    
    // Get test data
    $user = User::first();
    $book = Book::first();
    
    if (!$user || !$book) {
        echo "❌ Missing test data. Need user and book\n";
        exit;
    }
    
    echo "Test User: {$user->name}\n";
    echo "Test Book: {$book->name}\n\n";
    
    // Clean up existing data
    UserBadge::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserReadingLog::where('user_id', $user->id)->delete();
    Badge::where('name', 'LIKE', 'Test%')->delete();
    
    // Test Case 1: Create test badge with reading points rule
    echo "TEST CASE 1: Create test badge with reading points rule\n";
    
    $readingPointsBadge = Badge::create([
        'name' => 'Test Reading Points Badge',
        'description' => 'Earn 30 reading points',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $readingPointsBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::READING_POINTS)->first()->id,
        'rule_value' => 30,
        'active' => true,
    ]);
    
    echo "✅ Created test badge requiring 30 reading points\n\n";
    
    // Test Case 2: Create reading log that should trigger badge award
    echo "TEST CASE 2: Create reading log that should trigger badge award\n";
    
    $readingLog = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->toDateString(),
        'start_page' => 1,
        'end_page' => 30,
        'pages_read' => 30,
        'reading_minutes' => 45,
        'book_completed' => false,
    ]);
    
    $user->refresh();
    $userBadges = $user->getEarnedBadges();
    $readingLogBadges = $readingLog->userBadges;
    
    echo "- Reading Log ID: {$readingLog->id}\n";
    echo "- User Badges Count: {$userBadges->count()}\n";
    echo "- Reading Log Associated Badges: {$readingLogBadges->count()}\n";
    echo "- Has Associated Badges: " . ($readingLog->hasAssociatedBadges() ? 'YES' : 'NO') . "\n";
    echo "- Badge Count Attribute: {$readingLog->badge_count}\n";
    echo "- Awarded Badge Names: {$readingLog->awarded_badge_names}\n";
    
    if ($userBadges->count() > 0) {
        foreach ($userBadges as $userBadge) {
            echo "  - Badge: {$userBadge->badge->name}\n";
            echo "    - Award Type: {$userBadge->award_type}\n";
            echo "    - Trigger Source: {$userBadge->trigger_source}\n";
            echo "    - Reading Log ID: " . ($userBadge->reading_log_id ?: 'NULL') . "\n";
            echo "    - Triggered by Reading Log: " . ($userBadge->isTriggeredByReadingLog() ? 'YES' : 'NO') . "\n";
        }
    }
    
    echo "✅ " . ($userBadges->count() === 1 && $readingLogBadges->count() === 1 ? "PASS" : "FAIL") . " - Badge should be awarded and linked to reading log\n\n";
    
    // Test Case 3: Create another reading log to test multiple triggers
    echo "TEST CASE 3: Create another reading log (should not award duplicate badge)\n";
    
    $readingLog2 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->addDay()->toDateString(),
        'start_page' => 31,
        'end_page' => 60,
        'pages_read' => 30,
        'reading_minutes' => 45,
        'book_completed' => false,
    ]);
    
    $user->refresh();
    $userBadgesAfter = $user->getEarnedBadges();
    $readingLog2Badges = $readingLog2->userBadges;
    
    echo "- Second Reading Log ID: {$readingLog2->id}\n";
    echo "- User Badges Count After: {$userBadgesAfter->count()}\n";
    echo "- Second Reading Log Associated Badges: {$readingLog2Badges->count()}\n";
    echo "- Second Log Has Associated Badges: " . ($readingLog2->hasAssociatedBadges() ? 'YES' : 'NO') . "\n";
    
    echo "✅ " . ($userBadgesAfter->count() === 1 && $readingLog2Badges->count() === 0 ? "PASS" : "FAIL") . " - Duplicate badge should not be awarded\n\n";
    
    // Test Case 4: Create a new badge that should be triggered by second reading log
    echo "TEST CASE 4: Create new badge that should be triggered by second reading log\n";
    
    $readingDaysBadge = Badge::create([
        'name' => 'Test Reading Days Badge',
        'description' => 'Read for 2 different days',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $readingDaysBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::TOTAL_READING_DAYS)->first()->id,
        'rule_value' => 2,
        'active' => true,
    ]);
    
    // Trigger badge check manually since the reading log already exists
    $awardedBadges = $user->checkAndAwardBadges($readingLog2->id);
    
    $user->refresh();
    $userBadgesFinal = $user->getEarnedBadges();
    $readingLog2BadgesAfter = $readingLog2->userBadges;
    
    echo "- Newly Awarded Badges: " . count($awardedBadges) . "\n";
    echo "- Final User Badges Count: {$userBadgesFinal->count()}\n";
    echo "- Second Reading Log Associated Badges After: {$readingLog2BadgesAfter->count()}\n";
    
    foreach ($awardedBadges as $newBadge) {
        echo "  - New Badge: {$newBadge->badge->name} (Reading Log ID: {$newBadge->reading_log_id})\n";
    }
    
    echo "✅ " . (count($awardedBadges) === 1 && $readingLog2BadgesAfter->count() === 1 ? "PASS" : "FAIL") . " - New badge should be awarded and linked to second reading log\n\n";
    
    // Test Case 5: Test manual badge award (should not have reading_log_id)
    echo "TEST CASE 5: Test manual badge award (should not have reading_log_id)\n";
    
    $manualBadge = Badge::create([
        'name' => 'Test Manual Badge',
        'description' => 'Manually awarded badge',
        'manual' => true,
        'active' => true,
    ]);
    
    $teacher = User::where('id', '!=', $user->id)->first();
    $manualAward = UserBadge::awardBadgeToUser($user->id, $manualBadge->id, $teacher->id);
    
    echo "- Manual Award Result: " . ($manualAward ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Manual Badge Reading Log ID: " . ($manualAward->reading_log_id ?: 'NULL') . "\n";
    echo "- Manual Badge Trigger Source: {$manualAward->trigger_source}\n";
    echo "- Manual Badge Triggered by Reading Log: " . ($manualAward->isTriggeredByReadingLog() ? 'YES' : 'NO') . "\n";
    
    echo "✅ " . ($manualAward && is_null($manualAward->reading_log_id) ? "PASS" : "FAIL") . " - Manual badge should not have reading_log_id\n\n";
    
    // Test Case 6: Test cascade deletion
    echo "TEST CASE 6: Test cascade deletion when reading log is deleted\n";
    
    $badgesBeforeDeletion = UserBadge::where('user_id', $user->id)->count();
    $badgesLinkedToFirstLog = UserBadge::where('reading_log_id', $readingLog->id)->count();
    
    echo "- Total Badges Before Deletion: {$badgesBeforeDeletion}\n";
    echo "- Badges Linked to First Reading Log: {$badgesLinkedToFirstLog}\n";
    
    // Delete the first reading log
    $readingLog->delete();
    
    $badgesAfterDeletion = UserBadge::where('user_id', $user->id)->count();
    $badgesLinkedToFirstLogAfter = UserBadge::where('reading_log_id', $readingLog->id)->count();
    
    echo "- Total Badges After Deletion: {$badgesAfterDeletion}\n";
    echo "- Badges Linked to First Reading Log After: {$badgesLinkedToFirstLogAfter}\n";
    echo "- Expected Reduction: {$badgesLinkedToFirstLog}\n";
    echo "- Actual Reduction: " . ($badgesBeforeDeletion - $badgesAfterDeletion) . "\n";
    
    echo "✅ " . (($badgesBeforeDeletion - $badgesAfterDeletion) === $badgesLinkedToFirstLog ? "PASS" : "FAIL") . " - Cascade deletion should work correctly\n\n";
    
    // Test Case 7: Verify remaining badges are intact
    echo "TEST CASE 7: Verify remaining badges are intact\n";
    
    $remainingBadges = UserBadge::where('user_id', $user->id)->get();
    
    echo "- Remaining Badges Count: {$remainingBadges->count()}\n";
    
    foreach ($remainingBadges as $badge) {
        echo "  - Badge: {$badge->badge->name}\n";
        echo "    - Reading Log ID: " . ($badge->reading_log_id ?: 'NULL') . "\n";
        echo "    - Trigger Source: {$badge->trigger_source}\n";
    }
    
    echo "✅ PASS - Remaining badges verified\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    UserBadge::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserReadingLog::where('user_id', $user->id)->delete();
    Badge::where('name', 'LIKE', 'Test%')->delete();
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Badge reading log tracking tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
