<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ClassActivity;
use App\Models\SchoolClass;
use App\Models\Activity;

class ClassActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
//        $query = SchoolClass::where('active', true);
//        Log::info($query->toSql()); // Log the SQL query
//        Log::info($query->getBindings());

        // Get all active classes and activities
        $classes = SchoolClass::where('active', true)->get();
        $activities = Activity::where('active', true)->get();

        $classActivities = [];
        
        foreach ($classes as $class) {
            foreach ($activities as $activity) {
                // Check if ClassActivity already exists
                $exists = ClassActivity::where('class_id', $class->id)
                    ->where('activity_id', $activity->id)
                    ->exists();
                
                if (!$exists) {
                    $classActivities[] = [
                        'class_id' => $class->id,
                        'activity_id' => $activity->id,
                        'question_count' => $activity->question_count,
                        'min_grade' => $activity->min_grade,
                        'allowed_tries' => $activity->allowed_tries,
                        'min_word_count' => $activity->min_word_count,
                        'points' => $activity->points,
                        'required' => $activity->required,
                        'need_approval' => $activity->need_approval,
                        'active' => $activity->active,
                    ];
                }
            }
        }

        if (!empty($classActivities)) {
            // Insert in chunks to avoid memory issues
            $chunks = array_chunk($classActivities, 100);
            foreach ($chunks as $chunk) {
                ClassActivity::insert($chunk);
            }
            
            $this->command->info('Created ' . count($classActivities) . ' ClassActivity records.');
        } else {
            $this->command->info('No new ClassActivity records needed.');
        }
    }
}
