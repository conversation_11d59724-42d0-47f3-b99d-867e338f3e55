<?php

declare(strict_types=1);

namespace App\MoonShine\Pages\Student;

use App\Models\Reward;
use App\MoonShine\Resources\UserActivityResource;
use MoonShine\Laravel\Pages\Crud\DetailPage;
use MoonShine\UI\Components\Metrics\Wrapped\ValueMetric;

use MoonShine\UI\Components\{Layout\Box, Layout\Flex, Layout\Grid, Layout\LineBreak, Tabs\Tab, ActionButton, Card, CardsBuilder, Heading, Tabs};
use MoonShine\UI\Fields\{Date, Text};

class StudentDetailPage extends DetailPage
{
    private array $rewards = []; 
    
    protected function components(): iterable
    {
        $student = $this->getResource()->getItem();
        return [
            Flex::make([    
                Card::make(
                    // name (level number)
                    title: $student->name . ' (' . __('admin.level') . ' ' . $student->getCurrentLevelNumber() . ')' ,
                    thumbnail: asset('storage/' . $student->getAvatarDisplayImage()),
                    subtitle: $student->username,
                ),

                Grid::make([
                    ValueMetric::make(__('admin.books_completed'))
                        ->value(fn() => $student->getTotalBooksCompleted())
                        ->columnSpan(4),

                    ValueMetric::make(__('admin.reading_points'))
                        ->value(fn() => $student->getTotalPagePoints())
                        ->columnSpan(4),

                    ValueMetric::make(__('admin.reading_minutes'))
                        ->value(fn() => $student->readingLogs()->sum('reading_duration'))
                        ->columnSpan(4),

                    ValueMetric::make(__('admin.rewards_earned'))
                        ->value(fn() => $student->getTotalRewards())
                        ->columnSpan(4),

                    ValueMetric::make(__('admin.activity_points'))
                        ->value(fn() => $student->getTotalActivityPoints())
                        ->columnSpan(4),

                    ValueMetric::make(__('admin.reading_streak'))
                        ->value(fn() => $student->getCurrentReadingStreak() . ' ' . __('admin.days'))
                        ->columnSpan(4),
                ]),
            ]),
            // add edit and delete buttons
            $this->getPageButtons()[0],            
            LineBreak::make(),
            Heading::make(__('admin.active_tasks'), 2),
            $this->getActiveTasksCards(),

            // Tabbed Content
            Tabs::make([
                Tab::make(__('admin.reading_details'), [
                    Heading::make(__('admin.books_reading'), 2),
                    $this->getBooksReadingCards(),
                    Heading::make(__('admin.books_completed'), 2),
                    $this->getBooksCompletedCards(),
                ]),
                Tab::make(__('admin.activity_details'), [
                    $this->getActivityDetailsCards(),
                ]),
                Tab::make(__('admin.rewards_details'), 
                    $this->getRewardsDetailsCards(),
                ),
            ]),
        ];
    }

    /**
     * Get active tasks as cards.
     */
    protected function getActiveTasksCards()
    {
        $activeTasks = $this->getResource()->getItem()->getActiveTasks();

        // if no active tasks, return a message
        if ($activeTasks->count() === 0) {
            return Heading::make(__('admin.no_active_tasks'), 6);
        }

        return CardsBuilder::make($activeTasks)
                    ->fields([
                        Text::make(__('admin.task_name'), 'name'),
                        Text::make(__('admin.assigned_date'), 'assigned_date'),
                        Text::make(__('admin.progress'), 'progress'),
                    ]);
    }

    protected function getBooksReadingCards()
    {
        $currentBooks = $this->getResource()->getItem()->getReadingDetails()['current_books'];

        return CardsBuilder::make($currentBooks)
                    ->title(fn($book) => html_entity_decode($book['book_name']))
                    ->subtitle(fn($book) => $book['progress_percentage'] . '%')
                    ->thumbnail(fn($book) => asset('storage/' . $book['cover_image']))
                    ->columnSpan(2, 6)
                    ->fields([
                                Date::make(__('admin.start_date'), 'start_date')->format('d.m.Y'),
                            ]);
    }

    protected function getBooksCompletedCards()
    {
        $completedBooks = $this->getResource()->getItem()->getReadingDetails()['completed_books'];

        return CardsBuilder::make($completedBooks)
                    ->title(fn($book) => html_entity_decode($book['book_name']))
                    ->thumbnail(fn($book) => asset('storage/' . $book['cover_image']))
                    ->columnSpan(2, 6)
                    ->fields([
                                Date::make(__('admin.start_date'), 'start_date')->format('d.m.Y'),
                                Date::make(__('admin.end_date'), 'end_date')->format('d.m.Y'),
                            ]);
    }

    protected function getActivityDetailsCards($status = null)
    {
        $activities = $this->getResource()->getItem()->getActivityDetails($status);
        $resource = app(UserActivityResource::class);
        return CardsBuilder::make( $activities)
                    ->title('activity_name')
                    ->subtitle('book_name')
                    ->fields([
                                Date::make(__('admin.activity_date'), 'activity_date')->format('d.m.Y'),
                                Text::make(__('admin.status'), 'status_text'),
                                Text::make(__('admin.rating'), 'rating'),
                                Text::make(__('admin.points_earned'), 'points_earned'),
                                Text::make(__('admin.feedback'), 'feedback'),
                            ])
                    ->buttons([
                                ActionButton::make(__('admin.view'), fn($activity) => $resource->getDetailPageUrl($activity['id']))
                                    ->secondary()
                                    ->icon('eye'),
                            ]);    
    }

    /**
     * Get rewards details as cards grouped by type.
     */
    protected function getRewardsDetailsCards()
    {
        $result = [];
        $rewards = $this->getResource()->getItem()->getRewardDetails();
        $typeTranslationKeys = [
            Reward::TYPE_BADGE => 'badges',
            Reward::TYPE_GIFT => 'gifts',
            Reward::TYPE_TROPHY => 'trophies',
            Reward::TYPE_CARD => 'cards',
            Reward::TYPE_ITEM => 'items',
        ];        
        // loop on rewards types and create cards
        foreach ($rewards as $type => $rewards) {
            $result[] = Heading::make(__('admin.' . $typeTranslationKeys[$type]), 2);
            $result[] = CardsBuilder::make($rewards)
                        ->thumbnail(fn($reward) => asset('storage/' . $reward['image']))
                        ->title('name')
                        ->subtitle(fn($reward) => $reward['awarded_date']->format('d.m.Y'))
                        ->columnSpan(2, 6);
        }   
        return $result;                    
    }
}
