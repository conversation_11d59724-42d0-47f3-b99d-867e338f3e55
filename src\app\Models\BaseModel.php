<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

abstract class BaseModel extends Model
{
    public $timestamps = false;    
    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically set created_by  if model has created_by field
        static::creating(function ($model) {
            if (in_array('created_by', $model->getFillable()) && Auth::check()) {
                $model->created_by = Auth::id();
            }
        });
    }

    // add creator and created_by scope methods if model has created_by field   
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function scopeCreatedBy($query, $userId)
    {
        return $query->where('created_by', $userId);
    }


    /**
     * Get the display name for the model.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name ?? $this->title ?? $this->id;
    }
}
