
"} 
[2025-10-30 00:13:58] local.DEBUG: Local repository: JSON file not found {"isbn":"9786257947824","path":"book_repository/json/9786257947824.json"} 
[2025-10-30 00:13:58] local.INFO: Searching with provider: D&R {"isbn":"9786257947824"} 
[2025-10-30 00:13:59] local.INFO: Searching with provider: KitabinAbak {"isbn":"9786257947824"} 
[2025-10-30 00:14:02] local.INFO: Book not found in any external sources {"isbn":"9786257947824"} 
[2025-10-30 00:15:09] local.DEBUG: Local repository: JSON file not found {"isbn":"9786059375948","path":"book_repository/json/9786059375948.json"} 
[2025-10-30 00:15:09] local.INFO: Searching with provider: D&R {"isbn":"9786059375948"} 
[2025-10-30 00:15:10] local.INFO: Book found with provider: D&R {"isbn":"9786059375948","name":"Pırpır Okula Başlıyor 1"} 
[2025-10-30 00:15:10] local.INFO: Book created from discovery {"book_id":25,"name":"Pırpır Okula Başlıyor 1","isbn":"9786059375948","source":"D&R"} 
[2025-10-30 00:15:10] local.INFO: Book created successfully from discovery service {"book_id":25,"isbn":"9786059375948","source":"D&R"} 
[2025-10-30 00:17:01] local.DEBUG: Local repository: JSON file not found {"isbn":"9786055781712","path":"book_repository/json/9786055781712.json"} 
[2025-10-30 00:17:01] local.INFO: Searching with provider: D&R {"isbn":"9786055781712"} 
[2025-10-30 00:17:03] local.INFO: Searching with provider: KitabinAbak {"isbn":"9786055781712"} 
[2025-10-30 00:17:04] local.INFO: Book found with provider: KitabinAbak {"isbn":"9786055781712","name":"İstiklal Marşı Öyküleri"} 
[2025-10-30 00:17:04] local.INFO: Book created from discovery {"book_id":26,"name":"İstiklal Marşı Öyküleri","isbn":"9786055781712","source":"KitabinAbak"} 
[2025-10-30 00:17:04] local.INFO: Book created successfully from discovery service {"book_id":26,"isbn":"9786055781712","source":"KitabinAbak"} 
[2025-10-30 00:19:17] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:19:17] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:19:19] local.INFO: Searching with provider: KitabinAbak {"isbn":"9789944905039"} 
[2025-10-30 00:19:20] local.INFO: Book found with provider: KitabinAbak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:19:20] local.INFO: Book created from discovery {"book_id":27,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"KitabinAbak"} 
[2025-10-30 00:19:20] local.INFO: Book created successfully from discovery service {"book_id":27,"isbn":"9789944905039","source":"KitabinAbak"} 
[2025-10-30 00:26:21] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:26:21] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:26:23] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:26:23] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:26:23] local.INFO: Book created from discovery {"book_id":28,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:26:23] local.INFO: Book created successfully from discovery service {"book_id":28,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:28:50] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:28:50] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:28:52] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:28:52] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:28:52] local.INFO: Book created from discovery {"book_id":29,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:28:52] local.INFO: Book created successfully from discovery service {"book_id":29,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:29:50] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:29:50] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:29:52] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:29:52] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:29:52] local.INFO: Book created from discovery {"book_id":30,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:29:52] local.INFO: Book created successfully from discovery service {"book_id":30,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:30:59] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:30:59] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:31:01] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:31:01] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:31:01] local.INFO: Book created from discovery {"book_id":31,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:31:01] local.INFO: Book created successfully from discovery service {"book_id":31,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:35:55] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:35:55] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:35:57] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:35:57] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:35:57] local.INFO: Book created from discovery {"book_id":32,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:35:57] local.INFO: Book created successfully from discovery service {"book_id":32,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:41:44] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:41:44] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:41:46] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:41:46] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:41:46] local.INFO: Book created from discovery {"book_id":33,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:41:46] local.INFO: Book created successfully from discovery service {"book_id":33,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:47:35] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:47:35] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:47:36] local.INFO: Book data from provider: D&R {"data":null} 
[2025-10-30 00:47:37] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:47:37] local.INFO: Book data from provider: Kitabinabak {"data":{"name":"Alparslan Malazgirt Kahramanı","cover_image":"","isbn":"9789944905039","publisher":"Çamlıca Basım Yayın","page_count":72,"year_of_publish":2006,"source":"KitabinAbak"}} 
[2025-10-30 00:47:37] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:47:37] local.INFO: Book created from discovery {"book_id":34,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:47:37] local.INFO: Book created successfully from discovery service {"book_id":34,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:50:40] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:50:40] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:50:42] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:50:42] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:50:42] local.INFO: Book created from discovery {"book_id":35,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:50:42] local.INFO: Book created successfully from discovery service {"book_id":35,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:54:59] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:54:59] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:55:00] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:55:00] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:55:00] local.INFO: Book created from discovery {"book_id":36,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:55:01] local.INFO: Book created successfully from discovery service {"book_id":36,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:56:48] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:56:48] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:56:50] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:56:50] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:56:50] local.INFO: Book created from discovery {"book_id":37,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:56:50] local.INFO: Book created successfully from discovery service {"book_id":37,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:58:38] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 00:58:38] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 00:58:40] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 00:58:40] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 00:58:40] local.INFO: Creating book from discovery data {"isbn":"9789944905039","data":{"name":"Alparslan Malazgirt Kahramanı","cover_image":"","isbn":"9789944905039","publisher":"Çamlıca Basım Yayın","page_count":72,"year_of_publish":2006,"source":"Kitabinabak"}} 
[2025-10-30 00:58:40] local.INFO: Book created from discovery {"book_id":38,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 00:58:40] local.INFO: Book created successfully from discovery service {"book_id":38,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 01:00:43] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 01:00:43] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 01:00:44] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 01:00:44] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 01:00:44] local.INFO: Creating book from discovery data {"isbn":"9789944905039","data":{"name":"Alparslan Malazgirt Kahramanı","cover_image":"","isbn":"9789944905039","publisher":"Çamlıca Basım Yayın","page_count":72,"year_of_publish":2006,"source":"Kitabinabak"}} 
[2025-10-30 01:00:44] local.INFO: Book created from discovery {"book_id":39,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 01:00:44] local.INFO: Book created successfully from discovery service {"book_id":39,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 01:02:54] local.DEBUG: Local repository: JSON file not found {"isbn":"9789944905039","path":"book_repository/json/9789944905039.json"} 
[2025-10-30 01:02:54] local.INFO: Searching with provider: D&R {"isbn":"9789944905039"} 
[2025-10-30 01:02:56] local.INFO: Searching with provider: Kitabinabak {"isbn":"9789944905039"} 
[2025-10-30 01:02:56] local.INFO: Book found with provider: Kitabinabak {"isbn":"9789944905039","name":"Alparslan Malazgirt Kahramanı"} 
[2025-10-30 01:02:56] local.INFO: Creating book from discovery data {"isbn":"9789944905039","data":{"name":"Alparslan Malazgirt Kahramanı","cover_image":"","isbn":"9789944905039","publisher":"Çamlıca Basım Yayın","page_count":72,"year_of_publish":2006,"source":"Kitabinabak"}} 
[2025-10-30 01:02:56] local.INFO: Book created from discovery {"book_id":40,"name":"Alparslan Malazgirt Kahramanı","isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 01:02:56] local.INFO: Book created successfully from discovery service {"book_id":40,"isbn":"9789944905039","source":"Kitabinabak"} 
[2025-10-30 01:03:17] local.DEBUG: Local repository: JSON file not found {"isbn":"9786059375948","path":"book_repository/json/9786059375948.json"} 
[2025-10-30 01:03:17] local.INFO: Searching with provider: D&R {"isbn":"9786059375948"} 
[2025-10-30 01:03:17] local.INFO: Book found with provider: D&R {"isbn":"9786059375948","name":"Pırpır Okula Başlıyor 1"} 
[2025-10-30 01:03:17] local.INFO: Creating book from discovery data {"isbn":"9786059375948","data":{"name":"Pırpır Okula Başlıyor 1","author":["Asuman Sarıtaç"],"publisher":"Çamlıca Çocuk Yayınları","year":2017,"isbn":"9786059375948","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0001728599001-1.jpg","source":"D&R"}} 
[2025-10-30 01:03:17] local.ERROR: Failed to create book from discovery data {"data":{"name":"Pırpır Okula Başlıyor 1","author":["Asuman Sarıtaç"],"publisher":"Çamlıca Çocuk Yayınları","year":2017,"isbn":"9786059375948","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0001728599001-1.jpg","source":"D&R"},"error":"Undefined array key \"year_of_publish\"","trace":"#0 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\app\\Services\\BookDiscovery\\BookDiscoveryService.php(158): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\app\\MoonShine\\Resources\\BookResource.php(224): App\\Services\\BookDiscovery\\BookDiscoveryService->createBookFromData()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Controllers\\MethodController.php(35): App\\MoonShine\\Resources\\BookResource->searchAndImportBook()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): MoonShine\\Laravel\\Http\\Controllers\\MethodController->__invoke()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#61 {main}"} 
[2025-10-30 01:03:24] local.DEBUG: Local repository: JSON file not found {"isbn":"9786059375948","path":"book_repository/json/9786059375948.json"} 
[2025-10-30 01:03:24] local.INFO: Searching with provider: D&R {"isbn":"9786059375948"} 
[2025-10-30 01:03:24] local.INFO: Book found with provider: D&R {"isbn":"9786059375948","name":"Pırpır Okula Başlıyor 1"} 
[2025-10-30 01:03:24] local.INFO: Creating book from discovery data {"isbn":"9786059375948","data":{"name":"Pırpır Okula Başlıyor 1","author":["Asuman Sarıtaç"],"publisher":"Çamlıca Çocuk Yayınları","year":2017,"isbn":"9786059375948","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0001728599001-1.jpg","source":"D&R"}} 
[2025-10-30 01:03:24] local.ERROR: Failed to create book from discovery data {"data":{"name":"Pırpır Okula Başlıyor 1","author":["Asuman Sarıtaç"],"publisher":"Çamlıca Çocuk Yayınları","year":2017,"isbn":"9786059375948","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0001728599001-1.jpg","source":"D&R"},"error":"Undefined array key \"year_of_publish\"","trace":"#0 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\app\\Services\\BookDiscovery\\BookDiscoveryService.php(158): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\app\\MoonShine\\Resources\\BookResource.php(224): App\\Services\\BookDiscovery\\BookDiscoveryService->createBookFromData()
#3 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Controllers\\MethodController.php(35): App\\MoonShine\\Resources\\BookResource->searchAndImportBook()
#4 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): MoonShine\\Laravel\\Http\\Controllers\\MethodController->__invoke()
#5 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#8 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#9 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\moonshine\\moonshine\\src\\Laravel\\src\\Http\\Middleware\\ChangeLocale.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): MoonShine\\Laravel\\Http\\Middleware\\ChangeLocale->handle()
#12 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#16 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\AuthenticateSession->handle()
#18 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#27 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#29 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then()
#31 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute()
#33 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute()
#34 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#36 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#38 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#41 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#43 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#44 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#46 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#48 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle()
#50 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#52 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#54 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#56 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#58 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#59 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#60 D:\\ba\\calisma\\web\\kitapokuma\\okumobil\\src\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#61 {main}"} 
[2025-10-30 01:04:16] local.DEBUG: Local repository: JSON file not found {"isbn":"9786059375948","path":"book_repository/json/9786059375948.json"} 
[2025-10-30 01:04:16] local.INFO: Searching with provider: D&R {"isbn":"9786059375948"} 
[2025-10-30 01:04:16] local.INFO: Book found with provider: D&R {"isbn":"9786059375948","name":"Pırpır Okula Başlıyor 1"} 
[2025-10-30 01:04:16] local.INFO: Creating book from discovery data {"isbn":"9786059375948","data":{"name":"Pırpır Okula Başlıyor 1","author":["Asuman Sarıtaç"],"publisher":"Çamlıca Çocuk Yayınları","year":2017,"isbn":"9786059375948","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0001728599001-1.jpg","source":"D&R"}} 
[2025-10-30 01:04:16] local.INFO: Book created from discovery {"book_id":41,"name":"Pırpır Okula Başlıyor 1","isbn":"9786059375948","source":"D&R"} 
[2025-10-30 01:04:16] local.INFO: Book created successfully from discovery service {"book_id":41,"isbn":"9786059375948","source":"D&R"} 
[2025-10-30 01:12:00] local.DEBUG: Local repository: JSON file not found {"isbn":"9786055033002","path":"book_repository/json/9786055033002.json"} 
[2025-10-30 01:12:00] local.INFO: Searching with provider: D&R {"isbn":"9786055033002"} 
[2025-10-30 01:12:01] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786055033002"} 
[2025-10-30 01:12:03] local.INFO: Book not found in any external sources {"isbn":"9786055033002"} 
[2025-10-30 01:12:37] local.DEBUG: Local repository: JSON file not found {"isbn":"9786259471419","path":"book_repository/json/9786259471419.json"} 
[2025-10-30 01:12:37] local.INFO: Searching with provider: D&R {"isbn":"9786259471419"} 
[2025-10-30 01:12:38] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786259471419"} 
[2025-10-30 01:12:40] local.INFO: Book not found in any external sources {"isbn":"9786259471419"} 
[2025-10-30 01:13:20] local.DEBUG: Local repository: JSON file not found {"isbn":"9786053485100","path":"book_repository/json/9786053485100.json"} 
[2025-10-30 01:13:20] local.INFO: Searching with provider: D&R {"isbn":"9786053485100"} 
[2025-10-30 01:13:21] local.INFO: Book found with provider: D&R {"isbn":"9786053485100","name":"Kişilik İksiri"} 
[2025-10-30 01:13:21] local.INFO: Creating book from discovery data {"isbn":"9786053485100","data":{"name":"Kişilik İksiri","author":["Alan MacDonald"],"publisher":"Martı Yayınları","year":2015,"isbn":"9786053485100","cover_image":"https://i.dr.com.tr/cache/500x400-0/originals/0000000639937-1.jpg","source":"D&R"}} 
[2025-10-30 01:13:22] local.INFO: Book created from discovery {"book_id":42,"name":"Kişilik İksiri","isbn":"9786053485100","source":"D&R"} 
[2025-10-30 01:13:22] local.INFO: Book created successfully from discovery service {"book_id":42,"isbn":"9786053485100","source":"D&R"} 
[2025-10-30 10:48:40] local.DEBUG: Local repository: JSON file not found {"isbn":"9786054928163","path":"book_repository/json/9786054928163.json"} 
[2025-10-30 10:48:40] local.INFO: Searching with provider: D&R {"isbn":"9786054928163"} 
[2025-10-30 10:48:41] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786054928163"} 
[2025-10-30 10:48:43] local.INFO: Searching with provider: Amazon TR {"isbn":"9786054928163"} 
[2025-10-30 10:48:45] local.INFO: Book found with provider: Amazon TR {"isbn":"9786054928163","name":"Ramazan Kuzularına"} 
[2025-10-30 10:48:45] local.INFO: Creating book from discovery data {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 10:48:46] local.INFO: Book created from discovery {"book_id":43,"name":"Ramazan Kuzularına","isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 10:48:46] local.INFO: Book created successfully from discovery service {"book_id":43,"isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:00:26] local.DEBUG: Local repository: JSON file not found {"isbn":"9786054928163","path":"book_repository/json/9786054928163.json"} 
[2025-10-30 11:00:26] local.INFO: Searching with provider: D&R {"isbn":"9786054928163"} 
[2025-10-30 11:00:28] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786054928163"} 
[2025-10-30 11:00:30] local.INFO: Searching with provider: Amazon TR {"isbn":"9786054928163"} 
[2025-10-30 11:00:30] local.INFO: Book found with provider: Amazon TR {"isbn":"9786054928163","name":"Ramazan Kuzularına"} 
[2025-10-30 11:00:30] local.INFO: Creating book from discovery data {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 11:00:30] local.INFO: Book created from discovery {"book_id":44,"name":"Ramazan Kuzularına","isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:00:30] local.INFO: Book created successfully from discovery service {"book_id":44,"isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:04:02] local.DEBUG: Local repository: JSON file not found {"isbn":"9786054928163","path":"book_repository/json/9786054928163.json"} 
[2025-10-30 11:04:02] local.INFO: Searching with provider: D&R {"isbn":"9786054928163"} 
[2025-10-30 11:04:04] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786054928163"} 
[2025-10-30 11:04:05] local.INFO: Searching with provider: Amazon TR {"isbn":"9786054928163"} 
[2025-10-30 11:04:05] local.INFO: Book found with provider: Amazon TR {"isbn":"9786054928163","name":"Ramazan Kuzularına"} 
[2025-10-30 11:04:05] local.INFO: Creating book from discovery data {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 11:04:06] local.INFO: Book created from discovery {"book_id":45,"name":"Ramazan Kuzularına","isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:04:06] local.INFO: Book created successfully from discovery service {"book_id":45,"isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:07:04] local.DEBUG: Local repository: JSON file not found {"isbn":"9786054928163","path":"book_repository/json/9786054928163.json"} 
[2025-10-30 11:07:04] local.INFO: Searching with provider: D&R {"isbn":"9786054928163"} 
[2025-10-30 11:07:06] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786054928163"} 
[2025-10-30 11:07:07] local.INFO: Searching with provider: Amazon TR {"isbn":"9786054928163"} 
[2025-10-30 11:07:07] local.INFO: Book found with provider: Amazon TR {"isbn":"9786054928163","name":"Ramazan Kuzularına"} 
[2025-10-30 11:07:07] local.INFO: Creating book from discovery data {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 11:07:07] local.INFO: Book created from discovery {"book_id":46,"name":"Ramazan Kuzularına","isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:07:07] local.INFO: Book created successfully from discovery service {"book_id":46,"isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:08:08] local.DEBUG: Local repository: JSON file not found {"isbn":"9786054928163","path":"book_repository/json/9786054928163.json"} 
[2025-10-30 11:08:08] local.INFO: Searching with provider: D&R {"isbn":"9786054928163"} 
[2025-10-30 11:08:09] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786054928163"} 
[2025-10-30 11:08:11] local.INFO: Searching with provider: Amazon TR {"isbn":"9786054928163"} 
[2025-10-30 11:08:11] local.INFO: Book found with provider: Amazon TR {"isbn":"9786054928163","name":"Ramazan Kuzularına"} 
[2025-10-30 11:08:11] local.INFO: Creating book from discovery data {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 11:08:11] local.INFO: Book created from discovery {"book_id":47,"name":"Ramazan Kuzularına","isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:08:11] local.INFO: Book created successfully from discovery service {"book_id":47,"isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:11:49] local.DEBUG: Local repository: JSON file not found {"isbn":"9786054928163","path":"book_repository/json/9786054928163.json"} 
[2025-10-30 11:11:49] local.INFO: Searching with provider: D&R {"isbn":"9786054928163"} 
[2025-10-30 11:11:51] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786054928163"} 
[2025-10-30 11:11:53] local.INFO: Searching with provider: Amazon TR {"isbn":"9786054928163"} 
[2025-10-30 11:11:53] local.INFO: Book found with provider: Amazon TR {"isbn":"9786054928163","name":"Ramazan Kuzularına"} 
[2025-10-30 11:11:53] local.INFO: Creating book from discovery data {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 11:11:53] local.INFO: Book created from discovery {"book_id":48,"name":"Ramazan Kuzularına","isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:11:53] local.INFO: Book created successfully from discovery service {"book_id":48,"isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:14:14] local.DEBUG: Local repository: JSON file not found {"isbn":"9786054928163","path":"book_repository/json/9786054928163.json"} 
[2025-10-30 11:14:14] local.INFO: Searching with provider: D&R {"isbn":"9786054928163"} 
[2025-10-30 11:14:15] local.INFO: Book data extracted from provider: D&R {"isbn":"9786054928163","data":null} 
[2025-10-30 11:14:16] local.INFO: Searching with provider: Kitabinabak {"isbn":"9786054928163"} 
[2025-10-30 11:14:16] local.INFO: Book data extracted from provider: Kitabinabak {"isbn":"9786054928163","data":null} 
[2025-10-30 11:14:17] local.INFO: Searching with provider: Amazon TR {"isbn":"9786054928163"} 
[2025-10-30 11:14:17] local.INFO: Book data extracted from provider: Amazon TR {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 11:14:17] local.INFO: Book found with provider: Amazon TR {"isbn":"9786054928163","name":"Ramazan Kuzularına"} 
[2025-10-30 11:14:17] local.INFO: Creating book from discovery data {"isbn":"9786054928163","data":{"name":"Ramazan Kuzularına","cover_image":"https://m.media-amazon.com/images/I/61ZvTyBA79L._SY445_SX342_ML2_.jpg","isbn":"9786054928163","author":["Muammer Erkul"],"publisher":"Divanyolu Kitap","year_of_publish":2023,"source":"Amazon TR"}} 
[2025-10-30 11:14:18] local.INFO: Book created from discovery {"book_id":49,"name":"Ramazan Kuzularına","isbn":"9786054928163","source":"Amazon TR"} 
[2025-10-30 11:14:18] local.INFO: Book created successfully from discovery service {"book_id":49,"isbn":"9786054928163","source":"Amazon TR"} 
