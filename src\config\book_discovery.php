<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Book Discovery Sources Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the external sources for book discovery.
    | Each source includes URL templates, parsing rules, and priority order.
    |
    */

    'sources' => [
        'dr' => [
            'name' => 'D&R',
            'priority' => 1,
            'enabled' => true,
            'search_url' => 'https://www.dr.com.tr/search?q={isbn}',
            'detail_url_pattern' => null, // Direct search results
            'timeout' => 15,
            'user_agents' => [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ],
            'not_found_indicators' => [
                'status_codes' => [404, 500],
                'text_patterns' => [
                    'ürün bulunamadı',
                    'Sonuç bulunamadı',
                    'No results found',
                    'Ürün bulunamadı'
                ],
                'empty_selectors' => [] //['.search-results', '.product-list']
            ],
            'parsing' => [
                'name' => [
                    'selectors' => [
                        '.product-title',
                        '.book-title',
                        'h1.title',
                        '.product-name',
                        // New D&R structure: look for "Kitap Adı:" label
                        '.product-property li:has(strong:contains("Kitap Adı")) span',
                        '.js-list-prd-property li:has(strong:contains("Kitap Adı")) span'
                    ],
                    'attribute' => 'text',
                    'required' => true,
                    'cleanup_regex' => '/^\s*(.+?)\s*$/',
                ],
                'author' => [
                    'selectors' => [
                        '.author-name',
                        '.product-author',
                        '.book-author',
                        '.author',
                        // New D&R structure: look for "Yazar:" label
                        '.product-property li:has(strong:contains("Yazar")) span',
                        '.js-list-prd-property li:has(strong:contains("Yazar")) span'
                    ],
                    'attribute' => 'text',
                    'multiple' => true,
                    'separator' => ',',
                    'cleanup_regex' => '/Yazar:\s*(.+)/',
                ],
                'publisher' => [
                    'selectors' => [
                        '.publisher',
                        '.product-publisher',
                        '.book-publisher',
                        // New D&R structure: look for "Yayınevi:" label
                        '.product-property li:has(strong:contains("Yayınevi")) span',
                        '.js-list-prd-property li:has(strong:contains("Yayınevi")) span'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/Yayınevi:\s*(.+)/',
                ],
                'year' => [
                    'selectors' => [
                        '.publish-year',
                        '.year',
                        '.publication-date',
                        // New D&R structure: look for "İlk Baskı Yılı:" label
                        '.product-property li:has(strong:contains("İlk Baskı Yılı")) span',
                        '.js-list-prd-property li:has(strong:contains("İlk Baskı Yılı")) span',
                        '.product-property li:has(strong:contains("Baskı Yılı")) span',
                        '.js-list-prd-property li:has(strong:contains("Baskı Yılı")) span'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d{4})/',
                ],
                'page_count' => [
                    'selectors' => [
                        '.page-count',
                        '.pages',
                        '.sayfa-sayisi',
                        // New D&R structure: look for "Sayfa Sayısı:" label
                        '.product-property li:has(strong:contains("Sayfa Sayısı")) span',
                        '.js-list-prd-property li:has(strong:contains("Sayfa Sayısı")) span'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d+)\s*sayfa/',
                ],
                'cover_image' => [
                    'selectors' => ['.product-image img', '.book-cover img', '.cover-image'],
                    'attribute' => 'src',
                    'fallback_attribute' => 'data-src',
                ],
                'category' => [
                    'selectors' => ['.category', '.book-category', '.genre'],
                    'attribute' => 'text',
                    'multiple' => true,
                    'separator' => ',',
                ],
                'isbn' => [
                    'selectors' => [
                        // New D&R structure: look for "Barkod:" label
                        '.product-property li:has(strong:contains("Barkod")) span',
                        '.js-list-prd-property li:has(strong:contains("Barkod")) span'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d+)/',
                ]
            ]
        ],

        'fidankitap' => [
            'name' => 'Fidan Kitap',
            'priority' => 2,
            'enabled' => false,
            'search_url' => 'https://www.fidankitap.com/search?p=Products&q={isbn}',
            'detail_url_pattern' => null, // Direct search results or redirect to book page
            'timeout' => 15,
            'user_agents' => [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ],
            'not_found_indicators' => [
                'status_codes' => [404, 500],
                'text_patterns' => [
                    'kayıt bulunamadı',
                    'sonuç bulunamadı',
                    'ürün bulunamadı',
                    'no results found',
                    'aradığınız ürün bulunamadı'
                ],
                'empty_selectors' => []
            ],
            'parsing' => [
                'name' => [
                    'selectors' => [
                        'h1.contentHeader.prdHeader',
                        '.prd_view_item h1.contentHeader',
                        '.product-title',
                        'h1.title'
                    ],
                    'attribute' => 'text',
                    'required' => true,
                    'cleanup_regex' => '/^\s*(.+?)\s*$/',
                ],
                'author' => [
                    'selectors' => [
                        '.writers .writer span',
                        '.prd_brand_box .writer span',
                        '.author-name',
                        '.product-author'
                    ],
                    'attribute' => 'text',
                    'multiple' => true,
                    'separator' => ',',
                ],
                'publisher' => [
                    'selectors' => [
                        '.publisher span',
                        '.prd_brand_box .publisher span',
                        '.product-publisher'
                    ],
                    'attribute' => 'text',
                ],
                'year' => [
                    'selectors' => [
                        '.prd_fields_item:has(.prd_fields_label:contains("Basım Tarihi")) .prd_fields_text',
                        '.publish-year',
                        '.publication-date'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d{4})/',
                ],
                'page_count' => [
                    'selectors' => [
                        '.prd_fields_item:has(.prd_fields_label:contains("Sayfa Sayısı")) .prd_fields_text',
                        '.page-count',
                        '.pages'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d+)/',
                ],
                'cover_image' => [
                    'selectors' => [
                        '.prd_view_item img.prd_image',
                        '.product-image img',
                        'img[alt*="kitap"]',
                        'img[alt*="book"]'
                    ],
                    'attribute' => 'src',
                    'fallback_attribute' => 'data-src',
                ],
                'category' => [
                    'selectors' => [
                        '.prd_fields_item:has(.prd_fields_label:contains("Kategori")) .prd_fields_text a',
                        '.category',
                        '.book-category'
                    ],
                    'attribute' => 'text',
                    'multiple' => true,
                    'separator' => ',',
                ],
                'isbn' => [
                    'selectors' => [
                        '.prd_fields_item:has(.prd_fields_label:contains("Stok Kodu")) .prd_fields_text',
                        '.isbn',
                        '.barcode'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/([0-9X]+)/',
                ]
            ]
        ],

        'amazontr' => [
            'name' => 'Amazon TR',
            'priority' => 3,
            'enabled' => true,
            'search_url' => 'https://www.amazon.com.tr/s?k={isbn}&i=stripbooks',
            'detail_url_pattern' => null, // Extract from search results
            'timeout' => 15,
            'user_agents' => [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ],
            'not_found_indicators' => [
                'status_codes' => [404, 500],
                'text_patterns' => [
                    'sonuç bulunamadı',
                    'Sonuç bulunamadı',
                    'kayıt bulunamadı',
                    'ürün bulunamadı',
                    'no results found'
                ],
                'empty_selectors' => [
                    'div[data-cy="title-recipe"]'
                ]
            ],
            'parsing' => [
                'name' => [
                    'selectors' => [
                        'span#productTitle',
                        '.product-title',
                        'h1.title'
                    ],
                    'attribute' => 'text',
                    'required' => true,
                    'cleanup_regex' => '/^\s*(.+?)\s*$/',
                ],
                'author' => [
                    'selectors' => [
                        'span.author a',
                        '.author-name',
                        '.book-author'
                    ],
                    'attribute' => 'text',
                    'multiple' => true,
                    'separator' => ',',
                ],
                'publisher' => [
                    'selectors' => [
                        'span.a-list-item span.a-text-bold:contains("Yayıncı") + span',
                        '.publisher-name',
                        '.book-publisher'
                    ],
                    'attribute' => 'text',
                ],
                'year_of_publish' => [
                    'selectors' => [
                        'span.a-list-item span.a-text-bold:contains("Yayınlanma Tarihi") + span',
                        '.publish-year',
                        '.publication-date'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d{4})/',
                ],
                'page_count' => [
                    'selectors' => [
                        'div#rpi-attribute-book_details-fiona_pages .rpi-attribute-value span',
                        '.page-count',
                        '.pages'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d+)\s*sayfa/i',
                ],
                'cover_image' => [
                    'selectors' => [
                        'img#landingImage',
                        '.book-cover img',
                        '.cover-image'
                    ],
                    'attribute' => 'src',
                    'fallback_attribute' => 'data-src',
                ],
                'isbn' => [
                    'selectors' => [
                        'span.a-list-item span.a-text-bold:contains("ISBN-13") + span',
                        '.isbn',
                        '.barcode'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/([0-9X-]+)/',
                ]
            ]
        ],
        'kitabinabak' => [
            'name' => 'Kitabinabak',
            'priority' => 4,
            'enabled' => true,
            'search_url' => 'https://www.kitabinabak.com/sorgu/Kitap?bak={isbn}',
            'detail_url_pattern' => null, // Extract from search results
            'timeout' => 15,
            'user_agents' => [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ],
            'not_found_indicators' => [
                'status_codes' => [404, 500],
                'text_patterns' => [
                    'Sonuç bulunamadı',
                    'sonuç bulunamadı',
                    'kayıt bulunamadı',
                    'ürün bulunamadı',
                    'no results found'
                ],
                'empty_selectors' => [
                    '.single-product'
                ]
            ],
            'parsing' => [
                'name' => [
                    'selectors' => [
                        'h1.bookDescTitle',
                        '.book-title',
                        'h1.title'
                    ],
                    'attribute' => 'text',
                    'required' => true,
                    'cleanup_regex' => '/^\s*(.+?)\s*$/',
                ],
                'author' => [
                    'selectors' => [
                        '.col-lg-3:has(b:contains("Yazar")) + .col-lg-9 a',
                        '.author-name',
                        '.book-author'
                    ],
                    'attribute' => 'text',
                    'multiple' => true,
                    'separator' => ',',
                ],
                'publisher' => [
                    'selectors' => [
                        'div[itemprop="publisher"] div[itemprop="name"]',
                        '.publisher-name',
                        '.book-publisher'
                    ],
                    'attribute' => 'text',
                ],
                'year' => [
                    'selectors' => [
                        '.col-lg-3:has(b:contains("Yayın")) + .col-lg-9',
                        '.publish-year',
                        '.publication-date'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d{4})/',
                ],
                'page_count' => [
                    'selectors' => [
                        'div[itemprop="numberOfPages"]',
                        '.page-count',
                        '.pages'
                    ],
                    'attribute' => 'text',
                    'cleanup_regex' => '/(\d+)/',
                ],
                'cover_image' => [
                    'selectors' => [
                        'img.large-img',
                        '.book-cover img',
                        '.cover-image'
                    ],
                    'attribute' => 'src',
                    'fallback_attribute' => 'data-src',
                ],
                'isbn' => [
                    'selectors' => [
                        'meta[itemprop="isbn"]',
                        '.isbn',
                        '.barcode'
                    ],
                    'attribute' => 'content',
                    'fallback_attribute' => 'text',
                    'cleanup_regex' => '/([0-9X]+)/',
                ]
            ]
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | General Settings
    |--------------------------------------------------------------------------
    */

    'settings' => [
        'max_retries' => 3,
        'retry_delay' => 2, // seconds
        'cache_duration' => 3600, // 1 hour in seconds
        'image_download_timeout' => 30,
        'max_image_size' => 5 * 1024 * 1024, // 5MB
        'allowed_image_types' => ['jpg', 'jpeg', 'png', 'webp'],
        'image_storage_path' => 'books/covers',
        'fallback_to_google_books' => true,
        'log_failed_attempts' => true,
        'rate_limit_delay' => 1, // seconds between requests to same domain
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Validation Rules
    |--------------------------------------------------------------------------
    */

    'validation' => [
        'name' => [
            'min_length' => 2,
            'max_length' => 500,
            'required' => true,
        ],
        'author' => [
            'min_length' => 2,
            'max_length' => 200,
        ],
        'publisher' => [
            'min_length' => 2,
            'max_length' => 200,
        ],
        'year' => [
            'min_value' => 1000,
            'max_value' => null, // Will use current year + 1
        ],
        'page_count' => [
            'min_value' => 1,
            'max_value' => 10000,
        ],
    ],
];
