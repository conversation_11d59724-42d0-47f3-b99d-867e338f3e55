<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Author;
use App\Models\User;

class AuthorPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // All users can view authors for selection purposes
        return true;
    }

    public function view(User $user, Author $item): bool
    {
        // All users can view individual authors
        return true;
    }

    public function create(User $user): bool
    {
        // Only system admin can create authors (automatic creation via backend service)
        return $user->isSystemAdmin();
    }

    public function update(User $user, Author $item): bool
    {
        // Only system admin can update authors
        return $user->isSystemAdmin();
    }

    public function delete(User $user, Author $item): bool
    {
        // Only system admin can delete authors
        return $user->isSystemAdmin();
    }

    public function restore(User $user, Author $item): bool
    {
        // Only system admin can restore authors
        return $user->isSystemAdmin();
    }

    public function forceDelete(User $user, Author $item): bool
    {
        // Only system admin can force delete authors
        return $user->isSystemAdmin();
    }

    public function massDelete(User $user): bool
    {
        // Only system admin can mass delete authors
        return $user->isSystemAdmin();
    }
}
