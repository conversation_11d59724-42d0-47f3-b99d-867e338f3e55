Role
 - Create/Read/Update/Delete - system_admin only
Permission
 - Create/Read/Update/Delete - system_admin only
EnumSchoolType
 - Create/Read/Update/Delete - system_admin only
EnumClassLevel
 - Create/Read/Update/Delete - system_admin only
School
 - Create/Read/Update/Delete - system_admin only
 - Other roles can list and select their assigned schools in UserSchool table
UserAgreement
 - Management - system_admin only
 - Users can accept agreement and a record created
SchoolClass
 - Create/Read/Update/Delete - system_admin, school_admin (only for their assigned schools)
User
 - Create/Read/Update/Delete - system_admin, school_admin (with teacher and student roles only for their assigned schools), teacher (with student roles only for their assigned classes)
 - All users can list users with their scopes.
UserSchool
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for roles at their assigned schools and classes) 
UserClass
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for roles at their assigned schools and classes) 
Team
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes)  
UserTeam
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes)   
BookType
 - Create/Read/Update/Delete - system_admin only
 - All users can list and select book types when adding a book
Author
 - Create/Read/Update/Delete - system_admin
 - Create - backend find book service (automatically triggered by frontend find book by isbn)
Publisher
 - Create/Read/Update/Delete - system_admin
 - Create - backend find book service (automatically triggered by frontend find book by isbn)
Category
 - Create/Read/Update/Delete - system_admin only
Book
 - Create/Read/Update/Delete - system_admin
 - Create - backend find book service (automatically triggered by frontend find book by isbn)
ClassBook
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for their assigned schools and classes) 
BookQuestion
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for their own records) 
BookWord
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for their own records) 
UserBook
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes ), student (only for themselves) 
UserReadingLog
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes ), student (only for themselves) 
UserPoint
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes ), student (only for themselves) 
ActivityCategory
 - Create/Read/Update/Delete - system_admin only
 - All users can list and see for activities
Activity
 - Create/Read/Update/Delete - system_admin only
 - All users can list and see for activity recording
UserActivity
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for their assigned schools and classes)
 - student can create and update for themselves
UserActivityReview
 - Create/Read/Update/Delete - manually system_admin, automatically triggered by user activity entry
Avatar
 - Create/Read/Update/Delete - system_admin only
UserAvatar
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes ), student (only for themselves) 
Reward
 - Create/Read/Update/Delete - system_admin only
RewardTask
 - Create/Read/Update/Delete - system_admin only
UserReward
 - Create/Read/Update/Delete - manually system_admin, school_admin and teacher (only for students at their assigned schools and classes), automatically triggered by user reading actions
TeamReward
 - Create/Read/Update/Delete - manually system_admin, school_admin and teacher (only for teams with students at their assigned schools and classes), automatically triggered by user reading actions
EnumTaskType
 - Create/Read/Update/Delete - system_admin only
EnumTaskCycle
 - Create/Read/Update/Delete - system_admin only
Task
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for their own created challenge and goals )
Goal
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes )
GoalTask
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes )
UserGoal
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes )
UserGoalTask
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes )
 - students can update their assigned tasks
Challenge
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes )
ChallengeTask
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes )
UserChallengeTask
 - Create/Read/Update/Delete - system_admin, school_admin and teacher (only for students at their assigned schools and classes )
 - students can update their assigned tasks
