<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;
use MoonShine\UI\Components\ActionButton;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use App\Models\{EnumTaskCycle, EnumTaskType, Reward, SchoolClass, Task, Team, User, UserTask};
use Illuminate\Database\Eloquent\Relations\Relation;
use MoonShine\Laravel\Fields\Relationships\{BelongsTo, HasMany};
use MoonShine\Support\{Attributes\Icon, Enums\ToastType, ListOf};
use MoonShine\UI\Fields\{Date, Hidden, Select, Switcher, Text};

#[Icon('clipboard-document-list')]
class PanelTaskResource extends TaskResource
{
    use WithRolePermissions;

    const ASSIGN_TO_CLASS = 'class';
    const ASSIGN_TO_TEAM = 'team';
    const ASSIGN_TO_STUDENTS = 'students';

    protected function indexFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name')
                ->sortable(),

            BelongsTo::make(
                __('admin.task_cycle'),
                'taskCycle',
                formatted: fn(EnumTaskCycle $taskCycle) => $taskCycle->name,
                resource: EnumTaskCycleResource::class
            )
                ->link(link: fn() => '#')
                ->sortable(),

            Text::make(__('admin.task_value'), 'task_value')
                ->sortable(),

            BelongsTo::make(
                __('admin.task_type'),
                'taskType',
                formatted: fn(EnumTaskType $taskType) => $taskType->name,
                resource: EnumTaskTypeResource::class
            )
                ->link(link: fn() => '#')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }


    protected function detailFields(): iterable
    {
        return [
            ...parent::detailFields(),
            // add hasmany for user standalone tasks for this task
            HasMany::make(__('admin.individual_assignments'), 'userTasks', resource: PanelUserTaskResource::class)
                ->tabMode()
                ->modifyBuilder(fn(Relation $query, HasMany $ctx) => $query->whereNull('team_id')->whereNull('class_id')),

            HasMany::make(__('admin.team_assignments'), 'userTasks', resource: PanelUserTaskResource::class)
                ->tabMode()
                ->modifyBuilder(fn(Relation $query, HasMany $ctx) => $query->whereNotNull('team_id')),

            HasMany::make(__('admin.class_task_assignments'), 'userTasks', resource: PanelUserTaskResource::class)
                ->tabMode()
                ->modifyBuilder(fn(Relation $query, HasMany $ctx) => $query->whereNotNull('class_id')),
        ];
    }

    protected function detailButtons(): ListOf
    {
        $buttons = parent::detailButtons();

        $sharedFields = [
            Date::make(__('admin.due_date'), 'due_date')
                ->format('d.m.Y H:i')
                ->required(),
            Select::make(__('admin.reward'), 'reward_id')
                ->options(Reward::active()
                    ->where('created_by', auth('moonshine')->id())
                    ->whereDoesntHave('tasks')
                    ->pluck('name', 'id')->toArray())
                ->nullable(),
        ];

        if ($this->item->active) {
            $buttons->prepend(
                ActionButton::make(__('admin.assign_to_class'))
                    ->method('assignTask')
                    ->success()
                    ->withConfirm(
                        __('admin.assign_to_class'),
                        fields: [
                            Hidden::make('assignment_type')->setValue(self::ASSIGN_TO_CLASS),
                            ...$sharedFields,
                        ],
                        name: 'class-modal',
                    )
                    ->icon('user-group'),

                ActionButton::make(__('admin.assign_to_team'))
                    ->method('assignTask')
                    ->withConfirm(
                        __('admin.assign_to_team'),
                        fields: [
                            Hidden::make('assignment_type')->setValue(self::ASSIGN_TO_TEAM),
                            Select::make(__('admin.team'), 'team_id')
                                ->options(Team::createdBy(auth('moonshine')->id())->pluck('name', 'id')->toArray())
                                ->required(),
                            ...$sharedFields,
                        ],
                        name: 'team-modal',
                    )
                    ->success()
                    ->icon('users'),

                ActionButton::make(__('admin.assign_to_students'))
                    ->method('assignTask')
                    ->withConfirm(
                        __('admin.assign_to_students'),
                        fields: [
                            Hidden::make('assignment_type')->setValue(self::ASSIGN_TO_STUDENTS),
                            Select::make(__('admin.students'), 'student_ids')
                                ->options(User::forCurrentUser()->role('student')->pluck('name', 'id')->toArray())
                                ->required()
                                ->multiple(),
                            ...$sharedFields,
                        ],
                        name: 'students-modal',
                    )
                    ->success()
                    ->icon('user'),
            );
        }
        return $buttons;
    }

    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        // only show tasks created by current user
        return $builder->where('created_by', auth('moonshine')->user()->id);
    }

    public function assignTask(): MoonShineJsonResponse
    {
        $assignmentType = request('assignment_type');
        $taskId = request('resourceItem');
        $dueDate = request('due_date');
        $teamId = request('team_id');
        $rewardId = request('reward_id');
        $classId = null;
        $studentIds = request('student_ids', []);

        if (!$taskId || !$dueDate) {
            return MoonShineJsonResponse::make()->toast(__('admin.invalid_request'), ToastType::ERROR);
        }
        if ($dueDate < now()) {
            return MoonShineJsonResponse::make()->toast(__('admin.due_date_in_past'), ToastType::ERROR);
        }

        $task = Task::find($taskId);
        if (!$task || !$task->active) {
            return MoonShineJsonResponse::make()->toast(__('admin.task_not_found'), ToastType::ERROR);
        }

        if ($rewardId) {
            $reward = Reward::find($rewardId);
            if (!$reward) {
                return MoonShineJsonResponse::make()->toast(__('admin.reward_not_found'), ToastType::ERROR);
            }
            if (!$reward->active) {
                return MoonShineJsonResponse::make()->toast(__('admin.reward_not_active'), ToastType::ERROR);
            }
            if ($reward->created_by != auth('moonshine')->id()) {
                return MoonShineJsonResponse::make()->toast(__('admin.unauthorized_action'), ToastType::ERROR);
            }
            if ($reward-> tasks()->count() > 0) {
                return MoonShineJsonResponse::make()->toast(__('admin.reward_cannot_be_awarded'), ToastType::ERROR);
            }
        }
        // get user's default class
        $user = auth('moonshine')->user();
        $defaultClass = $user->getDefaultClass();
        if (!$defaultClass) {
            return MoonShineJsonResponse::make()->toast(__('admin.default_class_not_found'), ToastType::ERROR);
        }

        switch ($assignmentType) {
            case self::ASSIGN_TO_CLASS:
                $classId = $defaultClass->class_id;
                $studentIds = $defaultClass->schoolClass->users()->role('student')->pluck('users.id')->toArray();
                break;

            case self::ASSIGN_TO_TEAM:
                $team = Team::find($teamId);
                if (!$team) {
                    return MoonShineJsonResponse::make()->toast(__('admin.team_not_found'), ToastType::ERROR);
                }
                if ($team->created_by != auth('moonshine')->id()) {
                    return MoonShineJsonResponse::make()->toast(__('admin.unauthorized_action'), ToastType::ERROR);
                }
                if ($team->users()->role('student')->count() == 0) {
                    return MoonShineJsonResponse::make()->toast(__('admin.no_students_in_team'), ToastType::WARNING);
                }
                $studentIds = $team->users()->get()->pluck('id')->toArray();
                break;

            case self::ASSIGN_TO_STUDENTS:
                if (empty($studentIds)) {
                    return MoonShineJsonResponse::make()->toast(__('admin.no_students_selected'), ToastType::WARNING);
                }
                // check if all students are in user's default class
                $studentsInClass = $defaultClass->schoolClass->users()->role('student')->pluck('users.id')->toArray();
                $studentIds = array_intersect($studentIds, $studentsInClass);
                if (empty($studentIds)) {
                    return MoonShineJsonResponse::make()->toast(__('admin.no_students_in_class'), ToastType::WARNING);
                }
                break;

            default:
                return MoonShineJsonResponse::make()->toast(__('admin.invalid_request'), ToastType::ERROR);
                break;
        }

        // Assign task to selected students
        $assignedCount = 0;
        foreach ($studentIds as $studentId) {
            // Check if task is already assigned to this student
            $existingTask = UserTask::where('user_id', $studentId)
                ->where('task_id', $taskId)
                ->where('task_type', UserTask::TASK_TYPE_STANDALONE)
                ->where('due_date', $dueDate)
                ->first();

            if (!$existingTask) {
                UserTask::create([
                    'task_type' => UserTask::TASK_TYPE_STANDALONE,
                    'task_id' => $taskId,
                    'class_id' => $classId,
                    'team_id' => $teamId,
                    'user_id' => $studentId,
                    'assigned_by' => auth('moonshine')->id(),
                    'assign_date' => now(),
                    'due_date' => $dueDate,
                    'reward_id' => $rewardId,
                ]);
                $assignedCount++;
            }
        }

        if ($assignmentType == self::ASSIGN_TO_CLASS) {
            $message = __('admin.task_assigned_to_class_students', ['count' => $assignedCount]);
        } elseif ($assignmentType == self::ASSIGN_TO_TEAM) {
            $message = __('admin.task_assigned_to_team_students', ['count' => $assignedCount]);
        } else {
            $message = __('admin.task_assigned_to_students', ['count' => $assignedCount]);
        }

        return MoonShineJsonResponse::make()
            ->toast($message, ToastType::SUCCESS)
            ->redirect($this->getDetailPageUrl($taskId));
    }
}
