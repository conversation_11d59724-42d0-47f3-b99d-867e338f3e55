<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add description field to tasks table (only if it doesn't exist)
        if (!Schema::hasColumn('tasks', 'description')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->text('description')->nullable()->after('name');
            });
        }

        // Add new fields to user_tasks table (only if they don't exist)
        Schema::table('user_tasks', function (Blueprint $table) {
            if (!Schema::hasColumn('user_tasks', 'due_date')) {
                $table->timestamp('due_date')->nullable()->after('complete_date');
                $table->index('due_date');
                $table->index(['user_id', 'due_date']);
            }

            if (!Schema::hasColumn('user_tasks', 'class_id')) {
                $table->foreignId('class_id')->nullable()->constrained('school_classes')->onDelete('set null')->after('team_id');
                $table->index('class_id');
                $table->index(['class_id', 'due_date']);
            }

            if (!Schema::hasColumn('user_tasks', 'reward_id')) {
                $table->foreignId('reward_id')->nullable()->constrained('rewards')->onDelete('set null')->after('class_id');
                $table->index('reward_id');
            }
        });

        // Add repeatable field to rewards table (only if it doesn't exist)
        if (!Schema::hasColumn('rewards', 'repeatable')) {
            Schema::table('rewards', function (Blueprint $table) {
                $table->boolean('repeatable')->default(false)->after('active');

                // Add index for repeatable field
                $table->index('repeatable');
                $table->index(['active', 'repeatable']);
            });
        }

        // Drop challenge_task_id

        Schema::table('user_activities', function (Blueprint $table) {
            $table->dropForeign(['challenge_task_id']);
            $table->dropIndex(['challenge_task_id', 'user_id']);
            $table->dropIndex(['challenge_task_id', 'status']);


            $table->dropColumn('challenge_task_id');
        });

        Schema::table('user_books', function (Blueprint $table) {
            $table->dropForeign(['challenge_task_id']);
            $table->dropIndex(['challenge_task_id', 'user_id']);


            $table->dropColumn('challenge_task_id');
        });

        Schema::table('user_reading_logs', function (Blueprint $table) {
            $table->dropForeign(['challenge_task_id']);
            $table->dropIndex(['challenge_task_id', 'user_id']);
            $table->dropIndex(['challenge_task_id', 'log_date']);


            $table->dropColumn('challenge_task_id');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: challenge_task_id restoration is handled separately

        // Remove repeatable field from rewards table
        Schema::table('rewards', function (Blueprint $table) {
            $table->dropIndex(['active', 'repeatable']);
            $table->dropIndex('repeatable');
            $table->dropColumn('repeatable');
        });

        // Remove new fields from user_tasks table
        Schema::table('user_tasks', function (Blueprint $table) {
            $table->dropIndex(['class_id', 'due_date']);
            $table->dropIndex(['user_id', 'due_date']);
            $table->dropIndex('reward_id');
            $table->dropIndex('class_id');
            $table->dropIndex('due_date');
            
            $table->dropForeign(['reward_id']);
            $table->dropForeign(['class_id']);
            
            $table->dropColumn(['due_date', 'class_id', 'reward_id']);
        });

        // Remove description field from tasks table (only if we added it)
        if (Schema::hasColumn('tasks', 'description')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropColumn('description');
            });
        }
    }
};
