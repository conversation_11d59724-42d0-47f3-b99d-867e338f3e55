<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Services\StatisticsService;

class TeacherLast24Hours extends Component
{
    public $user;
    public $recentActivities = [];
    
    public function mount()
    {
        $this->user = Auth::user();
        
        // Ensure user is a teacher
        if (!$this->user->isTeacher()) {
            return redirect()->route('mobile.home');
        }
        
        $this->recentActivities = StatisticsService::getRecentBookActivities($this->user, 50);
    }
    

    
    public function render()
    {
        return view('livewire.mobile.teacher-last-24-hours');
    }
}
