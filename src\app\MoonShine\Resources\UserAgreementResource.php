<?php

namespace App\MoonShine\Resources;

use App\Models\UserAgreement;
use App\Models\User;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Text;
use MoonShine\Support\Attributes\Icon;

#[Icon('check-circle')]
class UserAgreementResource extends BaseResource
{
    protected string $model = UserAgreement::class;

    protected string $title = 'User Agreements';

    protected array $with = ['user'];

    public function getTitle(): string
    {
        return __('admin.user_agreements');
    }

    protected function indexFields(): iterable
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name . ' (' . $user->email . ')',
                resource: UserResource::class
            )
                ->sortable(),

            Select::make(__('admin.agreement_type'), 'agreement_type')
                ->options(UserAgreement::getAgreementTypes())
                ->sortable(),

            Text::make(__('admin.version'), 'version')
                ->sortable(),

            Date::make(__('admin.accepted_at'), 'accepted_at')
                ->format('d.m.Y H:i')
                ->sortable(),

            Text::make(__('admin.ip_address'), 'ip_address'),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        Flex::make([
                            BelongsTo::make(
                                __('admin.user'),
                                'user',
                                formatted: fn(User $user) => $user->name . ' (' . $user->email . ')',
                                resource: UserResource::class
                            ),

                            Select::make(__('admin.agreement_type'), 'agreement_type')
                                ->options(UserAgreement::getAgreementTypes()),
                        ]),

                        Flex::make([
                            Text::make(__('admin.version'), 'version'),

                            Date::make(__('admin.accepted_at'), 'accepted_at')
                                ->format('d.m.Y H:i:s'),
                        ]),

                        Text::make(__('admin.ip_address'), 'ip_address'),
                    ]),
                ])
            ])
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(
                            __('admin.user'),
                            'user',
                            formatted: fn(User $user) => $user->name . ' (' . $user->email . ')',
                            resource: UserResource::class
                        )
                            ->required()
                            ->placeholder(__('admin.select_user'))
                            ->asyncSearch('name'),

                        Select::make(__('admin.agreement_type'), 'agreement_type')
                            ->options(UserAgreement::getAgreementTypes())
                            ->required()
                            ->default(UserAgreement::TYPE_PRIVACY_POLICY),

                        Flex::make([
                            Text::make(__('admin.version'), 'version')
                                ->required()
                                ->default(UserAgreement::CURRENT_PRIVACY_VERSION)
                                ->placeholder(__('admin.enter_version')),

                            Date::make(__('admin.accepted_at'), 'accepted_at')
                                ->required()
                                ->default(now())
                                ->format('Y-m-d H:i:s'),
                        ]),

                        Text::make(__('admin.ip_address'), 'ip_address')
                            ->placeholder(__('admin.enter_ip_address')),
                    ]),
                ])
            ])
        ];
    }

    protected function filters(): iterable
    {
        return [
            Select::make(__('admin.agreement_type'), 'agreement_type')
                ->options(UserAgreement::getAgreementTypes())
                ->nullable(),

            Text::make(__('admin.version'), 'version'),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->asyncSearch('name'),
        ];
    }

    protected function search(): array
    {
        return [
            'user.name',
            'user.email',
            'agreement_type',
            'version',
            'ip_address',
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'agreement_type' => ['required', 'string', 'max:255'],
            'version' => ['required', 'string', 'max:255'],
            'accepted_at' => ['required', 'date'],
            'ip_address' => ['nullable', 'ip'],
        ];
    }

    public function getActiveActions(): array
    {
        return ['view', 'delete'];
    }
}
