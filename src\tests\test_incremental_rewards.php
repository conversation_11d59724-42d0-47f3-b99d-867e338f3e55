<?php

/**
 * Test script for Incremental Reading Rewards
 * 
 * This script tests that rewards are properly calculated and awarded for
 * incremental reading activities like pages read, minutes spent, reading streaks, etc.
 * 
 * Run from the src directory: php test_incremental_rewards.php
 */

require __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Book;
use App\Models\UserReadingLog;
use App\Models\Reward;
use App\Models\Task;
use App\Models\RewardTask;
use App\Models\UserReward;
use App\Models\EnumTaskType;
use App\Models\EnumTaskCycle;
use App\Services\RewardCalculationService;
use Carbon\Carbon;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "\n=== Incremental Reading Rewards Test ===\n\n";

// Clean up any existing test data
echo "1. Cleaning up existing test data...\n";
DB::table('user_rewards')->where('user_id', '>', 0)->delete();
DB::table('reward_tasks')->where('id', '>', 0)->delete();
DB::table('rewards')->where('name', 'LIKE', 'Test%')->delete();
DB::table('tasks')->where('name', 'LIKE', 'Test%')->delete();
DB::table('user_reading_logs')->where('user_id', '>', 0)->delete();

// Get or create test user
$testUser = User::where('email', '<EMAIL>')->first();
if (!$testUser) {
    echo "   Creating test user...\n";
    $testUser = User::create([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role' => 'student',
    ]);
}

// Note: We'll use saveQuietly() to bypass authentication issues in model events

// Get or create test book
$testBook = Book::where('name', 'Test Book for Rewards')->first();
if (!$testBook) {
    echo "   Creating test book...\n";
    $testBook = Book::create([
        'name' => 'Test Book for Rewards',
        'isbn' => '9999999999999',
        'page_count' => 100,
        'active' => true,
    ]);
}

echo "   ✓ Test data prepared\n\n";

// Test Case 1: Daily Pages Read Reward
echo "2. Testing Daily Pages Read Reward...\n";

// Create task for daily pages read
$dailyPagesTask = Task::create([
    'name' => 'Test Daily Pages',
    'task_type_id' => EnumTaskType::READ_PAGES,
    'task_cycle_id' => EnumTaskCycle::DAILY,
    'task_value' => 20, // 20 pages per day
    'active' => true,
]);

// Create reward for daily pages (bypass model events)
$dailyPagesReward = new Reward([
    'reward_type' => 1, // Badge
    'name' => 'Test Daily Reader',
    'description' => 'Read 20 pages in a day',
    'active' => true,
    'repeatable' => true,
    'created_by' => null, // System reward
]);
$dailyPagesReward->saveQuietly(); // Skip model events

// Link task to reward
RewardTask::create([
    'reward_id' => $dailyPagesReward->id,
    'task_id' => $dailyPagesTask->id,
]);

echo "   Created daily pages reward (20 pages/day)\n";

// Create reading log with 25 pages (should trigger reward)
$readingLog1 = UserReadingLog::create([
    'user_id' => $testUser->id,
    'book_id' => $testBook->id,
    'log_date' => now(),
    'pages_read' => 25,
    'reading_duration' => 30,
    'book_completed' => false,
]);

echo "   Created reading log: 25 pages, 30 minutes\n";

// Debug: Check if required activities are blocking rewards
$requiredActivitiesCompleted = $readingLog1->allRequiredActivitiesCompleted();
echo "   Required activities completed: " . ($requiredActivitiesCompleted ? 'YES' : 'NO') . "\n";

// Check if reward was awarded
$userReward1 = UserReward::where('user_id', $testUser->id)
    ->where('reward_id', $dailyPagesReward->id)
    ->first();

if ($userReward1) {
    echo "   ✓ Daily pages reward awarded successfully!\n";
} else {
    echo "   ✗ Daily pages reward NOT awarded (this is the bug we're fixing)\n";
}

// Test Case 2: Weekly Minutes Read Reward
echo "\n3. Testing Weekly Minutes Read Reward...\n";

// Create task for weekly minutes read
$weeklyMinutesTask = Task::create([
    'name' => 'Test Weekly Minutes',
    'task_type_id' => EnumTaskType::READ_MINUTES,
    'task_cycle_id' => EnumTaskCycle::WEEKLY,
    'task_value' => 100, // 100 minutes per week
    'active' => true,
]);

// Create reward for weekly minutes (bypass model events)
$weeklyMinutesReward = new Reward([
    'reward_type' => 1, // Badge
    'name' => 'Test Weekly Reader',
    'description' => 'Read for 100 minutes in a week',
    'active' => true,
    'repeatable' => true,
    'created_by' => null, // System reward
]);
$weeklyMinutesReward->saveQuietly(); // Skip model events

// Link task to reward
RewardTask::create([
    'reward_id' => $weeklyMinutesReward->id,
    'task_id' => $weeklyMinutesTask->id,
]);

echo "   Created weekly minutes reward (100 minutes/week)\n";

// Create additional reading logs to reach 100+ minutes this week
$readingLog2 = UserReadingLog::create([
    'user_id' => $testUser->id,
    'book_id' => $testBook->id,
    'log_date' => now()->subDay(),
    'pages_read' => 15,
    'reading_duration' => 40,
    'book_completed' => false,
]);

$readingLog3 = UserReadingLog::create([
    'user_id' => $testUser->id,
    'book_id' => $testBook->id,
    'log_date' => now()->subDays(2),
    'pages_read' => 10,
    'reading_duration' => 35,
    'book_completed' => false,
]);

echo "   Created additional reading logs: 40 + 35 = 75 minutes\n";
echo "   Total minutes this week: 30 + 40 + 35 = 105 minutes\n";

// Check if weekly minutes reward was awarded
$userReward2 = UserReward::where('user_id', $testUser->id)
    ->where('reward_id' , $weeklyMinutesReward->id)
    ->first();

if ($userReward2) {
    echo "   ✓ Weekly minutes reward awarded successfully!\n";
} else {
    echo "   ✗ Weekly minutes reward NOT awarded\n";
}

// Test Case 3: Reading Streak Reward
echo "\n4. Testing Reading Streak Reward...\n";

// Create task for reading streak
$streakTask = Task::create([
    'name' => 'Test Reading Streak',
    'task_type_id' => EnumTaskType::READ_STREAK,
    'task_cycle_id' => EnumTaskCycle::TOTAL,
    'task_value' => 3, // 3 consecutive days
    'active' => true,
]);

// Create reward for reading streak (bypass model events)
$streakReward = new Reward([
    'reward_type' => 2, // Trophy
    'name' => 'Test Streak Master',
    'description' => 'Read for 3 consecutive days',
    'active' => true,
    'repeatable' => false,
    'created_by' => null, // System reward
]);
$streakReward->saveQuietly(); // Skip model events

// Link task to reward
RewardTask::create([
    'reward_id' => $streakReward->id,
    'task_id' => $streakTask->id,
]);

echo "   Created reading streak reward (3 consecutive days)\n";

// We already have logs for today, yesterday, and 2 days ago - should trigger streak
$userReward3 = UserReward::where('user_id', $testUser->id)
    ->where('reward_id', $streakReward->id)
    ->first();

if ($userReward3) {
    echo "   ✓ Reading streak reward awarded successfully!\n";
} else {
    echo "   ✗ Reading streak reward NOT awarded\n";
}

// Test Case 4: Manual Reward Service Test
echo "\n5. Testing RewardCalculationService directly...\n";

$rewardService = new RewardCalculationService();

// Test direct service call
$awardedRewards = $rewardService->checkAndAwardUserRewards($testUser->id, $readingLog1->id);

echo "   Direct service call returned " . count($awardedRewards) . " rewards\n";

// Debug: Check if rewards are eligible using the service's method
$reflection = new ReflectionClass($rewardService);
$method = $reflection->getMethod('getEligibleRewardsForUser');
$method->setAccessible(true);
$eligibleRewardsFromService = $method->invoke($rewardService, $testUser->id);

echo "   Eligible rewards from service: " . $eligibleRewardsFromService->count() . "\n";
foreach ($eligibleRewardsFromService as $reward) {
    echo "   - {$reward->name} (ID: {$reward->id})\n";
}

// Debug: Check if rewards are eligible
$eligibleRewards = DB::table('rewards')
    ->join('reward_tasks', 'rewards.id', '=', 'reward_tasks.reward_id')
    ->join('tasks', 'reward_tasks.task_id', '=', 'tasks.id')
    ->where('rewards.active', true)
    ->where('tasks.active', true)
    ->select('rewards.name', 'tasks.name as task_name', 'tasks.task_type_id', 'tasks.task_cycle_id', 'tasks.task_value')
    ->get();

echo "   All rewards in system:\n";
foreach ($eligibleRewards as $reward) {
    echo "   - {$reward->name}: {$reward->task_name} (Type: {$reward->task_type_id}, Cycle: {$reward->task_cycle_id}, Value: {$reward->task_value})\n";
}

// Debug: Test individual task progress calculation
// Debug: Show reading log dates
echo "\n   Reading logs created:\n";
$allLogs = UserReadingLog::where('user_id', $testUser->id)->get();
foreach ($allLogs as $log) {
    echo "   - {$log->log_date->format('Y-m-d H:i:s')}: {$log->pages_read} pages, {$log->reading_duration} minutes\n";
}

echo "\n   Testing individual task progress:\n";
foreach ($eligibleRewardsFromService as $reward) {
    foreach ($reward->rewardTasks as $rewardTask) {
        $task = $rewardTask->task;

        // Test the calculateRewardTaskProgress method
        $progressMethod = $reflection->getMethod('calculateRewardTaskProgress');
        $progressMethod->setAccessible(true);
        $progress = $progressMethod->invoke($rewardService, $task, $testUser->id);

        echo "   - Task '{$task->name}' (Type: {$task->task_type_id}, Cycle: {$task->task_cycle_id}, Value: {$task->task_value}): Progress = {$progress}\n";

        // Debug date range for this task
        $dateRangeMethod = $reflection->getMethod('getDateRangeForCycle');
        $dateRangeMethod->setAccessible(true);
        $dateRange = $dateRangeMethod->invoke($rewardService, $task->task_cycle_id);

        if ($dateRange) {
            echo "     Date range: {$dateRange['start']->format('Y-m-d H:i:s')} to {$dateRange['end']->format('Y-m-d H:i:s')}\n";
        } else {
            echo "     Date range: No filtering (TOTAL)\n";
        }

        // Check if task is completed
        $completedMethod = $reflection->getMethod('isRewardTaskCompletedForUser');
        $completedMethod->setAccessible(true);
        $completed = $completedMethod->invoke($rewardService, $rewardTask, $testUser->id);

        echo "     Completed: " . ($completed ? 'YES' : 'NO') . "\n";
    }
}

if (!empty($awardedRewards)) {
    foreach ($awardedRewards as $reward) {
        echo "   - Awarded: " . $reward->reward->name . "\n";
    }
}

// Summary
echo "\n6. Test Summary:\n";

$totalUserRewards = UserReward::where('user_id', $testUser->id)->count();
echo "   Total rewards awarded to test user: {$totalUserRewards}\n";

$expectedRewards = [
    'Test Daily Reader' => 'Daily pages read (20+ pages)',
    'Test Weekly Reader' => 'Weekly minutes read (100+ minutes)',
    'Test Streak Master' => 'Reading streak (3+ consecutive days)',
];

foreach ($expectedRewards as $rewardName => $description) {
    $awarded = UserReward::whereHas('reward', function($q) use ($rewardName) {
        $q->where('name', $rewardName);
    })->where('user_id', $testUser->id)->exists();
    
    $status = $awarded ? '✓' : '✗';
    echo "   {$status} {$rewardName}: {$description}\n";
}

// Cleanup
echo "\n7. Cleaning up test data...\n";
UserReward::where('user_id', $testUser->id)->delete();
RewardTask::whereIn('reward_id', [$dailyPagesReward->id, $weeklyMinutesReward->id, $streakReward->id])->delete();
Reward::whereIn('id', [$dailyPagesReward->id, $weeklyMinutesReward->id, $streakReward->id])->delete();
Task::whereIn('id', [$dailyPagesTask->id, $weeklyMinutesTask->id, $streakTask->id])->delete();
UserReadingLog::whereIn('id', [$readingLog1->id, $readingLog2->id, $readingLog3->id])->delete();

echo "   ✓ Test data cleaned up\n";

echo "\n=== Test Complete ===\n\n";

if ($totalUserRewards >= 2) {
    echo "🎉 SUCCESS: Incremental reading rewards are working correctly!\n";
    echo "The fix to UserReadingLog model is successful.\n\n";
} else {
    echo "⚠️  PARTIAL SUCCESS: Some rewards may not be working as expected.\n";
    echo "Check the RewardCalculationService and task configurations.\n\n";
}
