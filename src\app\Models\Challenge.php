<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Challenge extends BaseModel
{
    protected $fillable = [
        'name',
        'description',
        'image',
        'start_date',
        'end_date',
        'prize',
        'active',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'active' => 'boolean',
    ];

    protected $attributes = [
        'active' => false,
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Listen for model updates to handle activation
        static::updated(function ($challenge) {
            if ($challenge->wasChanged('active') && $challenge->active) {
                $challenge->processAutomaticAssignments();
            }
        });
    }

    /**
     * Process automatic assignments when challenge is activated.
     */
    public function processAutomaticAssignments(): void
    {
        $assignedBy = auth()->guard('moonshine')->user()?->id ?? auth()->guard('web')->user()?->id ?? 1;

        // Get current relationship IDs
        $schoolIds = $this->schools()->pluck('schools.id')->toArray();
        $classIds = $this->classes()->pluck('school_classes.id')->toArray();
        $teamIds = $this->teams()->pluck('teams.id')->toArray();

        // Only process if there are participants and challenge tasks
        if (($schoolIds || $classIds || $teamIds) && $this->challengeTasks()->exists()) {
            // Process assignments if any relationships exist
//            if (!empty($schoolIds)) {
//                $this->assignToSchools($schoolIds, $assignedBy);
//           }

            if (!empty($classIds)) {
                $this->assignToClasses($classIds, $assignedBy);
            }

            if (!empty($teamIds)) {
                $this->assignToTeams($teamIds, $assignedBy);
            }
        }
    }



    /**
     * Get the challenge tasks for this challenge.
     */
    public function challengeTasks(): HasMany
    {
        return $this->hasMany(ChallengeTask::class);
    }

    /**
     * Get the user tasks for this challenge.
     */
    public function userTasks(): HasManyThrough
    {
        return $this->hasManyThrough(UserTask::class, ChallengeTask::class)
            ->where('user_tasks.task_type', UserTask::TASK_TYPE_CHALLENGE);
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use userTasks() instead
     */
    public function userChallengeTasks(): HasManyThrough
    {
        return $this->userTasks();
    }

    /**
     * Get the tasks associated with this challenge.
     */
    public function tasks(): BelongsToMany
    {
        return $this->belongsToMany(Task::class, 'challenge_tasks')
            ->withPivot(['start_date', 'end_date'])
            ->orderBy('challenge_tasks.start_date');
    }

    /**
     * Get the schools participating in this challenge.
     */
    public function schools(): BelongsToMany
    {
        return $this->belongsToMany(School::class, 'challenge_schools');
    }

    /**
     * Get the classes participating in this challenge.
     */
    public function classes(): BelongsToMany
    {
        return $this->belongsToMany(SchoolClass::class, 'challenge_classes', 'challenge_id', 'school_class_id');
    }

    /**
     * Get the teams participating in this challenge.
     */
    public function teams(): BelongsToMany
    {
        return $this->belongsToMany(Team::class, 'challenge_teams');
    }

    /**
     * Get the user who created this challenge.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active challenges.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope for current challenges (within date range).
     */
    public function scopeCurrent($query)
    {
        $now = Carbon::now()->toDateString();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now);
    }

    /**
     * Scope for upcoming challenges.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', Carbon::now()->toDateString());
    }

    /**
     * Scope for past challenges.
     */
    public function scopePast($query)
    {
        return $query->where('end_date', '<', Carbon::now()->toDateString());
    }

    /**
     * Check if challenge is currently active (within date range).
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->active) {
            return false;
        }

        $now = Carbon::now()->toDateString();
        return $this->start_date <= $now && $this->end_date >= $now;
    }

    /**
     * Check if challenge is upcoming.
     */
    public function isUpcoming(): bool
    {
        return $this->start_date > Carbon::now()->toDateString();
    }

    /**
     * Check if challenge is past.
     */
    public function isPast(): bool
    {
        return $this->end_date < Carbon::now()->toDateString();
    }

    /**
     * Get challenge status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        if (!$this->active) {
            return __('admin.inactive');
        }

        if ($this->isUpcoming()) {
            return __('admin.upcoming');
        }

        if ($this->isCurrentlyActive()) {
            return __('admin.active');
        }

        return __('admin.completed');
    }

    /**
     * Get date range display.
     */
    public function getDateRangeDisplayAttribute(): string
    {
        return $this->start_date->format('d/m/Y') . ' - ' . $this->end_date->format('d/m/Y');
    }

    /**
     * Get duration in days.
     */
    public function getDurationAttribute(): int
    {
        return (int) ($this->start_date->diffInDays($this->end_date) + 1);
    }

    /**
     * Get tasks count.
     */
    public function getTasksCountAttribute(): int
    {
        return $this->challengeTasks()->count();
    }
    
    /**
     * Get participants count.
     */
    public function getParticipantsCountAttribute(): int
    {
        return $this->userTasks()->distinct('user_id')->count('user_id');
    }

    /**
     * Get completion rate.
     */
    public function getCompletionRateAttribute(): float
    {
        $totalTasks = $this->userTasks()->count();
        if ($totalTasks === 0) {
            return 0.0;
        }

        $completedTasks = $this->userTasks()->where('completed', true)->count();
        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    /**
     * Get completion rate display.
     */
    public function getCompletionRateDisplayAttribute(): string
    {
        return $this->completion_rate . '%';
    }

    /**
     * Get summary information.
     */
    public function getSummaryAttribute(): string
    {
        $tasksCount = $this->tasks_count;
        $participantsCount = $this->participants_count;
        $completionRate = $this->completion_rate_display;

        return "Tasks: {$tasksCount}, Participants: {$participantsCount}, Completion: {$completionRate}";
    }

    /**
     * Assign challenge to schools (assigns to all classes in schools).
     */
    public function assignToSchools(array $schoolIds, int $assignedBy): array
    {
        $assignments = [];

        foreach ($schoolIds as $schoolId) {
            // Attach school to challenge
            $this->schools()->syncWithoutDetaching([$schoolId]);

            // Get all classes in the school and assign challenge to them
            $school = School::find($schoolId);
            if ($school) {
                $classIds = $school->schoolClasses()->pluck('id')->toArray();
                $classAssignments = $this->assignToClasses($classIds, $assignedBy);
                $assignments = array_merge($assignments, $classAssignments);
            }
        }

        return $assignments;
    }

    /**
     * Assign challenge to classes (assigns to all students in classes).
     */
    public function assignToClasses(array $classIds, int $assignedBy): array
    {
        $assignments = [];

        foreach ($classIds as $classId) {
            // Attach class to challenge
            $this->classes()->syncWithoutDetaching([$classId]);

            // Get all students in the class and create user challenge tasks
            $class = SchoolClass::find($classId);
            if ($class) {
                $students = $class->users()->role('student')->get();
                foreach ($students as $student) {
                    $studentAssignments = $this->createUserTasks($student->id, null, $assignedBy);
                    $assignments = array_merge($assignments, $studentAssignments);
                }
            }
        }

        return $assignments;
    }

    /**
     * Assign challenge to teams.
     */
    public function assignToTeams(array $teamIds, int $assignedBy): array
    {
        $assignments = [];

        foreach ($teamIds as $teamId) {
            // Attach team to challenge
            $this->teams()->syncWithoutDetaching([$teamId]);

            // Get all team members and create user challenge tasks
            $team = Team::find($teamId);
            if ($team) {
                $members = $team->users()->get();
                foreach ($members as $member) {
                    $memberAssignments = $this->createUserTasks($member->id, $teamId, $assignedBy);
                    $assignments = array_merge($assignments, $memberAssignments);
                }
            }
        }

        return $assignments;
    }

    /**
     * Create user tasks for a user.
     */
    public function createUserTasks(int $userId, ?int $teamId, int $assignedBy): array
    {
        $assignments = [];

        foreach ($this->challengeTasks as $challengeTask) {
            $userTask = UserTask::updateOrCreate([
                'task_type' => UserTask::TASK_TYPE_CHALLENGE,
                'challenge_task_id' => $challengeTask->id,
                'user_id' => $userId,
                'team_id' => $teamId,
            ], [
                'assigned_by' => $assignedBy,
                'assign_date' => now(),
                'completed' => false,
                'created_by' => $assignedBy,
            ]);

            $assignments[] = $userTask;
        }

        return $assignments;
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use createUserTasks() instead
     */
    public function createUserChallengeTasks(int $userId, ?int $teamId, int $assignedBy): array
    {
        return $this->createUserTasks($userId, $teamId, $assignedBy);
    }
}
