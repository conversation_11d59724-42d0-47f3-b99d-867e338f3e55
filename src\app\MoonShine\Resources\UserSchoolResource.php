<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\User;
use App\Models\School;
use App\Models\Role;
use App\Models\UserSchool;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('building-office')]
class UserSchoolResource extends BaseResource
{
    protected string $model = UserSchool::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'school', 'role'];

    public function getTitle(): string
    {
        return __('admin.school_assignments');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.school'),
                'school',
                formatted: fn(School $org) => $org->name,
                resource: SchoolResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.role'),
                'role',
                formatted: fn(Role $role) => $role->name,
                resource: RoleResource::class
            )
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),

            Switcher::make(__('admin.default'), 'default')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.user'),
                    'user',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class
                )
                    ->required(),

                BelongsTo::make(
                    __('admin.school'),
                    'school',
                    formatted: fn(School $org) => $org->name,
                    resource: SchoolResource::class
                )
                    ->required(),

                BelongsTo::make(
                    __('admin.role'),
                    'role',
                    formatted: fn(Role $role) => $role->name,
                    resource: RoleResource::class
                )
                    ->required(),

                Switcher::make(__('admin.active'), 'active')
                    ->default(true),

                Switcher::make(__('admin.default'), 'default')
                    ->default(false)
                    ->hint(__('admin.default_school_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.school'),
                'school',
                formatted: fn(School $org) => $org->name,
                resource: SchoolResource::class
            ),

            BelongsTo::make(
                __('admin.role'),
                'role',
                formatted: fn(Role $role) => $role->name,
                resource: RoleResource::class
            ),

            Switcher::make(__('admin.active'), 'active'),
            Switcher::make(__('admin.default'), 'default'),

            Text::make(__('admin.summary'), 'summary'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'school_id' => ['required', 'exists:schools,id'],
            'role_id' => ['required', 'exists:roles,id'],
            'active' => ['boolean'],
            'default' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'school.name', 'role.name'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0);  // No access if not authenticated
        }

        // System Admin can see all school assignments
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // Admin can see assignments in their school
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereIn('school_id', $userSchoolIds);
        }

        // Teachers and Students have no access to school user management
        return $builder->where('id', 0);
    }
}
