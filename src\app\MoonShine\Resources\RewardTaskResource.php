<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\Text;

use App\Models\{Reward, RewardTask, Task};

#[Icon('clipboard-document-check')]
class RewardTaskResource extends BaseResource
{
    protected string $model = RewardTask::class;

    protected string $column = 'id';

    protected array $with = ['reward', 'task.taskType', 'task.taskCycle'];

    public function getTitle(): string
    {
        return __('admin.reward_tasks');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(                
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: RewardResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name,
                resource: TaskResource::class
            )
                ->sortable(),

            Text::make(__('admin.task_type'), 'task.taskType.name')
                ->sortable(),

            Text::make(__('admin.task_cycle'), 'task.taskCycle.name')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: RewardResource::class
            )
                ->nullable()
                ->searchable(),

            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                resource: TaskResource::class
            )
                ->required()
                ->creatable()
                ->searchable(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: RewardResource::class
            ),

            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name,
                resource: TaskResource::class
            ),

            Text::make(__('admin.task_type'), 'task.taskType.name'),
            Text::make(__('admin.task_cycle'), 'task.taskCycle.name'),
            Text::make(__('admin.task_value'), 'task.task_value_with_unit'),
            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'reward_id' => ['required', 'exists:rewards,id'],
            'task_id' => ['required', 'exists:tasks,id'],
            ...parent::getCommonRules($item),
        ];
        return $rules;
    }

    protected function search(): array
    {
        return ['reward.name', 'task.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['id' => 'asc'];
    }
}
