<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use App\Models\Traits\BypassesPermissionScopes;

class UserPoint extends BaseModel
{
    use BypassesPermissionScopes;

    /**
     * Point type constants.
     */
    const POINT_TYPE_PAGE = 1;
    const POINT_TYPE_ACTIVITY = 2;
    const POINT_TYPE_TASK = 3;
    const POINT_TYPE_MANUAL = 4;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'point_date',
        'user_id',
        'book_id',
        'source_id',
        'point_type',
        'points',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'point_date' => 'datetime',
            'point_type' => 'integer',
            'points' => 'integer',
        ];
    }

    /**
     * Get the user who earned these points.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book associated with these points (if any).
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the user who created this point record.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by point type.
     */
    public function scopeByPointType($query, $pointType)
    {
        return $query->where('point_type', $pointType);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('point_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent points.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('point_date', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Scope to get page points only.
     */
    public function scopePagePoints($query)
    {
        return $query->where('point_type', self::POINT_TYPE_PAGE);
    }

    /**
     * Scope to get activity points only.
     */
    public function scopeActivityPoints($query)
    {
        return $query->where('point_type', self::POINT_TYPE_ACTIVITY);
    }

    /**
     * Scope to get task points only.
     */
    public function scopeTaskPoints($query)
    {
        return $query->where('point_type', self::POINT_TYPE_TASK);
    }

    /**
     * Scope to get manual points only.
     */
    public function scopeManualPoints($query)
    {
        return $query->where('point_type', self::POINT_TYPE_MANUAL);
    }

    /**
     * Get the point type name.
     */
    public function getPointTypeNameAttribute(): string
    {
        return match($this->point_type) {
            self::POINT_TYPE_PAGE => 'Page',
            self::POINT_TYPE_ACTIVITY => 'Activity',
            self::POINT_TYPE_TASK => 'Task',
            self::POINT_TYPE_MANUAL => 'Manual',
            default => 'Unknown',
        };
    }

    /**
     * Get the localized point type name.
     */
    public function getLocalizedPointTypeNameAttribute(): string
    {
        return match($this->point_type) {
            self::POINT_TYPE_PAGE => __('admin.point_type_page'),
            self::POINT_TYPE_ACTIVITY => __('admin.point_type_activity'),
            self::POINT_TYPE_TASK => __('admin.point_type_task'),
            self::POINT_TYPE_MANUAL => __('admin.point_type_manual'),
            default => __('admin.point_type_unknown'),
        };
    }

    /**
     * Get the display name for the point record.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' - ' . $this->points . ' pts (' . $this->point_type_name . ')';
    }

    /**
     * Get summary information for the point record.
     */
    public function getSummaryAttribute(): string
    {
        $bookInfo = $this->book ? " for {$this->book->name}" : '';
        return sprintf(
            '%d %s points%s on %s',
            $this->points,
            $this->point_type_name,
            $bookInfo,
            $this->point_date->format('M d, Y')
        );
    }

    /**
     * Get all point type options for forms.
     */
    public static function getPointTypeOptions(): array
    {
        return [
            self::POINT_TYPE_PAGE => __('admin.point_type_page'),
            self::POINT_TYPE_ACTIVITY => __('admin.point_type_activity'),
            self::POINT_TYPE_TASK => __('admin.point_type_task'),
            self::POINT_TYPE_MANUAL => __('admin.point_type_manual'),
        ];
    }

    /**
     * Calculate total points for a user.
     */
    public static function getTotalPointsForUser($userId): int
    {
        return self::where('user_id', $userId)->sum('points');
    }

    /**
     * Calculate total points for a user by type.
     */
    public static function getTotalPointsForUserByType($userId, $pointType): int
    {
        return self::where('user_id', $userId)
            ->where('point_type', $pointType)
            ->sum('points');
    }
}
