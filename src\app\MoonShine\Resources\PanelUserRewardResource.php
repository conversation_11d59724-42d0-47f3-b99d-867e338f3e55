<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;

use App\Models\{Reward, User};
use MoonShine\UI\Fields\{Date, Text};

#[Icon('star')]
class PanelUserRewardResource extends UserRewardResource
{
    protected function indexFields(): iterable
    {
        return [
             BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: PanelRewardResource::class
            )
                ->sortable(),

            Text::make(__('admin.reward_type'), 'reward.reward_type_display')
                ->sortable(),

            BelongsTo::make(
                __('admin.awarded_by'),
                'awarder',
                formatted: fn(?User $user) => $user ? $user->name : __('admin.system'),
                resource: UserResource::class
            )
                ->sortable(),

            Date::make(__('admin.awarded_date'), 'awarded_date')
                ->sortable(),

            Text::make(__('admin.award_type'), 'award_type'),

            Text::make(__('admin.trigger_source'), 'trigger_source'),

            Text::make(__('admin.award_age'), 'award_age_text'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            )
                ->required()
                ->searchable(),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name . ' (' . $reward->reward_type_display . ')',
                resource: PanelRewardResource::class
            )
                ->valuesQuery(function (Builder $query) { return $query->userRewards(); })
                ->required()
                ->searchable(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            ),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: PanelRewardResource::class
            ),

            Text::make(__('admin.reward_type'), 'reward.reward_type_display'),
            Text::make(__('admin.reward_description'), 'reward.description'),

            Text::make(__('admin.awarded_date'), 'awarded_date'),
            Text::make(__('admin.award_type'), 'award_type'),
            Text::make(__('admin.trigger_source'), 'trigger_source'),
            Text::make(__('admin.award_age'), 'award_age_text'),

            BelongsTo::make(
                __('admin.reading_log'),
                'readingLog',
                formatted: fn($log) => $log ? 'Reading Log #' . $log->id : '-',
                resource: UserReadingLogResource::class
            ),

            BelongsTo::make(
                __('admin.user_activity'),
                'userActivity',
                formatted: fn($activity) => $activity ? 'Activity #' . $activity->id : '-',
                resource: UserActivityResource::class
            ),
        ];
    }
}
