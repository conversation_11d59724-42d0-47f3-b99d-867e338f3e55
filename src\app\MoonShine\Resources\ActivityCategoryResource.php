<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ActivityCategory;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;

#[Icon('folder')]
class ActivityCategoryResource extends BaseResource
{
    protected string $model = ActivityCategory::class;

    protected string $column = 'name';

    protected array $with = ['activities'];

    public function getTitle(): string
    {
        return __('admin.activity_categories');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Number::make(__('admin.activity_count'), 'activity_count')
                ->sortable(),

            Number::make(__('admin.active_activity_count'), 'active_activity_count')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required(),

                Switcher::make(__('admin.active'), 'active')
                    ->default(true),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Switcher::make(__('admin.active'), 'active'),
            Number::make(__('admin.activity_count'), 'activity_count'),
            Number::make(__('admin.active_activity_count'), 'active_activity_count'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'unique:activity_categories,name,' . $item?->id],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
