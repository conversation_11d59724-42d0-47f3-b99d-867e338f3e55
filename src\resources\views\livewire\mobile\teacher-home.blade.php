<div class="min-h-screen bg-base-200">
    <!-- Header -->
    <div class="mobile-teacher-header bg-cover bg-center bg-no-repeat">
        <div class="flex items-start justify-between w-full">
            <!-- Left side: Avatar and greeting -->
            <div class="flex items-center space-x-3 mb-8">
                <!-- Teacher Avatar -->
                <div class="w-16 h-16 rounded-full overflow-hidden border-2 border-white/20">
                    @if($avatarImage)
                        <img src="{{ asset('storage/' . $avatarImage) }}" alt="Avatar" class="w-full h-full object-cover">
                    @else
                        <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                            <span class="text-white text-lg font-bold">{{ substr($user->name, 0, 1) }}</span>
                        </div>
                    @endif
                </div>
                
                <!-- Greeting -->
                 <div>
                    <p class="text-black text-lg opacity-90">{{ $greeting }},</p>
                    <h1 class="text-black text-lg font-bold">{{ $user->name }}</h1>
                </div>
            </div>

            <div class="flex items-center space-x-2" x-data="homeMessageBadge">
                <a href="{{ route('mobile.messages') }}" class="relative text-white hover:text-gray-200 transition-colors p-2">
                    <svg class="w-6 h-6 {{ ($unreadMessagesCount > 0 ? 'stroke-violet-600' : 'stroke-gray-600') }}" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    @if($unreadMessagesCount > 0)
                        <span class="absolute top-0 right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                            {{ $unreadMessagesCount > 9 ? '9+' : $unreadMessagesCount }}
                        </span>
                    @else
                        <span x-show="showWarningBadge"
                              class="absolute top-0 right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                            !
                        </span>
                    @endif
                </a>

                <!-- Logout Button -->
                <button wire:click="logout" class="text-white hover:text-gray-200 transition-colors p-2">
                    <img src="/images/power-icon.png" alt="Logout" class="w-6 h-6">
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4">
        <!-- Last 24 Hours Statistics Section -->
        <div class="bg-white rounded-2xl p-4 shadow-sm mb-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-2">
                    <span class="text-2xl">📊</span>
                    <h3 class="text-lg font-semibold text-gray-900">{{ __('mobile.last_24_hours') }}</h3>
                </div>
            </div>

            <!-- Statistics Grid -->
            <div class="grid grid-cols-4 gap-2 mb-4">
                <!-- Books Completed -->
                <div class="bg-purple-50 rounded-xl p-3 text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ $stats['books_completed'] }}</div>
                    <div class="text-sm text-purple-600">{{ __('mobile.books_localizable') }}</div>
                </div>

                <!-- Active Students -->
                <div class="bg-blue-50 rounded-xl p-2 text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ $stats['active_students'] }}</div>
                    <div class="text-sm text-blue-600">{{ __('mobile.students_localizable') }}</div>
                </div>

                <!-- Pages Read -->
                <div class="bg-green-50 rounded-xl p-2 text-center">
                    <div class="text-2xl font-bold text-green-600">{{ $stats['pages_read'] }}</div>
                    <div class="text-sm text-green-600">{{ __('mobile.pages_localizable') }}</div>
                </div>

                <!-- Activities Completed -->
                <div class="bg-orange-50 rounded-xl p-3 text-center">
                    <div class="text-2xl font-bold text-orange-600">{{ $stats['activities_completed'] }}</div>
                    <div class="text-sm text-orange-600">{{ __('mobile.activities_localizable') }}</div>
                </div>
            </div>

            <!-- Recent Activities Preview -->
            @if($recentActivities->count() > 0)
                <div class="space-y-2 mb-3">
                    @foreach($recentActivities as $activity)
                        <div class="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                            <!-- Student Avatar -->
                            <div class="w-8 h-8 rounded-full overflow-hidden">
                                @if($activity['student_avatar'])
                                    <img src="{{ asset('storage/' . $activity['student_avatar']) }}" alt="Avatar" class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                                        <span class="text-white text-xs font-bold">{{ substr($activity['student_name'], 0, 1) }}</span>
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Activity Info -->
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900 truncate">
                                    <span class="font-medium">{{ $activity['student_name'] }}</span>
                                    {{ $activity['type'] === 'completed' ? __('mobile.completed_reading') : __('mobile.began_reading') }}
                                </p>
                                <p class="text-xs text-blue-600">{!! $activity['book_title'] !!}</p>
                            </div>

                            <!-- Book Cover -->
                            @if($activity['book_cover'])
                                <div class="w-8 h-10 rounded overflow-hidden">
                                    <img src="{{ asset('storage/' . $activity['book_cover']) }}" alt="Book" class="w-full h-full object-cover">
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            <!-- View All Link -->
            <div class="text-center">
                <a href="{{ route('mobile.teacher.last-24-hours') }}" class="text-violet-600 text-sm font-medium hover:text-violet-700">
                    {{ __('mobile.view_all') }} →
                </a>
            </div>
            @endif
        </div>

        <!-- Pending Activities Section -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <div class="flex items-center space-x-2 mb-4">
                <span class="text-2xl">⏳</span>
                <h3 class="text-lg font-semibold text-gray-900">{{ __('mobile.pending_activities') }} ({{ $pendingActivities->count() }})</h3>
            </div>

            @if($pendingActivities->count() > 0)
                <div class="space-y-3">
                    @foreach($pendingActivities as $activity)
                        <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                            <div class="flex items-center space-x-3 flex-1 min-w-0">
                                <!-- Student Avatar -->
                                <div class="w-10 h-10 rounded-full overflow-hidden">
                                    @if($activity['student_avatar'])
                                        <img src="{{ asset('storage/' . $activity['student_avatar']) }}" alt="Avatar" class="w-full h-full object-cover">
                                    @else
                                        <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                                            <span class="text-white text-sm font-bold">{{ substr($activity['student_name'], 0, 1) }}</span>
                                        </div>
                                    @endif
                                </div>
                                
                                <!-- Activity Info -->
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $activity['student_name'] }}</p>
                                    <p class="text-xs text-gray-500">{{ $activity['book_title'] }}</p>
                                    <p class="text-xs text-gray-600 truncate">{{ $activity['activity_name'] }}</p>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="text-xs text-gray-500">{{ $activity['time'] }}</span>
                                        @if($activity['points'] > 0)
                                            <span class="text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full">{{ $activity['points'] }} {{ __('mobile.points') }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Review Button -->
                            <a href="{{ route('mobile.teacher.activity-review', $activity['id']) }}" 
                               class="bg-violet-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-violet-700 transition-colors">
                                {{ __('mobile.review') }}
                            </a>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-8">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <p class="text-gray-500 text-sm">{{ __('mobile.no_pending_activities') }}</p>
                </div>
            @endif
        </div>
    </div>
</div>
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('homeMessageBadge', () => ({
        showWarningBadge: false,

        init() {
            this.checkPWAStatus();
        },

        checkPWAStatus() {
            // Check if app is installed
            const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                               window.navigator.standalone === true;


            // Check notification permission
            const notificationPermission = 'Notification' in window ? Notification.permission : 'denied';
            // Show warning badge if app is not installed OR notifications are not enabled
            this.showWarningBadge = !isInstalled || notificationPermission !== 'granted';
        }
    }));
});
</script>
