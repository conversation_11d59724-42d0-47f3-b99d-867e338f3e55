<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClassBook extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'class_id',
        'book_id',
        'created_by',
    ];

    protected static function boot()
    {
        parent::boot();

    // fill class id with users default class while adding if it is not provided
        static::creating(function ($classBook) {
            $classBook->fillDefaultClassId($classBook);
        });
    }

    public function fillDefaultClassId($item): void
    {   
        if (isset($item->class_id)) {
            return;
        }   
        $user = auth('moonshine')->user();
        if (!$user || !($user instanceof User)) {
            return;
        }
        $defaultClass = $user->getDefaultClass();
        if (!$defaultClass) {
            return;
        }
        $item->class_id = $defaultClass->class_id;
    }

    /**
     * Get the school class this assignment belongs to.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the book in this assignment.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    // userbooks related to book id
    public function userBooks(): HasMany
    {
        return $this->hasMany(UserBook::class, 'book_id', 'book_id');
    }

    /**
     * Scope to filter by class.
     */
    public function scopeByClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by school (through class relationship).
     */
    public function scopeBySchool($query, $schoolId)
    {
        return $query->whereHas('schoolClass', function ($q) use ($schoolId) {
            $q->where('school_id', $schoolId);
        });
    }

    /**
     * Scope to get books for active classes only.
     */
    public function scopeActiveClasses($query)
    {
        return $query->whereHas('schoolClass', function ($q) {
            $q->where('active', true);
        });
    }

    /**
     * Get the display name for the assignment.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->schoolClass->name . ' - ' . html_entity_decode($this->book->name);
    }

    /**
     * Get summary information for the assignment.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            '%s (%s) - %s',
            $this->schoolClass->name,
            $this->schoolClass->school->name,
            $this->book->name
        );
    }

    /**
     * Apply role-based filtering based on current user's access to classes.
     */
    public function scopeForCurrentUser($query)
    {
        return $query->whereHas('schoolClass', function ($classQuery) {
            $classQuery->forCurrentUser();
        });
    }
}
