<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Role;
use App\Models\User;

class RolePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isSystemAdmin();
    }

    public function view(User $user, Role $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function create(User $user): bool
    {
        return $user->isSystemAdmin();
    }

    public function update(User $user, Role $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function delete(User $user, Role $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function restore(User $user, Role $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function forceDelete(User $user, Role $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin();
    }
}
