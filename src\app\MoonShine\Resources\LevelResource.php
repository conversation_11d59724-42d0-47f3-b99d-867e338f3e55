<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Level;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Image;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\HasMany;

#[Icon('trophy')]
class LevelResource extends BaseResource
{
    protected string $model = Level::class;

    protected string $column = 'name';

    protected array $with = ['userLevels'];

    public function getTitle(): string
    {
        return __('admin.levels');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Number::make(__('admin.level_number'), 'nr')
                ->sortable(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Number::make(__('admin.books_required'), 'books_count')
                ->sortable(),

            Number::make(__('admin.page_points_required'), 'page_points')
                ->sortable(),

            Switcher::make(__('admin.all_required'), 'all_required')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Number::make(__('admin.level_number'), 'nr')
                    ->required()
                    ->min(1)
                    ->hint(__('admin.level_number_hint')),

                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->hint(__('admin.level_name_hint')),

                Image::make(__('admin.level_image'), 'image')
                    ->dir('levels')
                    ->removable()
                    ->keepOriginalFileName(),

                Number::make(__('admin.books_required'), 'books_count')
                    ->required()
                    ->min(0)
                    ->default(0)
                    ->hint(__('admin.books_required_hint')),

                Number::make(__('admin.page_points_required'), 'page_points')
                    ->required()
                    ->min(0)
                    ->default(0)
                    ->hint(__('admin.page_points_required_hint')),

                Switcher::make(__('admin.all_required'), 'all_required')
                    ->default(true)
                    ->hint(__('admin.all_required_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Number::make(__('admin.level_number'), 'nr'),
            Text::make(__('admin.name'), 'name'),
            
            Image::make(__('admin.level_image'), 'image'),

            Number::make(__('admin.books_required'), 'books_count'),
            Number::make(__('admin.page_points_required'), 'page_points'),
            Switcher::make(__('admin.all_required'), 'all_required'),

            Text::make(__('admin.summary'), 'summary'),

            HasMany::make(__('admin.user_levels'), 'userLevels', UserLevelResource::class),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $levelId = $item?->id;

        return [
            'nr' => [
                'required', 
                'integer', 
                'min:1',
                'unique:levels,nr' . ($levelId ? ",$levelId" : '')
            ],
            'name' => ['required', 'string', 'max:255'],
            'image' => ['nullable', 'image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'books_count' => ['required', 'integer', 'min:0'],
            'page_points' => ['required', 'integer', 'min:0'],
            'all_required' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['nr' => 'asc'];
    }
}
