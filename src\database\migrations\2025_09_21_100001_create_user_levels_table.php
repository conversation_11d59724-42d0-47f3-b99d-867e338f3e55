<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_levels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('level_id')->constrained('levels')->onDelete('cascade');
            $table->timestamp('level_date')->comment('When the level was achieved');
            $table->foreignId('reading_log_id')->nullable()->constrained('user_reading_logs')->onDelete('cascade')->comment('Reading session that triggered the level up');

            // Add indexes for performance
            $table->index(['user_id', 'level_date']);
            $table->index(['user_id', 'level_id']);
            $table->index('level_id');
            $table->index('reading_log_id');
            $table->index('level_date');

            // Unique constraint to prevent duplicate user-level combinations
            $table->unique(['user_id', 'level_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_levels');
    }
};
