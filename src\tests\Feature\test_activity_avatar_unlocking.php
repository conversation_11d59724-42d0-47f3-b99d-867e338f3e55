<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Avatar;
use App\Models\UserAvatar;
use App\Models\UserPoint;
use App\Models\UserActivity;
use App\Models\Activity;
use App\Models\Book;

try {
    echo "=== TESTING ACTIVITY AVATAR UNLOCKING METHODS ===\n\n";
    
    // Get test data
    $user = User::first();
    $book = Book::first();
    $activity = Activity::first();
    $lowPointAvatar = Avatar::where('required_points', '<=', 50)->first();
    $midPointAvatar = Avatar::where('required_points', '>', 50)->where('required_points', '<=', 100)->first();
    $highPointAvatar = Avatar::where('required_points', '>', 100)->first();
    
    if (!$user || !$book || !$activity || !$lowPointAvatar || !$midPointAvatar || !$highPointAvatar) {
        echo "❌ Missing test data. Need user, book, activity, and avatars with different point requirements\n";
        exit;
    }
    
    echo "Test User: {$user->name}\n";
    echo "Test Activity: {$activity->name} ({$activity->points} pts)\n";
    echo "Low Point Avatar: {$lowPointAvatar->name} ({$lowPointAvatar->required_points} pts)\n";
    echo "Mid Point Avatar: {$midPointAvatar->name} ({$midPointAvatar->required_points} pts)\n";
    echo "High Point Avatar: {$highPointAvatar->name} ({$highPointAvatar->required_points} pts)\n\n";
    
    // Clean up existing data
    UserAvatar::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserActivity::where('user_id', $user->id)->delete();
    
    // Test Case 1: Create pending user activity (should not unlock avatars)
    echo "TEST CASE 1: Pending user activity (should not unlock avatars)\n";
    $userActivity = UserActivity::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'activity_id' => $activity->id,
        'activity_date' => now()->toDateString(),
        'content' => 'Test activity content',
        'status' => UserActivity::STATUS_PENDING,
    ]);
    
    $pointsValue = $userActivity->getActivityPointsValue();
    $canUnlock = $userActivity->canUnlockNewAvatars();
    $newlyUnlocked = $userActivity->getNewlyUnlockedAvatars();
    $hasUnlocked = $userActivity->hasUnlockedAvatarsForUser();
    
    echo "- Activity Status: " . $userActivity->status_name . "\n";
    echo "- Points Value: {$pointsValue}\n";
    echo "- Can Unlock New Avatars: " . ($canUnlock ? 'YES' : 'NO') . "\n";
    echo "- Newly Unlocked Count: {$newlyUnlocked->count()}\n";
    echo "- Has Unlocked Avatars: " . ($hasUnlocked ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($pointsValue === 0 && !$canUnlock && $newlyUnlocked->count() === 0 && !$hasUnlocked ? "PASS" : "FAIL") . " - Pending activity should not unlock avatars\n\n";
    
    // Test Case 2: Approve the activity (should unlock avatars)
    echo "TEST CASE 2: Approve activity (should unlock avatars)\n";
    $userActivity->update(['status' => UserActivity::STATUS_APPROVED]);
    
    $pointsValue = $userActivity->getActivityPointsValue();
    $canUnlock = $userActivity->canUnlockNewAvatars();
    $newlyUnlocked = $userActivity->getNewlyUnlockedAvatars();
    $hasUnlocked = $userActivity->hasUnlockedAvatarsForUser();
    
    echo "- Activity Status: " . $userActivity->status_name . "\n";
    echo "- Points Value: {$pointsValue}\n";
    echo "- Can Unlock New Avatars: " . ($canUnlock ? 'YES' : 'NO') . "\n";
    echo "- Newly Unlocked Count: {$newlyUnlocked->count()}\n";
    echo "- Has Unlocked Avatars: " . ($hasUnlocked ? 'YES' : 'NO') . "\n";
    
    if ($newlyUnlocked->count() > 0) {
        echo "- Newly Unlocked Avatars:\n";
        foreach ($newlyUnlocked as $avatar) {
            echo "  - {$avatar->name} ({$avatar->required_points} pts)\n";
        }
    }
    
    echo "✅ " . ($pointsValue > 0 && $hasUnlocked ? "PASS" : "FAIL") . " - Approved activity should provide points and unlock avatars\n\n";
    
    // Test Case 3: Add existing activity points to test incremental unlocking
    echo "TEST CASE 3: Add existing activity points to test incremental unlocking\n";
    UserPoint::create([
        'point_date' => now(),
        'user_id' => $user->id,
        'book_id' => $book->id,
        'source_id' => 999, // Simulate existing activity
        'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        'points' => 30,
    ]);
    
    $user->refresh();
    $currentActivityPoints = $user->getActivityPoints();
    $newlyUnlocked = $userActivity->getNewlyUnlockedAvatars();
    
    echo "- User's Current Activity Points: {$currentActivityPoints}\n";
    echo "- This Activity Points: {$userActivity->getActivityPointsValue()}\n";
    echo "- Total After This Activity: " . ($currentActivityPoints + $userActivity->getActivityPointsValue()) . "\n";
    echo "- Newly Unlocked Count: {$newlyUnlocked->count()}\n";
    
    if ($newlyUnlocked->count() > 0) {
        echo "- Newly Unlocked Avatars:\n";
        foreach ($newlyUnlocked as $avatar) {
            echo "  - {$avatar->name} ({$avatar->required_points} pts)\n";
        }
    }
    
    echo "✅ PASS - Incremental unlocking calculation working\n\n";
    
    // Test Case 4: Test with rejected activity (should not unlock)
    echo "TEST CASE 4: Test with rejected activity (should not unlock)\n";
    $rejectedActivity = UserActivity::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'activity_id' => $activity->id,
        'activity_date' => now()->toDateString(),
        'content' => 'Test rejected activity',
        'status' => UserActivity::STATUS_REJECTED,
    ]);
    
    $pointsValue = $rejectedActivity->getActivityPointsValue();
    $canUnlock = $rejectedActivity->canUnlockNewAvatars();
    $hasUnlocked = $rejectedActivity->hasUnlockedAvatarsForUser();
    
    echo "- Activity Status: " . $rejectedActivity->status_name . "\n";
    echo "- Points Value: {$pointsValue}\n";
    echo "- Can Unlock New Avatars: " . ($canUnlock ? 'YES' : 'NO') . "\n";
    echo "- Has Unlocked Avatars: " . ($hasUnlocked ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($pointsValue === 0 && !$canUnlock ? "PASS" : "FAIL") . " - Rejected activity should not provide points or unlock avatars\n\n";
    
    // Test Case 5: Test with completed activity (no approval needed)
    echo "TEST CASE 5: Test with completed activity (no approval needed)\n";
    $completedActivity = UserActivity::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'activity_id' => $activity->id,
        'activity_date' => now()->toDateString(),
        'content' => 'Test completed activity',
        'status' => UserActivity::STATUS_COMPLETED,
    ]);
    
    $pointsValue = $completedActivity->getActivityPointsValue();
    $canUnlock = $completedActivity->canUnlockNewAvatars();
    $newlyUnlocked = $completedActivity->getNewlyUnlockedAvatars();
    $hasUnlocked = $completedActivity->hasUnlockedAvatarsForUser();
    
    echo "- Activity Status: " . $completedActivity->status_name . "\n";
    echo "- Points Value: {$pointsValue}\n";
    echo "- Can Unlock New Avatars: " . ($canUnlock ? 'YES' : 'NO') . "\n";
    echo "- Newly Unlocked Count: {$newlyUnlocked->count()}\n";
    echo "- Has Unlocked Avatars: " . ($hasUnlocked ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($pointsValue > 0 && $hasUnlocked ? "PASS" : "FAIL") . " - Completed activity should provide points and unlock avatars\n\n";
    
    // Test Case 6: Test avatar unlocking progression
    echo "TEST CASE 6: Test avatar unlocking progression\n";
    $user->refresh();
    $totalActivityPoints = $user->getActivityPoints();
    $availableAvatars = $user->getAvailableAvatars();
    $lockedAvatars = $user->getLockedAvatars();
    
    echo "- User's Total Activity Points: {$totalActivityPoints}\n";
    echo "- Available Avatars: {$availableAvatars->count()}\n";
    echo "- Locked Avatars: {$lockedAvatars->count()}\n";
    
    foreach ($availableAvatars as $avatar) {
        echo "  - Available: {$avatar->name} ({$avatar->required_points} pts)\n";
    }
    
    foreach ($lockedAvatars as $avatar) {
        echo "  - Locked: {$avatar->name} ({$avatar->required_points} pts)\n";
    }
    
    echo "✅ PASS - Avatar progression working correctly\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    UserAvatar::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserActivity::where('user_id', $user->id)->delete();
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Activity avatar unlocking methods tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
