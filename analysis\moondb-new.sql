
CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `role_priority` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`role_priority`)),
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `activity_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `log_name` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `subject_type` varchar(255) DEFAULT NULL,
  `event` varchar(255) DEFAULT NULL,
  `subject_id` bigint(20) UNSIGNED DEFAULT NULL,
  `causer_type` varchar(255) DEFAULT NULL,
  `causer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`properties`)),
  `batch_uuid` char(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_agreements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `agreement_type` varchar(255) NOT NULL DEFAULT 'privacy_policy' COMMENT 'Type of agreement (privacy_policy, terms_of_service, etc.)',
  `version` varchar(255) NOT NULL DEFAULT '1.0' COMMENT 'Version of the agreement accepted',
  `accepted_at` timestamp NOT NULL COMMENT 'When the user accepted the agreement',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the user when accepting'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_class_levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_school_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_task_cycles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_task_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `school_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `school_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `class_level_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `default` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `default` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `leader_user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/* Parent account 

CREATE TABLE `user_relations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `child_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
 */


CREATE TABLE `avatars` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `base_image` varchar(255) NOT NULL,
  `happy_image` varchar(255) NOT NULL,
  `sad_image` varchar(255) NOT NULL,
  `sleepy_image` varchar(255) NOT NULL,
  `required_points` int(11) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_avatars` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `avatar_id` bigint(20) UNSIGNED NOT NULL,
  `selected_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `thumbnail` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `page_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_type_id` bigint(20) UNSIGNED NOT NULL,
  `class_level_id` bigint(20) UNSIGNED NOT NULL,
  `point` decimal(8,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `publishers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `isbn` varchar(255) NOT NULL,
  `publisher_id` bigint(20) UNSIGNED NOT NULL,
  `book_type_id` bigint(20) UNSIGNED NOT NULL,
  `page_count` int(11) NOT NULL,
  `year_of_publish` year(4) NOT NULL,
  `cover_image` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `author_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_questions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `question_text` text NOT NULL COMMENT 'The question content',
  `question_image_url` varchar(255) DEFAULT NULL COMMENT 'Optional image URL',
  `question_audio_url` varchar(255) DEFAULT NULL COMMENT 'Optional audio URL',
  `question_video_url` varchar(255) DEFAULT NULL COMMENT 'Optional video URL',
  `correct_answer` varchar(255) NOT NULL COMMENT 'The correct answer text',
  `incorrect_answer_1` varchar(255) DEFAULT NULL COMMENT 'First incorrect option',
  `incorrect_answer_2` varchar(255) DEFAULT NULL COMMENT 'Second incorrect option',
  `incorrect_answer_3` varchar(255) DEFAULT NULL COMMENT 'Third incorrect option',
  `incorrect_answer_4` varchar(255) DEFAULT NULL COMMENT 'Fourth incorrect option',
  `incorrect_answer_5` varchar(255) DEFAULT NULL COMMENT 'Fifth incorrect option',
  `page_start` int(11) DEFAULT NULL COMMENT 'Starting page reference',
  `page_end` int(11) DEFAULT NULL COMMENT 'Ending page reference',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether question is available for use'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `book_words` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `word` varchar(255) NOT NULL COMMENT 'The vocabulary word',
  `definition` text DEFAULT NULL COMMENT 'Word definition',
  `synonym` varchar(255) DEFAULT NULL COMMENT 'Synonym of the word',
  `antonym` varchar(255) DEFAULT NULL COMMENT 'Antonym of the word',
  `page_reference` int(11) DEFAULT NULL COMMENT 'Page where word appears',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `class_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `activity_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL,
  `activity_type` int(11) NOT NULL DEFAULT 1 COMMENT '1-Writing, 2-Rating, 3-Media, 4-Physical, 5-Game',
  `media_type` int(11) DEFAULT NULL COMMENT '1-image, 2-audio',
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `question_count` int(11) DEFAULT NULL,
  `min_word_count` int(11) DEFAULT NULL,
  `min_rating` int(11) DEFAULT NULL,
  `max_rating` int(11) DEFAULT NULL,
  `media_url` varchar(255) DEFAULT NULL COMMENT 'Playable game URL or media content',
  `points` int(11) NOT NULL DEFAULT 0,
  `allowed_tries` int(11) NOT NULL DEFAULT 1,
  `need_approval` tinyint(1) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `class_activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `question_count` int(11) DEFAULT NULL,
  `min_grade` int(11) DEFAULT NULL,
  `min_word_count` int(11) DEFAULT NULL,
  `points` int(11) DEFAULT NULL,
  `required` tinyint(1) NOT NULL DEFAULT 0,
  `allowed_tries` int(11) NOT NULL DEFAULT 1,
  `need_approval` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


/* CREATE TABLE `achievements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `type` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
--  `unlock_rule_id` bigint(20) UNSIGNED DEFAULT NULL,
  `map_start_x` int(11) DEFAULT NULL,
  `map_start_y` int(11) DEFAULT NULL,
  `map_end_x` int(11) DEFAULT NULL,
  `map_end_y` int(11) DEFAULT NULL,
  `is_dynamic_position` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--

CREATE TABLE `levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `sequence` int(11) NOT NULL,
--  `unlock_rule_id` bigint(20) UNSIGNED DEFAULT NULL,
  `map_start_x` int(11) DEFAULT NULL,
  `map_start_y` int(11) DEFAULT NULL,
  `map_end_x` int(11) DEFAULT NULL,
  `map_end_y` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
 */

CREATE TABLE `tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `task_type_id` bigint(20) UNSIGNED NOT NULL,
  `task_cycle_id` bigint(20) UNSIGNED NOT NULL,
  `task_value` int(11) DEFAULT NULL,
  `activity_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `task_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `task_book_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reward_type` int(11) NOT NULL DEFAULT 1 COMMENT '1-Badge, 2-Trophy, 3-Card, 4-Item',
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `reward_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `goals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `motto` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `goal_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `goal_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `user_goals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `goal_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `assigned_by` bigint(20) UNSIGNED NOT NULL,
  `assign_date` timestamp NULL DEFAULT NULL,
  `comment` text DEFAULT NULL,
  `achieved` tinyint(1) NOT NULL DEFAULT 0,
  `achieve_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_goal_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_goal_id` bigint(20) UNSIGNED NOT NULL,
  `goal_task_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` timestamp NULL DEFAULT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `complete_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


/*
CREATE TABLE `goal_messages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `min_rate` int(11) NOT NULL DEFAULT 0,  -- Rounded minimum goal achievement rate according to todays expected value, should be sorted by desc and show first suitable message
  `last5days_log` int(11) NOT NULL DEFAULT 0,  -- Last 5 days reading log activity
  `message` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

*/
CREATE TABLE `challenges` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL COMMENT 'Challenge banner/flyer image path',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `prize` text DEFAULT NULL COMMENT 'Challenge reward description',
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `school_class_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `reward_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `user_challenge_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `assigned_by` bigint(20) UNSIGNED NOT NULL,
  `assign_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `start_date` timestamp NULL DEFAULT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `complete_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


/*
CREATE TABLE `book_quizzes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED DEFAULT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `quiz_type` enum('completion','daily_reading','practice') NOT NULL COMMENT 'Type of quiz',
  `total_questions` int(11) NOT NULL COMMENT 'Number of questions in this quiz',
  `correct_answers` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of correct answers',
  `score_percentage` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Score as percentage (0.00-100.00)',
  `passing_score` decimal(5,2) NOT NULL COMMENT 'Required score to pass',
  `is_passed` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether student passed',
  `attempt_number` int(11) NOT NULL DEFAULT 1 COMMENT 'Which attempt this is',
  `started_at` datetime NOT NULL COMMENT 'When quiz was started',
  `completed_at` datetime DEFAULT NULL COMMENT 'When quiz was completed',
  `time_limit_minutes` int(11) DEFAULT NULL COMMENT 'Time limit for quiz'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--

CREATE TABLE `quest_quiz_questions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `quest_quiz_id` bigint(20) UNSIGNED NOT NULL,
  `book_question_id` bigint(20) UNSIGNED NOT NULL,
  `question_order` int(11) NOT NULL COMMENT 'Order of question in quiz',
  `student_answer` varchar(255) DEFAULT NULL COMMENT 'Student''s selected answer',
  `is_correct` tinyint(1) DEFAULT NULL COMMENT 'Whether answer was correct',
  `answered_at` datetime DEFAULT NULL COMMENT 'When question was answered',
  `points_earned` int(11) NOT NULL DEFAULT 0 COMMENT 'Points earned for this question'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

*/

CREATE TABLE `user_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `goal_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `user_reading_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `log_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `start_page` int(11) DEFAULT NULL COMMENT 'Optional start page information',
  `end_page` int(11) DEFAULT NULL COMMENT 'Optional end page information',
  `pages_read` int(11) NOT NULL,
  `reading_duration` int(11) DEFAULT NULL COMMENT 'Time spent reading in minutes',
  `book_completed` tinyint(1) NOT NULL DEFAULT 0,
  `goal_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `activity_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `content` text DEFAULT NULL COMMENT 'Written content for writing activities',
  `rating` int(11) DEFAULT NULL COMMENT 'Rating value for rating activities',
  `media_url` varchar(255) DEFAULT NULL COMMENT 'User-submitted media content',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0-pending, 1-approved, 2-rejected, 3-completed (no approval needed)',
  `goal_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_activity_reviews` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED NOT NULL,
  `review_date` date NOT NULL,
  `reviewed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0-waiting, 1-approved, 2-rejected',
  `feedback` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `point_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED DEFAULT NULL,
  `source_id` bigint(20) DEFAULT NULL,
  `point_type` int(11) NOT NULL COMMENT '1-Page, 2-Activity, 3-Task, 4-Manual',
  `points` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `awarded_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `awarded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reading_log_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `team_rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `awarded_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `awarded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reading_log_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/* 


CREATE TABLE `user_achievements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `quest_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `story_achievement_id` bigint(20) UNSIGNED NOT NULL,
  `earned_at` timestamp NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `quest_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `story_chapter_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--

CREATE TABLE `user_maps` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `quest_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `item_type` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `x_coordinate` decimal(8,2) NOT NULL,
  `y_coordinate` decimal(8,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
 */
/* Social 

CREATE TABLE `enum_reaction_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL DEFAULT 0, 
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
Clap, Star, Trophy, Thumbs up, neutral face , Thinking Face

CREATE TABLE `enum_feed_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL DEFAULT 0, 
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
- Friend logged reading
- Friend completed a book 
- Friend earned a badge 
- Friend rated a book 
- Friend completed a challenge task
- Friend completed a goal task

CREATE TABLE `user_followers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `follower_id` bigint(20) UNSIGNED NOT NULL,
  `following_id` bigint(20) UNSIGNED NOT NULL,
  `feed_type_id` bigint(20) UNSIGNED NOT NULL,
  `follow_date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_feeds` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `feed_type_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED DEFAULT NULL,
  `badge_id` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `goal_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `feed_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `message` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_feed_reactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `feed_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `reaction_type_id` bigint(20) UNSIGNED NOT NULL,
  `reaction_date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
 */


ALTER TABLE `activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activities_category_id_active_index` (`category_id`,`active`),
  ADD KEY `activities_activity_type_index` (`activity_type`),
  ADD KEY `activities_need_approval_index` (`need_approval`),
  ADD KEY `activities_active_index` (`active`),
  ADD KEY `activities_points_index` (`points`);

ALTER TABLE `activity_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activity_categories_active_index` (`active`),
  ADD KEY `activity_categories_name_index` (`name`);

ALTER TABLE `activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subject` (`subject_type`,`subject_id`),
  ADD KEY `causer` (`causer_type`,`causer_id`),
  ADD KEY `activity_log_log_name_index` (`log_name`);

ALTER TABLE `authors`
  ADD PRIMARY KEY (`id`),
  ADD KEY `authors_name_index` (`name`),
  ADD KEY `authors_created_by_index` (`created_by`);

ALTER TABLE `avatars`
  ADD PRIMARY KEY (`id`),
  ADD KEY `avatars_name_index` (`name`);

ALTER TABLE `books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `books_isbn_unique` (`isbn`),
  ADD KEY `books_name_index` (`name`),
  ADD KEY `books_publisher_id_index` (`publisher_id`),
  ADD KEY `books_book_type_id_index` (`book_type_id`),
  ADD KEY `books_year_of_publish_index` (`year_of_publish`),
  ADD KEY `books_created_by_index` (`created_by`);

ALTER TABLE `book_authors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_authors_book_id_author_id_unique` (`book_id`,`author_id`),
  ADD KEY `book_authors_author_id_index` (`author_id`);

ALTER TABLE `book_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_categories_book_id_category_id_unique` (`book_id`,`category_id`),
  ADD KEY `book_categories_category_id_index` (`category_id`);

ALTER TABLE `book_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `book_questions_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_questions_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_questions_page_start_page_end_index` (`page_start`,`page_end`),
  ADD KEY `book_questions_difficulty_level_index` (`difficulty_level`),
  ADD KEY `book_questions_created_by_index` (`created_by`);

ALTER TABLE `book_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_types_name_unique` (`name`),
  ADD KEY `book_types_name_index` (`name`);

ALTER TABLE `book_words`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_book_word` (`book_id`,`word`),
  ADD KEY `book_words_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_words_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_words_word_book_id_index` (`word`,`book_id`),
  ADD KEY `book_words_page_reference_index` (`page_reference`),
  ADD KEY `book_words_difficulty_level_index` (`difficulty_level`),
  ADD KEY `book_words_created_by_index` (`created_by`);

ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `categories_name_unique` (`name`),
  ADD KEY `categories_name_index` (`name`);

ALTER TABLE `challenges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `challenges_active_index` (`active`),
  ADD KEY `challenges_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `challenges_active_start_date_end_date_index` (`active`,`start_date`,`end_date`),
  ADD KEY `challenges_created_by_index` (`created_by`);

ALTER TABLE `challenge_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_classes_challenge_id_school_class_id_unique` (`challenge_id`,`school_class_id`),
  ADD KEY `challenge_classes_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_classes_school_class_id_index` (`school_class_id`);

ALTER TABLE `challenge_schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_schools_challenge_id_school_id_unique` (`challenge_id`,`school_id`),
  ADD KEY `challenge_schools_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_schools_school_id_index` (`school_id`);

ALTER TABLE `challenge_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_tasks_challenge_id_task_id_start_date_end_date_unique` (`challenge_id`,`task_id`,`start_date`,`end_date`),
  ADD KEY `challenge_tasks_task_id_foreign` (`task_id`),
  ADD KEY `challenge_tasks_challenge_id_task_id_index` (`challenge_id`,`task_id`),
  ADD KEY `challenge_tasks_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `challenge_tasks_challenge_id_start_date_end_date_index` (`challenge_id`,`start_date`,`end_date`),
  ADD KEY `challenge_tasks_reward_id_foreign` (`reward_id`),
  ADD KEY `challenge_tasks_challenge_id_reward_id_index` (`challenge_id`,`reward_id`),
  ADD KEY `challenge_tasks_created_by_index` (`created_by`);

ALTER TABLE `challenge_teams`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_teams_challenge_id_team_id_unique` (`challenge_id`,`team_id`),
  ADD KEY `challenge_teams_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_teams_team_id_index` (`team_id`);

ALTER TABLE `class_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `class_books_unique` (`class_id`,`book_id`),
  ADD KEY `class_books_class_id_index` (`class_id`),
  ADD KEY `class_books_book_id_index` (`book_id`),
  ADD KEY `class_books_created_by_index` (`created_by`);

ALTER TABLE `enum_class_levels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_class_levels_name_unique` (`name`),
  ADD KEY `enum_class_levels_name_index` (`name`);

ALTER TABLE `enum_school_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_school_types_name_unique` (`name`),
  ADD KEY `enum_school_types_name_index` (`name`);

ALTER TABLE `enum_task_cycles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_task_cycles_nr_unique` (`nr`),
  ADD KEY `enum_task_cycles_nr_index` (`nr`);

ALTER TABLE `enum_task_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_task_types_nr_unique` (`nr`),
  ADD KEY `enum_task_types_nr_index` (`nr`);

ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

ALTER TABLE `goals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `goals_name_index` (`name`),
  ADD KEY `goals_active_index` (`active`),
  ADD KEY `goals_active_name_index` (`active`,`name`),
  ADD KEY `goals_created_by_index` (`created_by`);

ALTER TABLE `goal_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `goal_tasks_goal_id_task_id_start_date_end_date_unique` (`goal_id`,`task_id`,`start_date`,`end_date`),
  ADD KEY `goal_tasks_task_id_foreign` (`task_id`),
  ADD KEY `goal_tasks_goal_id_task_id_index` (`goal_id`,`task_id`),
  ADD KEY `goal_tasks_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `goal_tasks_goal_id_start_date_end_date_index` (`goal_id`,`start_date`,`end_date`),
  ADD KEY `goal_tasks_created_by_index` (`created_by`);

ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

ALTER TABLE `page_points`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `page_points_book_type_id_class_level_id_unique` (`book_type_id`,`class_level_id`),
  ADD KEY `page_points_class_level_id_foreign` (`class_level_id`);

ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

ALTER TABLE `publishers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `publishers_name_index` (`name`),
  ADD KEY `publishers_created_by_index` (`created_by`);

ALTER TABLE `rewards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rewards_reward_type_index` (`reward_type`),
  ADD KEY `rewards_name_index` (`name`),
  ADD KEY `rewards_active_index` (`active`),
  ADD KEY `rewards_reward_type_active_index` (`reward_type`,`active`),
  ADD KEY `rewards_created_by_index` (`created_by`);

ALTER TABLE `reward_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reward_tasks_reward_id_task_id_unique` (`reward_id`,`task_id`),
  ADD KEY `reward_tasks_reward_id_task_id_index` (`reward_id`,`task_id`),
  ADD KEY `reward_tasks_reward_id_index` (`reward_id`),
  ADD KEY `reward_tasks_task_id_index` (`task_id`),
  ADD KEY `reward_tasks_created_by_index` (`created_by`);

ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

ALTER TABLE `schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `schools_name_school_type_id_unique` (`name`,`school_type_id`),
  ADD KEY `schools_school_type_id_active_index` (`school_type_id`,`active`),
  ADD KEY `schools_school_type_id_index` (`school_type_id`);

ALTER TABLE `school_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `school_classes_school_id_name_active_unique` (`school_id`,`name`,`active`),
  ADD KEY `school_classes_school_id_class_level_id_active_index` (`school_id`,`class_level_id`,`active`),
  ADD KEY `school_classes_class_level_id_index` (`class_level_id`),
  ADD KEY `school_classes_created_by_index` (`created_by`);

ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

ALTER TABLE `tasks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tasks_task_type_id_index` (`task_type_id`),
  ADD KEY `tasks_task_cycle_id_index` (`task_cycle_id`),
  ADD KEY `tasks_activity_id_index` (`activity_id`),
  ADD KEY `tasks_active_index` (`active`),
  ADD KEY `tasks_active_task_type_id_index` (`active`,`task_type_id`),
  ADD KEY `tasks_created_by_index` (`created_by`);

ALTER TABLE `task_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_books_task_id_book_id_unique` (`task_id`,`book_id`),
  ADD KEY `task_books_task_id_index` (`task_id`),
  ADD KEY `task_books_book_id_index` (`book_id`),
  ADD KEY `task_books_created_by_index` (`created_by`);

ALTER TABLE `task_book_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_book_categories_task_id_category_id_unique` (`task_id`,`category_id`),
  ADD KEY `task_book_categories_task_id_index` (`task_id`),
  ADD KEY `task_book_categories_category_id_index` (`category_id`),
  ADD KEY `task_book_categories_created_by_index` (`created_by`);

ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teams_name_index` (`name`),
  ADD KEY `teams_leader_user_id_index` (`leader_user_id`),
  ADD KEY `teams_active_index` (`active`),
  ADD KEY `teams_active_name_index` (`active`,`name`),
  ADD KEY `teams_created_by_index` (`created_by`);

ALTER TABLE `team_rewards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_reward_unique` (`team_id`,`reward_id`),
  ADD KEY `team_rewards_team_id_reward_id_index` (`team_id`,`reward_id`),
  ADD KEY `team_rewards_team_id_awarded_date_index` (`team_id`,`awarded_date`),
  ADD KEY `team_rewards_reward_id_awarded_date_index` (`reward_id`,`awarded_date`),
  ADD KEY `team_rewards_awarded_by_index` (`awarded_by`),
  ADD KEY `team_rewards_reading_log_id_index` (`reading_log_id`),
  ADD KEY `team_rewards_user_activity_id_index` (`user_activity_id`),
  ADD KEY `team_rewards_awarded_date_index` (`awarded_date`),
  ADD KEY `team_rewards_created_by_index` (`created_by`);

ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_username_unique` (`username`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

ALTER TABLE `user_activities`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_book_activity_unique` (`user_id`,`book_id`,`activity_id`),
  ADD KEY `user_activities_user_id_activity_date_index` (`user_id`,`activity_date`),
  ADD KEY `user_activities_book_id_activity_date_index` (`book_id`,`activity_date`),
  ADD KEY `user_activities_activity_id_status_index` (`activity_id`,`status`),
  ADD KEY `user_activities_status_index` (`status`),
  ADD KEY `user_activities_activity_date_index` (`activity_date`),
  ADD KEY `user_activities_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_activities_goal_task_id_status_index` (`goal_task_id`,`status`),
  ADD KEY `user_activities_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_activities_challenge_task_id_status_index` (`challenge_task_id`,`status`),
  ADD KEY `user_activities_created_by_index` (`created_by`);

ALTER TABLE `user_activity_reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_activity_reviews_user_activity_id_status_index` (`user_activity_id`,`status`),
  ADD KEY `user_activity_reviews_reviewed_by_review_date_index` (`reviewed_by`,`review_date`),
  ADD KEY `user_activity_reviews_status_index` (`status`),
  ADD KEY `user_activity_reviews_review_date_index` (`review_date`),
  ADD KEY `user_activity_reviews_created_by_index` (`created_by`);

ALTER TABLE `user_agreements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_agreements_user_id_agreement_type_index` (`user_id`,`agreement_type`),
  ADD KEY `user_agreements_agreement_type_version_index` (`agreement_type`,`version`),
  ADD KEY `user_agreements_accepted_at_index` (`accepted_at`);

ALTER TABLE `user_avatars`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_avatars_user_id_unique` (`user_id`),
  ADD KEY `user_avatars_avatar_id_index` (`avatar_id`),
  ADD KEY `user_avatars_selected_at_index` (`selected_at`),
  ADD KEY `user_avatars_created_by_index` (`created_by`);

ALTER TABLE `user_books`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_books_user_id_index` (`user_id`),
  ADD KEY `user_books_book_id_index` (`book_id`),
  ADD KEY `user_books_start_date_index` (`start_date`),
  ADD KEY `user_books_end_date_index` (`end_date`),
  ADD KEY `user_books_user_id_book_id_index` (`user_id`,`book_id`),
  ADD KEY `user_book_session_index` (`user_id`,`book_id`,`start_date`),
  ADD KEY `user_books_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_books_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_books_created_by_index` (`created_by`);

ALTER TABLE `user_challenge_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_challenge_task_unique` (`challenge_task_id`,`user_id`,`team_id`),
  ADD KEY `user_challenge_tasks_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_challenge_tasks_user_id_completed_index` (`user_id`,`completed`),
  ADD KEY `user_challenge_tasks_team_id_completed_index` (`team_id`,`completed`),
  ADD KEY `user_challenge_tasks_challenge_task_id_completed_index` (`challenge_task_id`,`completed`),
  ADD KEY `user_challenge_tasks_completed_at_index` (`complete_date`) USING BTREE,
  ADD KEY `user_challenge_tasks_assigned_by_assigned_at_index` (`assigned_by`,`assign_date`) USING BTREE,
  ADD KEY `user_challenge_tasks_created_by_index` (`created_by`);

ALTER TABLE `user_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_classes_unique` (`user_id`,`class_id`,`school_id`),
  ADD KEY `user_classes_class_id_active_index` (`class_id`,`active`),
  ADD KEY `user_classes_school_id_active_index` (`school_id`,`active`),
  ADD KEY `user_classes_user_id_active_index` (`user_id`,`active`),
  ADD KEY `user_classes_active_index` (`active`),
  ADD KEY `user_classes_user_id_default_index` (`user_id`,`default`),
  ADD KEY `user_classes_user_id_active_default_index` (`user_id`,`active`,`default`),
  ADD KEY `user_classes_created_by_index` (`created_by`);

ALTER TABLE `user_goals`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_goal` (`goal_id`,`user_id`),
  ADD UNIQUE KEY `unique_team_goal` (`goal_id`,`team_id`),
  ADD KEY `user_goals_goal_id_user_id_index` (`goal_id`,`user_id`),
  ADD KEY `user_goals_goal_id_team_id_index` (`goal_id`,`team_id`),
  ADD KEY `user_goals_user_id_achieved_index` (`user_id`,`achieved`),
  ADD KEY `user_goals_team_id_achieved_index` (`team_id`,`achieved`),
  ADD KEY `user_goals_assigned_by_index` (`assigned_by`),
  ADD KEY `user_goals_assigned_at_index` (`assign_date`),
  ADD KEY `user_goals_achieved_at_index` (`achieve_date`) USING BTREE,
  ADD KEY `user_goals_created_by_index` (`created_by`);

ALTER TABLE `user_goal_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_goal_task` (`user_goal_id`,`goal_task_id`,`user_id`),
  ADD KEY `user_goal_tasks_user_goal_id_goal_task_id_index` (`user_goal_id`,`goal_task_id`),
  ADD KEY `user_goal_tasks_user_id_completed_index` (`user_id`,`completed`),
  ADD KEY `user_goal_tasks_team_id_completed_index` (`team_id`,`completed`),
  ADD KEY `user_goal_tasks_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_goal_tasks_completed_at_index` (`complete_date`) USING BTREE,
  ADD KEY `user_goal_tasks_created_by_index` (`created_by`);

ALTER TABLE `user_points`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_points_user_id_point_date_index` (`user_id`,`point_date`),
  ADD KEY `user_points_book_id_point_date_index` (`book_id`,`point_date`),
  ADD KEY `user_points_user_id_point_type_index` (`user_id`,`point_type`),
  ADD KEY `user_points_point_type_index` (`point_type`),
  ADD KEY `user_points_point_date_index` (`point_date`),
  ADD KEY `user_points_point_type_source_id_index` (`point_type`,`source_id`),
  ADD KEY `user_points_created_by_index` (`created_by`);

ALTER TABLE `user_reading_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_reading_logs_user_id_log_date_index` (`user_id`,`log_date`),
  ADD KEY `user_reading_logs_book_id_log_date_index` (`book_id`,`log_date`),
  ADD KEY `user_reading_logs_user_id_book_id_index` (`user_id`,`book_id`),
  ADD KEY `user_reading_logs_log_date_index` (`log_date`),
  ADD KEY `user_reading_logs_book_completed_index` (`book_completed`),
  ADD KEY `user_reading_logs_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_reading_logs_goal_task_id_log_date_index` (`goal_task_id`,`log_date`),
  ADD KEY `user_reading_logs_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_reading_logs_challenge_task_id_log_date_index` (`challenge_task_id`,`log_date`),
  ADD KEY `user_reading_logs_created_by_index` (`created_by`);

ALTER TABLE `user_rewards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_reward_unique` (`user_id`,`reward_id`),
  ADD KEY `user_rewards_user_id_reward_id_index` (`user_id`,`reward_id`),
  ADD KEY `user_rewards_user_id_awarded_date_index` (`user_id`,`awarded_date`),
  ADD KEY `user_rewards_reward_id_awarded_date_index` (`reward_id`,`awarded_date`),
  ADD KEY `user_rewards_awarded_by_index` (`awarded_by`),
  ADD KEY `user_rewards_reading_log_id_index` (`reading_log_id`),
  ADD KEY `user_rewards_user_activity_id_index` (`user_activity_id`),
  ADD KEY `user_rewards_awarded_date_index` (`awarded_date`),
  ADD KEY `user_rewards_created_by_index` (`created_by`);

ALTER TABLE `user_schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_schools_unique` (`user_id`,`school_id`,`role_id`),
  ADD KEY `user_schools_role_id_foreign` (`role_id`),
  ADD KEY `user_schools_school_id_role_id_index` (`school_id`,`role_id`),
  ADD KEY `user_schools_user_id_active_index` (`user_id`,`active`),
  ADD KEY `user_schools_active_index` (`active`),
  ADD KEY `user_schools_user_id_default_index` (`user_id`,`default`),
  ADD KEY `user_schools_user_id_active_default_index` (`user_id`,`active`,`default`),
  ADD KEY `user_schools_created_by_index` (`created_by`);

ALTER TABLE `user_teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_teams_team_id_index` (`team_id`),
  ADD KEY `user_teams_user_id_index` (`user_id`),
  ADD KEY `user_teams_team_id_user_id_index` (`team_id`,`user_id`),
  ADD KEY `user_teams_created_by_index` (`created_by`);


ALTER TABLE `activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `activity_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `activity_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `avatars`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_questions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_words`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenges`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `class_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_class_levels`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_school_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_task_cycles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_task_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `goals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `goal_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `page_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `publishers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `reward_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `school_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `task_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `task_book_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `team_rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_activity_reviews`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_agreements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_avatars`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_challenge_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_goals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_goal_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_reading_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;


ALTER TABLE `activities`
  ADD CONSTRAINT `activities_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `activity_categories` (`id`) ON DELETE CASCADE;

ALTER TABLE `authors`
  ADD CONSTRAINT `authors_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `books`
  ADD CONSTRAINT `books_book_type_id_foreign` FOREIGN KEY (`book_type_id`) REFERENCES `book_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `books_publisher_id_foreign` FOREIGN KEY (`publisher_id`) REFERENCES `publishers` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_authors`
  ADD CONSTRAINT `book_authors_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_authors_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_categories`
  ADD CONSTRAINT `book_categories_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_categories_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_questions`
  ADD CONSTRAINT `book_questions_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_questions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `book_words`
  ADD CONSTRAINT `book_words_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_words_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `challenges`
  ADD CONSTRAINT `challenges_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `challenge_classes`
  ADD CONSTRAINT `challenge_classes_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_classes_school_class_id_foreign` FOREIGN KEY (`school_class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_schools`
  ADD CONSTRAINT `challenge_schools_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_schools_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_tasks`
  ADD CONSTRAINT `challenge_tasks_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `challenge_tasks_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `challenge_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_teams`
  ADD CONSTRAINT `challenge_teams_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_teams_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE;

ALTER TABLE `class_books`
  ADD CONSTRAINT `class_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `class_books_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `class_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `goals`
  ADD CONSTRAINT `goals_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `goal_tasks`
  ADD CONSTRAINT `goal_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `goal_tasks_goal_id_foreign` FOREIGN KEY (`goal_id`) REFERENCES `goals` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `goal_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

ALTER TABLE `page_points`
  ADD CONSTRAINT `page_points_book_type_id_foreign` FOREIGN KEY (`book_type_id`) REFERENCES `book_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `page_points_class_level_id_foreign` FOREIGN KEY (`class_level_id`) REFERENCES `enum_class_levels` (`id`) ON DELETE CASCADE;

ALTER TABLE `publishers`
  ADD CONSTRAINT `publishers_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `rewards`
  ADD CONSTRAINT `rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `reward_tasks`
  ADD CONSTRAINT `reward_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `reward_tasks_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reward_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

ALTER TABLE `schools`
  ADD CONSTRAINT `schools_school_type_id_foreign` FOREIGN KEY (`school_type_id`) REFERENCES `enum_school_types` (`id`) ON DELETE SET NULL;

ALTER TABLE `school_classes`
  ADD CONSTRAINT `school_classes_class_level_id_foreign` FOREIGN KEY (`class_level_id`) REFERENCES `enum_class_levels` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `school_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `school_classes_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE;

ALTER TABLE `tasks`
  ADD CONSTRAINT `tasks_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `tasks_task_cycle_id_foreign` FOREIGN KEY (`task_cycle_id`) REFERENCES `enum_task_cycles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `tasks_task_type_id_foreign` FOREIGN KEY (`task_type_id`) REFERENCES `enum_task_types` (`id`) ON DELETE CASCADE;

ALTER TABLE `task_books`
  ADD CONSTRAINT `task_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `task_books_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `task_book_categories`
  ADD CONSTRAINT `task_book_categories_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_book_categories_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `task_book_categories_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `teams`
  ADD CONSTRAINT `teams_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `teams_leader_user_id_foreign` FOREIGN KEY (`leader_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `team_rewards`
  ADD CONSTRAINT `team_rewards_awarded_by_foreign` FOREIGN KEY (`awarded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `team_rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `team_rewards_reading_log_id_foreign` FOREIGN KEY (`reading_log_id`) REFERENCES `user_reading_logs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_activities`
  ADD CONSTRAINT `user_activities_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_activities_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_activities_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activities_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activities_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activities_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_activity_reviews`
  ADD CONSTRAINT `user_activity_reviews_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activity_reviews_reviewed_by_foreign` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activity_reviews_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_agreements`
  ADD CONSTRAINT `user_agreements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_avatars`
  ADD CONSTRAINT `user_avatars_avatar_id_foreign` FOREIGN KEY (`avatar_id`) REFERENCES `avatars` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_avatars_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_avatars_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_books`
  ADD CONSTRAINT `user_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_books_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_books_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_books_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_challenge_tasks`
  ADD CONSTRAINT `user_challenge_tasks_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_challenge_tasks_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_challenge_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_challenge_tasks_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_challenge_tasks_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_classes`
  ADD CONSTRAINT `user_classes_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_classes_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_goals`
  ADD CONSTRAINT `user_goals_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goals_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_goals_goal_id_foreign` FOREIGN KEY (`goal_id`) REFERENCES `goals` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goals_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goals_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_goal_tasks`
  ADD CONSTRAINT `user_goal_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_goal_tasks_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goal_tasks_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goal_tasks_user_goal_id_foreign` FOREIGN KEY (`user_goal_id`) REFERENCES `user_goals` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goal_tasks_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_points`
  ADD CONSTRAINT `user_points_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_points_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_points_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_reading_logs`
  ADD CONSTRAINT `user_reading_logs_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_reading_logs_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_reading_logs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_reading_logs_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_reading_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_rewards`
  ADD CONSTRAINT `user_rewards_awarded_by_foreign` FOREIGN KEY (`awarded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_rewards_reading_log_id_foreign` FOREIGN KEY (`reading_log_id`) REFERENCES `user_reading_logs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_schools`
  ADD CONSTRAINT `user_schools_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_schools_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_schools_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_schools_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_teams`
  ADD CONSTRAINT `user_teams_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_teams_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_teams_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
