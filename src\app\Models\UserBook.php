<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserBook extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'book_id',
        'start_date',
        'end_date',
        'completed',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'completed' => 'boolean',
        ];
    }

    /**
     * Get the user who is reading this book.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book being read.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }


    /**
     * Get the user who created this record.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all reading logs for this user and book combination.
     * Note: This returns all logs for the user-book pair, not filtered by session dates.
     * For session-specific logs, use getSessionReadingLogs() method.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(UserReadingLog::class, 'book_id', 'book_id')
            ->where('user_id', $this->user_id)
            ->orderBy('log_date', 'desc');
    }

    /**
     * Scope to filter books currently in progress.
     */
    public function scopeInProgress($query)
    {
        return $query->whereNull('end_date');
    }

    /**
     * Scope to filter completed books.
     * Updated to use the dedicated 'completed' field instead of 'end_date'.
     */
    public function scopeCompleted($query)
    {
        return $query->where('completed', true);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by completion date range.
     */
    public function scopeCompletedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('end_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get books started in a specific period.
     */
    public function scopeStartedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get the latest session for a user-book combination.
     */
    public function scopeLatestSession($query, $userId, $bookId)
    {
        return $query->where('user_id', $userId)
            ->where('book_id', $bookId)
            ->orderBy('start_date', 'desc');
    }

    /**
     * Scope to get active (in-progress) sessions.
     */
    public function scopeActiveSessions($query)
    {
        return $query->whereNull('end_date');
    }

    /**
     * Scope to get completed sessions.
     */
    public function scopeCompletedSessions($query)
    {
        return $query->whereNotNull('end_date');
    }

    /**
     * Check if the book is currently in progress.
     */
    public function isInProgress(): bool
    {
        return is_null($this->end_date);
    }

    /**
     * Check if the book has been completed.
     * Updated to use the dedicated 'completed' field.
     */
    public function isCompleted(): bool
    {
        return $this->completed;
    }

    /**
     * Get the reading status as a string.
     * Updated to use the dedicated 'completed' field.
     */
    public function getReadingStatusAttribute(): string
    {
        return $this->completed ? 'Completed' : 'In Progress';
    }

    /**
     * Get the localized reading status.
     * Updated to use the dedicated 'completed' field.
     */
    public function getLocalizedReadingStatusAttribute(): string
    {
        return $this->completed ? __('admin.reading_status_completed') : __('admin.reading_status_in_progress');
    }

    /**
     * Get the reading duration in days.
     */
    public function getReadingDuration(): ?int
    {
        if ($this->isInProgress()) {
            // For in-progress books, calculate days since start
            return $this->start_date->diffInDays(now());
        }

        // For completed books, calculate actual reading duration
        return $this->start_date->diffInDays($this->end_date);
    }

    /**
     * Get the reading duration as a formatted string.
     */
    public function getReadingDurationTextAttribute(): string
    {
        $duration = $this->getReadingDuration();
        
        if ($duration === null) {
            return 'N/A';
        }

        if ($duration === 0) {
            return __('admin.same_day');
        }

        if ($duration === 1) {
            return __('admin.one_day');
        }

        return __('admin.days_count', ['count' => $duration]);
    }

    /**
     * Get the display name for the user book session.
     */
    public function getDisplayNameAttribute(): string
    {
        $sessionNumber = $this->getSessionNumber();
        return $this->user->name . ' - ' . $this->book->name . ' (Session ' . $sessionNumber . ' - ' . $this->localized_reading_status . ')';
    }

    /**
     * Apply role-based filtering based on current user's access to users.
     */
    public function scopeForCurrentUser($query)
    {
        return $query->whereHas('user', function ($userQuery) {
            $userQuery->forCurrentUser();
        });
    }

    /**
     * Get summary information for the user book session.
     */
    public function getSummaryAttribute(): string
    {
        $sessionNumber = $this->getSessionNumber();
        $status = $this->localized_reading_status;
        $duration = $this->reading_duration_text;

        return sprintf(
            'Session %d: %s - %s (%s)',
            $sessionNumber,
            $this->book->name,
            $status,
            $duration
        );
    }

    /**
     * Get session information text.
     */
    public function getSessionInfoAttribute(): string
    {
        $sessionNumber = $this->getSessionNumber();
        $startDate = $this->start_date->format('M d, Y');
        $endDate = $this->end_date ? $this->end_date->format('M d, Y') : 'Ongoing';

        return "Session {$sessionNumber}: {$startDate} - {$endDate}";
    }

    /**
     * Get reading history for all sessions of this user-book combination.
     */
    public function getReadingHistoryAttribute(): string
    {
        $allSessions = self::getAllSessions($this->user_id, $this->book_id);
        $history = [];

        foreach ($allSessions as $session) {
            $sessionNumber = $session->getSessionNumber();
            $status = $session->localized_reading_status;
            $duration = $session->reading_duration_text;
            $history[] = "Session {$sessionNumber}: {$status} ({$duration})";
        }

        return implode('; ', $history);
    }

    /**
     * Mark the book as completed.
     * This method sets the end_date but does NOT set completed=true.
     * The completed field should be set based on required activities logic.
     */
    public function markAsCompleted($completionDate = null)
    {
        $this->update([
            'end_date' => $completionDate ?: now()->toDateString()
        ]);
    }

    /**
     * Mark the book as truly completed (considering required activities).
     * This method sets both end_date and completed=true.
     */
    public function markAsTrulyCompleted($completionDate = null)
    {
        $this->update([
            'end_date' => $completionDate ?: now()->toDateString(),
            'completed' => true
        ]);
    }

    /**
     * Mark the book as in progress (remove completion date).
     */
    public function markAsInProgress()
    {
        $this->update([
            'end_date' => null
        ]);
    }

    /**
     * Get the progress percentage based on session-specific reading logs.
     */
    public function getProgressPercentage(): float
    {
        // First check if this session has been marked as completed in reading logs
        if ($this->hasSessionCompletionLog()) {
            return 100.0;
        }

        // If book has no page count or invalid page count, return 0%
        if (!$this->book->page_count || $this->book->page_count <= 0) {
            return 0.0;
        }

        // Sum pages_read from reading logs for this specific session
        $sessionPagesRead = $this->getSessionPagesRead();

        if (!$sessionPagesRead || $sessionPagesRead <= 0) {
            return 0.0;
        }

        // Calculate percentage: (session pages read / book page count) * 100
        $percentage = ($sessionPagesRead / $this->book->page_count) * 100;

        // Cap at 100%
        return min(100.0, $percentage);
    }

    /**
     * Get the progress percentage as a formatted string.
     */
    public function getProgressPercentageTextAttribute(): string
    {
        $percentage = $this->getProgressPercentage();
        return number_format($percentage, 1) . '%';
    }

    /**
     * Check if this session has any reading logs.
     */
    public function hasReadingLogs(): bool
    {
        return $this->hasSessionReadingLogs();
    }

    /**
     * Get the total pages read for this session.
     */
    public function getTotalPagesRead(): int
    {
        return $this->getSessionPagesRead();
    }

    /**
     * Get the total reading time for this session.
     */
    public function getTotalReadingTime(): int
    {
        return $this->getSessionReadingTime();
    }

    /**
     * Get all reading status options for forms.
     */
    public static function getReadingStatusOptions(): array
    {
        return [
            'in_progress' => __('admin.reading_status_in_progress'),
            'completed' => __('admin.reading_status_completed'),
        ];
    }

    /**
     * Get the current active session for a user-book combination.
     */
    public static function getCurrentSession($userId, $bookId): ?self
    {
        return self::where('user_id', $userId)
            ->where('book_id', $bookId)
            ->whereNull('end_date')
            ->orderBy('start_date', 'desc')
            ->first();
    }

    /**
     * Get all sessions for a user-book combination.
     */
    public static function getAllSessions($userId, $bookId)
    {
        return self::where('user_id', $userId)
            ->where('book_id', $bookId)
            ->orderBy('start_date', 'desc')
            ->get();
    }

    /**
     * Check if a user can start a new session for a book.
     */
    public static function canStartNewSession($userId, $bookId): bool
    {
        $activeSession = self::getCurrentSession($userId, $bookId);
        return is_null($activeSession);
    }

    /**
     * Get the session number for this reading session.
     */
    public function getSessionNumber(): int
    {
        return self::where('user_id', $this->user_id)
            ->where('book_id', $this->book_id)
            ->where('start_date', '<=', $this->start_date)
            ->count();
    }

    /**
     * Get reading logs associated with this specific session.
     * Fixed to handle date comparison between timestamp (log_date) and date-only (start_date/end_date).
     */
    public function getSessionReadingLogs()
    {
        // Use DATE() function to compare only the date part of log_date with session dates
        $query = UserReadingLog::where('user_id', $this->user_id)
            ->where('book_id', $this->book_id)
            ->whereRaw('DATE(log_date) >= ?', [$this->start_date]);

        // If session is completed, filter by end date
        if ($this->end_date) {
            $query->whereRaw('DATE(log_date) <= ?', [$this->end_date]);
        }

        return $query->orderBy('log_date', 'asc')->get();
    }

    /**
     * Check if this session has any reading logs.
     */
    public function hasSessionReadingLogs(): bool
    {
        return $this->getSessionReadingLogs()->count() > 0;
    }

    /**
     * Get total pages read in this specific session.
     */
    public function getSessionPagesRead(): int
    {
        return $this->getSessionReadingLogs()->sum('pages_read');
    }

    /**
     * Get total reading time for this specific session.
     */
    public function getSessionReadingTime(): int
    {
        return $this->getSessionReadingLogs()->sum('reading_duration') ?: 0;
    }

    /**
     * Check if this session has been marked as completed in reading logs.
     */
    public function hasSessionCompletionLog(): bool
    {
        return $this->getSessionReadingLogs()
            ->where('book_completed', true)
            ->count() > 0;
    }

    /**
     * Check and complete any UserTask instances that should be marked as completed
     * based on the current book session completion.
     *
     * This method uses the TaskProgressCalculationService to determine if any
     * assigned tasks for this user should be marked as completed based on their
     * current progress including this book completion.
     *
     * @return array Array of UserTask instances that were marked as completed
     */
    public function checkAndCompleteUserTasks(): array
    {
        $service = app(\App\Services\TaskProgressCalculationService::class);
        return $service->checkAndCompleteUserTasks($this->user_id, $this->book_id);
    }
    
    public function createReadingLogsForSession()
    {   
        // create user reading logs for each day of the session splitting book page count to each day, minutes with same count
        $startDate = new Carbon($this->start_date);
        $endDate = new Carbon($this->end_date);
        $duration = $startDate->diffInDays($endDate) + 1;
        $pagesPerDay = ceil($this->book->page_count / $duration);

        for ($i = 0; $i < $duration; $i++) {
            $logDate = $startDate->copy()->addDays($i);
            UserReadingLog::create([
                'user_id' => $this->user_id,
                'book_id' => $this->book_id,
                'log_date' => $logDate,
                'pages_read' => $pagesPerDay,
                'reading_duration' => $pagesPerDay,
                'book_completed' => false,
            ]);
        }
    }   
    
}
