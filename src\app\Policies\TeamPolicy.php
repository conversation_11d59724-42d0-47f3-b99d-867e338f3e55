<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Team;
use App\Models\User;

class TeamPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // System admin, school admin, teacher, and student can view teams
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher() || $user->isStudent();
    }

    public function view(User $user, Team $item): bool
    {
        // System admin can view all
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admin can view teams with members from their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();
            return $item->users()->whereHas('activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            })->exists();
        }

        // Teacher can view teams with members from their classes
        if ($user->isTeacher()) {
            $userClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();
            return $item->users()->whereHas('activeUserClasses', function ($q) use ($userClassIds) {
                $q->whereIn('class_id', $userClassIds);
            })->exists();
        }

        // Student can view teams they belong to
        if ($user->isStudent()) {
            return $item->users()->where('users.id', $user->id)->exists();
        }

        return false;
    }

    public function create(User $user): bool
    {
        // System admin, school admin, and teacher can create teams
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function update(User $user, Team $item): bool
    {
        // System admin can update all
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admin can update teams with members from their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();
            return $item->users()->whereHas('activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            })->exists();
        }

        // Teacher can update teams with members from their classes
        if ($user->isTeacher()) {
            $userClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();
            return $item->users()->whereHas('activeUserClasses', function ($q) use ($userClassIds) {
                $q->whereIn('class_id', $userClassIds);
            })->exists();
        }

        return false;
    }

    public function delete(User $user, Team $item): bool
    {
        return $this->update($user, $item);
    }

    public function restore(User $user, Team $item): bool
    {
        return $this->update($user, $item);
    }

    public function forceDelete(User $user, Team $item): bool
    {
        return $this->update($user, $item);
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }
}
