<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\BookType;
use App\Models\User;

class BookTypePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, BookType $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, BookType $item): bool
    {
        return true;
    }

    public function delete(User $user, BookType $item): bool
    {
        return true;
    }

    public function restore(User $user, BookType $item): bool
    {
        return true;
    }

    public function forceDelete(User $user, BookType $item): bool
    {
        return true;
    }

    public function massDelete(User $user): bool
    {
        return true;
    }
}
