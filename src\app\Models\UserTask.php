<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use App\Services\TaskProgressCalculationService;

class UserTask extends BaseModel
{
    // Task type constants
    const TASK_TYPE_STANDALONE = 0;
    const TASK_TYPE_CHALLENGE = 1;

    protected $fillable = [
        'task_type',
        'challenge_task_id',
        'task_id', // For standalone tasks
        'team_id',
        'class_id',
        'user_id',
        'assigned_by',
        'assign_date',
        'start_date',
        'completed',
        'complete_date',
        'due_date',
        'reward_id',
        'created_by',
    ];

    protected $casts = [
        'task_type' => 'integer',
        'assign_date' => 'datetime',
        'start_date' => 'datetime',
        'completed' => 'boolean',
        'complete_date' => 'datetime',
        'due_date' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // fill task type as standalone if not provided
        // fill assigned_by field as current user if not provided
        // fill assign_date as now() if not provided
        static::creating(function ($userTask) {
            if (!isset($userTask->task_type)) {
                $userTask->task_type = self::TASK_TYPE_STANDALONE;
            }
            if (!isset($userTask->assigned_by)) {
                $userTask->assigned_by = auth()->id();
            }
            if (!isset($userTask->assign_date)) {
                $userTask->assign_date = now();
            }
        });

        // Automatically update completion status based on progress
        static::saving(function ($userTask) {
            if ($userTask->completed && !$userTask->complete_date) {
                $userTask->complete_date = now();
            } elseif (!$userTask->completed) {
                $userTask->complete_date = null;
            }
        });
    }



    /**
     * Get the challenge task that this user task belongs to (for challenge tasks).
     */
    public function challengeTask(): BelongsTo
    {
        return $this->belongsTo(ChallengeTask::class);
    }

    /**
     * Get the team that this user task belongs to (if team assignment).
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the user that this user task belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who assigned this task.
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Get the class that this user task is assigned to (if class assignment).
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the reward associated with this user task (if any).
     */
    public function reward(): BelongsTo
    {
        return $this->belongsTo(Reward::class);
    }

    /**
     * Get the challenge through challenge task (for challenge tasks).
     */
    public function challenge(): BelongsTo
    {
        return $this->belongsTo(Challenge::class, 'challenge_id');
    }

    /**
     * Get the task directly (for standalone tasks) or through relationships.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Get the actual task based on task type.
     */
    public function getActualTask()
    {
        if ($this->isChallengeTask() && $this->challengeTask) {
            return $this->challengeTask->task;
        } elseif ($this->isStandaloneTask() && $this->task) {
            return $this->task;
        }

        return null;
    }

    // ========== SCOPES ==========

    /**
     * Scope to get completed user tasks.
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('completed', true);
    }

    /**
     * Scope to get pending user tasks.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('completed', false);
    }

    /**
     * Scope to get user tasks for a specific user.
     */
    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get user tasks for a specific team.
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }



    /**
     * Scope for challenge tasks only.
     */
    public function scopeChallengeTasks(Builder $query): Builder
    {
        return $query->where('task_type', self::TASK_TYPE_CHALLENGE);
    }

    /**
     * Scope for standalone tasks only.
     */
    public function scopeStandaloneTasks(Builder $query): Builder
    {
        return $query->where('task_type', self::TASK_TYPE_STANDALONE);
    }

    /**
     * Scope for individual assignments (not team).
     */
    public function scopeIndividual(Builder $query): Builder
    {
        return $query->whereNull('team_id');
    }

    /**
     * Scope for team assignments.
     */
    public function scopeTeam(Builder $query): Builder
    {
        return $query->whereNotNull('team_id');
    }

    /**
     * Scope for assignments by user.
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for assignments by team.
     */
    public function scopeByTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    // ========== HELPER METHODS ==========



    /**
     * Check if this is a challenge task.
     */
    public function isChallengeTask(): bool
    {
        return $this->task_type === self::TASK_TYPE_CHALLENGE;
    }

    /**
     * Check if this is a standalone task.
     */
    public function isStandaloneTask(): bool
    {
        return $this->task_type === self::TASK_TYPE_STANDALONE;
    }

    /**
     * Check if this user task is part of a team assignment.
     */
    public function isTeamTask(): bool
    {
        return !is_null($this->team_id);
    }

    /**
     * Check if this user task is part of an individual assignment.
     */
    public function isIndividualTask(): bool
    {
        return is_null($this->team_id) && is_null($this->class_id);
    }

    public function isClassTask(): bool
    {
        return !is_null($this->class_id);        
    }


    /**
     * Mark as completed manually.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'completed' => true,
            'complete_date' => Carbon::now(),
        ]);
    }

    /**
     * Reset completion status.
     */
    public function resetCompletion(): void
    {
        $this->update([
            'completed' => false,
            'complete_date' => null,
        ]);
    }

    /**
     * Mark as pending.
     */
    public function markAsPending(): void
    {
        $this->update([
            'completed' => false,
            'complete_date' => null,
        ]);
    }

    /**
     * Get the current progress for this specific task using TaskProgressCalculationService.
     */
    public function getProgress(): array
    {
        $service = app(TaskProgressCalculationService::class);
        $progress = $service->calculateProgress($this);

        // Convert to legacy format for backward compatibility
        return [
            'completed' => $progress['completed'] ?? false,
            'progress_percentage' => $progress['percentage'] ?? 0.0,
            'current_value' => $progress['current'] ?? 0,
            'target_value' => $progress['target'] ?? 1,
            'periods_completed' => $progress['periods_completed'] ?? 0,
            'total_periods' => $progress['total_periods'] ?? 1,
        ];
    }

    /**
     * Check and update completion status based on actual progress using TaskProgressCalculationService.
     */
    public function checkAndUpdateCompletion(): bool
    {
        if ($this->completed) {
            return false; // Already completed
        }

        $service = app(TaskProgressCalculationService::class);
        return $service->updateTaskCompletion($this);
    }

    /**
     * Get detailed progress information using TaskProgressCalculationService.
     */
    public function getDetailedProgress(): array
    {
        $service = app(TaskProgressCalculationService::class);
        return $service->calculateProgress($this);
    }

    /**
     * Get completion percentage for this specific task.
     */
    public function getCompletionPercentage(): float
    {
        $progress = $this->getProgress();
        return $progress['progress_percentage'];
    }

    /**
     * Get days since assignment.
     */
    public function getDaysSinceAssignmentAttribute(): int
    {
        if (!$this->assign_date) {
            return 0;
        }
        return (int) $this->assign_date->diffInDays(Carbon::now());
    }

    /**
     * Get days to complete (if not completed).
     */
    public function getDaysToCompleteAttribute(): ?int
    {
        if ($this->completed) {
            return null;
        }

        $endDate = null;
        if ($this->isChallengeTask() && $this->challengeTask) {
            $endDate = $this->challengeTask->end_date;
        }

        if (!$endDate) {
            return null;
        }

        $now = Carbon::now()->toDateString();
        if ($endDate < $now) {
            return 0; // Overdue
        }

        return (int) Carbon::parse($now)->diffInDays($endDate);
    }

    /**
     * Check if task is overdue.
     */
    public function isOverdue(): bool
    {
        if ($this->completed) {
            return false;
        }

        $endDate = null;
        if ($this->isChallengeTask() && $this->challengeTask) {
            $endDate = $this->challengeTask->end_date;
        }

        if (!$endDate) {
            return false;
        }

        return $endDate < Carbon::now()->toDateString();
    }

    /**
     * Get progress percentage based on related activities.
     */
    public function getProgressPercentageAttribute(): float
    {
        return $this->getCompletionPercentage();
    }

    /**
     * Get assignee display name.
     */
    public function getAssigneeDisplayAttribute(): string
    {
        return $this->isTeamTask() ? $this->team->name : $this->user->name;
    }

    /**
     * Get progress display.
     */
    public function getProgressDisplayAttribute(): string
    {
        $progress = $this->getProgress();
        return $progress['current_value'] . '/' . $progress['target_value'];
    }

    /**
     * Get status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        if ($this->completed) {
            return __('admin.completed');
        } elseif ($this->isOverdue()) {
            return __('admin.overdue');
        } else {
            return __('admin.in_progress');
        }
    }

    /**
     * Get display name for admin interfaces.
     */
    public function getDisplayNameAttribute(): string
    {
        $taskName = '';
        if ($this->isChallengeTask() && $this->challengeTask) {
            $taskName = $this->challengeTask->task->name;
        } elseif ($this->isStandaloneTask() && $this->task) {
            $taskName = $this->task->name;
        }
        return $taskName;
    }

    /**
     * Apply role-based filtering based on current user's access to users.
     */
    public function scopeForCurrentUser($query)
    {
        return $query->whereHas('user', function ($userQuery) {
            $userQuery->forCurrentUser();
        });
    }

    public function getTaskNameWithTeamOrClassName(): string
    {
        $taskName = $this->getDisplayNameAttribute();    
        if ($this->isTeamTask()) {
            $taskName .= ' ( ' . $this->team->name . ' )';
        } elseif ($this->isClassTask()) {
            $taskName .= ' ( ' . $this->schoolClass->name . ' )';
        }
        return $taskName;
    }   
}
