<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_schools', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('school_id')->constrained('schools')->onDelete('cascade');
            $table->foreignId('role_id')->constrained('roles')->onDelete('cascade');
            $table->boolean('active')->default(true);
            $table->boolean('default')->default(false);
            
            // Add indexes and constraints
            $table->unique(['user_id', 'school_id', 'role_id'], 'user_schools_unique');
            $table->index(['school_id', 'role_id']);
            $table->index(['user_id', 'active']);
            $table->index('active');
            $table->index(['user_id', 'default']);
            $table->index(['user_id', 'active', 'default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_schools');
    }
};
