<div class="min-h-screen bg-base-200">
     <x-mobile-page-header route="{{ route('mobile.books.activities', $book->id) }}" header="{{ __('mobile.upload_activity') }}" />

    <div class="p-4">
        <!-- Activity Info -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
            <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center">
                    <span class="text-3xl">🎨</span>
                </div>

                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-1">{{ $activity->name }}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ $activity->description }}</p>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="mobile-badge">{{ $activity->points }} points</span>
                        <span class="text-gray-500">📖 {!! $book->name !!}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <!-- Upload Form -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <form wire:submit="submitActivity" class="space-y-6">
                <div>
                    @if($mediaFile)
                        <!-- Preview -->
                        <div class="mb-4">
                            @if($activity->media_type === 2)
                                <!-- Audio Preview -->
                                <div class="bg-gray-100 rounded-xl p-4 text-center">
                                    <div class="w-16 h-16 bg-purple-100 rounded-2xl mx-auto mb-2 flex items-center justify-center">
                                        <span class="text-2xl">🎵</span>
                                    </div>
                                    <p class="text-sm text-gray-600">{{ $mediaFile->getClientOriginalName() }}</p>
                                </div>
                            @else
                                <!-- Image Preview -->
                                <img src="{{ $mediaFile->temporaryUrl() }}" class="w-full h-48 object-contain rounded-xl">
                            @endif
                            <button
                                type="button"
                                wire:click="$set('mediaFile', null)"
                                class="mt-2 text-red-500 text-sm hover:text-red-700"
                            >
                                🗑️ Remove file
                            </button>
                        </div>
                    @else
                        <!-- Upload Area -->
                        <div class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-primary transition-colors">
                            <input
                                type="file"
                                id="mediaFile"
                                wire:model="mediaFile"
                                @if($activity->media_type === 1)
                                    accept="image/*"
                                @elseif($activity->media_type === 2)
                                    accept="audio/*,.mp3,.wav,.m4a,.aac"
                                @endif
                                class="hidden"
                            >
                            <label for="mediaFile" class="cursor-pointer">
                                <div class="w-16 h-16 bg-purple-100 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                    @if($activity->media_type === 2)
                                        <span class="text-3xl">🎵</span>
                                    @else
                                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    @endif
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">
                                    @if($activity->media_type === 2)
                                        {{ __('mobile.upload_audio_file') }}
                                    @else
                                        {{ __('mobile.upload_photo') }}
                                    @endif
                                </h4>
                                <p class="text-gray-600">{{ __('mobile.tap_to_select') }}</p>
                                <p class="text-sm text-gray-500 mt-2">
                                    @if($activity->media_type === 2)
                                        {{ __('mobile.mp3_wav_m4a_aac_up_to_10mb') }}
                                    @else
                                        {{ __('mobile.jpg_png_5mb') }}
                                    @endif
                                </p>
                            </label>
                        </div>
                    @endif

                    @error('mediaFile')
                        <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('mobile.describe_artwork') }}
                    </label>
                    <textarea
                        id="description"
                        wire:model="description"
                        class="mobile-input @error('description') border-red-500 @enderror"
                        placeholder="{{ __('mobile.tell_about_artwork') }}"
                        rows="4"
                        maxlength="500"
                    ></textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">{{ strlen($description) }}/500 {{ __('mobile.characters') }}</p>
                </div>

                <!-- Submit Button -->
                @if($mode !== 'view')
                    <button
                        type="submit"
                        class="mobile-button {{ $isLoading || (!$mediaFile && !$existingMediaUrl) ? 'opacity-50 cursor-not-allowed' : '' }}"
                        wire:loading.attr="disabled"
                        {{ (!$mediaFile && !$existingMediaUrl) ? 'disabled' : '' }}
                    >
                        <span wire:loading.remove">
                            @if($mode === 'edit')
                                🔄 {{ __('mobile.update_submission') }}
                            @else
                                🎨 {{ __('mobile.submit_artwork') }}
                            @endif
                        </span>
                    <span wire:loading class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                            {{ $mode === 'edit' ? __('mobile.updating') : __('mobile.uploading') }}
                    </span>
                    </button>

                    @if(!$mediaFile && !$existingMediaUrl)
                        <p class="text-sm text-gray-500 text-center">
                            {{ __('mobile.upload_file_to_submit') }}
                        </p>
                    @endif
                @endif
            </form>
        </div>
    </div>
</div>
