# Student Detail Page - Active Tasks Fix

## Overview

Fixed the `getActiveTasksCards` method in StudentDetailPage that was calling an undefined `getActiveTasks()` method on the User model. The implementation now properly displays active tasks assigned to students with comprehensive task information.

## Implementation Date
October 8, 2025

## Problem Identified

### Error
```
Call to undefined method App\Models\User::getActiveTasks()
```

### Root Cause
- The `StudentDetailPage::getActiveTasksCards()` method was calling `$user->getActiveTasks()`
- The User model did not have this method or the necessary relationship to UserTask
- Missing translation keys for task-related UI elements

## Solution Implemented

### 1. Added User-Task Relationship

#### **File**: `src/app/Models/User.php`

**Added UserTask Relationship:**
```php
/**
 * Get tasks assigned to this user.
 */
public function userTasks(): HasMany
{
    return $this->hasMany(UserTask::class);
}
```

**Added getActiveTasks() Method:**
```php
/**
 * Get active (incomplete) tasks assigned to this user.
 */
public function getActiveTasks()
{
    return $this->userTasks()
        ->with(['task', 'challengeTask.task', 'challengeTask.challenge'])
        ->where('completed', false)
        ->orderBy('assign_date', 'desc')
        ->get()
        ->map(function ($userTask) {
            $actualTask = $userTask->getActualTask();
            if (!$actualTask) {
                return null;
            }

            $progress = $userTask->getDetailedProgress();
            
            return (object) [
                'id' => $userTask->id,
                'name' => $actualTask->name,
                'description' => $actualTask->description ?? '',
                'progress' => $progress['progress_percentage'] . '%',
                'completion_percentage' => $progress['progress_percentage'],
                'assigned_date' => $userTask->assign_date ? $userTask->assign_date->format('d.m.Y') : '',
                'task_type' => $userTask->task_type,
                'is_challenge' => $userTask->isChallengeTask(),
                'is_overdue' => $userTask->isOverdue(),
                'days_to_complete' => $userTask->days_to_complete,
            ];
        })
        ->filter(); // Remove null entries
}
```

### 2. Fixed StudentDetailPage Implementation

#### **File**: `src/app/MoonShine/Pages/Student/StudentDetailPage.php`

**Updated getActiveTasksCards() Method:**
```php
protected function getActiveTasksCards()
{
    $activeTasks = $this->getResource()->getItem()->getActiveTasks();
    
    // if no active tasks, return a message
    if ($activeTasks->count() === 0) {
        return Box::make(__('admin.no_active_tasks'), [
            Text::make('', __('admin.no_active_tasks'))
        ]);
    }
    
    return CardsBuilder::make($activeTasks)
                ->fields([
                    Text::make(__('admin.task_name'), 'name'),
                    Text::make(__('admin.description'), 'description'),
                    Text::make(__('admin.progress'), 'progress'),
                    Text::make(__('admin.completion_percentage'), 'completion_percentage')->badge('primary'),
                    Text::make(__('admin.assigned_date'), 'assigned_date'),
                ]);
}
```

**Added Missing Import:**
```php
use MoonShine\UI\Components\{Layout\Box, Layout\Flex, Layout\Grid, Layout\LineBreak, Tabs\Tab, ActionButton, Card, CardsBuilder, Heading, Tabs};
```

### 3. Added Translation Keys

#### **File**: `src/lang/en/admin.php`
```php
'no_active_tasks' => 'No Active Tasks',
'completion_percentage' => 'Completion %',
```

#### **File**: `src/lang/tr/admin.php`
```php
'no_active_tasks' => 'Aktif Görev Yok',
'completion_percentage' => 'Tamamlanma %',
```

## Technical Details

### Database Relationships Used
- **User → UserTask**: One-to-many relationship for assigned tasks
- **UserTask → Task**: Direct task relationship for standalone tasks
- **UserTask → ChallengeTask → Task**: Indirect task relationship for challenge tasks
- **UserTask → ChallengeTask → Challenge**: Challenge information for challenge tasks

### Task Types Supported
1. **Standalone Tasks** (`TASK_TYPE_STANDALONE = 0`)
   - Direct assignment of individual tasks
   - Uses `task_id` field in UserTask

2. **Challenge Tasks** (`TASK_TYPE_CHALLENGE = 1`)
   - Tasks assigned as part of challenges
   - Uses `challenge_task_id` field in UserTask

### Data Returned by getActiveTasks()
Each active task returns an object with:
- `id`: UserTask ID
- `name`: Task name from actual Task model
- `description`: Task description
- `progress`: Formatted progress percentage (e.g., "75%")
- `completion_percentage`: Numeric progress percentage
- `assigned_date`: Formatted assignment date (d.m.Y)
- `task_type`: Task type constant
- `is_challenge`: Boolean indicating if it's a challenge task
- `is_overdue`: Boolean indicating if task is overdue
- `days_to_complete`: Days remaining to complete (if applicable)

### Progress Calculation
- Uses `TaskProgressCalculationService` for accurate progress calculation
- Considers book completion, activity completion, and other task requirements
- Returns detailed progress information including percentage completion

### Error Handling
- Filters out tasks without valid actual tasks (`getActualTask()` returns null)
- Handles missing task relationships gracefully
- Displays appropriate message when no active tasks exist

## User Experience

### Active Tasks Display
- **Cards Layout**: Tasks displayed as cards with comprehensive information
- **Progress Indicators**: Visual progress percentage with badges
- **Task Details**: Name, description, progress, and assignment date
- **Empty State**: Clear message when no active tasks exist

### Information Hierarchy
- Task name as primary information
- Description for context
- Progress with visual badge
- Assignment date for reference

## Testing Results
- ✅ No more undefined method errors
- ✅ Active tasks display correctly when present
- ✅ Empty state displays properly when no tasks
- ✅ Progress calculations work correctly
- ✅ Both standalone and challenge tasks supported
- ✅ Translation keys work in both languages

## Files Modified
1. `src/app/Models/User.php` - Added userTasks relationship and getActiveTasks method
2. `src/app/MoonShine/Pages/Student/StudentDetailPage.php` - Fixed getActiveTasksCards method and imports
3. `src/lang/en/admin.php` - Added English translations
4. `src/lang/tr/admin.php` - Added Turkish translations

---

**Implementation Status**: ✅ Complete  
**Last Updated**: October 8, 2025  
**Implemented By**: Augment Agent  
**Review Status**: Ready for production use
