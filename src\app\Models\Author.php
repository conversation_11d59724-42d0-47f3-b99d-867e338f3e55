<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Author extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'created_by',
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get books by this author.
     */
    public function books(): BelongsToMany
    {
        return $this->belongsToMany(Book::class, 'book_authors');
    }

    /**
     * Scope to search authors by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%');
    }

    /**
     * Get book count for this author.
     */
    public function getBookCountAttribute(): int
    {
        return $this->books()->count();
    }

    /**
     * Check if author has books.
     */
    public function hasBooks(): bool
    {
        return $this->book_count > 0;
    }

    /**
     * Get the most recent book by this author.
     */
    public function getLatestBookAttribute(): ?Book
    {
        return $this->books()
                    ->orderBy('year_of_publish', 'desc')
                    ->first();
    }

    /**
     * Get books published in a specific year.
     */
    public function booksInYear(int $year)
    {
        return $this->books()->where('year_of_publish', $year);
    }

    /**
     * Get unique publishers this author has worked with.
     */
    public function getPublishersAttribute()
    {
        return Publisher::whereIn('id', 
            $this->books()->distinct()->pluck('publisher_id')
        )->get();
    }
}
