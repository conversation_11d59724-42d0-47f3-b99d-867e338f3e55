<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class ActivityCategory extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
        ];
    }

    /**
     * Get activities in this category.
     */
    public function activities(): HasMany
    {
        return $this->hasMany(Activity::class, 'category_id');
    }

    /**
     * Get active activities in this category.
     */
    public function activeActivities(): HasMany
    {
        return $this->hasMany(Activity::class, 'category_id')->where('active', true);
    }

    /**
     * Scope to filter active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Get the display name for the category.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ($this->active ? '' : ' (Inactive)');
    }

    /**
     * Get the count of activities in this category.
     */
    public function getActivityCountAttribute(): int
    {
        return $this->activities()->count();
    }

    /**
     * Get the count of active activities in this category.
     */
    public function getActiveActivityCountAttribute(): int
    {
        return $this->activeActivities()->count();
    }
}
