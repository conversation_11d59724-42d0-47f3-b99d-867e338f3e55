<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\MessageRecipient;

class MessageDetail extends Component
{
    public $messageRecipient;
    public $message;

    public function mount($messageRecipientId)
    {
        $user = Auth::user();
        
        // Load the message recipient with the message
        $this->messageRecipient = MessageRecipient::with('message')
            ->where('id', $messageRecipientId)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $this->message = $this->messageRecipient->message;

        // Mark as read if not already read
        if (!$this->messageRecipient->read) {
            $this->messageRecipient->markAsRead();
        }
    }

    public function backToMessages()
    {
        return redirect()->route('mobile.messages');
    }

    public function render()
    {
        return view('livewire.mobile.message-detail');
    }
}

