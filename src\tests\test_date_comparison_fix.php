<?php

/**
 * Test Date Comparison Fix for findSessionForLog
 * 
 * This script specifically tests the fix for date comparison between:
 * - UserReadingLog.log_date (timestamp)
 * - UserBook.start_date and end_date (date only)
 */

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Book;
use App\Models\UserBook;
use App\Models\UserReadingLog;
use Carbon\Carbon;

echo "=== DATE COMPARISON FIX TEST ===\n\n";

try {
    // Find a test user and book
    $user = User::first();
    $book = Book::where('active', true)->first();
    
    if (!$user || !$book) {
        echo "❌ No test data available\n";
        exit(1);
    }
    
    echo "📚 Test Setup:\n";
    echo "- User: {$user->name} (ID: {$user->id})\n";
    echo "- Book: {$book->name} (ID: {$book->id})\n\n";
    
    // Clean up any existing data
    echo "🧹 Cleaning up existing test data...\n";
    UserReadingLog::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserBook::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // STEP 1: Create a UserBook session with DATE-ONLY values
    echo "\n📅 STEP 1: Creating UserBook session with date-only values...\n";
    
    $sessionStartDate = '2024-01-15'; // Date only
    $sessionEndDate = '2024-01-17';   // Date only
    
    $userBook = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => $sessionStartDate,
        'end_date' => $sessionEndDate,
    ]);
    
    echo "✅ Created UserBook session:\n";
    echo "   ID: {$userBook->id}\n";
    echo "   Start Date: {$userBook->start_date} (type: " . gettype($userBook->start_date) . ")\n";
    echo "   End Date: {$userBook->end_date} (type: " . gettype($userBook->end_date) . ")\n";
    
    // STEP 2: Create reading logs with TIMESTAMP values
    echo "\n📖 STEP 2: Creating reading logs with timestamp values...\n";
    
    $testCases = [
        [
            'name' => 'Log within session (same day as start)',
            'log_date' => '2024-01-15 10:30:00',
            'expected' => true
        ],
        [
            'name' => 'Log within session (middle day)',
            'log_date' => '2024-01-16 14:45:00',
            'expected' => true
        ],
        [
            'name' => 'Log within session (same day as end)',
            'log_date' => '2024-01-17 20:15:00',
            'expected' => true
        ],
        [
            'name' => 'Log before session',
            'log_date' => '2024-01-14 12:00:00',
            'expected' => false
        ],
        [
            'name' => 'Log after session',
            'log_date' => '2024-01-18 09:00:00',
            'expected' => false
        ]
    ];
    
    $readingLogs = [];
    foreach ($testCases as $index => $testCase) {
        $log = UserReadingLog::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'log_date' => $testCase['log_date'],
            'start_page' => 1 + ($index * 10),
            'end_page' => 10 + ($index * 10),
            'pages_read' => 10,
            'reading_duration' => 30,
            'book_completed' => false,
        ]);
        
        $readingLogs[] = [
            'log' => $log,
            'testCase' => $testCase
        ];
        
        echo "✅ Created {$testCase['name']}:\n";
        echo "   ID: {$log->id}\n";
        echo "   Log Date: {$log->log_date} (type: " . gettype($log->log_date) . ")\n";
        echo "   Expected to find session: " . ($testCase['expected'] ? 'YES' : 'NO') . "\n\n";
    }
    
    // STEP 3: Test the findSessionForLog method
    echo "🔍 STEP 3: Testing findSessionForLog method with date comparison fix...\n\n";
    
    $allPassed = true;
    
    foreach ($readingLogs as $index => $testData) {
        $log = $testData['log'];
        $testCase = $testData['testCase'];
        
        echo "Testing: {$testCase['name']}\n";
        echo "Log Date: {$log->log_date}\n";
        echo "Session Range: {$userBook->start_date} to {$userBook->end_date}\n";
        
        // Test the method
        $foundSession = $log->findSessionForLog();
        
        $actualResult = ($foundSession && $foundSession->id === $userBook->id);
        $expectedResult = $testCase['expected'];
        
        if ($actualResult === $expectedResult) {
            echo "✅ PASS: " . ($actualResult ? "Found session correctly" : "Correctly found no session") . "\n";
        } else {
            echo "❌ FAIL: Expected " . ($expectedResult ? "to find session" : "no session") . 
                 ", but " . ($actualResult ? "found session" : "found no session") . "\n";
            $allPassed = false;
        }
        
        // Show date comparison details
        $logDateOnly = Carbon::parse($log->log_date)->format('Y-m-d');
        $sessionStartDate = Carbon::parse($userBook->start_date)->format('Y-m-d');
        $sessionEndDate = Carbon::parse($userBook->end_date)->format('Y-m-d');
        
        echo "Date Comparison Details:\n";
        echo "  Log Date (date-only): {$logDateOnly}\n";
        echo "  Session Start: {$sessionStartDate}\n";
        echo "  Session End: {$sessionEndDate}\n";
        echo "  Within Range: " . (($logDateOnly >= $sessionStartDate && $logDateOnly <= $sessionEndDate) ? 'YES' : 'NO') . "\n";
        echo "\n";
    }
    
    // STEP 4: Overall results
    echo "📊 STEP 4: Overall Test Results...\n";
    
    if ($allPassed) {
        echo "🎉 ✅ ALL TESTS PASSED!\n";
        echo "✅ Date comparison fix is working correctly\n";
        echo "✅ findSessionForLog properly handles timestamp vs date-only comparison\n";
    } else {
        echo "❌ SOME TESTS FAILED!\n";
        echo "❌ Date comparison fix may need further adjustment\n";
    }
    
    // STEP 5: Test edge cases
    echo "\n🧪 STEP 5: Testing edge cases...\n";
    
    // Test with active session (end_date = null)
    $activeSession = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => '2024-01-20',
        'end_date' => null,
    ]);
    
    $activeLog = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => '2024-01-21 15:30:00',
        'start_page' => 1,
        'end_page' => 10,
        'pages_read' => 10,
        'reading_duration' => 30,
        'book_completed' => false,
    ]);
    
    $foundActiveSession = $activeLog->findSessionForLog();
    
    if ($foundActiveSession && $foundActiveSession->id === $activeSession->id) {
        echo "✅ Active session test PASSED\n";
    } else {
        echo "❌ Active session test FAILED\n";
        $allPassed = false;
    }
    
    echo "\n🎉 Final Result: " . ($allPassed ? "ALL TESTS PASSED! 🎉" : "SOME TESTS FAILED ❌") . "\n";
    
} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
