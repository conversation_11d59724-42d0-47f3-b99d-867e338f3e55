<?php

namespace App\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Spatie\Activitylog\Models\Activity;

/**
 * Service class for querying authentication activity logs.
 * 
 * This service provides convenient methods to retrieve and analyze
 * authentication-related activities logged by the LogAuthEvent listener.
 */
class AuthActivityLogService
{
    /**
     * Check if the activity_log table has timestamp columns.
     *
     * @return bool
     */
    private function hasTimestamps(): bool
    {
        try {
            Activity::whereNotNull('created_at')->limit(1)->get();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    /**
     * Get the last login activity for a specific user.
     * 
     * @param User $user
     * @return Activity|null
     */
    public function getLastLogin(User $user): ?Activity
    {
        return Activity::forSubject($user)
            ->forEvent('login')
            ->latest()
            ->first();
    }

    /**
     * Get the last logout activity for a specific user.
     * 
     * @param User $user
     * @return Activity|null
     */
    public function getLastLogout(User $user): ?Activity
    {
        return Activity::forSubject($user)
            ->whereIn('event', ['logout', 'current-device-logout', 'other-device-logout'])
            ->latest()
            ->first();
    }

    /**
     * Get all failed login attempts for a specific user.
     * 
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    public function getFailedLoginAttempts(User $user, int $limit = 10): Collection
    {
        return Activity::forSubject($user)
            ->forEvent('failed')
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent failed login attempts by IP address.
     *
     * @param string $ipAddress
     * @param int $hours
     * @return Collection
     */
    public function getRecentFailedAttemptsByIp(string $ipAddress, int $hours = 24): Collection
    {
        $query = Activity::where('log_name', 'auth')
            ->where('event', 'failed')
            ->where('properties->ip_address', $ipAddress);

        if ($this->hasTimestamps()) {
            $query->where('created_at', '>=', Carbon::now()->subHours($hours));
        }

        return $query->latest('id')->get();
    }

    /**
     * Get all authentication activities for a specific user.
     * 
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    public function getUserAuthActivities(User $user, int $limit = 50): Collection
    {
        return Activity::forSubject($user)
            ->where('log_name', 'auth')
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get authentication activities within a date range.
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int $limit
     * @return Collection
     */
    public function getAuthActivitiesByDateRange(Carbon $startDate, Carbon $endDate, int $limit = 100): Collection
    {
        $query = Activity::where('log_name', 'auth');

        // Only add date filtering if created_at column exists
        if ($this->hasTimestamps()) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return $query->latest('id')->limit($limit)->get();
    }

    /**
     * Get login statistics for a specific user.
     *
     * @param User $user
     * @param int $days
     * @return array
     */
    public function getUserLoginStats(User $user, int $days = 30): array
    {
        $query = Activity::forSubject($user)->where('log_name', 'auth');

        if ($this->hasTimestamps()) {
            $startDate = Carbon::now()->subDays($days);
            $query->where('created_at', '>=', $startDate);
        }

        $activities = $query->get();

        $logins = $activities->where('event', 'login');
        $failures = $activities->where('event', 'failed');
        $logouts = $activities->whereIn('event', ['logout', 'current-device-logout', 'other-device-logout']);

        return [
            'total_logins' => $logins->count(),
            'total_failures' => $failures->count(),
            'total_logouts' => $logouts->count(),
            'success_rate' => $logins->count() > 0 ? 
                round(($logins->count() / ($logins->count() + $failures->count())) * 100, 2) : 0,
            'last_login' => $logins->first()?->created_at,
            'last_logout' => $logouts->first()?->created_at,
            'unique_ips' => $activities->pluck('properties.ip_address')->unique()->filter()->count(),
            'period_days' => $days,
        ];
    }

    /**
     * Get system-wide authentication statistics.
     *
     * @param int $days
     * @return array
     */
    public function getSystemAuthStats(int $days = 30): array
    {
        $query = Activity::where('log_name', 'auth');

        if ($this->hasTimestamps()) {
            $startDate = Carbon::now()->subDays($days);
            $query->where('created_at', '>=', $startDate);
        }

        $activities = $query->get();

        $logins = $activities->where('event', 'login');
        $failures = $activities->where('event', 'failed');
        $registrations = $activities->where('event', 'registered');

        return [
            'total_logins' => $logins->count(),
            'total_failures' => $failures->count(),
            'total_registrations' => $registrations->count(),
            'unique_users' => $activities->pluck('subject_id')->unique()->filter()->count(),
            'unique_ips' => $activities->pluck('properties.ip_address')->unique()->filter()->count(),
            'success_rate' => $logins->count() > 0 ? 
                round(($logins->count() / ($logins->count() + $failures->count())) * 100, 2) : 0,
            'period_days' => $days,
            'daily_average_logins' => round($logins->count() / $days, 2),
        ];
    }

    /**
     * Check if a user has suspicious login activity.
     *
     * @param User $user
     * @param int $hours
     * @param int $threshold
     * @return bool
     */
    public function hasSuspiciousActivity(User $user, int $hours = 24, int $threshold = 5): bool
    {
        $query = Activity::forSubject($user)
            ->where('log_name', 'auth')
            ->where('event', 'failed');

        if ($this->hasTimestamps()) {
            $query->where('created_at', '>=', Carbon::now()->subHours($hours));
        }

        $recentFailures = $query->count();

        return $recentFailures >= $threshold;
    }

    /**
     * Get concurrent login sessions for a user.
     *
     * @param User $user
     * @param int $hours
     * @return Collection
     */
    public function getConcurrentSessions(User $user, int $hours = 24): Collection
    {
        $query = Activity::forSubject($user)
            ->where('log_name', 'auth')
            ->where('event', 'login');

        if ($this->hasTimestamps()) {
            $startTime = Carbon::now()->subHours($hours);
            $query->where('created_at', '>=', $startTime);
        }

        return $query->get()
            ->groupBy('properties.ip_address')
            ->filter(function ($sessions) {
                return $sessions->count() > 1;
            })
            ->flatten();
    }

    /**
     * Get authentication events by guard.
     * 
     * @param string $guard
     * @param int $limit
     * @return Collection
     */
    public function getAuthEventsByGuard(string $guard, int $limit = 50): Collection
    {
        return Activity::where('log_name', 'auth')
            ->where('properties->guard', $guard)
            ->latest()
            ->limit($limit)
            ->get();
    }
}
