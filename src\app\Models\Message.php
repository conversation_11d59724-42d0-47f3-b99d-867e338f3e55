<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Auth;

class Message extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'message',
        'message_date',
        'default',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'message_date' => 'datetime',
            'default' => 'boolean',
        ];
    }

    /**
     * Get the message recipients for this message.
     */
    public function messageRecipients(): Has<PERSON><PERSON>
    {
        return $this->hasMany(MessageRecipient::class);
    }

    /**
     * Get the users who received this message.
     */
    public function recipients(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'message_recipients')
            ->withPivot('read', 'sent_date', 'read_date');
    }

    /**
     * Get unread recipients count.
     */
    public function getUnreadCountAttribute(): int
    {
        return $this->messageRecipients()->where('read', false)->count();
    }

    /**
     * Get read recipients count.
     */
    public function getReadCountAttribute(): int
    {
        return $this->messageRecipients()->where('read', true)->count();
    }

    /**
     * Get total recipients count.
     */
    public function getTotalRecipientsAttribute(): int
    {
        return $this->messageRecipients()->count();
    }

    /**
     * Scope to get default messages (auto-assigned to new users).
     */
    public function scopeDefault($query)
    {
        return $query->where('default', true);
    }

    /**
     * Scope to get non-default messages.
     */
    public function scopeNonDefault($query)
    {
        return $query->where('default', false);
    }

    /**
     * Assign this message to a user.
     */
    public function assignToUser(User $user): MessageRecipient
    {
        return MessageRecipient::create([
            'message_id' => $this->id,
            'user_id' => $user->id,
            'read' => false,
            'sent_date' => now(),
        ]);
    }

    /**
     * Assign this message to multiple users.
     */
    public function assignToUsers(array $userIds): void
    {
        foreach ($userIds as $userId) {
            MessageRecipient::firstOrCreate(
                [
                    'message_id' => $this->id,
                    'user_id' => $userId,
                ],
                [
                    'read' => false,
                    'sent_date' => now(),
                ]
            );
        }
    }
}

