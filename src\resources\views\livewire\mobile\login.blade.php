<div class="min-h-screen bg-purple-300 flex flex-col justify-center p-6" x-data="loginForm()">
    <!-- App <PERSON> and Brand -->
    <div class="text-center mb-6">
        <div class="flex items-center justify-center mb-6">
            <img src="/images/logo.png" alt="{{ __('mobile.app_name') }}" class="w-2/3">
        </div>
    </div>

    <!-- Step 1: Username Selection -->
    <div x-show="$wire.currentStep === 1" class="max-w-sm mx-auto w-full space-y-6">
        <!-- Previous Users Cards -->
        <template x-for="(username, index) in previousUsernames.slice(0, 3)" :key="username">
            <button
                type="button"
                @click="$wire.selectUsername(username)"
                class="w-full bg-purple-200 bg-opacity-90 rounded-2xl p-4 hover:bg-opacity-100 transition-all duration-200 flex items-center justify-between"
            >
                <span class="text-gray-700 font-medium" x-text="username"></span>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </template>

        <!-- OR Separator (only show if there are previous users) -->
        <div x-show="previousUsernames.length > 0" class="text-center">
            <span class="text-purple-900 font-semibold text-lg">{{ __('mobile.or') }}</span>
        </div>

        <!-- Manual Username Input -->
        <div class="space-y-4">
            <input
                type="text"
                wire:model="username"
                wire:keydown.enter="proceedToPasswordStep" 
                placeholder="{{ __('mobile.enter_username') }}"
                class="w-full bg-white bg-opacity-90 rounded-2xl p-4 text-gray-700 placeholder-gray-500 border-0 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:bg-white"
                @error('username') style="border: 2px solid #ef4444;" @enderror
            >

            @error('username')
                <p class="text-red-600 text-sm">{{ $message }}</p>
            @enderror

            <!-- NEXT Button -->
            <button
                type="button"
                wire:click="proceedToPasswordStep"
                class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-4 rounded-2xl transition-colors duration-200"
            >
                {{ __('mobile.next') }}
            </button>
        </div>
    </div>

    <!-- Step 2: Password Entry -->
    <div x-show="$wire.currentStep === 2" class="max-w-sm mx-auto w-full">
        <form wire:submit="login" class="space-y-6">
            <!-- Password Field -->
            <div class="space-y-4">
                <p class="text-purple-900 font-medium">{{ __('mobile.your_password') }}</p>

                <div class="relative">
                    <input 
                        type="{{ $showPassword ? 'text' : 'password' }}"
                        wire:model="password"
                        placeholder="••••••••"
                        class="w-full bg-white bg-opacity-90 rounded-2xl p-4 pr-12 text-gray-700 placeholder-gray-400 border-0 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:bg-white"
                        @error('password') style="border: 2px solid #ef4444;" @enderror
                        autocomplete="current-password" 
                        wire:keydown.enter="login"
                    >
                </div>

                @error('password')
                    <p class="text-red-600 text-sm">{{ $message }}</p>
                @enderror
            </div>

            <!-- Error Message -->
            @if($errorMessage)
                <div class="bg-red-100 text-red-700 px-4 py-3 rounded-xl">
                    <p class="text-sm">{{ $errorMessage }}</p>
                </div>
            @endif

            <!-- Session Messages -->
            @if(session('info'))
                <div class="bg-blue-100 text-blue-700 px-4 py-3 rounded-xl">
                    <p class="text-sm">{{ session('info') }}</p>
                </div>
            @endif

            <!-- Login Button -->
            <button
                type="submit"
                class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-4 rounded-2xl transition-colors duration-200 {{ $isLoading ? 'opacity-75' : '' }}"
                wire:loading.attr="disabled"
            >
                <span wire:loading.remove>{{ __('mobile.login_button') }}</span>
                <span wire:loading class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    {{ __('mobile.logging_in') }}
                </span>
            </button>

            <!-- Remember Me -->
            <div class="flex">
                <label class="flex items-center">
                    <input
                        type="checkbox"
                        wire:model="remember"
                        class="w-5 h-5 text-purple-600 bg-purple-200 bg-opacity-90 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                    >
                    <span class="ml-3 text-purple-900 font-medium">{{ __('mobile.remember_me') }}</span>
                </label>
            </div>

            <!-- Back Button -->
            <div class="text-center">
                <button
                    type="button"
                    wire:click="goBackToUsernameStep"
                    class="text-purple-900 font-medium hover:text-purple-700 transition-colors"
                >
                    {{ __('mobile.back_to_username') }}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function loginForm() {
    return {
        previousUsernames: [],

        init() {
            this.loadPreviousUsernames();

            // Listen for store username event
            this.$wire.on('store-username', (event) => {
                this.storeUsername(event.username);
            });
        },

        loadPreviousUsernames() {
            const stored = localStorage.getItem('mobile_usernames');
            this.previousUsernames = stored ? JSON.parse(stored) : [];

            // Send to Livewire component
            this.$wire.call('loadPreviousUsernames', this.previousUsernames);
        },

        storeUsername(username) {
            if (!username) return;

            let usernames = this.previousUsernames.slice();

            // Remove if already exists
            usernames = usernames.filter(u => u !== username);

            // Add to beginning
            usernames.unshift(username);

            // Keep only last 3
            usernames = usernames.slice(0, 3);

            // Store in localStorage
            localStorage.setItem('mobile_usernames', JSON.stringify(usernames));

            // Update local array
            this.previousUsernames = usernames;
        }
    }
}
</script>
