<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('books', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('isbn')->unique();
            $table->foreignId('publisher_id')->nullable()->constrained('publishers')->onDelete('cascade');
            $table->foreignId('book_type_id')->nullable()->constrained('book_types')->onDelete('cascade');
            $table->integer('page_count')->nullable();
            $table->year('year_of_publish')->nullable();
            $table->string('cover_image')->nullable(); // Path to cover image
            $table->boolean('active')->default(true);

            // Add indexes
            $table->index('name');
            $table->index('publisher_id');
            $table->index('book_type_id');
            $table->index('year_of_publish');
            $table->index('active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('books');
    }
};
