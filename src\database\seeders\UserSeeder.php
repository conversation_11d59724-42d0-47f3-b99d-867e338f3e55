<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // system_admin
        $systemAdmin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'admin',
                'name' => 'system_admin',
                'title' => 'System Admin',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $systemAdmin->assignRole('system_admin');

        // School Admin
        $schoolAdmin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'schooladmin',
                'name' => 'Ahmet Yılmaz',
                'title' => 'School Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $schoolAdmin->assignRole('school_admin');

        // Teachers
        $teachers = [
            [
                'username' => 'teacher1',
                'name' => '<PERSON><PERSON>',
                'title' => 'Turkish Language Teacher',
                'email' => '<EMAIL>',
            ],
            [
                'username' => 'teacher2',
                'name' => 'Mehmet Kaya',
                'title' => 'Literature Teacher',
                'email' => '<EMAIL>',
            ],
            [
                'username' => 'teacher3',
                'name' => 'Ayşe Özkan',
                'title' => 'Primary School Teacher',
                'email' => '<EMAIL>',
            ],
            [
                'username' => 'teacher4',
                'name' => 'Ali Çelik',
                'title' => 'Reading Specialist',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($teachers as $teacherData) {
            $teacher = User::updateOrCreate(
                ['email' => $teacherData['email']],
                [
                    'username' => $teacherData['username'],
                    'name' => $teacherData['name'],
                    'title' => $teacherData['title'],
                    'password' => Hash::make('password'),
                    'email_verified_at' => now(),
                ]
            );
            $teacher->assignRole('teacher');
        }

        // Students
        $students = [
            ['username' => 'student1', 'name' => 'Zeynep Aktaş', 'email' => '<EMAIL>'],
            ['username' => 'student2', 'name' => 'Emre Yıldız', 'email' => '<EMAIL>'],
            ['username' => 'student3', 'name' => 'Selin Koç', 'email' => '<EMAIL>'],
            ['username' => 'student4', 'name' => 'Burak Şahin', 'email' => '<EMAIL>'],
            ['username' => 'student5', 'name' => 'Elif Arslan', 'email' => '<EMAIL>'],
            ['username' => 'student6', 'name' => 'Cem Doğan', 'email' => '<EMAIL>'],
            ['username' => 'student7', 'name' => 'Nisa Güler', 'email' => '<EMAIL>'],
            ['username' => 'student8', 'name' => 'Kaan Özdemir', 'email' => '<EMAIL>'],
            ['username' => 'student9', 'name' => 'Dila Aydın', 'email' => '<EMAIL>'],
            ['username' => 'student10', 'name' => 'Arda Polat', 'email' => '<EMAIL>'],
            ['username' => 'student11', 'name' => 'İrem Çakır', 'email' => '<EMAIL>'],
            ['username' => 'student12', 'name' => 'Emir Tunç', 'email' => '<EMAIL>'],
            ['username' => 'student13', 'name' => 'Lara Öztürk', 'email' => '<EMAIL>'],
            ['username' => 'student14', 'name' => 'Berat Kılıç', 'email' => '<EMAIL>'],
            ['username' => 'student15', 'name' => 'Ela Yaman', 'email' => '<EMAIL>'],
            ['username' => 'student16', 'name' => 'Deniz Erdoğan', 'email' => '<EMAIL>'],
            ['username' => 'student17', 'name' => 'Mira Başak', 'email' => '<EMAIL>'],
            ['username' => 'student18', 'name' => 'Yusuf Karaca', 'email' => '<EMAIL>'],
            ['username' => 'student19', 'name' => 'Defne Çetin', 'email' => '<EMAIL>'],
            ['username' => 'student20', 'name' => 'Eren Güneş', 'email' => '<EMAIL>'],
        ];

        foreach ($students as $studentData) {
            $student = User::updateOrCreate(
                ['email' => $studentData['email']],
                [
                    'username' => $studentData['username'],
                    'name' => $studentData['name'],
                    'title' => 'Student',
                    'password' => Hash::make('password'),
                    'email_verified_at' => now(),
                ]
            );
            $student->assignRole('student');
        }

        $this->command->info('Users seeded successfully!');
    }
}
