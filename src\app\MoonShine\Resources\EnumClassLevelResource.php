<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\EnumClassLevel;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\Support\Attributes\Icon;

#[Icon('tag')]
class EnumClassLevelResource extends BaseResource
{
    protected string $model = EnumClassLevel::class;

    protected string $column = 'name';

    public function getTitle(): string
    {
        return __('admin.class_levels');
    }

    public function indexFields(): array
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),
        ];
    }

    public function formFields(): array
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),
            ]),
        ];
    }

    public function detailFields(): array
    {
        return [
            Text::make(__('admin.name'), 'name'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'unique:enum_class_levels,name,' . $item?->id],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
