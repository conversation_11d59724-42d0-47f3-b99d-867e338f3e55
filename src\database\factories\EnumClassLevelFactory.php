<?php

namespace Database\Factories;

use App\Models\EnumClassLevel;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EnumClassLevel>
 */
class EnumClassLevelFactory extends Factory
{
    protected $model = EnumClassLevel::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->unique()->randomElement(['1. Sınıf', '2. Sınıf', '3. Sınıf', '4. Sını<PERSON>', '5. <PERSON>ını<PERSON>']) . ' ' . fake()->unique()->numberBetween(1, 1000),
        ];
    }
}
