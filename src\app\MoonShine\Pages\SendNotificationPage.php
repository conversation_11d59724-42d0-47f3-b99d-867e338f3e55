<?php

declare(strict_types=1);

namespace App\MoonShine\Pages;

use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use MoonShine\Laravel\Pages\Page;
use MoonShine\UI\Components\FormBuilder;

use App\Models\{SchoolClass, Team, User};
use MoonShine\UI\Fields\{Select, Text, Textarea, Url};

class SendNotificationPage extends Page
{
    /**
     * @return array<string, string>
     */
    public function getBreadcrumbs(): array
    {
        return [
            '#' => $this->getTitle()
        ];
    }

    public function getTitle(): string
    {
        return __('Send Push Notification');
    }

    /**
     * @return list<MoonShineComponent>
     */
    protected function components(): iterable
    {
        return [
            FormBuilder::make('/admin-send-notification')
                ->fields([
                    Select::make('Recipient Type', 'recipient_type')
                        ->options([
                            'user' => 'Individual User',
                            'class' => 'School Class',
                            'team' => 'Team',
                            'all' => 'All Users'
                        ])
                        ->required(),

                    Select::make('User', 'user_id')
                        ->options(function () {
                            return User::whereNotNull('fcm_token')
                                ->pluck('name', 'id')
                                ->toArray();
                        })
                        ->showWhen('recipient_type', 'user')
                        ->required(),

                    Select::make('School Class', 'class_id')
                        ->options(function () {
                            return SchoolClass::where('active', true)
                                ->pluck('name', 'id')
                                ->toArray();
                        })
                        ->showWhen('recipient_type', 'class')
                        ->required(),

                    Select::make('Team', 'team_id')
                        ->options(function () {
                            return Team::where('active', true)
                                ->pluck('name', 'id')
                                ->toArray();
                        })
                        ->showWhen('recipient_type', 'team')
                        ->required(),

                    Text::make('Title', 'title')
                        ->required()
                        ->placeholder('Enter notification title'),

                    Textarea::make('Message', 'body')
                        ->required()
                        ->placeholder('Enter notification message'),

                    Select::make('Notification Type', 'type')
                        ->options([
                            'admin_broadcast' => 'Admin Broadcast',
                            'general' => 'General',
                            'announcement' => 'Announcement',
                            'reminder' => 'Reminder'
                        ])
                        ->default('admin_broadcast'),

                    Url::make('Deep Link URL', 'deep_link_url')
                        ->placeholder('Optional: /books, /home, etc.')
                ])
                ->submit('Send Notification', [
                    'class' => 'btn btn-primary'
                ])
        ];
    }

    public function sendNotification(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'recipient_type' => 'required|in:user,class,team,all',
            'user_id' => 'required_if:recipient_type,user|exists:users,id',
            'class_id' => 'required_if:recipient_type,class|exists:school_classes,id',
            'team_id' => 'required_if:recipient_type,team|exists:teams,id',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:1000',
            'type' => 'required|string',
            'deep_link_url' => 'nullable|string|max:255'
        ]);

        $fcmService = app(FCMService::class);
        
        try {
            $recipientType = $request->input('recipient_type');
            $title = $request->input('title');
            $body = $request->input('body');
            $type = $request->input('type');
            $deepLinkUrl = $request->input('deep_link_url');

            $successCount = 0;
            $totalCount = 0;

            switch ($recipientType) {
                case 'user':
                    $user = User::findOrFail($request->input('user_id'));
                    $success = $fcmService->sendToUser($user, $title, $body, $type, $deepLinkUrl);
                    $successCount = $success ? 1 : 0;
                    $totalCount = 1;
                    break;

                case 'class':
                    $class = SchoolClass::findOrFail($request->input('class_id'));
                    $results = $fcmService->sendToClass($class, $title, $body, $type, $deepLinkUrl);
                    $successCount = array_sum($results);
                    $totalCount = count($results);
                    break;

                case 'team':
                    $team = Team::findOrFail($request->input('team_id'));
                    $results = $fcmService->sendToTeam($team, $title, $body, $type, $deepLinkUrl);
                    $successCount = array_sum($results);
                    $totalCount = count($results);
                    break;

                case 'all':
                    $users = User::whereNotNull('fcm_token')->get();
                    $results = $fcmService->sendToUsers($users, $title, $body, $type, $deepLinkUrl);
                    $successCount = array_sum($results);
                    $totalCount = count($results);
                    break;
            }

            if ($successCount > 0) {
                $message = "Notification sent successfully to {$successCount}";
                if ($totalCount > $successCount) {
                    $message .= " out of {$totalCount}";
                }
                $message .= " recipients.";
                
                return back()->with('success', $message);
            } else {
                return back()->with('error', 'Failed to send notification to any recipients.');
            }

        } catch (\Exception $e) {
            Log::error('Failed to send FCM notification from admin panel: ' . $e->getMessage());
            return back()->with('error', 'Error sending notification: ' . $e->getMessage());
        }
    }
}
