<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Reward;
use App\Models\User;

class RewardPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Reward $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, Reward $item): bool
    {
        return true;
    }

    public function delete(User $user, Reward $item): bool
    {
        return true;
    }

    public function restore(User $user, Reward $item): bool
    {
        return true;
    }

    public function forceDelete(User $user, Reward $item): bool
    {
        return true;
    }

    public function massDelete(User $user): bool
    {
        return true;
    }
}
