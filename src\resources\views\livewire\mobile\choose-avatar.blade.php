<div class="min-h-screen bg-base-200">
     <x-mobile-page-header route="{{ route('mobile.me') }}" header="{{ __('mobile.select_avatar') . ' (' . $userActivityPoints . ' ' . __('mobile.points') . ')' }}" />

    <!-- Main Content -->
    <div class="p-4 space-y-6">

        <!-- Flash Messages -->
        @if (session()->has('success'))
            <div class="bg-green-100 text-green-700 px-4 py-3 rounded-xl">
                {{ session('success') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="bg-red-100 text-red-700 px-4 py-3 rounded-xl">
                {{ session('error') }}
            </div>
        @endif

        <!-- Available Avatars Section -->
        @if($availableAvatars->count() > 0)
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h2 class="text-lg font-bold text-gray-900 mb-4">{{ __('mobile.available_avatars') }}</h2>
                
                <div class="grid grid-cols-2 gap-4">
                    @foreach($availableAvatars as $avatar)
                        <div class="text-center">
                            <!-- Avatar Card -->
                            <div class="relative">
                                <button 
                                    wire:click="selectAvatar({{ $avatar->id }})"
                                    class="w-full bg-gray-50 rounded-2xl p-4 hover:bg-gray-100 transition-colors {{ $currentAvatar && $currentAvatar->id === $avatar->id ? 'ring-2 ring-violet-500' : '' }}"
                                >
                                    <!-- Avatar Image -->
                                    <div class="w-20 h-20 mx-auto mb-3 rounded-full overflow-hidden border-2 border-gray-200">
                                        @if($avatar->base_image)
                                            <img src="{{ asset('storage/' . $avatar->base_image) }}" 
                                                 alt="{{ $avatar->name }}" 
                                                 class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full bg-orange-400 flex items-center justify-center">
                                                <span class="text-white text-xl font-bold">{{ substr($avatar->name, 0, 1) }}</span>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Avatar Name -->
                                    <h3 class="font-semibold text-gray-900 text-sm mb-1">{{ $avatar->name }}</h3>
                                    
                                    <!-- Points Required -->
                                    <p class="text-xs text-gray-600">
                                        ({{ $avatar->required_points }} {{ __('mobile.points') }})
                                    </p>
                                </button>

                                <!-- Selected Indicator -->
                                @if($currentAvatar && $currentAvatar->id === $avatar->id)
                                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-violet-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Unlockable Avatars Section -->
        @if($lockedAvatars->count() > 0)
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h2 class="text-lg font-bold text-gray-900 mb-4">{{ __('mobile.unlockable_avatars') }}</h2>
                
                <div class="grid grid-cols-2 gap-4">
                    @foreach($lockedAvatars as $avatar)
                        <div class="text-center">
                            <!-- Locked Avatar Card -->
                            <div class="relative">
                                <div class="w-full bg-gray-50 rounded-2xl p-4 opacity-60">
                                    <!-- Avatar Image (Grayed Out) -->
                                    <div class="w-20 h-20 mx-auto mb-3 rounded-full overflow-hidden border-2 border-gray-200 relative">
                                        @if($avatar->base_image)
                                            <img src="{{ asset('storage/' . $avatar->base_image) }}" 
                                                 alt="{{ $avatar->name }}" 
                                                 class="w-full h-full object-cover grayscale">
                                        @else
                                            <div class="w-full h-full bg-gray-400 flex items-center justify-center">
                                                <span class="text-white text-xl font-bold">{{ substr($avatar->name, 0, 1) }}</span>
                                            </div>
                                        @endif
                                        
                                        <!-- Lock Icon -->
                                        <div class="absolute inset-0 flex items-center justify-center bg-black/20">
                                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <!-- Avatar Name -->
                                    <h3 class="font-semibold text-gray-700 text-sm mb-1">{{ $avatar->name }}</h3>
                                    
                                    <!-- Points Required -->
                                    <p class="text-xs text-gray-500">
                                        ({{ $avatar->required_points }} {{ __('mobile.points') }})
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- No Avatars Message -->
        @if($availableAvatars->count() === 0 && $lockedAvatars->count() === 0)
            <div class="bg-white rounded-2xl p-8 shadow-sm text-center">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <p class="text-gray-500">{{ __('mobile.no_avatars_available') }}</p>
            </div>
        @endif

    </div>
</div>

<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('avatar-selected', () => {
            setTimeout(() => {
                window.location.href = '{{ route("mobile.me") }}';
            }, 1500);
        });
    });
</script>
