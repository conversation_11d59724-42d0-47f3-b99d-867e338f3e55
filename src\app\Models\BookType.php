<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class BookType extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'thumbnail',
    ];

    /**
     * Get schools that have this type.
     */
    public function books(): HasMany
    {
        return $this->hasMany(Book::class, 'book_type_id');
    }
    
    public function pagePoints(): HasMany
    {
        return $this->hasMany(PagePoint::class);
    }

}
