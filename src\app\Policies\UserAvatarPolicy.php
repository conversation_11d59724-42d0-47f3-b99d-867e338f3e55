<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\UserAvatar;
use App\Models\User;

class UserAvatarPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // System admin, school admin, teacher, and student can view user avatars
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher() || $user->isStudent();
    }

    public function view(User $user, UserAvatar $item): bool
    {
        // System admin can view all
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admin can view avatars for students in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();
            return $item->user->activeUserClasses()->whereIn('school_id', $userSchoolIds)->exists();
        }

        // Teacher can view avatars for students in their classes
        if ($user->isTeacher()) {
            $userClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();
            return $item->user->activeUserClasses()->whereIn('class_id', $userClassIds)->exists();
        }

        // Student can only view their own avatars
        if ($user->isStudent()) {
            return $item->user_id === $user->id;
        }

        return false;
    }

    public function create(User $user): bool
    {
        // System admin, school admin, teacher, and student can create user avatars
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher() || $user->isStudent();
    }

    public function update(User $user, UserAvatar $item): bool
    {
        return $this->view($user, $item);
    }

    public function delete(User $user, UserAvatar $item): bool
    {
        return $this->view($user, $item);
    }

    public function restore(User $user, UserAvatar $item): bool
    {
        return $this->view($user, $item);
    }

    public function forceDelete(User $user, UserAvatar $item): bool
    {
        return $this->view($user, $item);
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }
}
