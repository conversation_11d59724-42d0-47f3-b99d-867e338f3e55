<div class="min-h-screen bg-base-200">
     <x-mobile-page-header route="{{ route('mobile.me') }}" header="{{ __('mobile.my_badges') . ' (' . $totalBadges . ')' }}" />

     <!-- Navigation to All Rewards -->
     @if($totalBadges > 0)
         <div class="p-4 pb-0">
             <div class="bg-violet-50 rounded-xl p-3 mb-4">
                 <div class="flex items-center justify-between">
                     <div class="flex items-center">
                         <span class="text-lg mr-2">🎁</span>
                         <span class="text-sm text-violet-700">{{ __('mobile.view_all') }} {{ __('mobile.rewards') }}</span>
                     </div>
                     <a href="{{ route('mobile.my-rewards') }}" class="text-violet-600 text-sm font-semibold hover:text-violet-700">
                         {{ __('mobile.view_all') }} >
                     </a>
                 </div>
             </div>
         </div>
     @endif

     <div class="p-4">

        @if($badges->count() > 0)
            <!-- Badges Grid -->
            <div class="grid grid-cols-2 gap-4">
                @foreach($badges as $userReward)
                    <div class="bg-white rounded-2xl p-4 shadow-sm text-center">
                        <!-- Badge Image -->
                        <div class="w-20 h-20 mx-auto mb-3 rounded-full overflow-hidden border-2 border-gray-200">
                            @if($userReward->reward && $userReward->reward->image)
                                <img src="{{ asset('storage/' . $userReward->reward->image) }}" 
                                     alt="{{ $userReward->reward->name }}" 
                                     class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full bg-yellow-400 flex items-center justify-center">
                                    <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <!-- Badge Name -->
                        <h3 class="font-bold text-gray-900 text-sm mb-2">
                            {{ $userReward->reward->name ?? __('mobile.badge') }}
                        </h3>

                        <!-- Badge Description (if available) -->
                        @if($userReward->reward && $userReward->reward->description)
                            <p class="text-xs text-gray-600 mb-2">
                                {{ $userReward->reward->description }}
                            </p>
                        @endif

                        <!-- Earned Date -->
                        <p class="text-xs text-gray-500">
                            {{ $userReward->awarded_date->format('d.m.Y') }}
                        </p>

{{--                        
                        <!-- Source Information -->
                        @if($userReward->readingLog && $userReward->readingLog->book)
                            <p class="text-xs text-violet-600 mt-1">
                                {{ __('mobile.from_book') }}: {{ $userReward->readingLog->book->name }}
                            </p>
                        @elseif($userReward->userActivity && $userReward->userActivity->activity)
                            <p class="text-xs text-violet-600 mt-1">
                                {{ __('mobile.from_activity') }}: {{ $userReward->userActivity->activity->name }}
                            </p>
                        @endif
--}}
                    </div>
                @endforeach
            </div>
        @else
            <!-- No Badges Message -->
            <div class="bg-white rounded-2xl p-8 shadow-sm text-center">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('mobile.no_badges_yet') }}</h3>
                <p class="text-gray-500 text-sm mb-4">{{ __('mobile.complete_activities_to_earn_badges') }}</p>
                
                <!-- Motivational Tips -->
                <div class="bg-violet-50 rounded-xl p-4 text-left">
                    <h4 class="font-semibold text-violet-800 mb-2">{{ __('mobile.how_to_earn_badges') }}:</h4>
                    <ul class="text-sm text-violet-700 space-y-1">
                        <li>• {{ __('mobile.complete_reading_activities') }}</li>
                        <li>• {{ __('mobile.finish_books') }}</li>
                        <li>• {{ __('mobile.maintain_reading_streaks') }}</li>
                        <li>• {{ __('mobile.participate_in_challenges') }}</li>
                    </ul>
                </div>
            </div>
        @endif

    </div>
</div>
