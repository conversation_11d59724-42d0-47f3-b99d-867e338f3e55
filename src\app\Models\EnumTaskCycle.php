<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class EnumTaskCycle extends BaseModel
{
    /**
     * Task cycle constants.
     */
    const TOTAL = 1;
    const DAILY = 2;
    const WEEKLY = 3;
    const MONTHLY = 4;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'nr',
        'name',
    ];

    /**
     * Get the tasks for this task cycle.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class, 'task_cycle_id');
    }

    /**
     * Scope to get task cycle by number.
     */
    public function scopeByNumber($query, $nr)
    {
        return $query->where('nr', $nr);
    }

    /**
     * Get the number of periods within a date range.
     */
    public function getPeriodsInRange(Carbon $startDate, Carbon $endDate): int
    {
        return match($this->nr) {
            self::TOTAL => 1,
            self::DAILY => $startDate->diffInDays($endDate) + 1,
            self::WEEKLY => $startDate->diffInWeeks($endDate) + 1,
            self::MONTHLY => $startDate->diffInMonths($endDate) + 1,
            default => 1,
        };
    }

    /**
     * Get the current period number within a date range.
     */
    public function getCurrentPeriod(Carbon $startDate, Carbon $endDate, Carbon $currentDate = null): int
    {
        $currentDate = $currentDate ?: now();
        
        // Ensure current date is within range
        $currentDate = max($startDate, min($currentDate, $endDate));
        
        return match($this->nr) {
            self::TOTAL => 1,
            self::DAILY => $startDate->diffInDays($currentDate) + 1,
            self::WEEKLY => $startDate->diffInWeeks($currentDate) + 1,
            self::MONTHLY => $startDate->diffInMonths($currentDate) + 1,
            default => 1,
        };
    }

    /**
     * Get the expected progress percentage for a given date within a date range.
     */
    public function getExpectedProgressPercentage(Carbon $startDate, Carbon $endDate, Carbon $currentDate = null): float
    {
        $currentDate = $currentDate ?: now();

        // If before start date, expected progress is 0%
        if ($currentDate->lt($startDate)) {
            return 0.0;
        }

        // If after end date, expected progress is 100%
        if ($currentDate->gt($endDate)) {
            return 100.0;
        }

        // For TOTAL cycle, calculate based on time elapsed
        if ($this->nr === self::TOTAL) {
            $totalDays = $startDate->diffInDays($endDate) + 1;
            $elapsedDays = $startDate->diffInDays($currentDate) + 1;
            return ($elapsedDays / $totalDays) * 100;
        }

        // For time-based cycles, calculate based on periods
        $totalPeriods = $this->getPeriodsInRange($startDate, $endDate);
        $currentPeriod = $this->getCurrentPeriod($startDate, $endDate, $currentDate);

        return ($currentPeriod / $totalPeriods) * 100;
    }

    /**
     * Get the period start date for a specific period number.
     */
    public function getPeriodStartDate(Carbon $startDate, int $periodNumber): Carbon
    {
        return match($this->nr) {
            self::TOTAL => $startDate->copy(),
            self::DAILY => $startDate->copy()->addDays($periodNumber - 1),
            self::WEEKLY => $startDate->copy()->addWeeks($periodNumber - 1),
            self::MONTHLY => $startDate->copy()->addMonths($periodNumber - 1),
            default => $startDate->copy(),
        };
    }

    /**
     * Get the period end date for a specific period number.
     */
    public function getPeriodEndDate(Carbon $startDate, Carbon $endDate, int $periodNumber): Carbon
    {
        $periodStart = $this->getPeriodStartDate($startDate, $periodNumber);
        
        $periodEnd = match($this->nr) {
            self::TOTAL => $endDate->copy(),
            self::DAILY => $periodStart->copy()->endOfDay(),
            self::WEEKLY => $periodStart->copy()->addWeek()->subDay()->endOfDay(),
            self::MONTHLY => $periodStart->copy()->addMonth()->subDay()->endOfDay(),
            default => $endDate->copy(),
        };
        
        // Ensure period end doesn't exceed overall end date
        return min($periodEnd, $endDate);
    }

    /**
     * Check if this cycle is time-based.
     */
    public function isTimeBased(): bool
    {
        return in_array($this->nr, [self::DAILY, self::WEEKLY, self::MONTHLY]);
    }

    /**
     * Check if this cycle is cumulative.
     */
    public function isCumulative(): bool
    {
        return $this->nr === self::TOTAL;
    }

    /**
     * Get the description for this task cycle.
     */
    public function getDescriptionAttribute(): string
    {
        return match($this->nr) {
            self::TOTAL => __('admin.task_cycle_total_desc'),
            self::DAILY => __('admin.task_cycle_daily_desc'),
            self::WEEKLY => __('admin.task_cycle_weekly_desc'),
            self::MONTHLY => __('admin.task_cycle_monthly_desc'),
            default => $this->name,
        };
    }

    /**
     * Get the unit for this task cycle.
     */
    public function getUnitAttribute(): string
    {
        return match($this->nr) {
            self::TOTAL => __('admin.total'),
            self::DAILY => __('admin.per_day'),
            self::WEEKLY => __('admin.per_week'),
            self::MONTHLY => __('admin.per_month'),
            default => '',
        };
    }

    /**
     * Get all time-based cycles.
     */
    public static function getTimeBasedCycles(): array
    {
        return [
            self::DAILY,
            self::WEEKLY,
            self::MONTHLY,
        ];
    }

    /**
     * Get all cumulative cycles.
     */
    public static function getCumulativeCycles(): array
    {
        return [
            self::TOTAL,
        ];
    }

    /**
     * Get the display name with description.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' - ' . $this->description;
    }

    /**
     * Get the summary of this task cycle.
     */
    public function getSummaryAttribute(): string
    {
        $type = $this->isTimeBased() ? __('admin.time_based') : __('admin.cumulative');
        return sprintf('%s (%s)', $this->name, $type);
    }
}
