<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class School extends BaseModel
{
    use HasFactory;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'school_type_id',
        'active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
        ];
    }

    /**
     * Get the school type.
     */
    public function schoolType(): BelongsTo
    {
        return $this->belongsTo(EnumSchoolType::class, 'school_type_id');
    }

    /**
     * Get school classes for this school.
     */
    public function schoolClasses(): HasMany
    {
        return $this->hasMany(SchoolClass::class);
    }

    /**
     * Get school user assignments for this school.
     */
    public function userSchools(): HasMany
    {
        return $this->hasMany(UserSchool::class);
    }

    /**
     * Get active school user assignments for this school.
     */
    public function activeUserSchools(): Has<PERSON>any
    {
        return $this->hasMany(UserSchool::class)->where('active', true);
    }

    /**
     * Get user class assignments for this school.
     */
    public function userClasses(): HasMany
    {
        return $this->hasMany(UserClass::class);
    }

    /**
     * Get active user class assignments for this school.
     */
    public function activeUserClasses(): HasMany
    {
        return $this->hasMany(UserClass::class)->where('active', true);
    }

    /**
     * Get users assigned to this school.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_schools')
                    ->withPivot(['role_id', 'active']);
    }

    /**
     * Get active users assigned to this school.
     */
    public function activeUsers()
    {
        return $this->belongsToMany(User::class, 'user_schools')
                    ->withPivot(['role_id', 'active'])
                    ->wherePivot('active', true);
    }


    /**
     * Scope to get active schools.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

}
