<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\EnumTaskCycle;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;

#[Icon('clock')]
class EnumTaskCycleResource extends BaseResource
{
    protected string $model = EnumTaskCycle::class;

    protected string $column = 'name';

    protected array $with = [];

    public function getTitle(): string
    {
        return __('admin.task_cycles');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Number::make(__('admin.number'), 'nr')
                ->sortable(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Text::make(__('admin.unit'), 'unit')
                ->sortable(),

/*                
            Text::make(__('admin.type'), function ($item) {
                return $item->isTimeBased() ? __('admin.time_based') : __('admin.cumulative');
            })
                ->badge(fn($value) => match($value) {
                    __('admin.time_based') => 'warning',
                    __('admin.cumulative') => 'primary',
                    default => 'secondary'
                })
                ->sortable(),
*/
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Number::make(__('admin.number'), 'nr')
                ->required()
                ->min(1)
                ->hint(__('admin.task_cycle_number_hint')),

            Text::make(__('admin.name'), 'name')
                ->required()
                ->hint(__('admin.task_cycle_name_hint')),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Number::make(__('admin.number'), 'nr'),
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.description'), 'description'),
            Text::make(__('admin.unit'), 'unit'),
            Text::make(__('admin.display_name'), 'display_name'),
            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $itemId = $item?->id;

        $rules = [
            'nr' => ['required', 'integer', 'min:1', 'unique:enum_task_cycles,nr' . ($itemId ? ",$itemId" : '')],
            'name' => ['required', 'string', 'max:255'],
            ...parent::getCommonRules($item),
        ];

        return $rules;
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['nr' => 'asc'];
    }
}
