<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Book;
use App\Models\BookType;
use App\Models\Publisher;
use App\Models\User;
use App\Services\BookDiscovery\BookDiscoveryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class FidanKitapIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected BookType $bookType;
    protected Publisher $publisher;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'username' => 'testuser',
            'password' => bcrypt('password'),
        ]);

        // Create test book type
        $this->bookType = BookType::create([
            'name' => 'Test Book Type',
            'description' => 'Test book type description',
            'thumbnail' => 'test-thumbnail.jpg',
            'created_by' => $this->user->id,
        ]);

        // Create test publisher
        $this->publisher = Publisher::create([
            'name' => 'Test Publisher',
            'created_by' => $this->user->id,
        ]);
    }

    /** @test */
    public function fidan_kitap_provider_is_registered_in_discovery_service()
    {
        $discoveryService = app(BookDiscoveryService::class);
        
        // Use reflection to access protected method
        $reflection = new \ReflectionClass($discoveryService);
        $method = $reflection->getMethod('createProvider');
        $method->setAccessible(true);

        $config = config('book_discovery.sources.fidankitap');
        $provider = $method->invoke($discoveryService, 'fidankitap', $config);

        $this->assertNotNull($provider);
        $this->assertInstanceOf(\App\Services\BookDiscovery\Providers\FidanKitapProvider::class, $provider);
    }

    /** @test */
    public function fidan_kitap_provider_handles_successful_search()
    {
        // Mock HTTP response with Fidan Kitap book page
        $mockHtml = $this->getFidanKitapMockHtml();
        
        Http::fake([
            'fidankitap.com/*' => Http::response($mockHtml, 200)
        ]);

        $discoveryService = app(BookDiscoveryService::class);
        $result = $discoveryService->searchByIsbn('9789754034929');

        $this->assertNotNull($result);
        $this->assertEquals('Test Kitap Adı', $result['name']);
        $this->assertEquals(['Test Yazar 1', 'Test Yazar 2'], $result['author']);
        $this->assertEquals('Test Yayınevi', $result['publisher']);
        $this->assertEquals('9789754034929', $result['isbn']);
        $this->assertEquals(250, $result['page_count']);
        $this->assertEquals(2023, $result['year']);
        $this->assertEquals('Fidan Kitap', $result['source']);
    }

    /** @test */
    public function fidan_kitap_provider_handles_book_not_found()
    {
        // Mock HTTP response with "not found" message
        Http::fake([
            'fidankitap.com/*' => Http::response('<div>kayıt bulunamadı</div>', 200)
        ]);

        $discoveryService = app(BookDiscoveryService::class);
        $result = $discoveryService->searchByIsbn('9999999999999');

        $this->assertNull($result);
    }

    /** @test */
    public function fidan_kitap_provider_creates_book_correctly()
    {
        $this->actingAs($this->user);

        // Mock HTTP response
        $mockHtml = $this->getFidanKitapMockHtml();
        Http::fake([
            'fidankitap.com/*' => Http::response($mockHtml, 200)
        ]);

        $discoveryService = app(BookDiscoveryService::class);
        $bookData = $discoveryService->searchByIsbn('9789754034929');
        
        $this->assertNotNull($bookData);

        // Create book from discovered data
        $book = $discoveryService->createBookFromData($bookData, $this->user->id);

        $this->assertNotNull($book);
        $this->assertInstanceOf(Book::class, $book);
        $this->assertEquals('Test Kitap Adı', $book->name);
        $this->assertEquals('9789754034929', $book->isbn);
        $this->assertEquals(250, $book->page_count);
        $this->assertEquals(2023, $book->year_of_publish);

        // Verify book exists in database
        $this->assertDatabaseHas('books', [
            'name' => 'Test Kitap Adı',
            'isbn' => '9789754034929',
            'page_count' => 250,
            'year_of_publish' => 2023,
        ]);
    }

    /** @test */
    public function fidan_kitap_provider_respects_priority_order()
    {
        // Ensure Fidan Kitap has priority 2 (after D&R)
        $config = config('book_discovery.sources');
        
        $this->assertArrayHasKey('fidankitap', $config);
        $this->assertEquals(2, $config['fidankitap']['priority']);
        $this->assertTrue($config['fidankitap']['enabled']);
    }

    /** @test */
    public function fidan_kitap_provider_caches_results()
    {
        // Clear cache first
        Cache::flush();

        // Mock HTTP response
        $mockHtml = $this->getFidanKitapMockHtml();
        Http::fake([
            'fidankitap.com/*' => Http::response($mockHtml, 200)
        ]);

        $discoveryService = app(BookDiscoveryService::class);
        
        // First call should hit the HTTP endpoint
        $result1 = $discoveryService->searchByIsbn('9789754034929');
        $this->assertNotNull($result1);

        // Second call should use cache (HTTP should not be called again)
        Http::assertSentCount(1);
        $result2 = $discoveryService->searchByIsbn('9789754034929');
        $this->assertNotNull($result2);
        $this->assertEquals($result1, $result2);
        
        // Still only one HTTP call should have been made
        Http::assertSentCount(1);
    }

    /** @test */
    public function fidan_kitap_provider_handles_network_errors()
    {
        // Mock network error
        Http::fake([
            'fidankitap.com/*' => Http::response('', 500)
        ]);

        $discoveryService = app(BookDiscoveryService::class);
        $result = $discoveryService->searchByIsbn('9789754034929');

        $this->assertNull($result);
    }

    /** @test */
    public function fidan_kitap_provider_validates_configuration()
    {
        $config = config('book_discovery.sources.fidankitap');

        // Verify required configuration keys exist
        $this->assertArrayHasKey('name', $config);
        $this->assertArrayHasKey('priority', $config);
        $this->assertArrayHasKey('enabled', $config);
        $this->assertArrayHasKey('search_url', $config);
        $this->assertArrayHasKey('timeout', $config);
        $this->assertArrayHasKey('not_found_indicators', $config);
        $this->assertArrayHasKey('parsing', $config);

        // Verify search URL contains ISBN placeholder
        $this->assertStringContainsString('{isbn}', $config['search_url']);

        // Verify not found indicators include expected patterns
        $this->assertContains('kayıt bulunamadı', $config['not_found_indicators']['text_patterns']);
    }

    /**
     * Get mock HTML for Fidan Kitap book page.
     */
    protected function getFidanKitapMockHtml(): string
    {
        return '<div class="prd_view_item">
                    <h1 class="contentHeader prdHeader">Test Kitap Adı</h1>
                    <div class="prd_brand_box">
                        <div class="writers">
                            <a class="writer"><span>Test Yazar 1</span></a>
                            <a class="writer"><span>Test Yazar 2</span></a>
                        </div>
                        <a class="publisher"><span>Test Yayınevi</span></a>
                    </div>
                    <div class="prd_fields">
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Stok Kodu:</div>
                            <div class="prd_fields_text">9789754034929</div>
                        </div>
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Sayfa Sayısı:</div>
                            <div class="prd_fields_text">250</div>
                        </div>
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Basım Tarihi:</div>
                            <div class="prd_fields_text">2023</div>
                        </div>
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Kategori:</div>
                            <div class="prd_fields_text">
                                <a href="#">Roman</a>/
                                <a href="#">Türk Edebiyatı</a>
                            </div>
                        </div>
                    </div>
                    <img class="prd_image" src="/images/test-book-cover.jpg" alt="Test Kitap Adı">
                </div>';
    }
}
