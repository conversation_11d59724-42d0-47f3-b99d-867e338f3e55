<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <x-mobile-page-header route="{{ route('mobile.teacher.home') }}" header="{{ __('mobile.last_24_hours') }}" />

    <div class="p-4">
    @if($recentActivities->count() > 0)
        <div class="space-y-2 mb-3">
            @foreach($recentActivities as $activity)
            <div class="bg-white rounded-xl p-3 shadow-sm">
                <div class="flex items-center space-x-3">
                    <!-- Student Avatar -->
                    <div class="w-8 h-8 rounded-full overflow-hidden">
                        @if($activity['student_avatar'])
                            <img src="{{ asset('storage/' . $activity['student_avatar']) }}" alt="Avatar" class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs font-bold">{{ substr($activity['student_name'], 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                    
                    <!-- Activity Info -->
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-gray-900 truncate">
                            <span class="font-medium">{{ $activity['student_name'] }}</span>
                            {{ $activity['type'] === 'completed' ? __('mobile.completed_reading') : __('mobile.began_reading') }}
                        </p>
                        <p class="text-xs text-blue-600">{!! $activity['book_title'] !!}</p>
                    </div>

                    <!-- Book Cover -->
                    @if($activity['book_cover'])
                        <div class="w-8 h-10 rounded overflow-hidden">
                            <img src="{{ asset('storage/' . $activity['book_cover']) }}" alt="Book" class="w-full h-full object-cover">
                        </div>
                    @endif
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('mobile.no_activities_last_24h') }}</h3>
            <p class="text-gray-500 text-sm">{{ __('mobile.no_student_activities_description') }}</p>
        </div>
    @endif
    </div>
</div>
