<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\FCMService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class SendTodaysReadingReminderJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $fcmService = app(FCMService::class);

        if (!config('fcm.todays_reading_reminder_enabled', true)) {
            Log::info('Todays reading reminders are disabled');
            return;
        }

        // Get students who haven't had reading activity since yesterday
        $cutoffDate = Carbon::yesterday();

        $students = User::whereHas('roles', function ($query) {
                $query->where('name', 'student');
            })
            ->whereNotNull('fcm_token')
            ->whereDoesntHave('readingLogs', function ($query) use ($cutoffDate) {
                $query->where('log_date', '>=', $cutoffDate);
            })
            ->get();

        Log::info("Found {$students->count()} students for today's reading reminder notification");

        $successCount = 0;
        foreach ($students as $student) {
            try {
                if ($fcmService->sendTodaysReadingReminder($student)) {
                    $successCount++;
                }
            } catch (\Exception $e) {
                Log::error("Failed to send todays reading reminder to user {$student->id}: " . $e->getMessage());
            }
        }

        Log::info("Sent {$successCount} todays reading reminder notifications out of {$students->count()} students");
    }
}
