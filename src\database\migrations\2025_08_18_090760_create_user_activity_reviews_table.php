<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_activity_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_activity_id')->constrained('user_activities')->onDelete('cascade');
            $table->date('review_date');
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->tinyInteger('status')->default(0)->comment('0-waiting, 1-approved, 2-rejected');
            $table->text('feedback')->nullable();

            // Add indexes for performance
            $table->index(['user_activity_id', 'status']);
            $table->index(['reviewed_by', 'review_date']);
            $table->index('status');
            $table->index('review_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_activity_reviews');
    }
};
