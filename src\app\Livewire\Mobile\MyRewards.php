<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserReward;
use App\Models\Reward;

class MyRewards extends Component
{
    public $user;
    public $rewardsByType = [];
    public $totalRewards = 0;
    public $rewardTypes = [];

    public function mount()
    {
        $this->user = Auth::user();
        $this->loadRewards();
        $this->loadRewardTypes();
    }

    private function loadRewards()
    {
        // Get all rewards earned by this user grouped by type
        $allRewards = $this->user->userRewards()
            ->with(['reward', 'readingLog.book', 'userActivity.activity'])
            ->orderBy('awarded_date', 'desc')
            ->get();

        $this->totalRewards = $allRewards->count();
        
        // Group rewards by type
        $this->rewardsByType = $allRewards->groupBy('reward.reward_type')->map(function ($rewards, $type) {
            return [
                'type' => $type,
                'type_name' => $this->getRewardTypeName($type),
                'type_icon' => $this->getRewardTypeIcon($type),
                'count' => $rewards->count(),
                'rewards' => $rewards
            ];
        })->sortBy('type');
    }

    private function loadRewardTypes()
    {
        $this->rewardTypes = Reward::getVisibleRewardTypes();
    }

    private function getRewardTypeName($type)
    {
        return match($type) {
            Reward::TYPE_BADGE => __('mobile.badges'),
            Reward::TYPE_GIFT => __('mobile.gift'),
            Reward::TYPE_TROPHY => __('mobile.trophy'),
            Reward::TYPE_CARD => __('mobile.card'),
            Reward::TYPE_ITEM => __('mobile.item'),
            default => __('mobile.rewards'),
        };
    }

    private function getRewardTypeIcon($type)
    {
        return match($type) {
            Reward::TYPE_BADGE => '🏅',
            Reward::TYPE_GIFT => '🎁',
            Reward::TYPE_TROPHY => '🏆',
            Reward::TYPE_CARD => '🃏',
            Reward::TYPE_ITEM => '📦',
            default => '🏅',
        };
    }

    private function getRewardTypeColor($type)
    {
        return match($type) {
            Reward::TYPE_BADGE => 'bg-yellow-400',
            Reward::TYPE_GIFT => 'bg-green-400',
            Reward::TYPE_TROPHY => 'bg-purple-400',
            Reward::TYPE_CARD => 'bg-blue-400',
            Reward::TYPE_ITEM => 'bg-orange-400',
            default => 'bg-gray-400',
        };
    }

    public function goBack()
    {
        return redirect()->route('mobile.me');
    }

    public function render()
    {
        return view('livewire.mobile.my-rewards', [
            'getRewardTypeColor' => [$this, 'getRewardTypeColor']
        ]);
    }
}
