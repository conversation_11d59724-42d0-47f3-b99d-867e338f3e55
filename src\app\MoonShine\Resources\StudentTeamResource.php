<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Http\Request;
use MoonShine\UI\Fields\Field;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Components\Layout\Box;

use App\Models\{Team, User};
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Laravel\Fields\Relationships\{BelongsTo, BelongsToMany, HasMany};
use MoonShine\Support\AlpineJs;
use MoonShine\Support\Enums\JsEvent;
use MoonShine\UI\Components\CardsBuilder;
use MoonShine\UI\Fields\{Date, Image, Number, Switcher, Text};
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

#[Icon('users')]
class StudentTeamResource extends BaseResource
{
    use WithRolePermissions;
    
    protected string $model = Team::class;

    protected string $column = 'name';

    protected array $with = ['leader', 'users', 'teamRewards'];

    public function getTitle(): string
    {
        return __('admin.teams');
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required(),

                Image::make(__('admin.logo'), 'logo')
                    ->dir('teams')
                    ->removable(),

                BelongsTo::make(
                    __('admin.leader'),
                    'leader',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class
                )
                    ->nullable()
                    ->valuesQuery(function (Builder $query) {
                        return $query->forCurrentUser();
                    })
                    ->hint(__('admin.team_leader_hint')),
                    
                Switcher::make(__('admin.active'), 'active')
                    ->default(true),
            ]),

            Box::make(__('admin.team_members'), [
                BelongsToMany::make(
                    __('admin.members'),
                    'users',
                    formatted: fn(User $user) => $user->name . ' (' . $user->classes->implode('name', ', ') . ')',
                    resource: UserResource::class
                )
                ->horizontalMode()
                ->valuesQuery(function (Builder $query) {
                    return $query->forCurrentUser();
                })
                ->hint(__('admin.team_members_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Image::make(__('admin.logo'), 'logo'),

            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.leader'), 'leader_name'),
            Number::make(__('admin.members'), 'member_count'),
            Number::make(__('admin.total_points'), 'total_points'),
            Switcher::make(__('admin.active'), 'active'),
            BelongsToMany::make(
                __('admin.team_members'),
                'users',
                formatted: fn(User $user) => $user->name . ' (' . $user->classes->implode('name', ', ') . ')' ,
                resource: UserResource::class
            )
            ->inLine(separator: ', '),

            HasMany::make(__('admin.team_rewards'), 'teamRewards', StudentTeamRewardResource::class)
            ->fields([
                Image::make(__('admin.reward_image'), 'reward.image'),
                Text::make(__('admin.reward_name'), 'reward.name'),
                Text::make(__('admin.reward_type'), 'reward.reward_type_display'),
                Text::make(__('admin.trigger_source'), 'trigger_source'),
                Date::make(__('admin.awarded_date'), 'awarded_date')
                ->format('d.m.Y'),
            ]),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'logo' => ['image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'leader_user_id' => ['nullable', 'exists:users,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];

        // Add custom validation to ensure leader is a team member
        $rules['leader_user_id'][] = function ($attribute, $value, $fail) {
            if ($value) {
                $teamId = request()->route('resourceItem');
                if ($teamId) {
                    $team = Team::find($teamId);
                    if ($team && !$team->users()->where('users.id', $value)->exists()) {
                        $user = User::find($value);
                        $fail(__('admin.leader_must_be_team_member', [
                            'user' => $user->name,
                            'team' => $team->name
                        ]));
                    }
                }
            }
        };

        return $rules;
    }

    protected function search(): array
    {
        return ['name', 'leader.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        // Cast to concrete Eloquent Builder to access query methods
        if (!$builder instanceof EloquentBuilder) {
            return $builder;
        }
        return $builder->forCurrentUser();
    }

    public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();

        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }

    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), []) 
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
            ->title( fn($team) => html_entity_decode($team->name) . ' (' . $team->member_count . ' ' . __('admin.members') . ')')
            ->subtitle( fn($team) => __('admin.leader') . ': ' . $team->leader_name)
            ->url(fn($team) => $this->getDetailPageUrl($team->getKey()))
            ->thumbnail(fn($team) => asset('storage/' . $team->logo));
            //->buttons($this->getIndexButtons());
    }    
}
