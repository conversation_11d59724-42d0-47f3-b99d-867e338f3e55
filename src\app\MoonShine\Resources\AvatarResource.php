<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Avatar;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Support\AlpineJs;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Support\Enums\JsEvent;
use MoonShine\UI\Components\Badge;
use MoonShine\UI\Components\CardsBuilder;
use MoonShine\UI\Components\{ Layout\Box, Tabs\Tab, Tabs};
use MoonShine\UI\Fields\{Image, Number, Switcher, Text, Textarea };

#[Icon('user')]
class AvatarResource extends BaseResource
{
    protected string $model = Avatar::class;

    protected string $column = 'name';

    public function getTitle(): string
    {
        return __('admin.avatars');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Image::make(__('admin.base_image'), 'base_image'),
            Number::make(__('admin.required_points'), 'required_points')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        Text::make(__('admin.name'), 'name')
                            ->required()
                            ->placeholder(__('admin.enter_name')),

                        Textarea::make(__('admin.description'), 'description')
                            ->required()
                            ->placeholder(__('admin.enter_description')),

                        Image::make(__('admin.base_image'), 'base_image')
                            ->dir('avatars')
                            ->removable()
                            ->keepOriginalFileName(),

                        Image::make(__('admin.happy_image'), 'happy_image')
                            ->dir('avatars')
                            ->removable()
                            ->keepOriginalFileName(),
                        Image::make(__('admin.sad_image'), 'sad_image')
                            ->dir('avatars')
                            ->removable()
                            ->keepOriginalFileName(),
                        Image::make(__('admin.sleepy_image'), 'sleepy_image')
                            ->dir('avatars')
                            ->removable()
                            ->keepOriginalFileName(),

                        Number::make(__('admin.required_points'), 'required_points')
                            ->required()
                            ->min(0)
                            ->placeholder(__('admin.required_points')),

                        Switcher::make(__('admin.active'), 'active')
                            ->default(true),
                    ]),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Textarea::make(__('admin.description'), 'description'),
            Image::make(__('admin.base_image'), 'base_image'),
            Image::make(__('admin.happy_image'), 'happy_image'),
            Image::make(__('admin.sad_image'), 'sad_image'),
            Image::make(__('admin.sleepy_image'), 'sleepy_image'),
            Number::make(__('admin.required_points'), 'required_points'),
            Switcher::make(__('admin.active'), 'active'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'base_image' => ['required', 'image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'happy_image' => [ 'image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'sad_image' => [ 'image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'sleepy_image' => [ 'image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'required_points' => ['required', 'integer', 'min:0'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name', 'description'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
    public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();

        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }

    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), []) //$this->getIndexFields())
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
//            ->header(static fn() => Badge::make('new', 'success'))
            ->title('name')
            ->subtitle(fn($avatar) =>  $avatar->required_points . ' ' .  __('admin.points'))
//            ->subtitle( 'description')
            ->url(fn($avatar) => $this->getFormPageUrl($avatar->getKey()))
            ->thumbnail(fn($avatar) => asset('storage/' . $avatar->base_image))
            ->buttons($this->getIndexButtons());
    }    
}
