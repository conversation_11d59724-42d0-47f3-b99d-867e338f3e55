<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Book;
use App\Models\Activity;
use App\Models\UserActivity;
use App\Models\UserActivityReview;
use App\Models\UserPoint;
use App\Models\UserReadingLog;
use App\Models\ClassActivity;
use App\Models\SchoolClass;
use App\Models\UserClass;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ActivityCompletionFlowTest extends TestCase
{
    use RefreshDatabase;

    public function test_test_activity_without_approval_creates_points_immediately()
    {
        // Create user and activity
        $user = User::factory()->create();
        $book = Book::factory()->create();
        $activity = Activity::factory()->create([
            'activity_type' => Activity::ACTIVITY_TYPE_QUIZ,
            'need_approval' => false,
            'required' => false,
            'points' => 100,
            'min_grade' => 70,
        ]);

        // Create user activity
        $userActivity = UserActivity::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'activity_date' => now(),
        ]);

        // Simulate passing test results
        $quizData = ['quiz_type' => 'quiz'];
        $userAnswers = [['question_id' => 1, 'answer' => 'A']];
        $scoreData = [
            'total_questions' => 10,
            'correct_answers' => 8,
            'score_percentage' => 80,
            'results' => []
        ];

        $userActivity->storeTestResults($quizData, $userAnswers, $scoreData);

        // Should be completed immediately
        $this->assertEquals(UserActivity::STATUS_COMPLETED, $userActivity->fresh()->status);
        
        // Should create points immediately
        $this->assertDatabaseHas('user_points', [
            'user_id' => $user->id,
            'points' => 100,
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        ]);

        // Should not create review record
        $this->assertDatabaseMissing('user_activity_reviews', [
            'user_activity_id' => $userActivity->id,
        ]);
    }

    public function test_test_activity_with_approval_creates_review_record()
    {
        // Create user and activity that needs approval
        $user = User::factory()->create();
        $book = Book::factory()->create();
        $activity = Activity::factory()->create([
            'activity_type' => Activity::ACTIVITY_TYPE_QUIZ,
            'need_approval' => true,
            'required' => false,
            'points' => 100,
            'min_grade' => 70,
        ]);

        // Create user activity
        $userActivity = UserActivity::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'activity_date' => now(),
        ]);

        // Simulate passing test results
        $quizData = ['quiz_type' => 'quiz'];
        $userAnswers = [['question_id' => 1, 'answer' => 'A']];
        $scoreData = [
            'total_questions' => 10,
            'correct_answers' => 8,
            'score_percentage' => 80,
            'results' => []
        ];

        $userActivity->storeTestResults($quizData, $userAnswers, $scoreData);

        // Should be pending approval
        $this->assertEquals(UserActivity::STATUS_PENDING, $userActivity->fresh()->status);
        
        // Should not create points yet
        $this->assertDatabaseMissing('user_points', [
            'user_id' => $user->id,
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        ]);

        // Should create review record
        $this->assertDatabaseHas('user_activity_reviews', [
            'user_activity_id' => $userActivity->id,
            'status' => UserActivityReview::STATUS_WAITING,
        ]);
    }

    public function test_failed_test_activity_does_not_create_points_or_reviews()
    {
        // Create user and activity
        $user = User::factory()->create();
        $book = Book::factory()->create();
        $activity = Activity::factory()->create([
            'activity_type' => Activity::ACTIVITY_TYPE_QUIZ,
            'need_approval' => false,
            'required' => false,
            'points' => 100,
            'min_grade' => 70,
        ]);

        // Create user activity
        $userActivity = UserActivity::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'activity_date' => now(),
        ]);

        // Simulate failing test results
        $quizData = ['quiz_type' => 'quiz'];
        $userAnswers = [['question_id' => 1, 'answer' => 'A']];
        $scoreData = [
            'total_questions' => 10,
            'correct_answers' => 5,
            'score_percentage' => 50, // Below min_grade of 70
            'results' => []
        ];

        $userActivity->storeTestResults($quizData, $userAnswers, $scoreData);

        // Should be failed
        $this->assertEquals(UserActivity::STATUS_FAILED, $userActivity->fresh()->status);
        
        // Should not create points
        $this->assertDatabaseMissing('user_points', [
            'user_id' => $user->id,
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        ]);

        // Should not create review record
        $this->assertDatabaseMissing('user_activity_reviews', [
            'user_activity_id' => $userActivity->id,
        ]);
    }

    public function test_required_test_activity_withholds_reading_rewards_until_completion()
    {
        // Create user and required activity
        $user = User::factory()->create();
        $book = Book::factory()->create();
        $activity = Activity::factory()->create([
            'activity_type' => Activity::ACTIVITY_TYPE_QUIZ,
            'need_approval' => false,
            'required' => true,
            'points' => 100,
            'min_grade' => 70,
        ]);

        // Create reading log (this would normally trigger rewards, but should be withheld)
        $readingLog = UserReadingLog::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'pages_read' => $book->total_pages,
            'reading_date' => now(),
            'status' => UserReadingLog::STATUS_COMPLETED,
        ]);

        // Verify reading rewards are withheld (no reading points created yet)
        $readingPoints = UserPoint::where('user_id', $user->id)
            ->where('point_type', UserPoint::POINT_TYPE_READING)
            ->count();
        $this->assertEquals(0, $readingPoints);

        // Now complete the required test activity
        $userActivity = UserActivity::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'activity_date' => now(),
        ]);

        // Simulate passing test results
        $quizData = ['quiz_type' => 'quiz'];
        $userAnswers = [['question_id' => 1, 'answer' => 'A']];
        $scoreData = [
            'total_questions' => 10,
            'correct_answers' => 8,
            'score_percentage' => 80,
            'results' => []
        ];

        $userActivity->storeTestResults($quizData, $userAnswers, $scoreData);

        // Should be completed
        $this->assertEquals(UserActivity::STATUS_COMPLETED, $userActivity->fresh()->status);
        
        // Should create activity points
        $this->assertDatabaseHas('user_points', [
            'user_id' => $user->id,
            'points' => 100,
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        ]);

        // Should now award the withheld reading rewards
        $readingPoints = UserPoint::where('user_id', $user->id)
            ->where('point_type', UserPoint::POINT_TYPE_READING)
            ->count();
        $this->assertGreaterThan(0, $readingPoints);
    }

    public function test_class_specific_activity_settings_are_respected()
    {
        // Create school class and user
        $schoolClass = SchoolClass::factory()->create();
        $user = User::factory()->create();
        UserClass::create([
            'user_id' => $user->id,
            'class_id' => $schoolClass->id,
            'school_id' => $schoolClass->school_id,
            'active' => true,
            'default' => true,
        ]);

        $book = Book::factory()->create();
        
        // Create activity with base settings
        $activity = Activity::factory()->create([
            'activity_type' => Activity::ACTIVITY_TYPE_QUIZ,
            'need_approval' => true,  // Base setting: needs approval
            'required' => false,      // Base setting: not required
            'points' => 100,          // Base setting: 100 points
            'min_grade' => 70,
        ]);

        // Create class-specific overrides
        ClassActivity::create([
            'class_id' => $schoolClass->id,
            'activity_id' => $activity->id,
            'need_approval' => false, // Override: no approval needed
            'required' => true,       // Override: make it required
            'points' => 200,          // Override: 200 points
        ]);

        // Create user activity (should use class-specific settings)
        $userActivity = UserActivity::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'activity_date' => now(),
        ]);

        // Simulate passing test results
        $quizData = ['quiz_type' => 'quiz'];
        $userAnswers = [['question_id' => 1, 'answer' => 'A']];
        $scoreData = [
            'total_questions' => 10,
            'correct_answers' => 8,
            'score_percentage' => 80,
            'results' => []
        ];

        $userActivity->storeTestResults($quizData, $userAnswers, $scoreData);

        // Should be completed immediately (class override: no approval needed)
        $this->assertEquals(UserActivity::STATUS_COMPLETED, $userActivity->fresh()->status);
        
        // Should create points with class-specific value (200, not 100)
        $this->assertDatabaseHas('user_points', [
            'user_id' => $user->id,
            'points' => 200, // Class-specific points value
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        ]);

        // Should not create review record (class override: no approval needed)
        $this->assertDatabaseMissing('user_activity_reviews', [
            'user_activity_id' => $userActivity->id,
        ]);
    }
}
