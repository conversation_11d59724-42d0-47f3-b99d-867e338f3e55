<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Task;
use App\Models\EnumTaskType;
use App\Models\EnumTaskCycle;
use App\Models\Activity;
use App\Models\Book;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('clipboard-document-list')]
class TaskResource extends BaseResource
{
    protected string $model = Task::class;

    protected string $column = 'name';

    protected array $with = ['taskType', 'taskCycle', 'activity', 'books', 'categories'];

    public function getTitle(): string
    {
        return __('admin.tasks');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            BelongsTo::make(
                __('admin.task_cycle'),
                'taskCycle',
                formatted: fn(EnumTaskCycle $taskCycle) => $taskCycle->name,
                resource: EnumTaskCycleResource::class
            )
                ->sortable(),

            Text::make(__('admin.task_value'), 'task_value')
                ->sortable(),

            BelongsTo::make(
                __('admin.task_type'),
                'taskType',
                formatted: fn(EnumTaskType $taskType) => $taskType->name,
                resource: EnumTaskTypeResource::class
            )
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        $taskTypes = EnumTaskType::getTaskTypes();
        return [
            Box::make(__('admin.task_details'), [
                    Text::make(__('admin.name'), 'name')
                        ->required(),

                    Textarea::make(__('admin.description'), 'description')
                        ->nullable()
                        ->hint(__('admin.task_description_hint')),

                Flex::make([

                    BelongsTo::make(
                        __('admin.task_cycle'),
                        'taskCycle',
                        'name',
                        resource: EnumTaskCycleResource::class
                    )
                        ->required()
                        ->searchable(),

                    Number::make(__('admin.task_value'), 'task_value')
                        ->min(1),

                    BelongsTo::make(
                        __('admin.task_type'),
                        'taskType',
                        'name',
                        resource: EnumTaskTypeResource::class
                    )
                        ->required()
                        ->searchable(),
                ]),
                Switcher::make(__('admin.active'), 'active')
                    ->default(true),
            ]),

            Box::make(__('admin.related_information'), [
                BelongsTo::make(
                    __('admin.activity'),
                    'activity',
                    formatted: fn(?Activity $activity) => $activity ? $activity->name : null,
                    resource: ActivityResource::class
                )
                    ->nullable()
                    ->searchable()
                    ->hint(__('admin.task_activity_hint'))
                    ->showWhen('task_type_id', $taskTypes[EnumTaskType::COMPLETE_BOOK_ACTIVITY]['id']),

                BelongsToMany::make(
                    __('admin.books'),
                    'books',
                    resource: BookResource::class
                )
                    ->selectMode()
                    ->asyncSearch('name',
                        formatted: fn(Book $book) => html_entity_decode($book->name) . ' (' . $book->author_names . ')')
                    ->hint(__('admin.task_books_hint'))
                    ->showWhen('task_type_id', $taskTypes[EnumTaskType::COMPLETE_BOOK_LIST]['id']),

                BelongsToMany::make(
                    __('admin.categories'),
                    'categories',
                    resource: CategoryResource::class
                )
                    ->selectMode()
                    ->hint(__('admin.task_categories_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            
            BelongsTo::make(
                __('admin.task_cycle'),
                'taskCycle',
                formatted: fn(?EnumTaskCycle $taskCycle) => $taskCycle ? $taskCycle->display_name : null,
                resource: EnumTaskCycleResource::class
            )
                ->link(link: fn() => '#'),

            Text::make(__('admin.task_value'), 'task_value'),

            BelongsTo::make(
                __('admin.task_type'),
                'taskType',
                formatted: fn(?EnumTaskType $taskType) => $taskType ? $taskType->display_name : null,
                resource: EnumTaskTypeResource::class
            )
                ->link(link: fn() => '#'),


            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(?Activity $activity) => $activity ? $activity->name : null,
                resource: ActivityResource::class
            )
                ->link(link: fn() => '#'),


            BelongsToMany::make(__('admin.books'), 'books', BookResource::class),
            BelongsToMany::make( __('admin.categories'), 'categories',
                    resource: CategoryResource::class)
                ->inLine(separator: ', '),

            Switcher::make(__('admin.active'), 'active'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'task_type_id' => ['required', 'exists:enum_task_types,id'],
            'task_cycle_id' => ['required', 'exists:enum_task_cycles,id'],
            'task_value' => ['nullable', 'integer', 'min:1'],
            'activity_id' => ['nullable', 'exists:activities,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];

        return $rules;
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        // For now, all authenticated users can see tasks
        // This can be extended for role-based filtering in the future
        return $builder;
    }
}
