<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserReward;
use App\Models\UserLevel;
use App\Models\Avatar;

class BadgeUnlocked extends Component
{
    public $rewards = [];
    public $levels = [];
    public $avatars = [];
    public $currentItemIndex = 0;
    public $userName;
    public $allItems = [];

    public function mount()
    {
        $this->userName = Auth::user()->name;

        // Check for individual rewards
        $rewardIds = session('unlocked_rewards', []);
        if (!empty($rewardIds)) {
            $this->rewards = UserReward::whereIn('id', $rewardIds)
                ->with('reward:id,name,description,image,reward_type')
                ->get()
                ->toArray() ?? [];
        }

        // Check for team rewards
        $teamRewardIds = session('unlocked_team_rewards', []);
        $teamRewards = [];
        if (!empty($teamRewardIds)) {
            $teamRewards = \App\Models\TeamReward::whereIn('id', $teamRewardIds)
                ->with(['reward:id,name,description,image,reward_type', 'team:id,name'])
                ->get()
                ->toArray() ?? [];
        }

        // Check for levels
        $levelIds = session('achieved_levels', []);
        if (!empty($levelIds)) {
            $this->levels = UserLevel::whereIn('id', $levelIds)
                ->with('level:id,nr,name,image')
                ->get()
                ->toArray() ?? [];
        }

        // Check for avatars
        $avatarIds = session('unlocked_avatars', []);
        if (!empty($avatarIds)) {
            $this->avatars = Avatar::whereIn('id', $avatarIds)
                ->get()
                ->toArray() ?? [];
        }

        // Combine all items into a single array for sequential display
        $this->allItems = array_merge(
            array_map(fn($reward) => ['type' => 'reward', 'data' => $reward], $this->rewards),
            array_map(fn($teamReward) => ['type' => 'team_reward', 'data' => $teamReward], $teamRewards),
            array_map(fn($level) => ['type' => 'level', 'data' => $level], $this->levels),
            array_map(fn($avatar) => ['type' => 'avatar', 'data' => $avatar], $this->avatars)
        );

        if (empty($this->allItems)) {
            return redirect()->route('mobile.home');
        }
    }

    public function continue()
    {
        $this->currentItemIndex++;

        // If we've shown all items, clear session and redirect
        if ($this->currentItemIndex >= count($this->allItems)) {
            session()->forget(['unlocked_rewards', 'unlocked_team_rewards', 'achieved_levels', 'unlocked_avatars']);
            $redirectRoute = session('reward_redirect_route', 'mobile.home');
            $redirectParams = session('reward_redirect_params', []);

            session()->forget(['reward_redirect_route', 'reward_redirect_params']);

            return redirect()->route($redirectRoute, $redirectParams);
        }
    }

    public function getCurrentItem()
    {
        return $this->allItems[$this->currentItemIndex] ?? null;
    }

    public function getCurrentReward()
    {
        $currentItem = $this->getCurrentItem();
        return ($currentItem && $currentItem['type'] === 'reward') ? $currentItem['data'] : null;
    }

    public function getCurrentLevel()
    {
        $currentItem = $this->getCurrentItem();
        return ($currentItem && $currentItem['type'] === 'level') ? $currentItem['data'] : null;
    }

    public function getCurrentAvatar()
    {
        $currentItem = $this->getCurrentItem();
        return ($currentItem && $currentItem['type'] === 'avatar') ? $currentItem['data'] : null;
    }

    // Backward compatibility method
    public function getCurrentBadge()
    {
        return $this->getCurrentReward();
    }

    public function render()
    {
        return view('livewire.mobile.badge-unlocked');
    }
}
