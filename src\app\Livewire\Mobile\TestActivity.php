<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Book;
use App\Models\Activity;
use App\Models\UserActivity;

use App\Services\QuizGenerationService;
use Illuminate\Support\Facades\Log;

class TestActivity extends Component
{
    public $book;
    public $activity; // Resolved activity with class-specific settings
    public $mode;
    public $quiz = null;
    public $currentQuestionIndex = 0;
    public $userAnswers = [];
    public $isCompleted = false;
    public $testResults = null;
    public $existingUserActivity = null;

    protected $quizService;

    public function boot(QuizGenerationService $quizService)
    {
        $this->quizService = $quizService;
    }

    public function mount($book, $activity, $mode = 'create')
    {
        $this->book = Book::with(['authors', 'publisher'])->findOrFail($book);
        $user = Auth::user();
//        $this->activity = Activity::resolvedForUser($user)->findOrFail($activity);
        $this->activity = Activity::findOrFail($activity);
        $this->mode = $mode;

        // Check if activity is a test activity
        if (!$this->activity->isTestActivity()) {
            session()->flash('error', __('mobile.invalid_activity_type'));
            return redirect()->route('mobile.books.activities', $this->book->id);
        }

        // Check if user can take this test
        if ($this->mode === 'create') {
            $this->checkTestEligibility();
        }

        // Load existing user activity if in view mode
        if ($this->mode === 'view') {
            $this->loadExistingUserActivity();
        }

        // Generate quiz if in create mode
        if ($this->mode === 'create' && !$this->isCompleted) {
            $this->generateQuiz();
        }
    }

    private function checkTestEligibility()
    {
        $userId = Auth::id();
        
        // Check if user has exceeded retry limits
        $attemptCount = UserActivity::where('user_id', $userId)
            ->where('book_id', $this->book->id)
            ->where('activity_id', $this->activity->id)
            ->count();

        if ($attemptCount >= $this->activity->allowed_tries) {
            session()->flash('error', __('mobile.test_retry_limit_exceeded'));
            return redirect()->route('mobile.books.activities', $this->book->id);
        }

        // Check if book has enough content
        if ($this->activity->isQuiz()) {
            if (!$this->quizService->bookHasEnoughQuestionsForQuiz($this->book)) {
                session()->flash('error', __('mobile.insufficient_questions_for_quiz'));
                return redirect()->route('mobile.books.activities', $this->book->id);
            }
        } elseif ($this->activity->isVocabularyTest()) {
            if (!$this->quizService->bookHasEnoughWordsForVocabularyTest($this->book)) {
                session()->flash('error', __('mobile.insufficient_words_for_vocabulary_test'));
                return redirect()->route('mobile.books.activities', $this->book->id);
            }
        }
    }

    private function loadExistingUserActivity()
    {
        $this->existingUserActivity = UserActivity::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->where('activity_id', $this->activity->id)
            ->orderBy('activity_date', 'desc')
            ->first();

        if ($this->existingUserActivity && $this->existingUserActivity->isTestActivity()) {
            $this->testResults = $this->existingUserActivity->getTestResults();
            $this->isCompleted = true;
        }
    }

    private function generateQuiz()
    {
        try {
            if ($this->activity->isQuiz()) {
                $this->quiz = $this->quizService->generateQuiz($this->book, $this->activity);
            } elseif ($this->activity->isVocabularyTest()) {
                $this->quiz = $this->quizService->generateVocabularyTest($this->book, $this->activity);
            }

            // Ensure quiz was generated successfully
            if (!$this->quiz || !isset($this->quiz['questions']) || empty($this->quiz['questions'])) {
                throw new \Exception(__('mobile.failed_to_generate_quiz'));
            }

            // Initialize user answers array
            $questionCount = count($this->quiz['questions']);
            $this->userAnswers = array_fill(0, $questionCount, null);

            // Reset current question index if it's out of bounds
            if ($this->currentQuestionIndex >= $questionCount) {
                $this->currentQuestionIndex = 0;
            }
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
            return redirect()->route('mobile.books.activities', $this->book->id);
        }
    }

    public function selectAnswer($questionIndex, $answerIndex)
    {
        if ($this->isCompleted) {
            return;
        }

        // Ensure the userAnswers array is properly initialized
        if (!is_array($this->userAnswers)) {
            $this->userAnswers = [];
        }

        // Ensure the array has enough elements
        while (count($this->userAnswers) <= $questionIndex) {
            $this->userAnswers[] = null;
        }

        $this->userAnswers[$questionIndex] = $answerIndex;
    }

    public function nextQuestion()
    {
        if ($this->currentQuestionIndex < count($this->quiz['questions']) - 1) {
            $this->currentQuestionIndex++;
        }
    }

    public function previousQuestion()
    {
        if ($this->currentQuestionIndex > 0) {
            $this->currentQuestionIndex--;
        }
    }

    public function goToQuestion($index)
    {
        if ($index >= 0 && $index < count($this->quiz['questions'])) {
            $this->currentQuestionIndex = $index;
        }
    }

    public function submitTest()
    {
        // Validate that all questions are answered
        foreach ($this->userAnswers as $answer) {
            if ($answer === null) {
                session()->flash('error', __('mobile.please_answer_all_questions'));
                return;
            }
        }

        // Calculate score
        $scoreData = $this->quizService->calculateScore($this->quiz, $this->userAnswers);

        // Create user activity record
        $userActivity = UserActivity::create([
            'user_id' => Auth::id(),
            'book_id' => $this->book->id,
            'activity_id' => $this->activity->id,
            'activity_date' => now(),
        ]);

        // Store test results
        $userActivity->storeTestResults($this->quiz, $this->userAnswers, $scoreData);

        // Set completion state
        $this->isCompleted = true;
        $this->testResults = $userActivity->getTestResults();

        // Check for rewards if test was passed and doesn't need approval (immediate completion)
        if ($userActivity->passedTest() && $userActivity->status === UserActivity::STATUS_COMPLETED) {
            $rewardRedirect = $this->checkForRewards($userActivity);
// redirect logic moved to goBackToActivities button
//            if ($rewardRedirect) {
//                return $rewardRedirect;
//            }
        }

        // Show success message
        if ($userActivity->passedTest()) {
            session()->flash('success', __('mobile.test_passed_successfully'));
        } else {
            session()->flash('error', __('mobile.test_failed_try_again'));
        }
    }

    public function retakeTest()
    {
        $attemptCount = UserActivity::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->where('activity_id', $this->activity->id)
            ->count();

        if ($attemptCount >= $this->activity->allowed_tries) {
            session()->flash('error', __('mobile.test_retry_limit_exceeded'));
            return;
        }

        // Reset test state
        $this->isCompleted = false;
        $this->testResults = null;
        $this->currentQuestionIndex = 0;
        $this->generateQuiz();
    }

    public function goBackToActivities()
    {
        // check for session(['unlocked_rewards', 'unlocked_team_rewards']) and if it exists, redirect to badge-unlocked page
        if (session('unlocked_rewards') || session('unlocked_team_rewards')) {
            return redirect()->route('mobile.badge-unlocked');
        }
        return redirect()->route('mobile.books.activities', $this->book->id);
    }

    /**
     * Check for newly unlocked rewards after test completion.
     * This includes both activity-related rewards and any retroactive rewards
     * that were awarded when required activities were completed.
     */
    private function checkForRewards($userActivity)
    {
        $rewardService = app(\App\Services\MobileRewardDisplayService::class);

        // Check for all recent rewards and levels (including retroactive ones)
        // This captures both activity rewards and any reading rewards/levels
        // that were awarded retroactively when required activities were completed
        $rewardResult = $rewardService->checkForAllRecentRewards($userActivity->id);

        if ($rewardResult && $rewardResult['redirect_to_celebration']) {
            // Set redirect back to activities page after celebration
            $rewardService->setRedirectRoute('mobile.books.activities', [$this->book->id]);

            return redirect()->route('mobile.badge-unlocked');
        }

        return null;
    }

    public function getCurrentQuestion()
    {
        if (!$this->quiz || !isset($this->quiz['questions'][$this->currentQuestionIndex])) {
            return null;
        }

        return $this->quiz['questions'][$this->currentQuestionIndex];
    }

    public function getProgressPercentage()
    {
        if (!$this->quiz) {
            return 0;
        }

        $answeredCount = count(array_filter($this->userAnswers, fn($answer) => $answer !== null));
        return round(($answeredCount / count($this->quiz['questions'])) * 100);
    }

    /**
     * Safely get user answer for a specific question index.
     */
    public function getUserAnswer($questionIndex)
    {
        if (!is_array($this->userAnswers) || !isset($this->userAnswers[$questionIndex])) {
            return null;
        }
        return $this->userAnswers[$questionIndex];
    }

    /**
     * Check if a specific answer is selected for a question.
     */
    public function isAnswerSelected($questionIndex, $answerIndex)
    {
        return $this->getUserAnswer($questionIndex) === $answerIndex;
    }

    public function render()
    {
        $this->activity = Activity::findOrFail($this->activity->id);
        return view('livewire.mobile.test-activity', [
            'activity' => $this->activity,
            'currentQuestion' => $this->getCurrentQuestion(),
            'progressPercentage' => $this->getProgressPercentage(),
            'totalQuestions' => $this->quiz ? count($this->quiz['questions']) : 0,
        ]);
    }
}
