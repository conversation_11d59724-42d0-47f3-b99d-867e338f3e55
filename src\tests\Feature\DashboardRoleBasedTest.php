<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\UserActivity;
use App\Models\UserActivityReview;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\UserClass;
use App\Models\Book;
use App\Models\Activity;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

class DashboardRoleBasedTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'system_admin', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'school_admin', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'teacher', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'student', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'parent', 'guard_name' => 'moonshine']);
    }

    public function test_teacher_can_see_reviews_from_their_students_only()
    {
        // Create a teacher
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        // Create a school and class
        $school = School::factory()->create();
        $class = SchoolClass::factory()->create(['school_id' => $school->id]);

        // Assign teacher to class
        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Create a student in the same class
        $student = User::factory()->create();
        $student->assignRole('student');
        UserClass::create([
            'user_id' => $student->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Create a student from different class
        $otherStudent = User::factory()->create();
        $otherStudent->assignRole('student');

        // Create book and activity
        $book = Book::factory()->create();
        $activity = Activity::factory()->create(['need_approval' => true]);

        // Create user activities
        $studentActivity = UserActivity::factory()->create([
            'user_id' => $student->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'status' => UserActivity::STATUS_PENDING,
        ]);

        $otherStudentActivity = UserActivity::factory()->create([
            'user_id' => $otherStudent->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'status' => UserActivity::STATUS_PENDING,
        ]);

        // Create reviews
        $studentReview = UserActivityReview::factory()->create([
            'user_activity_id' => $studentActivity->id,
            'status' => UserActivityReview::STATUS_WAITING,
        ]);

        UserActivityReview::factory()->create([
            'user_activity_id' => $otherStudentActivity->id,
            'status' => UserActivityReview::STATUS_WAITING,
        ]);

        // Test the query logic that would be used in the dashboard
        $teacherClassIds = $teacher->activeUserClasses()->pluck('class_id')->toArray();

        $teacherReviews = UserActivityReview::where('status', UserActivityReview::STATUS_WAITING)
            ->whereHas('userActivity.user.activeUserClasses', function ($query) use ($teacherClassIds) {
                $query->whereIn('class_id', $teacherClassIds);
            })
            ->get();

        // Teacher should only see review from their student
        $this->assertCount(1, $teacherReviews);
        $this->assertEquals($studentReview->id, $teacherReviews->first()->id);
    }

    public function test_user_role_checking_methods_work()
    {
        $systemAdmin = User::factory()->create();
        $systemAdmin->assignRole('system_admin');

        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        $student = User::factory()->create();
        $student->assignRole('student');

        // Test role checking methods
        $this->assertTrue($systemAdmin->isSystemAdmin());
        $this->assertFalse($systemAdmin->isTeacher());

        $this->assertTrue($teacher->isTeacher());
        $this->assertFalse($teacher->isSystemAdmin());

        $this->assertTrue($student->isStudent());
        $this->assertFalse($student->isTeacher());
    }

    public function test_review_approval_logic_works()
    {
        // Create teacher and student setup
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        $school = School::factory()->create();
        $class = SchoolClass::factory()->create(['school_id' => $school->id]);

        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        $student = User::factory()->create();
        $student->assignRole('student');
        UserClass::create([
            'user_id' => $student->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        $book = Book::factory()->create();
        $activity = Activity::factory()->create(['need_approval' => true]);

        $studentActivity = UserActivity::factory()->create([
            'user_id' => $student->id,
            'book_id' => $book->id,
            'activity_id' => $activity->id,
            'status' => UserActivity::STATUS_PENDING,
        ]);

        $review = UserActivityReview::factory()->create([
            'user_activity_id' => $studentActivity->id,
            'status' => UserActivityReview::STATUS_WAITING,
        ]);

        // Test the approval logic directly
        $review->approveReview($teacher->id);

        // Check that review was approved
        $review->refresh();
        $this->assertEquals(UserActivityReview::STATUS_APPROVED, $review->status);
        $this->assertEquals($teacher->id, $review->reviewed_by);
    }
}
