<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Category extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get books by this category.
     */
    public function books(): BelongsToMany
    {
        return $this->belongsToMany(Book::class, 'book_categories');
    }

    /**
     * Scope to search categories by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%');
    }

    /**
     * Get book count for this category.
     */
    public function getBookCountAttribute(): int
    {
        return $this->books()->count();
    }

    /**
     * Check if category has books.
     */
    public function hasBooks(): bool
    {
        return $this->book_count > 0;
    }

    /**
     * Get the most recent book by this category.
     */
    public function getLatestBookAttribute(): ?Book
    {
        return $this->books()
                    ->orderBy('year_of_publish', 'desc')
                    ->first();
    }

    /**
     * Get books published in a specific year.
     */
    public function booksInYear(int $year)
    {
        return $this->books()->where('year_of_publish', $year);
    }

    /**
     * Get unique publishers this category has worked with.
     */
    public function getPublishersAttribute()
    {
        return Publisher::whereIn('id', 
            $this->books()->distinct()->pluck('publisher_id')
        )->get();
    }
}
