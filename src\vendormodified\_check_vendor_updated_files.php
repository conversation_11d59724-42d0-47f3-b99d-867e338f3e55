<?php

// Path to the file that stores the hashes
$hashesFile = dirname(__FILE__).'\_modified_file_hashes.json';

// Check if the hash file exists
if (!file_exists($hashesFile)) {
    echo $red . "ERROR: The hashes file ($hashesFile) does not exist.\n";
    exit(1);
}

// Load the hashes from the file
$previousHashes = json_decode(file_get_contents($hashesFile), true);

// If the hash file is empty or invalid
if (!is_array($previousHashes)) {
    echo $red . "ERROR: The hashes file is not valid or is empty.\n";
    exit(1);
}

// Variable to track if any changes were found
$changesDetected = false;
$red = "\033[1;31m";

// Loop through each file and check the current hash
foreach ($previousHashes as $file => $storedHash) {
    if (file_exists($file)) {
        // Calculate the current hash of the file
        $currentHash = md5_file($file);

        // Compare the current hash with the stored hash
        if ($storedHash !== $currentHash) {
            echo $red . "WARNING: The file '$file' has been modified after the vendor update.\n";
            $changesDetected = true;
        }
    } else {
        echo $red . "WARNING: The file '$file' does not exist.\n";
        $changesDetected = true;
    }
}

// If changes were detected, update the hashes file
if ($changesDetected) {
    echo "Updating the hash file...\n";

    // Recalculate the hashes for all files and store them in the hash file
    foreach ($previousHashes as $file => $storedHash) {
        if (file_exists($file)) {
            $currentHash = md5_file($file);
            $previousHashes[$file] = $currentHash;
        } else {
            unset($previousHashes[$file]); // Remove non-existing files
        }
    }

    // Save the updated hashes to the file
    file_put_contents($hashesFile, json_encode($previousHashes, JSON_PRETTY_PRINT));

    echo "Hashes file updated successfully.\n";
}

