<div class="min-h-screen bg-base-200">
    <!-- Header Section -->
    <div>
        <div class="px-4 py-6">
            <h1 class="text-2xl font-bold text-gray-900">{{ __('mobile.profile') }}</h1>
        </div>
        <div class="grid grid-cols-2 w-full">
            <!-- Left side: Avatar and Change Avatar button -->
            <div class="flex flex-col items-center space-y-2">
                <!-- User Avatar (Clickable) -->
                <button wire:click="showAvatarDetail" class="w-28 h-28 rounded-full overflow-hidden border-2 border-white/20 hover:border-violet-300 transition-colors focus:outline-none focus:ring-2 focus:ring-violet-400">
                    @php
                        $avatarImage = $user->getAvatarDisplayImage();
                    @endphp
                    @if($avatarImage)
                        <img src="{{ asset('storage/' . $avatarImage) }}" alt="Avatar" class="w-full h-full object-cover">
                    @else
                        <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                            <span class="text-white text-xl font-bold">{{ substr($user->name, 0, 1) }}</span>
                        </div>
                    @endif
                </button>

                <!-- Change Avatar Button -->
                <button 
                    wire:click="navigateToAvatarSelection"
                    class="text-xs text-gray-600 font-semibold bg-violet-200 px-3 py-1 rounded-full hover:bg-white/30 transition-colors"
                >
                    {{ __('mobile.change_avatar') }}
                </button>
                <!-- User Name --> 
                <div class="flex-1 text-center">
                    <h1 class="text-black text-2xl font-bold">{{ $user->name }}</h1>
                </div>
            </div>

            <!-- Right side: Statistics-->
            <div class="flex flex-col space-y-2 text-black text-sm">
                <!-- Current Level -->
                <div class="flex items-center space-x-1">
                    <img src="/images/level-icon.png" alt="{{ __('mobile.level') }}" class="w-8 h-8 mr-2">
                    <span>{{ __('mobile.level') }} {{ $stats ['level_progress']['current_level'] }}</span>
                </div>

                <!-- Books Read -->
                <div class="flex items-center space-x-1">
                    <img src="/images/books-icon.png" alt="{{ __('mobile.profile_books') }}" class="w-8 h-8 mr-2">
                    <span>{{ $stats['books_completed'] }} {{ __('mobile.profile_books') }}</span>
                </div>

                <!-- Page Points -->
                <div class="flex items-center space-x-1">
                    <img src="/images/pages-icon.png" alt="{{ __('mobile.page_points') }}" class="w-8 h-8 mr-2">
                    <span>{{ $stats['page_points'] }} {{ __('mobile.page_points') }}</span>
                </div>

                <!-- Rewards -->
                <div class="flex items-center space-x-1">
                    <img src="/images/badge-icon.png" alt="{{ __('mobile.badges_alt') }}" class="w-8 h-8 mr-2">
                    <span>{{ $stats['rewards_earned'] }} {{ __('mobile.rewards') }}</span>
                </div>

                <!-- Activity Points -->
                <div class="flex items-center space-x-1">
                    <img src="/images/star-icon.png" alt="{{ __('mobile.activity_points') }}" class="w-8 h-8 mr-2">
                    <span>{{ $stats['activity_points'] }} {{ __('mobile.activity_points') }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-4 space-y-6">
        
        <!-- Reading Streak Card -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <h2 class="text-lg font-bold text-gray-900 mb-4">{{ __('mobile.last_30_days') }}</h2>
            
            <!-- 30-day grid -->
            <div class="mb-4">
                <table class="w-full border-separate" style="border-spacing: 4px;">
                    @for($row = 0; $row < 3; $row++)
                        <tr>
                            @for($col = 0; $col < 10; $col++)
                                @php
                                    $dayIndex = $row * 10 + $col;
                                    $day = $readingStreak[$dayIndex] ?? null;
                                @endphp
                                @if($day)
                                    <td class="w-7 h-7 border-2 border-white rounded">
                                        @if($day['has_reading'])
                                            <div class="w-full h-full bg-green-500 rounded flex items-center justify-center">
                                               <img src="/images/logo-white-48.webp" class="w-4">
                                            </div>
                                        @else
                                            <div class="w-full h-full bg-gray-200 rounded"></div>
                                        @endif
                                    </td>
                                @else
                                    <td class="w-7 h-7"></td>
                                @endif
                            @endfor
                        </tr>
                    @endfor
                </table>
            </div>

            <!-- Motivational Message -->
            <div class="bg-violet-50 rounded-xl p-3">
                <p class="text-violet-800 text-sm font-medium">
                    {{ $motivationalMessage }}
                </p>
            </div>
        </div>

        <!-- Earned Rewards Preview Card -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-bold text-gray-900">{{ __('mobile.earned_rewards') }}</h2>
                @if($recentRewards->count() > 0)
                    <button
                        wire:click="navigateToMyRewards"
                        class="text-violet-600 text-sm font-semibold hover:text-violet-700"
                    >
                        {{ __('mobile.view_all') }} >
                    </button>
                @endif
            </div>

            @if($recentRewards->count() > 0)
                <!-- Rewards Preview Grid -->
                <div class="flex space-x-3 overflow-x-auto pb-2">
                    @foreach($recentRewards as $userReward)
                        <div class="flex-shrink-0 text-center">
                            <div class="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 mb-1 relative">
                                @if($userReward->reward && $userReward->reward->image)
                                    <img src="{{ asset('storage/' . $userReward->reward->image) }}"
                                         alt="{{ $userReward->reward->name }}"
                                         class="w-full h-full object-cover">
                                @else
                                    @php
                                        $rewardType = $userReward->reward->reward_type ?? 1;
                                        $bgColor = match($rewardType) {
                                            1 => 'bg-yellow-400', // Badge
                                            2 => 'bg-green-400',  // Gift
                                            3 => 'bg-purple-400', // Trophy
                                            4 => 'bg-blue-400',   // Item
                                            default => 'bg-gray-400'
                                        };
                                        $icon = match($rewardType) {
                                            1 => 'M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z', // Star for badge
                                            2 => 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z', // Gift
                                            3 => 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z', // Trophy
                                            4 => 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z', // Item
                                            default => 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'
                                        };
                                    @endphp
                                    <div class="w-full h-full {{ $bgColor }} flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="{{ $icon }}"></path>
                                        </svg>
                                    </div>
                                @endif
                                <!-- Reward Type Indicator -->
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 rounded-full bg-white border border-gray-200 flex items-center justify-center">
                                    <span class="text-xs">
                                        @switch($userReward->reward->reward_type ?? 1)
                                            @case(1) 🏅 @break
                                            @case(2) 🎁 @break
                                            @case(3) 🏆 @break
                                            @case(4) 📦 @break
                                            @default 🏅
                                        @endswitch
                                    </span>
                                </div>
                            </div>
                            <p class="text-xs text-gray-600 font-medium truncate w-12">
                                {{ $userReward->reward->name ?? __('mobile.rewards') }}
                            </p>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- No Rewards Message -->
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500 text-sm">{{ __('mobile.no_rewards_yet') }}</p>
                    <p class="text-gray-400 text-xs mt-1">{{ __('mobile.complete_activities_to_earn_rewards') }}</p>
                </div>
            @endif
        </div>

        <!-- Level Progress Card -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <h2 class="text-lg font-bold text-gray-900 mb-2">{{ __('mobile.level_progress') }}</h2>

            @if($stats['level_progress']['current_level_image'])
                <div class="flex justify-center items-center">
                    <div class="w-12 h-12 mb-3 overflow-hidden">
                        <img src="{{ asset('storage/' . $stats['level_progress']['current_level_image']) }}" alt="{{ $stats['level_progress']['current_level_name'] }}" class="w-full h-full object-cover">
                    </div>
                    <div class="ml-3 mb-3">
                        <h3 class="text-xl">{{ $stats['level_progress']['current_level'] . ' - ' . $stats['level_progress']['current_level_name'] }}</h3>
                    </div>
                </div>
            @endif        
            

            @if($stats['level_progress']['is_max_level'])
                <div class="text-center py-4">
                    <div class="text-4xl mb-2">🏆</div>
                    <p class="text-gray-600">{{ __('mobile.max_level_reached') }}</p>
                </div>
            @else
                <div class="mb-4">
                    <span class="text-sm font-medium text-gray-700">
                        {{ __('mobile.next_level_progress_text', ['progress' => $stats['level_progress']['overall_progress']]) }}
                    </span>

                    <div class="mobile-progress-bar">
                        <div class="mobile-progress-fill" style="width: {{ $stats['level_progress']['overall_progress'] }}%"></div>
                    </div>
                </div>

                <div class="space-y-2 text-sm">
                    @if($stats['level_progress']['books_required'] > 0)
                        <div class="flex justify-between">
                            <span>{{ __('mobile.profile_books') }}:</span>
                            <span>{{ $stats['level_progress']['books_current'] }}/{{ $stats['level_progress']['books_required'] }}
                                @if($stats['level_progress']['books_current'] >= $stats['level_progress']['books_required']) 
                                    <span class="text-green-500">✔️</span>
                                @else
                                    ({{ $stats['level_progress']['books_required'] - $stats['level_progress']['books_current'] }} {{ __('mobile.more_books') }})
                                @endif
                            </span>
                        </div>
                    @endif

                    @if($stats['level_progress']['points_required'] > 0)
                        <div class="flex justify-between">
                            <span>{{ __('mobile.page_points') }}:</span>
                            <span>{{ $stats['level_progress']['points_current'] }}/{{ $stats['level_progress']['points_required'] }}
                                @if($stats['level_progress']['points_current'] >= $stats['level_progress']['points_required']) 
                                    <span class="text-green-500">✔️</span>
                                @else
                                    ({{ $stats['level_progress']['points_required'] - $stats['level_progress']['points_current'] }} {{ __('mobile.more_points') }})
                                @endif
                            </span>
                        </div>
                    @endif

                    <div class="text-xs text-gray-500 mt-2">
                        {{ $stats['level_progress']['next_level'] }}.  {{ __('mobile.level_requirements') }}:                        
                        @if($stats['level_progress']['all_required'])
                            {{ __('mobile.books_and_points', [
                                'books' => $stats['level_progress']['books_required'],
                                'points' => $stats['level_progress']['points_required']
                            ]) }}
                        @else
                            {{ __('mobile.books_or_points', [
                                'books' => $stats['level_progress']['books_required'],
                                'points' => $stats['level_progress']['points_required']
                            ]) }}
                        @endif
                    </div>
                </div>
            @endif
        </div>

        @if(!$hasReadingHistory)
            <!-- Add First Book Card (if no reading history) -->
            <x-mobile-add-book-card />
        @endif

    </div>

    <!-- Avatar Detail Modal -->
    @if($showAvatarModal)
        <div class="mobile-modal" x-data="{ show: @entangle('showAvatarModal') }" x-show="show" x-transition>
            <div class="mobile-modal-content">
                <!-- Avatar Image -->
                <div class="text-center mb-6">
                        @php
                            $avatarImage = $user->getAvatarDisplayImage();
                        @endphp
                        @if($avatarImage)
                            <img src="{{ asset('storage/' . $avatarImage) }}" alt="Avatar" class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                                <span class="text-white text-xl font-bold">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                        @endif
                </div>

                <!-- Avatar Name -->
                @if($currentAvatar)
                    <div class="text-center mb-4">
                        <h2 class="text-2xl font-bold text-gray-900">
                            {{ $currentAvatar->name }}
                        </h2>
                    </div>
                @endif
                    

                <!-- Avatar Description -->
                @if($currentAvatar && $currentAvatar->description)
                    <div class="text-center mb-6">
                        <p class="text-gray-600 text-sm leading-relaxed">{{ $currentAvatar->description }}</p>
                    </div>
                @endif

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <button wire:click="closeAvatarModal" class="flex-1 mobile-button-secondary">
                        {{ __('mobile.close') }}
                    </button>
                    <button wire:click="navigateToAvatarSelection" class="flex-1 mobile-button">
                        {{ __('mobile.change_avatar') }}
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>
