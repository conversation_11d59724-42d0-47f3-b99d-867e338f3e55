// Alpine.js extensions for mobile app functionality
// This will be loaded after Livewire's Alpine.js instance

document.addEventListener('livewire:init', () => {
    // Alpine.js is now available through Livewire
    
    // Global Alpine data removed - PWA prompts now handled in Messages page

    // Add Book Scanner Component
    Alpine.data('addBookScanner', () => ({
        cameraSupported: false,
        scanning: false,
        codeReader: null,
        cameraStream: null,

        init() {
            console.log('Initializing addBookScanner component...');
            this.initBarcodeReader();
            this.setupEventListeners();

            // Check camera support after a short delay to ensure everything is ready
            setTimeout(() => {
                this.checkCameraSupport();
            }, 100);
        },

        checkCameraSupport() {
            setTimeout(() => {
                if (typeof window.MobileApp !== 'undefined' && window.MobileApp.checkCameraSupport) {
                    window.MobileApp.checkCameraSupport().then((supported) => {
                        this.cameraSupported = supported;
                        // Call the Livewire method directly
                        this.$wire.call('checkCameraSupport', supported);
                    });
                } else {
                    setTimeout(() => this.checkCameraSupport(), 100);
                }
            }, 100);
        },

        initBarcodeReader() {
            if (window.BrowserMultiFormatReader) {
                this.codeReader = new window.BrowserMultiFormatReader();
            }
        },

        setupEventListeners() {
            // Listen for Livewire events
            this.$wire.on('start-barcode-scanning', () => {
                this.startScanning();
            });

            this.$wire.on('stop-barcode-scanning', () => {
                this.stopScanning();
            });

            this.$wire.on('checkCameraSupport', () => {
                this.checkCameraSupport();
            });
        },

        async startScanning(retryCount = 0) {
            console.log('Starting camera scanning...', 'Retry:', retryCount);

            if (!this.codeReader) {
                console.error('Barcode reader not initialized');
                this.$wire.call('cameraError', 'Barcode scanner not ready. Please refresh the page.');
                return;
            }

            this.scanning = true;

            // Wait a bit for Livewire to re-render the template with the video element
            setTimeout(async () => {
                const videoElement = document.getElementById('barcode-scanner');

                if (!videoElement) {
                    console.error('Video element not found, retry count:', retryCount);

                    // Retry up to 2 times with short delays
                    if (retryCount < 2) {
                        this.scanning = false;
                        this.startScanning(retryCount + 1);
                        return;
                    } else {
                        this.scanning = false;
                        this.$wire.call('cameraError', 'Camera interface not ready. Please refresh the page.');
                        return;
                    }
                }

                await this.initializeCamera(videoElement);
            }, retryCount === 0 ? 100 : 300); // Initial delay for DOM update, then longer delays for retries
        },

        async initializeCamera(videoElement) {
            try {
                console.log('Requesting camera access...');

                // Start the camera stream first
                this.cameraStream = await window.MobileApp.startCamera(videoElement);

                console.log('Camera stream started, notifying Livewire...');

                // Notify Livewire that camera is initialized
                this.$wire.call('cameraInitialized');

                // Small delay to ensure video is playing before starting barcode detection
                setTimeout(() => {
                    console.log('Starting barcode detection...');

                    // Then start decoding
                    this.codeReader.decodeFromVideoDevice(null, 'barcode-scanner', (result, error) => {
                        if (result) {
                            console.log('Barcode detected:', result.text);
                            console.log('Barcode format:', result.format);
                            console.log('Full result object:', result);

                            // Send the scanned code to Livewire for processing
                            this.$wire.call('handleScannedCode', result.text);
                            this.stopScanning();
                        }
                        // Log errors for debugging but don't stop scanning
                        if (error && error.name !== 'NotFoundException') {
                            console.log('Barcode scanning error:', error);
                        }
                    });
                }, 500);

            } catch (error) {
                console.error('Failed to start camera:', error);
                this.scanning = false;

                // Provide more specific error messages
                let errorMessage = 'Camera access failed. ';
                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Please allow camera access and try again.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage += 'Camera is being used by another application.';
                } else {
                    errorMessage += 'Please check permissions and try again.';
                }

                // Notify Livewire about camera error
                this.$wire.call('cameraError', errorMessage);
            }
        },

        stopScanning() {
            console.log('Stopping camera scanning...');

            if (this.codeReader) {
                this.codeReader.reset();
                this.scanning = false;
            }

            // Stop camera stream
            if (this.cameraStream) {
                console.log('Stopping camera stream...');
                window.MobileApp.stopCamera(this.cameraStream);
                this.cameraStream = null;
            }

            // Clean up video element
            const videoElement = document.getElementById('barcode-scanner');
            if (videoElement) {
                videoElement.srcObject = null;
                videoElement.pause();
            }

            console.log('Camera scanning stopped');
        }
    }));
});
