<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\MessageRecipient;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Date;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\QueryTags\QueryTag;
use Illuminate\Database\Eloquent\Builder;

#[Icon('user-circle')]
class MessageRecipientResource extends BaseResource
{
    protected string $model = MessageRecipient::class;

    protected string $column = 'id';

    protected array $with = ['message', 'user'];

    public function getTitle(): string
    {
        return __('admin.message_recipients');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.message'),
                'message',
                formatted: fn($message) => $message->title
            )->sortable(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            )->sortable(),

            Switcher::make(__('admin.read'), 'read')
                ->sortable(),

            Date::make(__('admin.sent_date'), 'sent_date')
                ->format('d.m.Y H:i')
                ->sortable(),

            Date::make(__('admin.read_date'), 'read_date')
                ->format('d.m.Y H:i')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make(__('admin.recipient_info'), [
                BelongsTo::make(
                    __('admin.message'),
                    'message',
                    formatted: fn($message) => $message->title
                )->required(),

                BelongsTo::make(
                    __('admin.user'),
                    'user',
                    formatted: fn(User $user) => $user->name,
                    resource: StudentResource::class
                )->required(),

                Switcher::make(__('admin.read'), 'read')
                    ->onValue(1)
                    ->offValue(0),

                Date::make(__('admin.sent_date'), 'sent_date')
                    ->format('d.m.Y H:i')
                    ->default(now()),

                Date::make(__('admin.read_date'), 'read_date')
                    ->format('d.m.Y H:i'),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.message'), 'message.title'),
            Text::make(__('admin.user'), 'user.name'),
            
            Switcher::make(__('admin.read'), 'read')
                ->onValue(1)
                ->offValue(0),

            Date::make(__('admin.sent_date'), 'sent_date')
                ->format('d.m.Y H:i'),

            Date::make(__('admin.read_date'), 'read_date')
                ->format('d.m.Y H:i'),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'message_id' => ['required', 'exists:messages,id'],
            'user_id' => ['required', 'exists:users,id'],
            'read' => ['boolean'],
            'sent_date' => ['required', 'date'],
            'read_date' => ['nullable', 'date'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['user.name', 'message.title'];
    }

    protected function getDefaultSort(): array
    {
        return ['sent_date' => 'desc'];
    }
}

