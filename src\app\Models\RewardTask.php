<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RewardTask extends BaseModel
{
    protected $fillable = [
        'reward_id',
        'task_id',
        'created_by',
    ];

    /**
     * Get the reward that this reward task belongs to.
     */
    public function reward(): BelongsTo
    {
        return $this->belongsTo(Reward::class);
    }

    /**
     * Get the task that this reward task belongs to.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Get the user who created this reward task.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by reward.
     */
    public function scopeByReward($query, int $rewardId)
    {
        return $query->where('reward_id', $rewardId);
    }

    /**
     * Scope to filter by task.
     */
    public function scopeByTask($query, int $taskId)
    {
        return $query->where('task_id', $taskId);
    }

    /**
     * Get display name for this reward task.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->reward->name . ' - ' . $this->task->name;
    }

    /**
     * Get summary information.
     */
    public function getSummaryAttribute(): string
    {
        $rewardType = $this->reward->reward_type_display;
        $taskType = $this->task->taskType->name ?? 'Unknown';
        
        return "Reward: {$rewardType}, Task: {$taskType}";
    }
}
