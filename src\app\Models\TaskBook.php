<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TaskBook extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'task_id',
        'book_id',
        'created_by',
    ];

    /**
     * Get the task for this task book.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Get the book for this task book.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the user who created this task book.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by task.
     */
    public function scopeByTask($query, $taskId)
    {
        return $query->where('task_id', $taskId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Get the display name for this task book.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->task->name . ' - ' . $this->book->name;
    }

    /**
     * Add a book to a task.
     */
    public static function addBookToTask($taskId, $bookId): ?self
    {
        // Check if book is already in task
        if (self::where('task_id', $taskId)->where('book_id', $bookId)->exists()) {
            return null;
        }

        return self::create([
            'task_id' => $taskId,
            'book_id' => $bookId,
        ]);
    }

    /**
     * Remove a book from a task.
     */
    public static function removeBookFromTask($taskId, $bookId): bool
    {
        return self::where('task_id', $taskId)->where('book_id', $bookId)->delete() > 0;
    }

    /**
     * Get books for a task.
     */
    public static function getBooksForTask($taskId)
    {
        return self::where('task_id', $taskId)
            ->with(['book'])
            ->get();
    }

    /**
     * Check if a book is in a task.
     */
    public static function isBookInTask($taskId, $bookId): bool
    {
        return self::where('task_id', $taskId)->where('book_id', $bookId)->exists();
    }

    /**
     * Get tasks that include a specific book.
     */
    public static function getTasksForBook($bookId)
    {
        return self::where('book_id', $bookId)
            ->with(['task'])
            ->get();
    }

    /**
     * Get the summary of this task book.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            'Book "%s" is part of task "%s"',
            $this->book->name,
            $this->task->name
        );
    }
}
