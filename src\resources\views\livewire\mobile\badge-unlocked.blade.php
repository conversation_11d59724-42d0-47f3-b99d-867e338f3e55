<div>
    <div class="min-h-screen bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600 flex items-center justify-center p-4 relative overflow-hidden">
        <!-- Audio Element for Badge Unlock Sound -->
        <audio id="badgeUnlockAudio" preload="auto">
            <source src="{{ asset('audio/badge_unlocked.mp3') }}" type="audio/mpeg">
            <!-- Fallback for browsers that don't support MP3 -->
            Your browser does not support the audio element.
        </audio>

        <!-- Confetti Animation -->
        <div class="confetti-container absolute inset-0 pointer-events-none">
        </div>

        @if($currentItem = $this->getCurrentItem())
            <div class="text-center z-10 max-w-sm mx-auto">
                <!-- Congratulations Text -->
                <h1 class="text-white text-2xl font-bold mb-2">{{ __('mobile.congratulations') }}</h1>
                <h2 class="text-white text-3xl font-black mb-8">{{ $userName }}!</h2>

                @if($currentItem['type'] === 'reward')
                    @php $currentReward = $currentItem['data']; @endphp
                    <!-- Badge Unlock Message -->
                    <div class="text-yellow-300 text-xl font-bold mb-8">
                        <div class="flex items-center justify-center space-x-2 mb-2">
                            <span class="text-2xl">⭐</span>
                            <span>{{ __('mobile.you_unlocked_badge') }}</span>
                            <span class="text-2xl">⭐</span>
                        </div>
                    </div>

                    <!-- Badge Display -->
                    <div class="relative mb-12">

                        <!-- Badge Container -->
                        <div class="relative p-8 mx-auto w-96 h-96 flex flex-col items-center justify-center">
                            <!-- Glow Effect -->
                            <div class="absolute inset-0 bg-yellow-100 rounded-full blur-2xl opacity-30 animate-pulse scale-110"></div>
                            <!-- Badge Image -->
                            @if($currentReward['reward']['image'])
                                <img src="{{ asset('storage/' . $currentReward['reward']['image']) }}"
                                     alt="{{ $currentReward['reward']['name'] }}"
                                     class="w-80 h-80 mb-2">
                            @else
                                <div class="w-80 h-80 bg-white rounded-full flex items-center justify-center mb-2">
                                    <span class="text-3xl">🏆</span>
                                </div>
                            @endif

                        </div>
                        <!-- Badge Name -->
                        <h3 class="text-white font-black text-2xl text-center leading-tight">
                            {{ $currentReward['reward']['name'] }}
                        </h3>
                        @if($currentReward['reward']['description'])
                            <p class="text-white text-lg font-medium text-center mt-1">
                                {{ $currentReward['reward']['description'] }}
                            </p>
                        @endif                            
                    </div>
                @elseif($currentItem['type'] === 'team_reward')
                    @php $currentTeamReward = $currentItem['data']; @endphp
                    <!-- Team Badge Unlock Message -->
                    <div class="text-blue-300 text-xl font-bold mb-8">
                        <div class="flex items-center justify-center space-x-2 mb-2">
                            <span class="text-2xl">🏆</span>
                            <span>{{ __('mobile.team_unlocked_badge') }}</span>
                            <span class="text-2xl">🏆</span>
                        </div>
                        <div class="text-blue-200 text-lg font-semibold mt-2">
                            {{ $currentTeamReward['team']['name'] }}
                        </div>
                    </div>

                    <!-- Team Badge Display -->
                    <div class="relative mb-12">

                        <!-- Team Badge Container -->
                        <div class="relative p-8 mx-auto w-96 h-96 flex flex-col items-center justify-center">
                            <!-- Glow Effect -->
                            <div class="absolute inset-0 bg-yellow-100 rounded-full blur-2xl opacity-30 animate-pulse scale-110"></div>
                            <!-- Team Badge Image -->
                            @if($currentTeamReward['reward']['image'])
                                <img src="{{ asset('storage/' . $currentTeamReward['reward']['image']) }}"
                                     alt="{{ $currentTeamReward['reward']['name'] }}"
                                     class="w-80 h-80 mb-2">
                            @else
                                <div class="w-80 h-80 bg-white rounded-full flex items-center justify-center mb-2">
                                    <span class="text-3xl">👥</span>
                                </div>
                            @endif

                            <!-- Team Badge Name -->
                            <h3 class="text-white font-black text-2xl text-center leading-tight">
                                {{ $currentTeamReward['reward']['name'] }}
                            </h3>
                        </div>
                    </div>
                @elseif($currentItem['type'] === 'level')
                    @php $currentLevel = $currentItem['data']; @endphp
                    <!-- Level Achievement Message -->
                    <div class="text-yellow-300 text-xl font-bold mb-8">
                        <div class="flex items-center justify-center space-x-2 mb-2">
                            <span class="text-2xl">🎉</span>
                            <span>{{ __('mobile.level_achieved') }}</span>
                            <span class="text-2xl">🎉</span>
                        </div>
                    </div>

                    <!-- Level Display -->
                    <div class="relative mb-12">

                        <!-- Level Container -->
                        <div class="relative p-8 mx-auto w-96 h-96 flex flex-col items-center justify-center">
                            <!-- Glow Effect -->
                            <div class="absolute inset-0 bg-yellow-100 rounded-full blur-2xl opacity-30 animate-pulse scale-110"></div>
                            <!-- Level Image -->
                            @if($currentLevel['level']['image'])
                                <img src="{{ asset('storage/' . $currentLevel['level']['image']) }}"
                                     alt="{{ $currentLevel['level']['name'] }}"
                                     class="w-80 h-80 mb-2">
                            @else
                                <div class="w-80 h-80 bg-white rounded-full flex items-center justify-center mb-2">
                                    <span class="text-3xl font-bold text-purple-600">{{ $currentLevel['level']['nr'] }}</span>
                                </div>
                            @endif

                            <!-- Level Name -->
                            <h3 class="text-white font-black text-2xl text-center leading-tight">
                                {{ __('mobile.level') . ' ' . $currentLevel['level']['nr'] }}
                            </h3>
                            <p class="text-purple-100 text-2xl font-medium text-center mt-1">
                                {{ $currentLevel['level']['name'] }}
                            </p>
                        </div>
                    </div>
                @elseif($currentItem['type'] === 'avatar')
                    @php $currentAvatar = $currentItem['data']; @endphp
                    <!-- Avatar Unlock Message -->
                    <div class="text-green-300 text-xl font-bold mb-8">
                        <div class="flex items-center justify-center space-x-2 mb-2">
                            <span class="text-2xl">🎭</span>
                            <span>{{ __('mobile.avatar_unlocked') }}</span>
                            <span class="text-2xl">🎭</span>
                        </div>
                    </div>

                    <!-- Avatar Display -->
                    <div class="relative mb-12">

                        <!-- Avatar Container -->
                        <div class="relative p-8 mx-auto w-96 h-96 flex flex-col items-center justify-center">
                            <!-- Glow Effect -->
                            <div class="absolute inset-0 bg-yellow-100 rounded-full blur-2xl opacity-30 animate-pulse scale-110"></div>
                            <!-- Avatar Image -->
                            @if($currentAvatar['base_image'])
                                <img src="{{ asset('storage/' . $currentAvatar['base_image']) }}"
                                     alt="{{ $currentAvatar['name'] }}"
                                     class="w-80 h-80 mb-2 rounded-full">
                            @else
                                <div class="w-80 h-80 bg-white rounded-full flex items-center justify-center mb-2">
                                    <span class="text-3xl">👤</span>
                                </div>
                            @endif

                            <!-- Avatar Name -->
                            <h3 class="text-white font-black text-2xl text-center leading-tight">
                                {{ $currentAvatar['name'] }}
                            </h3>
                            @if($currentAvatar['description'])
                                <p class="text-green-100 text-lg font-medium text-center mt-1">
                                    {{ $currentAvatar['description'] }}
                                </p>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Continue Button -->
                <button
                    wire:click="continue"
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-12 rounded-2xl text-xl transition-colors shadow-lg"
                >
                    @if($currentItemIndex < count($allItems) - 1)
                        {{ __('mobile.next') }}
                    @else
                        {{ __('mobile.yay') }}
                    @endif
                </button>
            </div>
        @endif
    </div>

 <x-slot name="scripts">
        <script>
            // Badge unlock audio and celebration functionality
            document.addEventListener('DOMContentLoaded', function() {
                const audio = document.getElementById('badgeUnlockAudio');
                let audioPlayed = false;

                // Function to play badge unlock audio
                const playBadgeUnlockAudio = () => {
                    if (audio && !audioPlayed) {
                        // Reset audio to beginning
                        audio.currentTime = 0;

                        // Attempt to play audio
                        const playPromise = audio.play();

                        if (playPromise !== undefined) {
                            playPromise
                                .then(() => {
                                    console.log('Badge unlock audio played successfully');
                                    audioPlayed = true;
                                })
                                .catch(error => {
                                    console.log('Badge unlock audio autoplay prevented:', error);
                                    // Audio autoplay was prevented, but don't block the UI
                                    // The audio will be available for user interaction
                                });
                        }
                    }
                };

                // Function to show confetti animation
                const showConfettiWhenReady = () => {
                    if (typeof MobileApp !== 'undefined' && MobileApp.showConfetti) {
                        // Initial confetti burst
                        MobileApp.showConfetti();
                    } else {
                        // Retry after a short delay if MobileApp is not ready
                        setTimeout(showConfettiWhenReady, 100);
                    }
                };

                // Initialize celebration effects
                const initializeCelebration = () => {
                    // Play audio first (if allowed by browser)
                    playBadgeUnlockAudio();

                    // Then show confetti
                    showConfettiWhenReady();
                };

                // Start celebration
                initializeCelebration();
            });

            // Livewire hook to play audio when component updates (for multiple rewards)
            document.addEventListener('livewire:navigated', function() {
                // Reset and play audio for new reward display
                setTimeout(() => {
                    const audio = document.getElementById('badgeUnlockAudio');

                    if (audio) {
                        audio.currentTime = 0;
                        audio.play().catch(e => console.log('Audio play prevented on navigation:', e));
                    }
                }, 200);
            });
        </script>
    </x-slot>

</div>

