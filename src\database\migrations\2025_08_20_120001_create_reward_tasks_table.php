<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reward_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reward_id')->constrained('rewards')->onDelete('cascade');
            $table->foreignId('task_id')->constrained('tasks')->onDelete('cascade');

            // Indexes for performance
            $table->index(['reward_id', 'task_id']);
            $table->index('reward_id');
            $table->index('task_id');
            
            // Unique constraint to prevent duplicate reward-task relationships
            $table->unique(['reward_id', 'task_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reward_tasks');
    }
};
