<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add created_by fields to tables that need ownership tracking
        $tablesWithCreatedBy = [
            'authors', 
            'books',
            'book_words',
            'book_questions',
            'challenges',
            'challenge_tasks',
            'class_books',
            'publishers',
            'rewards',
            'reward_tasks',
            'school_classes',
            'tasks',
            'task_books',
            'task_book_categories',
            'teams',
            'team_rewards',
            'user_activities',
            'user_activity_reviews',
            'user_avatars',
            'user_books',
            'user_challenge_tasks',
            'user_classes',
            'user_schools',
            'user_points',
            'user_reading_logs',
            'user_rewards',
            'user_teams'
        ];

        foreach ($tablesWithCreatedBy as $table) {
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $table) {
                    if (!Schema::hasColumn($table->getTable(), 'created_by')) {
                        $table->foreignId('created_by')->nullable()->after('id')->constrained('users')->onDelete('set null');
                        $table->index('created_by');
                    }
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tablesWithCreatedBy = [
            'activities',
            'authors', 
            'books',
            'book_words',
            'book_questions',
            'categories',
            'challenges',
            'challenge_tasks',
            'class_books',
            'publishers',
            'rewards',
            'reward_tasks',
            'school_classes',
            'tasks',
            'task_books',
            'task_book_categories',
            'teams',
            'team_rewards',
            'user_activities',
            'user_activity_reviews',
            'user_avatars',
            'user_books',
            'user_challenge_tasks',
            'user_classes',
            'user_schools',
            'user_points',
            'user_reading_logs',
            'user_rewards',
            'user_teams'
        ];

        foreach ($tablesWithCreatedBy as $table) {
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $table) {
                    if (Schema::hasColumn($table->getTable(), 'created_by')) {
                        $table->dropForeign(['created_by']);
                        $table->dropIndex([$table->getTable() . '_created_by_index']);
                        $table->dropColumn('created_by');
                    }
                });
            }
        }
    }
};
