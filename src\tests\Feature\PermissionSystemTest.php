<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\UserBook;
use App\Models\UserActivity;
use App\Models\UserReadingLog;
use App\Models\SchoolClass;
use App\Models\Team;
use App\Models\Task;
use App\Models\School;
use App\Models\UserSchool;
use App\Models\UserClass;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

class PermissionSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'system_admin', 'guard_name' => 'web']);
        Role::create(['name' => 'school_admin', 'guard_name' => 'web']);
        Role::create(['name' => 'teacher', 'guard_name' => 'web']);
        Role::create(['name' => 'student', 'guard_name' => 'web']);
    }

    public function test_system_admin_can_access_all_data()
    {
        $systemAdmin = User::factory()->create();
        $systemAdmin->assignRole('system_admin');
        
        $student = User::factory()->create();
        $student->assignRole('student');
        
        $userBook = UserBook::factory()->create(['user_id' => $student->id]);
        
        $this->actingAs($systemAdmin);
        
        // System admin should see all user books
        $this->assertGreaterThan(0, UserBook::count());
        $this->assertTrue(UserBook::where('id', $userBook->id)->exists());
    }

    public function test_student_can_only_access_own_data()
    {
        $student1 = User::factory()->create();
        $student1->assignRole('student');

        $student2 = User::factory()->create();
        $student2->assignRole('student');

        $userBook1 = UserBook::factory()->create(['user_id' => $student1->id]);
        $userBook2 = UserBook::factory()->create(['user_id' => $student2->id]);

        $this->actingAs($student1);

        // Student should only see their own books (filtered by user_id, not created_by)
        $this->assertEquals(1, UserBook::count());
        $this->assertTrue(UserBook::where('id', $userBook1->id)->exists());
        $this->assertFalse(UserBook::where('id', $userBook2->id)->exists());
    }

    public function test_student_access_uses_user_id_not_created_by()
    {
        $student = User::factory()->create();
        $student->assignRole('student');

        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        // Create a user book for student but created by teacher
        $userBook = UserBook::factory()->create([
            'user_id' => $student->id,
            'created_by' => $teacher->id
        ]);

        $this->actingAs($student);

        // Student should see the book because user_id matches, not because of created_by
        $this->assertEquals(1, UserBook::count());
        $this->assertTrue(UserBook::where('id', $userBook->id)->exists());
    }

    public function test_school_admin_can_access_students_in_their_schools()
    {
        $school = School::factory()->create();
        $schoolClass = SchoolClass::factory()->create(['school_id' => $school->id]);
        
        $schoolAdmin = User::factory()->create();
        $schoolAdmin->assignRole('school_admin');
        
        $student = User::factory()->create();
        $student->assignRole('student');
        
        // Assign school admin to school
        UserSchool::create([
            'user_id' => $schoolAdmin->id,
            'school_id' => $school->id,
            'role_id' => Role::where('name', 'school_admin')->first()->id,
            'active' => true
        ]);
        
        // Assign student to class in the school
        UserClass::create([
            'user_id' => $student->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true
        ]);
        
        $userBook = UserBook::factory()->create(['user_id' => $student->id]);
        
        $this->actingAs($schoolAdmin);
        
        // School admin should see student's books
        $this->assertGreaterThan(0, UserBook::count());
        $this->assertTrue(UserBook::where('id', $userBook->id)->exists());
    }

    public function test_automatic_reward_creation_bypasses_scopes()
    {
        $student = User::factory()->create();
        $student->assignRole('student');
        
        // Create user reading log which should trigger automatic operations
        $userReadingLog = UserReadingLog::factory()->create([
            'user_id' => $student->id,
            'pages_read' => 10
        ]);
        
        // Verify that automatic operations work even with scopes
        $this->assertNotNull($userReadingLog);
        
        // Test that automatic point creation works
        $userReadingLog->calculateAndCreatePoints();
        
        // Points should be created even with permission scopes
        $this->assertGreaterThan(0, \App\Models\UserPoint::withoutGlobalScopes()->count());
    }

    public function test_ownership_scope_works_for_tasks()
    {
        $schoolAdmin = User::factory()->create();
        $schoolAdmin->assignRole('school_admin');
        
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');
        
        $task1 = Task::factory()->create(['created_by' => $schoolAdmin->id]);
        $task2 = Task::factory()->create(['created_by' => $teacher->id]);
        
        $this->actingAs($schoolAdmin);
        
        // School admin should see their own tasks
        $this->assertTrue(Task::where('id', $task1->id)->exists());
        
        // But may not see teacher's tasks if teacher is not in their scope
        // This depends on the specific school/class relationships
    }

    public function test_policies_enforce_permissions()
    {
        $student = User::factory()->create();
        $student->assignRole('student');

        $otherStudent = User::factory()->create();
        $otherStudent->assignRole('student');

        $userBook = UserBook::factory()->create(['user_id' => $otherStudent->id]);

        $this->actingAs($student);

        // Student should not be able to view other student's book
        $this->assertFalse($student->can('view', $userBook));

        // But should be able to view their own
        $ownBook = UserBook::factory()->create(['user_id' => $student->id]);
        $this->assertTrue($student->can('view', $ownBook));
    }

    public function test_system_admin_only_models_are_restricted()
    {
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        $systemAdmin = User::factory()->create();
        $systemAdmin->assignRole('system_admin');

        $role = Role::first();

        $this->actingAs($teacher);

        // Teacher should not be able to manage roles
        $this->assertFalse($teacher->can('view', $role));
        $this->assertFalse($teacher->can('create', Role::class));

        $this->actingAs($systemAdmin);

        // System admin should be able to manage roles
        $this->assertTrue($systemAdmin->can('view', $role));
        $this->assertTrue($systemAdmin->can('create', Role::class));
    }

    public function test_ownership_scope_filters_correctly()
    {
        $schoolAdmin = User::factory()->create();
        $schoolAdmin->assignRole('school_admin');

        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        $task1 = Task::factory()->create(['created_by' => $schoolAdmin->id]);
        $task2 = Task::factory()->create(['created_by' => $teacher->id]);

        $this->actingAs($schoolAdmin);

        // School admin should see their own tasks
        $this->assertTrue(Task::where('id', $task1->id)->exists());

        // Verify ownership-based filtering works
        $this->assertTrue($schoolAdmin->can('view', $task1));
    }
}
