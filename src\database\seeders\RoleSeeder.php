<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'system_admin',
                'guard_name' => 'moonshine',
                'description' => 'Full system access and management',
            ],
            [
                'name' => 'school_admin',
                'guard_name' => 'moonshine',
                'description' => 'Manages a single school',
            ],
            [
                'name' => 'teacher',
                'guard_name' => 'moonshine',
                'description' => 'Teaches classes and manages students',
            ],
            [
                'name' => 'student',
                'guard_name' => 'moonshine',
                'description' => 'Participates in reading activities',
            ],
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
    }
}
