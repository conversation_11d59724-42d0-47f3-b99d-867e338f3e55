<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\UserActivityReview;
use App\Models\User;

class UserActivityReviewPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, UserActivityReview $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, UserActivityReview $item): bool
    {
        return true;
    }

    public function delete(User $user, UserActivityReview $item): bool
    {
        return true;
    }

    public function restore(User $user, UserActivityReview $item): bool
    {
        return true;
    }

    public function forceDelete(User $user, UserActivityReview $item): bool
    {
        return true;
    }

    public function massDelete(User $user): bool
    {
        return true;
    }
}
