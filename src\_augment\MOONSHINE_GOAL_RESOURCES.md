# MoonShine Goal Management Resources

## Overview

This document describes the MoonShine 3.x admin resources created for the goal management system. All resources follow the established project patterns and conventions, ensuring seamless integration with the existing codebase.

## Created Resources

### 1. GoalResource (`app/MoonShine/Resources/GoalResource.php`)

**Purpose:** Manage Goal model - create, edit, and view reading goals with their associated tasks.

**Key Features:**
- **Index Fields:** Name, description, motto, tasks count, assignments count, completion rate, active status
- **Form Fields:** Basic goal information with nested goal tasks management
- **Detail Fields:** Complete goal overview with related tasks and assignments
- **Role-based Access:** System Admin, School Admin, and Teachers can manage goals
- **Search:** Name, description, motto
- **Validation:** Required name, optional description/motto, boolean active status

**Relationships:**
- HasMany: goalTasks, userGoals
- Displays task count and assignment statistics
- Shows completion rate across all assignments

### 2. GoalTaskResource (`app/MoonShine/Resources/GoalTaskResource.php`)

**Purpose:** Manage GoalTask model - link tasks to goals with specific date ranges.

**Key Features:**
- **Index Fields:** Goal name, task name, task type, task cycle, date range, status
- **Form Fields:** Goal selection, task selection, start/end dates
- **Detail Fields:** Complete task details with duration and status information
- **Role-based Access:** System Admin, School Admin, and Teachers can manage goal tasks
- **Search:** Goal name, task name
- **Validation:** Required goal/task, valid date range, prevents duplicate goal-task combinations

**Relationships:**
- BelongsTo: Goal, Task
- Displays task type and cycle information
- Shows date range and duration calculations

### 3. UserGoalResource (`app/MoonShine/Resources/UserGoalResource.php`)

**Purpose:** Manage UserGoal model - assign goals to individual users or teams and track progress.

**Key Features:**
- **Index Fields:** Goal name, assignee, assigned by, progress, completion rate, achievement status
- **Form Fields:** Goal selection, user/team assignment, assignment details, achievement status
- **Detail Fields:** Complete assignment overview with progress tracking and motivational insights
- **Role-based Access:** Filtered by user's school/class assignments for proper access control
- **Search:** Goal name, user name, team name, assigned by, comments
- **Validation:** Required goal and assignee, prevents duplicate assignments, ensures either user OR team

**Relationships:**
- BelongsTo: Goal, User, Team, AssignedBy
- HasMany: userGoalTasks
- Displays progress statistics and achievement status

**Access Control:**
- **System Admin:** All user goals
- **School Admin:** Goals for students in their schools
- **Teachers:** Goals for students in their assigned classes
- **Students:** Only their own goals (individual and team)

### 4. UserGoalTaskResource (`app/MoonShine/Resources/UserGoalTaskResource.php`)

**Purpose:** Manage UserGoalTask model - track individual task completion within goals.

**Key Features:**
- **Index Fields:** Goal name, task name, assignee, progress, completion status
- **Form Fields:** User goal selection, goal task selection, assignee, completion status
- **Detail Fields:** Detailed task progress with status tracking and time analytics
- **Role-based Access:** Same filtering as UserGoalResource for proper access control
- **Search:** Goal name, task name, user name, team name
- **Validation:** Required assignments, prevents duplicate task assignments, ensures either user OR team

**Relationships:**
- BelongsTo: UserGoal, GoalTask, User, Team
- Displays task-level progress and completion details

## Model Enhancements

### Added Accessor Methods

**Goal Model:**
- `tasks_count` - Count of associated goal tasks
- `assignments_count` - Count of user goal assignments
- `completion_rate_display` - Formatted completion rate with statistics
- `summary` - Overview of goal statistics

**GoalTask Model:**
- `display_name` - Goal name + Task name combination
- `date_range_display` - Formatted date range
- `duration_days` - Duration in days
- `status_display` - Current status (Not Started, In Progress, Deadline Approaching, Overdue)
- `summary` - Task overview with duration and status

**UserGoal Model:**
- `display_name` - Goal name + Assignee combination
- `assignee_display` - Formatted assignee with type (Individual/Team)
- `progress_display` - Formatted progress with task counts
- `completion_percentage` - Progress percentage
- `completed_tasks_count` - Number of completed tasks
- `total_tasks_count` - Total number of tasks
- `estimated_completion_date` - Estimated completion date
- `summary` - Assignment overview with progress and status

**UserGoalTask Model:**
- `display_name` - Goal + Task + User combination
- `assignee_display` - User name with team context if applicable
- `progress_display` - Formatted progress with current/target values
- `progress_percentage` - Task progress percentage
- `current_value` - Current progress value
- `target_value` - Target value for completion
- `status_display` - Human-readable status
- `days_remaining` - Days until deadline
- `completion_time_days` - Time taken to complete (if completed)
- `summary` - Task assignment overview

## Translation Integration

### English Translations (`lang/en/admin.php`)
Added comprehensive translations for:
- Goal management terminology
- Form field labels and hints
- Status displays and progress indicators
- Validation error messages
- Assignment and progress descriptions

### Turkish Translations (`lang/tr/admin.php`)
Complete Turkish localization for all goal management terms, maintaining consistency with existing translation patterns.

## Menu Integration

### MoonShine Layout (`app/MoonShine/Layouts/MoonShineLayout.php`)
Added "Goal Management" menu group with:
- Goals
- Goal Tasks
- User Goals
- User Goal Tasks

### Service Provider (`app/Providers/MoonShineServiceProvider.php`)
Registered all four resources in the MoonShine service provider for proper initialization.

## Design Patterns Followed

### 1. BaseResource Extension
All resources extend the project's BaseResource class, inheriting:
- Common index/detail fields
- Standard validation patterns
- Consistent search functionality
- Role-based access control traits

### 2. Field Patterns
- **Box/Flex Layout:** Consistent form organization
- **BelongsTo Relationships:** Proper resource linking with formatted display
- **HasMany Relationships:** Nested resource management
- **Validation Rules:** Following project validation patterns
- **Search Fields:** Multi-field search capabilities

### 3. Access Control
- **Role-based Filtering:** Using modifyQueryBuilder() method
- **School/Class Filtering:** Respecting organizational boundaries
- **User Context:** Proper filtering based on user's role and assignments

### 4. Translation Keys
- **Dot Notation:** Using 'admin.field_name' pattern
- **Consistent Naming:** Following established translation conventions
- **Comprehensive Coverage:** All UI elements properly translated

## Usage Examples

### Creating a Goal
1. Navigate to Goals in admin panel
2. Click "Create"
3. Fill in goal details (name, description, motto)
4. Add goal tasks with date ranges
5. Save to create the goal

### Assigning Goals
1. Navigate to User Goals
2. Click "Create"
3. Select goal and assignee (user or team)
4. Add assignment comment
5. Save to assign the goal

### Monitoring Progress
1. View User Goals index for overview
2. Click on specific assignment for detailed progress
3. View User Goal Tasks for task-level tracking
4. Monitor completion rates and deadlines

## Integration with Existing Systems

### Task Management
- Seamless integration with existing Task, TaskType, and TaskCycle models
- Proper relationship handling and validation

### User Management
- Respects existing role-based access control
- Integrates with school and class assignment systems

### Team Management
- Full support for team-based goal assignments
- Proper team member filtering and access control

The MoonShine goal management resources provide a comprehensive admin interface for managing the goal system while maintaining full consistency with the project's established patterns and conventions.
