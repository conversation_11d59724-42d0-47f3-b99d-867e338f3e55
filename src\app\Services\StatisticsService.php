<?php

namespace App\Services;

use App\Models\{User, UserActivity, UserActivityReview, UserReadingLog, UserBook, UserPoint, Reward};
use Carbon\Carbon;
use Illuminate\Support\Collection;

/**
 * Unified Statistics Service
 * 
 * Centralized service for all statistics calculations across the application.
 * 
 * Used by:
 * - MoonShine admin panel (Dashboard.php)
 * - Mobile teacher interface (TeacherHome.php, TeacherLast24Hours.php)
 * - Mobile student interface (Home.php, Profile.php)
 * - Any other components requiring statistics
 */
class StatisticsService
{
    // ========== TEACHER CLASS MANAGEMENT ==========
    
    /**
     * Get teacher's assigned class IDs using the same logic as Dashboard.php.
     * Uses default class approach for consistency.
     */
    public static function getTeacherClassIds(User $teacher): array
    {
        // Use default class approach like in Dashboard.php
        $defaultClass = $teacher->getDefaultClass();
        if ($defaultClass) {
            return [$defaultClass->class_id];
        }
        
        // Fallback to all active classes
        return $teacher->activeUserClasses()->pluck('class_id')->toArray();
    }

    // ========== TEACHER DASHBOARD METRICS (24-HOUR STATS) ==========
    
    /**
     * Get comprehensive teacher statistics for the last 24 hours.
     * Consolidates all 24-hour metrics into a single method with optimized queries.
     */
    public static function getTeacherLast24HourStats(User $teacher): array
    {
        $teacherClassIds = self::getTeacherClassIds($teacher);
        
        if (empty($teacherClassIds)) {
            return [
                'books_completed' => 0,
                'active_students' => '0 / 0',
                'pages_read' => 0,
                'activities_completed' => 0,
                'activities_pending_approval' => 0,
            ];
        }
        
        $yesterday = Carbon::now()->subDay();
        
        // Books completed in last 24 hours (including with required/need_approval activities not yet completed/approved )
        $booksCompleted = UserBook::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->whereRaw('DATE(end_date) >= ?', [$yesterday->format('Y-m-d')])
            ->count();
        
        // Student activity ratios
        $totalStudents = User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->count();
            
        $activeStudents = User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->whereHas('readingLogs', function ($q) use ($yesterday) {
                $q->where('log_date', '>=', $yesterday);
            })
            ->count();
        
        // Pages read in last 24 hours
        $pagesRead = UserReadingLog::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('log_date', '>=', $yesterday)
            ->sum('pages_read') ?? 0;
        
        // Activities completed in last 24 hours (including pending, excluding rejected)
        $activitiesCompleted = UserActivity::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('activity_date', '>=', $yesterday)
            ->whereIn('status', [
                UserActivity::STATUS_PENDING,
                UserActivity::STATUS_APPROVED,
                UserActivity::STATUS_COMPLETED
            ])
            ->count();
        
        // Activities pending approval
        $activitiesPendingApproval = UserActivityReview::where('status', UserActivityReview::STATUS_WAITING)
            ->whereHas('userActivity', function ($q) use ($teacherClassIds) {
                $q->whereHas('user.activeUserClasses', function ($subQ) use ($teacherClassIds) {
                    $subQ->whereIn('class_id', $teacherClassIds);
                });
            })
            ->count();
        
        return [
            'books_completed' => $booksCompleted,
            'active_students' => "{$activeStudents} / {$totalStudents}",
            'pages_read' => (int)$pagesRead,
            'activities_completed' => $activitiesCompleted,
            'activities_pending_approval' => $activitiesPendingApproval,
        ];
    }
    
    // ========== INDIVIDUAL METRIC METHODS (For Dashboard.php compatibility) ==========
    
    /**
     * Get count of activities pending approval for teacher's students.
     */
    public static function getActivitiesPendingApprovalCount(array $teacherClassIds): int
    {
        if (empty($teacherClassIds)) {
            return 0;
        }

        return UserActivityReview::where('status', UserActivityReview::STATUS_WAITING)
            ->whereHas('userActivity', function ($q) use ($teacherClassIds) {
                $q->whereHas('user.activeUserClasses', function ($subQ) use ($teacherClassIds) {
                    $subQ->whereIn('class_id', $teacherClassIds);
                });
            })
            ->count();
    }
    
    /**
     * Get students who read in last 24 hours / total students ratio.
     */
    public static function getStudentsReadLast24Hours(array $teacherClassIds): string
    {
        if (empty($teacherClassIds)) {
            return '0 / 0';
        }

        // Get total students in teacher's classes
        $totalStudents = User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->count();

        // Get students who read in last 24 hours
        $studentsReadLast24h = User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->whereHas('readingLogs', function ($q) {
                $q->where('log_date', '>=', Carbon::now()->subDay());
            })
            ->count();

        return "{$studentsReadLast24h} / {$totalStudents}";
    }
    
    /**
     * Get total pages read in last 24 hours by teacher's students.
     */
    public static function getPagesReadLast24Hours(array $teacherClassIds): int
    {
        if (empty($teacherClassIds)) {
            return 0;
        }

        $pagesReadLast24h = UserReadingLog::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('log_date', '>=', Carbon::now()->subDay())
            ->sum('pages_read') ?? 0;

        return (int)$pagesReadLast24h;
    }
    
    /**
     * Get activities done in last 24 hours by teacher's students (including pending, excluding rejected).
     */
    public static function getActivitiesDoneLast24Hours(array $teacherClassIds): int
    {
        if (empty($teacherClassIds)) {
            return 0;
        }

        return UserActivity::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('activity_date', '>=', Carbon::now()->subDay())
            ->whereIn('status', [
                UserActivity::STATUS_PENDING,
                UserActivity::STATUS_APPROVED,
                UserActivity::STATUS_COMPLETED
            ])
            ->count();
    }

    // ========== STUDENT RANKING DATA (For Dashboard.php) ==========
    
    /**
     * Get all teacher's students with necessary relationships loaded for ranking calculations.
     */
    public static function getTeacherStudentsWithRelationships(array $teacherClassIds): Collection
    {
        if (empty($teacherClassIds)) {
            return collect(); // Return empty collection
        }

        return User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->withCount([
                'userBooks as books_completed' => function ($q) {
                    $q->where('completed', true);
                },
                'userRewards as badges_count' => function ($q) {
                    $q->whereHas('reward', function ($subQ) {
                        $subQ->where('reward_type', Reward::TYPE_BADGE);
                    });
                }
            ])
            ->get()
            ->map(function ($student) {
                // Calculate complex metrics that require method calls
                $student->current_level_number = $student->getCurrentLevel() ? $student->getCurrentLevel()->nr : 0;
                $student->reading_streak = $student->getCurrentReadingStreak();
                $student->activity_points = $student->getTotalActivityPoints();
                $student->days_since_reading = $student->getDaysSinceLastReading();
                return $student;
            });
    }

    // ========== TEACHER ACTIVITY DATA (For Mobile Interface) ==========

    /**
     * Get recent student book activities (for preview on teacher home screen).
     * Consolidates book completions and reading sessions.
     */
    public static function getRecentBookActivities(User $teacher, int $limit = 3): Collection
    {
        $teacherClassIds = self::getTeacherClassIds($teacher);

        if (empty($teacherClassIds)) {
            return collect();
        }

        // Get recent book completions (enddate is not null) and book beginnings (enddate is null and start_date is within last 24 hours )
        $booksRecentlyBeganAndCompleted = UserBook::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where(function ($query) {
                $query->whereRaw('DATE(end_date) >= ?', [Carbon::now()->subDay()->format('Y-m-d')])
                    ->orWhere(function ($subQuery) {
                        $subQuery->whereNull('end_date')
                            ->whereRaw('DATE(start_date) >= ?', [Carbon::now()->subDay()->format('Y-m-d')]);
                    });
            })
            ->orderBy('end_date', 'desc')
            ->limit($limit)
            ->with(['user', 'book'])
            ->get()
            ->map(function ($userBook) {
                return [
                    'type' => $userBook->end_date ? 'completed' : 'began',
                    'student_name' => $userBook->user->name,
                    'student_avatar' => $userBook->user->avatar ?? $userBook->user->getAvatarDisplayImage(),
                    'book_title' => $userBook->book->name,
                    'book_author' => $userBook->book->author,
                    'book_cover' => $userBook->book->cover_image,
                ];
            });

        return $booksRecentlyBeganAndCompleted;
    }
    
    /**
     * Get activities pending approval for teacher review.
     */
    public static function getPendingActivities(User $teacher, int $limit = 10): Collection
    {
        $teacherClassIds = self::getTeacherClassIds($teacher);

        if (empty($teacherClassIds)) {
            return collect();
        }

        return UserActivity::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('status', UserActivity::STATUS_PENDING)
            ->orderBy('activity_date', 'desc')
            ->with(['user', 'book', 'activity'])
            ->limit($limit)
            ->get()
            ->map(function ($userActivity) {
                return [
                    'id' => $userActivity->id,
                    'student_name' => $userActivity->user->name,
                    'student_avatar' => $userActivity->user->getAvatarDisplayImage(),
                    'book_title' => $userActivity->book->name,
                    'activity_name' => $userActivity->activity->name,
                    'activity_type' => $userActivity->activity->activity_type,
                    'time' => $userActivity->activity_date->diffForHumans(),
                    'points' => $userActivity->activity->points ?? 0,
                ];
            });
    }

    /**
     * Get count of activities pending approval for teacher.
     */
    public static function getPendingActivitiesCount(User $teacher): int
    {
        $teacherClassIds = self::getTeacherClassIds($teacher);

        if (empty($teacherClassIds)) {
            return 0;
        }

        return UserActivity::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('status', UserActivity::STATUS_PENDING)
            ->count();
    }

    // ========== STUDENT STATISTICS (For Mobile Student Interface) ==========

    /**
     * Get comprehensive student statistics for mobile home screen.
     * Consolidates all student metrics into a single optimized query.
     */
    public static function getStudentStats(User $student): array
    {
        return [
            'books_read' => $student->getTotalBooksCompleted(),
            'rewards_earned' => $student->getTotalRewards(),
            'total_points' => ($student->getTotalPagePoints() ?? 0) + ($student->getTotalActivityPoints() ?? 0),
            'current_level' => $student->getCurrentLevelNumber(),
        ];
    }

    /**
     * Get detailed student statistics for profile screen.
     */
    public static function getStudentProfileStats(User $student): array
    {
        return [
            'books_completed' => $student->getTotalBooksCompleted(),
            'page_points' => $student->getTotalPagePoints(),
            'rewards_earned' => $student->getTotalRewards(),
            'activity_points' => $student->getTotalActivityPoints(),
            'level_progress' => $student->getLevelProgress(),
        ];
    }

    // ========== RESOURCE METRICS (For MoonShine Resources) ==========

    /**
     * Get student resource metrics for StudentResource.
     */
    public static function getStudentResourceMetrics($query): array
    {
        return [
            'total_students' => $query->count(),
            'active_readers_today' => $query->withCount(['readingLogs' => function ($q) {
                $q->whereDate('log_date', today());
            }])->get()->sum('reading_logs_count'),
            'books_completed_this_month' => $query->withCount(['readingLogs' => function ($q) {
                $q->where('book_completed', true)
                  ->whereMonth('log_date', now()->month)
                  ->whereYear('log_date', now()->year);
            }])->get()->sum('reading_logs_count'),
        ];
    }

    // ========== STUDENT RANKING METHODS (For Dashboard.php) ==========

    /**
     * Get top 5 students by books completed.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getTopStudentsByBooksRead(Collection $students): Collection
    {
        return $students->sortByDesc('books_completed')->take(5);
    }

    /**
     * Get top 5 students by level.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getTopStudentsByLevel(Collection $students): Collection
    {
        return $students->sortByDesc('current_level_number')->take(5);
    }

    /**
     * Get top 5 students by badges.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getTopStudentsByBadges(Collection $students): Collection
    {
        return $students->sortByDesc('badges_count')->take(5);
    }

    /**
     * Get top 5 students by reading streak.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getTopStudentsByReadingStreak(Collection $students): Collection
    {
        return $students->sortByDesc('reading_streak')->take(5);
    }

    /**
     * Get top 5 students by activity points.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getTopStudentsByActivityScore(Collection $students): Collection
    {
        return $students->sortByDesc('activity_points')->take(5);
    }

    /**
     * Get bottom 5 students by books completed.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getBottomStudentsByBooksRead(Collection $students): Collection
    {
        return $students->sortBy('books_completed')->take(5);
    }

    /**
     * Get bottom 5 students by level.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getBottomStudentsByLevel(Collection $students): Collection
    {
        return $students->sortBy('current_level_number')->take(5);
    }

    /**
     * Get bottom 5 students by badges.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getBottomStudentsByBadges(Collection $students): Collection
    {
        return $students->sortBy('badges_count')->take(5);
    }

    /**
     * Get students with longest reading gap.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getStudentsWithLongestReadingGap(Collection $students): Collection
    {
        $topStreakStudents = $students->sortByDesc('days_since_reading')->take(5);
        $topStreakStudents->transform(function ($student) {
            if ($student->days_since_reading == 999) {
                $student->days_since_reading = __('admin.never_read');
            }
            return $student;
        });
        return $topStreakStudents;
    }

    /**
     * Get bottom 5 students by activity points.
     * Uses pre-loaded student data to avoid additional queries.
     */
    public static function getBottomStudentsByActivityScore(Collection $students): Collection
    {
        return $students->sortBy('activity_points')->take(5);
    }

    // ========== UTILITY METHODS ==========

    /**
     * Get time-based greeting for mobile interfaces.
     */
    public static function getTimeBasedGreeting(): string
    {
        $hour = now()->hour;

        if ($hour < 12) {
            return __('mobile.good_morning');
        } elseif ($hour < 17) {
            return __('mobile.good_afternoon');
        } else {
            return __('mobile.good_evening');
        }
    }
}
