<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Book;
use App\Models\UserBook;
use App\Models\Activity;
use App\Models\UserActivity;
use App\Models\Avatar;
use App\Models\UserPoint;
use App\Models\BookQuestion;
use App\Models\BookWord;

use Illuminate\Support\Facades\Log;

class Activities extends Component
{
    public $book;
    public $userBook;
    public $availableActivities = [];
    public $completedActivities = [];
    public $pendingActivities = [];
    public $rejectedActivities = [];
    public $failedActivities = [];
    public $avatarUnlockProgress = null;

    public function mount($book)
    {
        $this->book = Book::with(['authors', 'publisher'])->findOrFail($book);
        $this->userBook = UserBook::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->first();

        $this->loadActivities();
        $this->calculateAvatarUnlockProgress();
    }

    public function startActivity($activityId)
    {
        // Check if book is active
        if (!$this->book->canCreateActivities()) {
            session()->flash('error', __('mobile.book_inactive_warning'));
            return;
        }

        $user = Auth::user();
//        $activity = Activity::resolvedForUser($user)->find($activityId);
        $activity = Activity::find($activityId);

        if (!$activity) {
            session()->flash('error', __('mobile.activity_not_found'));
            return;
        }

        // Check if user can start/retry this activity
        $existingUserActivities = UserActivity::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->where('activity_id', $activityId)
            ->get();

        if ($existingUserActivities->isNotEmpty()) {
            // For test activities, check retry limits using class-specific settings
            if ($activity->isTestActivity()) {
                $attemptCount = $existingUserActivities->count();

                // Activity is already resolved with class-specific settings
                if ($attemptCount >= $activity->allowed_tries) {
                    session()->flash('error', __('mobile.test_retry_limit_exceeded'));
                    return;
                }

                // Check if there's a pending test (shouldn't happen but just in case)
                $pendingTest = $existingUserActivities->where('status', UserActivity::STATUS_PENDING)->first();
                if ($pendingTest) {
                    session()->flash('error', __('mobile.test_in_progress'));
                    return;
                }
            } else {
                // For non-test activities, use the old logic
                session()->flash('error', __('mobile.activity_already_started'));
                return;
            }
        }

        // Redirect to specific activity form based on activity_type
        switch ($activity->activity_type) {
            case Activity::ACTIVITY_TYPE_WRITING:
                return redirect()->route('mobile.activities.writing', [
                    'book' => $this->book->id,
                    'activity' => $activityId
                ]);
            case Activity::ACTIVITY_TYPE_RATING:
                return redirect()->route('mobile.activities.rating', [
                    'book' => $this->book->id,
                    'activity' => $activityId
                ]);
            case Activity::ACTIVITY_TYPE_MEDIA:
                return redirect()->route('mobile.activities.upload', [
                    'book' => $this->book->id,
                    'activity' => $activityId
                ]);
            case Activity::ACTIVITY_TYPE_QUIZ:
            case Activity::ACTIVITY_TYPE_VOCABULARY_TEST:
                return redirect()->route('mobile.activities.test', [
                    'book' => $this->book->id,
                    'activity' => $activityId
                ]);
            default:
                session()->flash('error', __('mobile.unknown_activity_type'));
                return;
        }
    }

    private function loadActivities()
    {
        $userId = Auth::id();
        $bookId = $this->book->id;

        // Get all activities with class-specific resolution
        $user = Auth::user();
        $allActivities = Activity::where('activities.active', true)->get();

        // Get user's activities for this book
        $userActivities = UserActivity::where('user_id', $userId)
            ->where('book_id', $bookId)
            ->get()
            ->groupBy('activity_id');

        $this->availableActivities = [];
        $this->completedActivities = [];
        $this->pendingActivities = [];
        $this->rejectedActivities = [];
        $this->failedActivities = [];

        foreach ($allActivities as $activity) {
            $activityUserActivities = $userActivities->get($activity->id, collect());

            // For test activities, check if book has enough content
            if ($activity->isTestActivity()) {
                $hasEnoughContent = $this->bookHasEnoughContentForActivity($activity);
                if (!$hasEnoughContent) {
                    // Skip this activity if book doesn't have enough content
                    continue;
                }
            }

            // Activity is already resolved with class-specific settings
            $activityData = [
                'id' => $activity->id,
                'name' => $activity->name,
                'description' => $activity->description,
                'activity_type' => $activity->activity_type,
                'activity_type_name' => $activity->activity_type_name,
                'points' => $activity->points,
                'required' => $activity->required,
                'need_approval' => $activity->need_approval,
                'allowed_tries' => $activity->allowed_tries,
                'is_required' => $activity->required,
            ];

            // Get the latest user activity for this activity
            $latestUserActivity = $activityUserActivities->sortByDesc('activity_date')->first();

            if (!$latestUserActivity) {
                // Activity not started yet
                $this->availableActivities[] = $activityData;
            } elseif ($latestUserActivity->status === UserActivity::STATUS_APPROVED || $latestUserActivity->status === UserActivity::STATUS_COMPLETED) {
                // Activity completed and approved or completed without approval
                $activityData['user_activity'] = $latestUserActivity->toArray();
                if ($activity->isTestActivity()) {
                    $activityData['test_results'] = $latestUserActivity->getTestResults();
                }
                $this->completedActivities[] = $activityData;
            } elseif ($latestUserActivity->status === UserActivity::STATUS_REJECTED) {
                // Activity rejected - check if can retry using resolved activity settings
                $attemptCount = $activityUserActivities->count();
                $canRetry = $attemptCount < $activityData['allowed_tries'];

                $activityData['user_activity'] = $latestUserActivity->toArray();
                $activityData['can_retry'] = $canRetry;
                $activityData['attempt_count'] = $attemptCount;
                // allowed_tries already resolved with class-specific settings

                if ($canRetry) {
                    $this->availableActivities[] = $activityData;
                } else {
                    $activityData['retry_exceeded'] = true;
                    $this->rejectedActivities[] = $activityData;
                }
            } elseif ($latestUserActivity->status === UserActivity::STATUS_FAILED) {
                // Test activity failed - check if can retry using resolved activity settings
                $attemptCount = $activityUserActivities->count();
                $canRetry = $attemptCount < $activityData['allowed_tries'];

                $activityData['user_activity'] = $latestUserActivity->toArray();
                $activityData['can_retry'] = $canRetry;
                $activityData['attempt_count'] = $attemptCount;
                // allowed_tries already resolved with class-specific settings
                $activityData['test_results'] = $latestUserActivity->getTestResults();

                if ($canRetry) {
                    $this->availableActivities[] = $activityData;
                } else {
                    $activityData['retry_exceeded'] = true;
                }
                $this->failedActivities[] = $activityData;
            } elseif ($latestUserActivity->status === UserActivity::STATUS_PENDING) {
                // Activity submitted but pending approval
                $activityData['user_activity'] = $latestUserActivity->toArray();
                $this->pendingActivities[] = $activityData;
            }
        }
        // sort available activities by required first
        usort($this->availableActivities, function ($a, $b) {
            return ($b['is_required'] ?? false) <=> ($a['is_required'] ?? false);
        });
    }

    /**
     * Check if book has enough content for a test activity.
     */
    private function bookHasEnoughContentForActivity(Activity $activity): bool
    {
        if ($activity->isQuiz()) {
            // Check if book has at least 10 questions
            $questionCount = BookQuestion::where('book_id', $this->book->id)
                ->where('is_active', true)
                ->count();
            return $questionCount >= 10;
        }

        if ($activity->isVocabularyTest()) {
            // Check if book has at least 10 words
            $wordCount = BookWord::where('book_id', $this->book->id)
                ->where('is_active', true)
                ->count();
            return $wordCount >= 10;
        }

        return true;
    }

    public function viewActivity($activityId)
    {
        $user = Auth::user();
//        $activity = Activity::resolvedForUser($user)->findOrFail($activityId);
        $activity = Activity::findOrFail($activityId);

        // Redirect to view mode based on activity type
        switch ($activity->activity_type) {
            case Activity::ACTIVITY_TYPE_WRITING:
                return redirect()->route('mobile.activities.writing', [
                    'book' => $this->book->id,
                    'activity' => $activityId,
                    'mode' => 'view'
                ]);
            case Activity::ACTIVITY_TYPE_RATING:
                return redirect()->route('mobile.activities.rating', [
                    'book' => $this->book->id,
                    'activity' => $activityId,
                    'mode' => 'view'
                ]);
            case Activity::ACTIVITY_TYPE_MEDIA:
                return redirect()->route('mobile.activities.upload', [
                    'book' => $this->book->id,
                    'activity' => $activityId,
                    'mode' => 'view'
                ]);
            case Activity::ACTIVITY_TYPE_QUIZ:
            case Activity::ACTIVITY_TYPE_VOCABULARY_TEST:
                return redirect()->route('mobile.activities.test', [
                    'book' => $this->book->id,
                    'activity' => $activityId,
                    'mode' => 'view'
                ]);
            default:
                session()->flash('error', __('mobile.unknown_activity_type'));
                return;
        }
    }

    public function resubmitActivity($activityId)
    {
        $user = Auth::user();
        $activity = Activity::findOrFail($activityId);

        // Redirect to edit mode based on activity type
        switch ($activity->activity_type) {
            case Activity::ACTIVITY_TYPE_WRITING:
                return redirect()->route('mobile.activities.writing', [
                    'book' => $this->book->id,
                    'activity' => $activityId,
                    'mode' => 'edit'
                ]);
            case Activity::ACTIVITY_TYPE_RATING:
                return redirect()->route('mobile.activities.rating', [
                    'book' => $this->book->id,
                    'activity' => $activityId,
                    'mode' => 'edit'
                ]);
            case Activity::ACTIVITY_TYPE_MEDIA:
                return redirect()->route('mobile.activities.upload', [
                    'book' => $this->book->id,
                    'activity' => $activityId,
                    'mode' => 'edit'
                ]);
            default:
                session()->flash('error', __('mobile.unknown_activity_type'));
                return;
        }
    }



    private function calculateAvatarUnlockProgress()
    {
        $user = Auth::user();
        $currentActivityPoints = UserPoint::where('user_id', $user->id)
        ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
        ->sum('points') ?? 0;

        // Get the next locked avatar
        $nextAvatar = Avatar::where('required_points', '>', $currentActivityPoints)
            ->where('active', true)
            ->orderBy('required_points', 'asc')
            ->first();

        if (!$nextAvatar) {
            // User has unlocked all avatars
            $this->avatarUnlockProgress = null;
            return;
        }

        // Calculate points needed for next avatar
        $pointsNeeded = $nextAvatar->required_points - $currentActivityPoints;

        // Get the 3 highest point activities from this book that user hasn't completed
//        $totalPossiblePoints = Activity::resolvedForUser($user)
//            ->where('book_id', $this->book->id)
//            ->where('activities.active', true)
        $totalPossiblePoints = Activity::where('activities.active', true)
            ->get()
            ->sortByDesc('points')->take(3)->sum('points');

        // Only show progress if the top 3 activities can unlock new avatars
        if ($totalPossiblePoints >= $pointsNeeded) {
            $this->avatarUnlockProgress = [
                'points_needed' => $pointsNeeded,
                'next_avatar' => $nextAvatar,
                'can_unlock' => true
            ];
        } else {
            $this->avatarUnlockProgress = null;
        }
    }

    public function getActivityTypeIcon($activityType)
    {
        return match($activityType) {
            Activity::ACTIVITY_TYPE_WRITING => '✍️',
            Activity::ACTIVITY_TYPE_RATING => '⭐',
            Activity::ACTIVITY_TYPE_MEDIA => '🎨',  
            Activity::ACTIVITY_TYPE_QUIZ => '📝',
            Activity::ACTIVITY_TYPE_VOCABULARY_TEST => '📚',
            default => '',
        };
    }

    public function render()
    {
        return view('livewire.mobile.activities');
    }
}
