<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Team;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Image;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Number;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Http\Request;
use MoonShine\UI\Fields\Field;

#[Icon('users')]
class TeamResource extends BaseResource
{
    protected string $model = Team::class;

    protected string $column = 'name';

    protected array $with = ['leader', 'users', 'teamRewards'];

    public function getTitle(): string
    {
        return __('admin.teams');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Image::make(__('admin.logo'), 'logo'),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Text::make(__('admin.leader'), 'leader_name')
                ->sortable(),

            Number::make(__('admin.members'), 'member_count')
                ->sortable(),

            Number::make(__('admin.total_points'), 'total_points')
                ->sortable(),

            Number::make(__('admin.badges'), 'badge_count')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required(),

                Image::make(__('admin.logo'), 'logo')
                    ->dir('teams')
                    ->removable()
                    ->hint(__('admin.team_logo_hint')),

                BelongsTo::make(
                    __('admin.leader'),
                    'leader',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class
                )
                    ->nullable()
                    ->asyncSearch(
                        'name',
                        searchQuery: function (Builder $query, Request $request, Field $field): Builder {
                            // Apply role-based filtering using User model's local scope
                            return User::query()->forCurrentUser()->where('name', 'like', '%' . $request->get('search', '') . '%');
                        }
                    )
                    ->hint(__('admin.team_leader_hint')),
                    
                Switcher::make(__('admin.active'), 'active')
                    ->default(true),
            ]),

            Box::make(__('admin.team_members'), [
                BelongsToMany::make(
                    __('admin.members'),
                    'users',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class
                )
                    ->asyncSearch(
                        'name',
                        searchQuery: function (Builder $query, Request $request, Field $field): Builder {
                            // Apply role-based filtering using User model's local scope
                            return User::query()->forCurrentUser()->where('name', 'like', '%' . $request->get('search', '') . '%');
                        }
                    )
                    ->hint(__('admin.team_members_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Image::make(__('admin.logo'), 'logo')
                ->disk('public')
                ->allowedExtensions(['jpg', 'jpeg', 'png', 'gif'])
                ->removable(false),

            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.leader'), 'leader_name'),
            Number::make(__('admin.members'), 'member_count'),
            Number::make(__('admin.total_points'), 'total_points'),
            Number::make(__('admin.badges'), 'badge_count'),
            Switcher::make(__('admin.active'), 'active'),
            Text::make(__('admin.summary'), 'summary'),

            HasMany::make(__('admin.team_members'), 'userTeams', UserTeamResource::class),
            HasMany::make(__('admin.team_rewards'), 'teamRewards', TeamRewardResource::class),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'logo' => ['nullable', 'string'],
            'leader_user_id' => ['nullable', 'exists:users,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];

        // Add custom validation to ensure leader is a team member
        $rules['leader_user_id'][] = function ($attribute, $value, $fail) {
            if ($value) {
                $teamId = request()->route('resourceItem');
                if ($teamId) {
                    $team = Team::find($teamId);
                    if ($team && !$team->users()->where('users.id', $value)->exists()) {
                        $user = User::find($value);
                        $fail(__('admin.leader_must_be_team_member', [
                            'user' => $user->name,
                            'team' => $team->name
                        ]));
                    }
                }
            }
        };

        return $rules;
    }

    protected function search(): array
    {
        return ['name', 'leader.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        // Cast to concrete Eloquent Builder to access query methods
        if (!$builder instanceof EloquentBuilder) {
            return $builder;
        }

        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all teams
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see teams with members from their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('users.activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            });
        }

        // Teacher can see teams with students from their classes
        if ($user->isTeacher()) {
            $userClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($userClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('users.activeUserClasses', function ($q) use ($userClassIds) {
                $q->whereIn('class_id', $userClassIds);
            });
        }

        // Students and Parents can only see teams they are members of
        return $builder->whereHas('users', function ($q) use ($user) {
            $q->where('users.id', $user->id);
        });
    }
}
