<x-layouts.mobile title="{{ __('mobile.app_name') }}">
    <div class="flex flex-col items-center justify-center min-h-screen bg-purple-300  p-8"
         data-redirect-url="{{ auth()->check() ? app(\App\Http\Controllers\MobileController::class)->getRedirectUrl() : route('mobile.login') }}">
        <!-- App Logo with Zoom Animation -->
        <div class="flex items-center justify-center mb-8">
            <img src="/images/logo-img.png" alt="App Logo" class="w-24 h-24 animate-zoom-in-out">
        </div>
    </div>

    <style>
        @keyframes zoom-in-out {
            0% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .animate-zoom-in-out {
            animation: zoom-in-out 2s ease-in-out;
        }
    </style>

    <script>
        // Auto-redirect after exactly 2 seconds
        setTimeout(() => {
            const container = document.querySelector('[data-redirect-url]');
            const redirectUrl = container.getAttribute('data-redirect-url');
            window.location.href = redirectUrl;
        }, 2000);
    </script>
</x-layouts.mobile>
