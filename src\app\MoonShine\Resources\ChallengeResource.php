<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Challenge;
use App\Models\Reward;
use App\Models\Task;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Image;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\RelationRepeater;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Components\ActionButton;
use MoonShine\Support\ListOf;
use MoonShine\Support\Enums\JsEvent;
use MoonShine\Support\AlpineJs;

#[Icon('trophy')]
class ChallengeResource extends BaseResource
{
    protected string $model = Challenge::class;

    protected string $column = 'name';

    protected array $with = ['challengeTasks.task', 'schools', 'classes', 'teams'];

    public function getTitle(): string
    {
        return __('admin.challenges');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.challenge_name'), 'name')
                ->sortable(),

            Text::make(__('admin.date_range'), 'date_range_display'),

            Number::make(__('admin.tasks_count'), 'tasks_count')
                ->sortable(),

            Text::make(__('admin.status'), 'status_display'),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make(__('admin.basic_info'), [
                Text::make(__('admin.challenge_name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_challenge_name')),

                Textarea::make(__('admin.challenge_description'), 'description')
                    ->placeholder(__('admin.enter_challenge_description')),

                Image::make(__('admin.challenge_image'), 'image')
                    ->dir('challenges')
                    ->keepOriginalFileName()
                    ->removable()
                    ->hint(__('admin.challenge_image_hint')),

                Date::make(__('admin.start_date'), 'start_date')
                    ->required()
                    ->default(now()->format('Y-m-d')),

                Date::make(__('admin.end_date'), 'end_date')
                    ->required()
                    ->default(now()->addDays(30)->format('Y-m-d')),

                Textarea::make(__('admin.prize'), 'prize')
                    ->placeholder(__('admin.enter_prize_description')),
            ]),

            Box::make(__('admin.challenge_tasks'), [
                RelationRepeater::make(__('admin.challenge_tasks'), 'challengeTasks')
                    ->fields([
                    BelongsTo::make(
                        __('admin.task'),
                        'task',
                        formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                        resource: TaskResource::class
                    )
                        ->required()
                        ->searchable(),
                    Date::make(__('admin.start_date'), 'start_date')
                        ->required()
                        ->default(now()->format('Y-m-d')),

                    Date::make(__('admin.end_date'), 'end_date')
                        ->required()
                        ->default(now()->addDays(30)->format('Y-m-d')),
                    BelongsTo::make(
                        __('admin.reward'),
                        'reward',
                        formatted: fn(?Reward $reward) => $reward ? $reward->name . ' (' . $reward->reward_type_display . ')' : null,
                        resource: RewardResource::class
                    )
                        ->nullable()
                        ->searchable()
                    ])
                    ->vertical()
                    ->creatable()
                    ->removable(),
            ]),

            Box::make(__('admin.participants'), [
                BelongsToMany::make(__('admin.participating_schools'), 'schools', 'name', SchoolResource::class)
                    ->selectMode()
                    ->valuesQuery(fn($query) => $this->getSchoolsQuery($query))
                    ->hint(__('admin.select_schools_for_challenge')),

                BelongsToMany::make(__('admin.participating_classes'), 'classes', 'name', SchoolClassResource::class)
                    ->selectMode()
                    ->valuesQuery(fn($query) => $this->getClassesQuery($query))
                    ->hint(__('admin.select_classes_for_challenge')),

                BelongsToMany::make(__('admin.participating_teams'), 'teams', 'name', TeamResource::class)
                    ->selectMode()
                    ->valuesQuery(fn($query) => $this->getTeamsQuery($query))
                    ->hint(__('admin.select_teams_for_challenge')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Image::make(__('admin.challenge_image'), 'image'),

            Text::make(__('admin.challenge_name'), 'name'),
            Textarea::make(__('admin.challenge_description'), 'description'),
            Text::make(__('admin.date_range'), 'date_range_display'),
            Textarea::make(__('admin.prize'), 'prize'),
            Text::make(__('admin.status'), 'status_display'),
            Switcher::make(__('admin.active'), 'active'),
            
            Number::make(__('admin.tasks_count'), 'tasks_count'),
            Number::make(__('admin.participants_count'), 'participants_count'),
            Text::make(__('admin.completion_rate'), 'completion_rate_display'),
            Text::make(__('admin.summary'), 'summary'),

            HasMany::make(__('admin.challenge_tasks'), 'challengeTasks', ChallengeTaskResource::class),
            HasMany::make(__('admin.user_tasks'), 'userTasks', UserTaskResource::class)
              ->async()
              ->creatable(),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'image' => ['image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'prize' => ['nullable', 'string'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];

        return $rules;
    }

    protected function search(): array
    {
        return ['name', 'description', 'prize'];
    }

    protected function getDefaultSort(): array
    {
        return ['start_date' => 'desc'];
    }

    /**
     * Get schools query for participation selection.
     */
    private function getSchoolsQuery($query)
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $query->where('id', 0);
        }

        // System Admin can select all schools
        if ($user->isSystemAdmin()) {
            return $query;
        }

        // School Admin can select their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $query->where('id', 0);
            }

            return $query->whereIn('id', $userSchoolIds);
        }

        // Teachers can select schools where they have classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $query->where('id', 0);
            }

            return $query->whereHas('schoolClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('id', $teacherClassIds);
            });
        }

        return $query->where('id', 0);
    }

    /**
     * Get classes query for participation selection.
     */
    private function getClassesQuery($query)
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $query->where('id', 0);
        }

        // System Admin can select all classes
        if ($user->isSystemAdmin()) {
            return $query;
        }

        // School Admin can select classes in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $query->where('id', 0);
            }

            return $query->whereIn('school_id', $userSchoolIds);
        }

        // Teachers can select their assigned classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $query->where('id', 0);
            }

            return $query->whereIn('id', $teacherClassIds);
        }

        return $query->where('id', 0);
    }

    /**
     * Get teams query for participation selection.
     */
    private function getTeamsQuery($query)
    {
        // For simplicity, allow all active teams for now
        // This could be filtered based on user permissions if needed
        return $query->where('active', true);
    }



    /**
     * Toggle challenge active status and process assignments.
     */
    public function toggleActive(): void
    {
        $challenge = $this->getItem();

        if (!$challenge instanceof Challenge) {
            session()->flash('error', __('admin.challenge_not_found'));
            return;
        }

        $wasActive = $challenge->active;
        $challenge->active = !$challenge->active;
        $challenge->save();

        if ($challenge->active && !$wasActive) {
            // Challenge was activated - show success message
            session()->flash('success', __('admin.challenge_activated_successfully'));
        } elseif (!$challenge->active && $wasActive) {
            // Challenge was deactivated
            session()->flash('success', __('admin.challenge_deactivated_successfully'));
        }
    }

    protected function indexButtons(): ListOf
    {
        return parent::indexButtons()
            ->add(
            ActionButton::make('')
                ->method('toggleActive')
                ->withConfirm(__('admin.confirm_toggle_challenge_status'))
                ->secondary()
                ->icon('bolt')
                ->async(
                    events: [ AlpineJs::event(JsEvent::TABLE_UPDATED, $this->getListComponentName())] )
            );
    }

    
}
