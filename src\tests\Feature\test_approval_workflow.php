<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\UserActivityReview;
use App\Models\UserActivity;
use App\Models\UserPoint;

try {
    echo "=== TESTING APPROVAL WORKFLOW ===\n\n";
    
    // Find a pending review
    $pendingReview = UserActivityReview::where('status', UserActivityReview::STATUS_WAITING)->first();
    
    if (!$pendingReview) {
        echo "❌ No pending reviews found. Create a user activity that requires approval first.\n";
        exit;
    }
    
    echo "Found pending review:\n";
    echo "- Review ID: {$pendingReview->id}\n";
    echo "- User Activity ID: {$pendingReview->user_activity_id}\n";
    echo "- Current Status: {$pendingReview->status_name}\n";
    echo "- User Activity Status: {$pendingReview->userActivity->status_name}\n\n";
    
    // Check current points for the user
    $userId = $pendingReview->userActivity->user_id;
    $currentPoints = UserPoint::where('user_id', $userId)->sum('points');
    echo "User's current total points: {$currentPoints}\n\n";
    
    // Approve the review
    echo "Approving the review...\n";
    $pendingReview->update([
        'status' => UserActivityReview::STATUS_APPROVED,
        'reviewed_by' => 1, // Assuming user ID 1 exists
        'feedback' => 'Test approval via script',
        'review_date' => now()->toDateString(),
    ]);
    
    // Refresh the models
    $pendingReview->refresh();
    $pendingReview->userActivity->refresh();
    
    echo "After approval:\n";
    echo "- Review Status: {$pendingReview->status_name}\n";
    echo "- User Activity Status: {$pendingReview->userActivity->status_name}\n";
    
    // Check if points were created
    $newPoints = UserPoint::where('user_id', $userId)->sum('points');
    $pointsAdded = $newPoints - $currentPoints;
    echo "- Points added: {$pointsAdded}\n";
    
    // Check if there's a point record with the correct source_id
    $activityPoints = UserPoint::where('user_id', $userId)
        ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
        ->where('source_id', $pendingReview->user_activity_id)
        ->first();
    
    if ($activityPoints) {
        echo "✅ Activity points created successfully!\n";
        echo "- Points: {$activityPoints->points}\n";
        echo "- Source ID: {$activityPoints->source_id}\n";
        echo "- Point Type: {$activityPoints->point_type} (Activity)\n";
    } else {
        echo "❌ Activity points were not created.\n";
    }
    
    echo "\n✅ Approval workflow test completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
