<div class="min-h-screen bg-base-200">
    <!-- Header -->
    <div class="mobile-header bg-cover bg-center bg-no-repeat" style="background-image: url('/images/header-bg.jpg');">
        <div class="flex items-start justify-between w-full">
            <!-- Left side: Avatar and greeting -->
            <div class="flex items-center space-x-3 mb-26">
                <!-- User Avatar -->
                <a href="{{ route('mobile.me') }}"> 
                    <div class="w-16 h-16 rounded-full overflow-hidden border-2 border-white/20">
                        @if($avatarImage)
                            <img src="{{ asset('storage/' . $avatarImage) }}" alt="Avatar" class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                                <span class="text-white text-lg font-bold">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                </a> 

                <!-- Greeting and Name -->
                <div>
                    <p class="text-black text-lg opacity-90">{{ $greeting }},</p>
                    <h1 class="text-black text-lg font-bold">{{ $user->name }}</h1>
                </div>
            </div>

            <!-- Right side: Messages and Power button -->
            <div class="flex items-center space-x-2" x-data="homeMessageBadge">
                <!-- Messages Icon with Badge -->
                <a href="{{ route('mobile.messages') }}" class="relative text-white hover:text-gray-200 transition-colors p-2">
                    <svg class="w-6 h-6 {{ ($unreadMessagesCount > 0 ? 'stroke-violet-600' : 'stroke-gray-600') }}" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    @if($unreadMessagesCount > 0)
                        <span class="absolute top-0 right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                            {{ $unreadMessagesCount > 9 ? '9+' : $unreadMessagesCount }}
                        </span>
                    @else
                        <span x-show="showWarningBadge"  
                              class="absolute top-0 right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                            !
                        </span>
                    @endif
                </a>

                <!-- Logout Button -->
                <button wire:click="logout" class="text-white hover:text-gray-200 transition-colors p-2">
                    <img src="/images/power-icon.png" alt="Logout" class="w-6 h-6">
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
        <!-- Stats boxes in header -->         
        <div class="flex justify-center space-x-4 -mt-7 text-black font-bold bg-gray-50 rounded-lg mx-8 shadow">
            <x-mobile-stat-box  icon="level-icon.png" alt="{{ __('mobile.level') }}"  :value="$stats['current_level']"  />
            <x-mobile-stat-box  icon="books-icon.png" alt="{{ __('mobile.books_alt') }}"  :value="$stats['books_read']"  />
            <x-mobile-stat-box  icon="badge-icon.png" alt="{{ __('mobile.rewards_alt') }}"  :value="$stats['rewards_earned']"  />
            <x-mobile-stat-box  icon="star-icon.png" alt="{{ __('mobile.points_alt') }}"  :value="$stats['total_points']"  />
        </div>
    <div class="p-4">

        @if(!$hasReadingHistory)
            <h3 class="mb-2 font-semibold text-gray-900 text-lg text-center">{{ __('mobile.start_reading_journey') }}</h3>
            <x-mobile-add-book-card />
        @else
            <!-- Tab Navigation -->
            <div class="flex bg-white rounded-2xl p-1 mb-6 shadow-sm">
                <button
                    wire:click="setActiveTab('read')"
                    class="flex-1 py-2 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'read' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
                >
                    {{ __('mobile.read_tab') }}
                </button>
                <button
                    wire:click="setActiveTab('earn')"
                    class="flex-1 py-2 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'earn' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
                >
                    {{ __('mobile.earn_points_tab') }}
                </button>
            </div>

            @if($activeTab === 'read')
                <!-- Reading Tab Content -->
                <div class="space-y-4">
                    <!-- Reading Streak -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm">
                        <div class="flex items-center mb-3">
                            <span class="text-lg">🔥</span>
                            <h3 class="ml-2 font-semibold text-gray-900">{{ __('mobile.keep_streak_alive') }}</h3>
                        </div>

                        <div class="flex justify-between">
                            @foreach($readingStreak as $day)
                                <div class="text-center">
                                    <div class="mobile-streak-day {{ $day['has_reading'] ? 'completed' : ($day['is_today'] ? 'today' : 'missed') }}">
                                        @if($day['has_reading'])
                                            <img src="/images/logo-white-48.webp" class="w-6">
                                        @else
                                            {{ $day['short_day'] }}
                                        @endif
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1">{{ $day['day'] }}</p>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Assigned Tasks Progress -->
                    @if(count($assignedTasks) > 0)
                        <div class="bg-white rounded-2xl p-4 shadow-sm mb-4">
                            <div class="flex items-center mb-3">
                                <span class="text-lg">📋</span>
                                <h3 class="ml-2 font-semibold text-gray-900">{{ __('mobile.assigned_tasks') }}</h3>
                            </div>
                            <div class="space-y-3">
                                @foreach($assignedTasks as $task)
                                    <div class="border {{ $task['progress']['completed'] ? 'border-green-200 bg-green-50' : 'border-gray-200' }} rounded-xl p-3">
                                        <div class="flex justify-between items-start mb-2">
                                            <h4 class="font-medium text-gray-900 text-sm">{{ $task['name'] }}</h4>
                                            @if($task['due_date'])
                                                <span class="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                                                    {{ __('mobile.due_on') . ' ' . \Carbon\Carbon::parse($task['due_date'])->format('d.m.Y') }}
                                                </span>
                                            @endif
                                        </div>

                                        @if($task['description'])
                                            <p class="text-xs text-gray-600 mb-2">{{ $task['description'] }}</p>
                                        @endif

                                        <div class="flex justify-between items-center text-xs text-gray-500 mb-2">
                                            <span>{{ $task['type'] }} - {{ $task['cycle'] }}</span>
                                            <span>{{ $task['progress']['current'] }}/{{ $task['progress']['target'] }} {{ $task['unit'] }}</span>
                                        </div>

                                        <div class="mobile-progress-bar">
                                            <div class="mobile-progress-fill" style="width: {{ $task['progress']['percentage'] }}%"></div>
                                        </div>

                                        <div class="flex justify-between items-center text-xs text-gray-500 mt-2">
                                            @if($task['reward'])
                                                <div class="flex items-center mt-2 text-xs text-purple-600">
                                                    <span class="text-sm">🏆</span>
                                                    <span class="ml-1">{{ $task['reward']['name'] }}</span>
                                                </div>
                                            @endif
                                            @if($task['progress']['completed']) 
                                                <span class="ml-1 text-green-600">{{ __('mobile.congratulations') }}!</span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Active Challenges Progress -->
                    @if(count($activeChallenges) > 0)
                        <div class="bg-white rounded-2xl p-4 shadow-sm mb-4">
                            <div class="flex items-center mb-3">
                                <span class="text-lg">🏆</span>
                                <h3 class="ml-2 font-semibold text-gray-900">{{ __('mobile.active_challenges') }}</h3>
                            </div>
                            <div class="space-y-3">
                                @foreach($activeChallenges as $challenge)
                                    <div class="border border-gray-200 rounded-xl p-3">
                                        <div class="flex justify-between items-start mb-2">
                                            <h4 class="font-medium text-gray-900 text-sm">{{ $challenge['challenge_name'] }}</h4>
                                            <span class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                                {{ \Carbon\Carbon::parse($challenge['end_date'])->format('d/m') }}
                                            </span>
                                        </div>

                                        <p class="text-xs text-gray-600 mb-2">{{ $challenge['task_name'] }}</p>

                                        <div class="flex justify-between items-center text-xs text-gray-500 mb-2">
                                            <span>{{ $challenge['task_type'] }}</span>
                                            <span>{{ $challenge['progress']['current'] }}/{{ $challenge['progress']['target'] }}</span>
                                        </div>

                                        <div class="mobile-progress-bar">
                                            <div class="mobile-progress-fill bg-blue-500" style="width: {{ $challenge['progress']['percentage'] }}%"></div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Current Books -->
                    @if($currentBooks && $currentBooks->count() > 0)
                        @foreach($currentBooks as $userBook)
                            @php
                                $progress = $this->getReadingProgress($userBook);
                                $challengeInfo = $this->getChallengeInfo($userBook);
                                $totalPagesRead = $this->getTotalPagesRead($userBook);
                                $bookPageCount = $userBook->book->page_count ?? 0;
                            @endphp
                            <a href="{{ route('mobile.books.log', $userBook->book->id) }}" class="block">
                                <div class="bg-white rounded-2xl p-4 shadow-sm hover:shadow-md transition-shadow relative">
                                    @if($progress > 70)
                                        <div class="absolute top-1 right-1 px-2 py-1 text-xs font-semibold text-black bg-green-200 rounded-full z-10">
                                            {{ __('mobile.almost_there')  }}
                                        </div>
                                    @endif               
                                    <div class="flex items-center space-x-4">
                                        <img src="{{ $userBook->book->cover_image ? asset('storage/' . $userBook->book->cover_image) : '/images/default-book-cover.png' }}"
                                             alt="{!! $userBook->book->name !!}"
                                             class="mobile-book-cover">

                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-900 mb-1">{!! $userBook->book->name !!}</h4>
                                            <p class="text-sm text-gray-600 mb-2">{{ $userBook->book->authors->pluck('name')->join(', ') }}</p>

                                            <!-- Progress Bar -->
                                            <div class="mobile-progress-bar mb-2">
                                                <div class="mobile-progress-fill" style="width: {{ $progress }}%"></div>
                                            </div>

                                            <div class="flex justify-between text-sm text-gray-600">
                                                <span>{{ $challengeInfo['name'] }}</span>
                                                <span>{{ number_format($progress, 0) }}%</span>
                                            </div>

                                            @if($bookPageCount > 0)
                                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                                    <span>{{ $totalPagesRead }} / {{ $bookPageCount }} {{ __('mobile.pages') }}</span>
                                                    <span>{{ $bookPageCount - $totalPagesRead }} {{ __('mobile.pages_left') }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </a>
                        @endforeach
                    @else
                        <x-mobile-add-book-card />
                    @endif                    
                </div>
            @else
                <!-- Earn Points Tab Content -->
                <div class="space-y-4">
                    @if($completedBooks && $completedBooks->count() > 0)
                        @foreach($completedBooks as $userBook)
                            <a href="{{ route('mobile.books.activities', $userBook->book->id) }}" class="block">
                                <div class="bg-white rounded-2xl p-4 shadow-sm hover:shadow-md transition-shadow">
                                    <div class="flex items-center space-x-4">
                                        <div class="relative">
                                            <img src="{{ $userBook->book->cover_image ? asset('storage/' . $userBook->book->cover_image) : '/images/default-book-cover.png' }}"
                                                 alt="{{ $userBook->book->name }}"
                                                 class="mobile-book-cover">

                                            @if(!$userBook->completed)
                                                <div class="absolute top-0 right-0 transform translate-x-2 translate-y-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                                                    <span class="text-white text-xs font-bold">!</span>
                                                </div>
                                            @endif
                                        </div>

                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-900 mb-1">{!! $userBook->book->name !!}</h4>
                                            <p class="text-sm text-gray-600 mb-2">{{ $userBook->book->authors->pluck('name')->join(', ') }}</p>
                                            <p class="text-sm text-green-600 font-semibold">{{ __('mobile.completed') }}</p>                                            
                                            @if(!$userBook->completed)
                                                <p class="text-sm text-orange-800">
                                                    <span class="text-2xl">⚠️</span>{{ __('mobile.complete_required_activities_message') }}
                                                </p>
                                            @endif
                                        </div>

                                        <div class="bg-violet-600 text-white px-4 py-2 rounded-xl text-sm font-semibold">
                                            {{ __('mobile.activities') }}
                                        </div>
                                    </div>
                                </div>
                            </a>
                        @endforeach
                    @else
                        <div class="bg-white rounded-2xl p-8 text-center shadow-sm">
                            <p class="text-gray-600">{{ __('mobile.complete_books_message') }}</p>
                        </div>
                    @endif
                </div>
            @endif
        @endif
    </div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('homeMessageBadge', () => ({
        showWarningBadge: false,

        init() {
            this.checkPWAStatus();
        },

        checkPWAStatus() {
            // Check if app is installed
            const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                               window.navigator.standalone === true;

            // Check notification permission
            const notificationPermission = 'Notification' in window ? Notification.permission : 'denied';
            // Show warning badge if app is not installed OR notifications are not enabled
            this.showWarningBadge = !isInstalled || notificationPermission !== 'granted';
        }
    }));
});
</script>
