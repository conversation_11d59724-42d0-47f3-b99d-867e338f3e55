<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\BookDiscovery\Providers\FidanKitapProvider;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class FidanKitapProviderTest extends TestCase
{
    protected FidanKitapProvider $provider;

    protected function setUp(): void
    {
        parent::setUp();
        
        $config = config('book_discovery.sources.fidankitap');
        $this->provider = new FidanKitapProvider('fidankitap', $config);
    }

    /** @test */
    public function it_can_parse_fidan_kitap_structure()
    {
        $html = $this->getFidanKitapSampleHtml();

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseFidanKitapStructure');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertEquals('Test Kitap Adı', $result['name']);
        $this->assertEquals(['Test Yazar 1', 'Test Yazar 2'], $result['author']);
        $this->assertEquals('Test Yayınevi', $result['publisher']);
        $this->assertEquals('9789754034929', $result['isbn']);
        $this->assertEquals(250, $result['page_count']);
        $this->assertEquals(2023, $result['year']);
        $this->assertEquals(['Kategori 1', 'Kategori 2'], $result['category']);
        $this->assertEquals('Fidan Kitap', $result['source']);
    }

    /** @test */
    public function it_handles_missing_elements_gracefully()
    {
        $html = '<div class="prd_view_item">
                    <h1 class="contentHeader prdHeader">Minimal Book Title</h1>
                 </div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseFidanKitapStructure');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertEquals('Minimal Book Title', $result['name']);
        $this->assertArrayNotHasKey('author', $result);
        $this->assertArrayNotHasKey('publisher', $result);
    }

    /** @test */
    public function it_detects_book_not_found_correctly()
    {
        $notFoundHtml = '<div>kayıt bulunamadı</div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('isBookNotFound');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $notFoundHtml);
        $this->assertTrue($result);

        // Test with different not found patterns
        $patterns = [
            'sonuç bulunamadı',
            'ürün bulunamadı',
            'no results found',
            'aradığınız ürün bulunamadı'
        ];

        foreach ($patterns as $pattern) {
            $html = "<div>{$pattern}</div>";
            $result = $method->invoke($this->provider, $html);
            $this->assertTrue($result, "Failed to detect not found pattern: {$pattern}");
        }
    }

    /** @test */
    public function it_cleans_text_correctly()
    {
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('cleanText');
        $method->setAccessible(true);

        $testCases = [
            '  Test Text  ' => 'Test Text',
            'Text with spaces' => 'Text with spaces',
            "Multiple\n\nspaces   here" => 'Multiple spaces here',
            '&quot;Quoted Text&quot;' => '"Quoted Text"',
        ];

        foreach ($testCases as $input => $expected) {
            $result = $method->invoke($this->provider, $input);
            $this->assertEquals($expected, $result);
        }
    }

    /** @test */
    public function it_extracts_isbn_correctly()
    {
        $html = '<div class="prd_view_item">
                    <div class="prd_fields">
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Stok Kodu:</div>
                            <div class="prd_fields_text">9789754034929</div>
                        </div>
                    </div>
                 </div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseFidanKitapStructure');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertArrayHasKey('isbn', $result);
        $this->assertEquals('9789754034929', $result['isbn']);
    }

    /** @test */
    public function it_extracts_page_count_correctly()
    {
        $html = '<div class="prd_view_item">
                    <div class="prd_fields">
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Sayfa Sayısı:</div>
                            <div class="prd_fields_text">320 sayfa</div>
                        </div>
                    </div>
                 </div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseFidanKitapStructure');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertArrayHasKey('page_count', $result);
        $this->assertEquals(320, $result['page_count']);
    }

    /** @test */
    public function it_extracts_year_correctly()
    {
        $html = '<div class="prd_view_item">
                    <div class="prd_fields">
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Basım Tarihi:</div>
                            <div class="prd_fields_text">Ocak 2023</div>
                        </div>
                    </div>
                 </div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseFidanKitapStructure');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertArrayHasKey('year', $result);
        $this->assertEquals(2023, $result['year']);
    }

    /** @test */
    public function it_extracts_categories_correctly()
    {
        $html = '<div class="prd_view_item">
                    <div class="prd_fields">
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Kategori:</div>
                            <div class="prd_fields_text">
                                <a href="#">Roman</a>/
                                <a href="#">Türk Edebiyatı</a>
                            </div>
                        </div>
                    </div>
                 </div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseFidanKitapStructure');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNotNull($result);
        $this->assertArrayHasKey('category', $result);
        $this->assertEquals(['Roman', 'Türk Edebiyatı'], $result['category']);
    }

    /** @test */
    public function it_handles_empty_structure()
    {
        $html = '<div>No product structure here</div>';

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('parseFidanKitapStructure');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $html);

        $this->assertNull($result);
    }

    /** @test */
    public function it_post_processes_data_correctly()
    {
        $bookData = [
            'name' => '  Test Book  ',
            'author' => ['  Author 1  ', '  Author 2  '],
            'publisher' => '  Test Publisher  ',
            'category' => ['  Category 1  ', '  Category 2  ']
        ];

        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('postProcessFidanKitapData');
        $method->setAccessible(true);

        $result = $method->invoke($this->provider, $bookData);

        $this->assertEquals('Test Book', $result['name']);
        $this->assertEquals(['Author 1', 'Author 2'], $result['author']);
        $this->assertEquals('Test Publisher', $result['publisher']);
        $this->assertEquals(['Category 1', 'Category 2'], $result['category']);
        $this->assertEquals('Fidan Kitap', $result['source']);
    }

    /**
     * Get sample HTML structure for testing.
     */
    protected function getFidanKitapSampleHtml(): string
    {
        return '<div class="prd_view_item">
                    <h1 class="contentHeader prdHeader">Test Kitap Adı</h1>
                    <div class="prd_brand_box">
                        <div class="writers">
                            <a class="writer"><span>Test Yazar 1</span></a>
                            <a class="writer"><span>Test Yazar 2</span></a>
                        </div>
                        <a class="publisher"><span>Test Yayınevi</span></a>
                    </div>
                    <div class="prd_fields">
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Stok Kodu:</div>
                            <div class="prd_fields_text">9789754034929</div>
                        </div>
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Sayfa Sayısı:</div>
                            <div class="prd_fields_text">250</div>
                        </div>
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Basım Tarihi:</div>
                            <div class="prd_fields_text">2023</div>
                        </div>
                        <div class="prd_fields_item">
                            <div class="prd_fields_label">Kategori:</div>
                            <div class="prd_fields_text">
                                <a href="#">Kategori 1</a>/
                                <a href="#">Kategori 2</a>
                            </div>
                        </div>
                    </div>
                </div>';
    }
}
