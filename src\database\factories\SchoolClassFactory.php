<?php

namespace Database\Factories;

use App\Models\SchoolClass;
use App\Models\School;
use App\Models\EnumClassLevel;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SchoolClass>
 */
class SchoolClassFactory extends Factory
{
    protected $model = SchoolClass::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->randomElement(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']) . ' Sınıfı ' . fake()->unique()->numberBetween(1, 10000),
            'active' => true,
            'school_id' => function () {
                return School::factory()->create()->id;
            },
            'class_level_id' => function () {
                return EnumClassLevel::factory()->create()->id;
            },
        ];
    }

    /**
     * Indicate that the class should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'active' => false,
        ]);
    }
}
