<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Str;
use ReflectionClass;

use Illuminate\Auth\Events\{Attempting, Failed, Login};

/**
 * Event listener for logging authentication events using Spatie ActivityLog.
 * 
 * This listener captures all authentication-related events and logs them
 * with relevant context information including IP address, user agent, and
 * authentication guard information.
 * 
 * Based on the approach from: https://fsylum.net/blog/extending-spatie-laravel-activitylog-for-auth-events/
 */
class LogAuthEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the authentication event.
     * 
     * @param object $event The authentication event instance
     */
    public function handle(Attempting|Failed|Login $event): void
    {
        // Get the event type in kebab-case format
        $eventType = Str::of((new ReflectionClass($event))->getShortName())->kebab()->toString();
        
        // Get request context for additional logging information
        $request = request();
        $properties = [
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'guard' => $this->getGuardFromEvent($event),
            'timestamp' => now()->toISOString(),
        ];

        // Handle different event types with specific logic
        switch (true) {
            case $event instanceof Attempting:
                $this->logAttemptingEvent($event, $eventType, $properties);
                break;
                
            case $event instanceof Failed:
                $this->logFailedEvent($event, $eventType, $properties);
                break;
                
            default:
                $this->logStandardEvent($event, $eventType, $properties);
                break;
        }
    }

    /**
     * Log authentication attempt events.
     * 
     * @param Attempting $event
     * @param string $eventType
     * @param array $properties
     */
    private function logAttemptingEvent(Attempting $event, string $eventType, array $properties): void
    {
        // Add attempt-specific properties
        $properties['credentials'] = [
            'username' => $event->credentials['username'] ?? $event->credentials['email'] ?? 'unknown',
            'guard' => $event->guard,
            'remember' => $event->remember ?? false,
        ];

        // Log the attempt without a specific user (since authentication hasn't succeeded yet)
        activity('auth')
            ->withProperties($properties)
            ->event($eventType)
            ->log("Authentication attempt for user: {$properties['credentials']['username']}");
    }

    /**
     * Log failed authentication events.
     * 
     * @param Failed $event
     * @param string $eventType
     * @param array $properties
     */
    private function logFailedEvent(Failed $event, string $eventType, array $properties): void
    {
        // Add failure-specific properties
        $properties['credentials'] = [
            'username' => $event->credentials['username'] ?? $event->credentials['email'] ?? 'unknown',
            'guard' => $event->guard,
        ];

        // If user exists, log against the user; otherwise log as system event
        if ($event->user) {
            activity('auth')
                ->causedBy($event->user)
                ->performedOn($event->user)
                ->withProperties($properties)
                ->event($eventType)
                ->log("Failed authentication attempt");
        } else {
            activity('auth')
                ->withProperties($properties)
                ->event($eventType)
                ->log("Failed authentication attempt for user: {$properties['credentials']['username']}");
        }
    }

    /**
     * Log standard authentication events (login, logout, etc.).
     * 
     * @param object $event
     * @param string $eventType
     * @param array $properties
     */
    private function logStandardEvent(object $event, string $eventType, array $properties): void
    {
        // Only log if the event has a user property
        if (!property_exists($event, 'user') || !$event->user) {
            return;
        }

        // Add user-specific properties
        $properties['user_id'] = $event->user->id;
        $properties['username'] = $event->user->username ?? $event->user->email ?? 'unknown';

        // Add guard information if available
        if (property_exists($event, 'guard')) {
            $properties['guard'] = $event->guard;
        }

        // Create the activity log entry
        activity('auth')
            ->causedBy($event->user)
            ->performedOn($event->user)
            ->withProperties($properties)
            ->event($eventType)
            ->log($this->getLogMessage($eventType, $event->user));
    }

    /**
     * Get the authentication guard from the event.
     * 
     * @param object $event
     * @return string|null
     */
    private function getGuardFromEvent(object $event): ?string
    {
        if (property_exists($event, 'guard')) {
            return $event->guard;
        }

        // Try to determine guard from request or context
        $request = request();
        if ($request && $request->is('*/moonshine*')) {
            return 'moonshine';
        }

        return 'web'; // Default guard
    }

    /**
     * Get a human-readable log message for the event.
     * 
     * @param string $eventType
     * @param mixed $user
     * @return string
     */
    private function getLogMessage(string $eventType, $user): string
    {
        $username = $user->username ?? $user->email ?? 'User';
        
        return match ($eventType) {
            'login' => "User '{$username}' logged in successfully",
            'logout' => "User '{$username}' logged out",
            'current-device-logout' => "User '{$username}' logged out from current device",
            'other-device-logout' => "User '{$username}' logged out from other devices",
            /*
            'registered' => "User '{$username}' registered successfully",
            'verified' => "User '{$username}' verified their email address",
            'password-reset' => "User '{$username}' reset their password",
            */
            'authenticated' => "User '{$username}' was authenticated",
            default => "Authentication event '{$eventType}' for user '{$username}'",
        };
    }
}
