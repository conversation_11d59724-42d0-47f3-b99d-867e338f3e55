<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTeam extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'team_id',
        'user_id',
        'created_by',
    ];

    /**
     * Get the team for this membership.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the user for this membership.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to filter by team.
     */
    public function scopeByTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get the display name for this team membership.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' - ' . $this->team->name;
    }

    /**
     * Check if user is team leader.
     */
    public function isTeamLeader(): bool
    {
        return $this->team->leader_user_id === $this->user_id;
    }

    /**
     * Get the membership role.
     */
    public function getMembershipRoleAttribute(): string
    {
        return $this->isTeamLeader() ? __('admin.team_leader') : __('admin.team_member');
    }

    /**
     * Get the summary of this team membership.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            '%s is %s of %s',
            $this->user->name,
            $this->membership_role,
            $this->team->name
        );
    }

    /**
     * Add user to team.
     */
    public static function addUserToTeam($userId, $teamId): ?self
    {
        // Check if user is already in team
        if (self::where('user_id', $userId)->where('team_id', $teamId)->exists()) {
            return null;
        }

        return self::create([
            'user_id' => $userId,
            'team_id' => $teamId,
        ]);
    }

    /**
     * Remove user from team.
     */
    public static function removeUserFromTeam($userId, $teamId): bool
    {
        return self::where('user_id', $userId)->where('team_id', $teamId)->delete() > 0;
    }

    /**
     * Get teams for a user.
     */
    public static function getTeamsForUser($userId)
    {
        return self::where('user_id', $userId)
            ->with(['team'])
            ->get();
    }

    /**
     * Get members for a team.
     */
    public static function getMembersForTeam($teamId)
    {
        return self::where('team_id', $teamId)
            ->with(['user'])
            ->get();
    }

    /**
     * Check if user is member of team.
     */
    public static function isUserInTeam($userId, $teamId): bool
    {
        return self::where('user_id', $userId)->where('team_id', $teamId)->exists();
    }
}
