<?php

namespace App\MoonShine\AuthPipelines;

use App\Models\User;
use App\Models\UserAgreement;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PrivacyAgreementPipe
{
    //TODO: not working at login

    /**
     * Handle the authentication pipeline.
     */
    public function handle(Request $request, Closure $next)
    {
        // Get the authenticated user
        $user = Auth::guard('moonshine')->user();
        
        // Skip if no user is authenticated
        if (!$user instanceof User) {
            return $next($request);
        }

        // Skip for API requests (JSON requests)
        if ($request->expectsJson()) {
            return $next($request);
        }

        // Skip if already on privacy agreement page or processing consent
        if ($request->is('*/privacy-agreement*') || $request->is('*/process-privacy-consent*')) {
            return $next($request);
        }

        // Skip for logout requests
        if ($request->is('*/logout*')) {
            return $next($request);
        }

        // Check if user has accepted the current privacy policy
        if (!$user->hasAcceptedCurrentPrivacyPolicy()) {
            // Store the intended URL in session
            $request->session()->put('privacy_agreement.intended_url', $request->fullUrl());
            
            // Redirect to privacy agreement page
            return redirect()->route('moonshine.privacy-agreement');
        }

        return $next($request);
    }
}
