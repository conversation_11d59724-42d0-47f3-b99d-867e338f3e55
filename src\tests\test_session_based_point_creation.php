<?php

/**
 * Test Session-Based Point Creation Implementation
 * 
 * This script tests the new session-based point creation logic to ensure
 * that when a book has NO required activities, ALL reading logs in the
 * current session receive points, not just the current log.
 */

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Book;
use App\Models\UserBook;
use App\Models\UserReadingLog;
use App\Models\UserPoint;
use App\Models\Activity;
use Illuminate\Support\Facades\DB;

echo "=== SESSION-BASED POINT CREATION TEST ===\n\n";

try {
    // Find a test user and book
    $user = User::where('email', 'like', '%test%')->first();
    if (!$user) {
        $user = User::first();
    }
    
    if (!$user) {
        echo "❌ No users found in database\n";
        exit(1);
    }
    
    $book = Book::where('active', true)->first();
    if (!$book) {
        echo "❌ No active books found in database\n";
        exit(1);
    }
    
    echo "📚 Test Setup:\n";
    echo "- User: {$user->name} (ID: {$user->id})\n";
    echo "- Book: {$book->name} (ID: {$book->id})\n\n";
    
    // Clean up any existing data for this test
    echo "🧹 Cleaning up existing test data...\n";
    UserPoint::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserReadingLog::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserBook::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // Check if book has required activities
    $requiredActivities = Activity::where('activities.active', true)
        ->get()
        ->where('required', true);
    
    echo "🔍 Required Activities Check:\n";
    if ($requiredActivities->isEmpty()) {
        echo "✅ No required activities found - perfect for testing session-based point creation\n\n";
    } else {
        echo "⚠️  Found " . $requiredActivities->count() . " required activities:\n";
        foreach ($requiredActivities as $activity) {
            echo "   - {$activity->name} (ID: {$activity->id})\n";
        }
        echo "   This test will still work as it tests the session logic\n\n";
    }
    
    // Create a new reading session
    echo "📖 Creating new reading session...\n";
    $userBook = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now(),
        'end_date' => null, // Active session
    ]);
    echo "✅ Created UserBook session (ID: {$userBook->id})\n\n";
    
    // Create multiple reading logs in the session
    echo "📝 Creating multiple reading logs in the session...\n";
    
    $readingLogs = [];
    
    // Log 1: 10 pages, not completed
    $log1 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->subDays(2),
        'start_page' => 1,
        'end_page' => 10,
        'pages_read' => 10,
        'reading_duration' => 30,
        'book_completed' => false,
    ]);
    $readingLogs[] = $log1;
    echo "✅ Created Log 1: 10 pages, not completed (ID: {$log1->id})\n";
    
    // Log 2: 15 pages, not completed
    $log2 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->subDays(1),
        'start_page' => 11,
        'end_page' => 25,
        'pages_read' => 15,
        'reading_duration' => 45,
        'book_completed' => false,
    ]);
    $readingLogs[] = $log2;
    echo "✅ Created Log 2: 15 pages, not completed (ID: {$log2->id})\n";
    
    // Log 3: 20 pages, COMPLETED - This should trigger session-based point creation
    echo "\n🎯 Creating completion log (should trigger session-based point creation)...\n";
    $log3 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now(),
        'start_page' => 26,
        'end_page' => 45,
        'pages_read' => 20,
        'reading_duration' => 60,
        'book_completed' => true,
    ]);
    $readingLogs[] = $log3;
    echo "✅ Created Log 3: 20 pages, COMPLETED (ID: {$log3->id})\n\n";
    
    // Check points created
    echo "💰 Checking points created:\n";
    $allPoints = UserPoint::where('user_id', $user->id)
        ->where('book_id', $book->id)
        ->where('point_type', UserPoint::POINT_TYPE_PAGE)
        ->get();
    
    echo "Total points records: " . $allPoints->count() . "\n";
    
    foreach ($readingLogs as $index => $log) {
        $logPoints = $allPoints->where('source_id', $log->id)->first();
        if ($logPoints) {
            echo "✅ Log " . ($index + 1) . " (ID: {$log->id}): {$logPoints->points} points awarded\n";
        } else {
            echo "❌ Log " . ($index + 1) . " (ID: {$log->id}): NO points awarded\n";
        }
    }
    
    $totalPoints = $allPoints->sum('points');
    echo "\n📊 Total Points Awarded: {$totalPoints}\n";
    
    // Verify session-based logic worked
    echo "\n🔍 Session-Based Logic Verification:\n";
    
    if ($requiredActivities->isEmpty()) {
        // No required activities - all logs should have points
        if ($allPoints->count() === count($readingLogs)) {
            echo "✅ SUCCESS: All " . count($readingLogs) . " reading logs received points (no required activities)\n";
        } else {
            echo "❌ FAILURE: Expected " . count($readingLogs) . " point records, got " . $allPoints->count() . "\n";
        }
    } else {
        // Has required activities - only completion log should have points initially
        if ($allPoints->count() === 1 && $allPoints->first()->source_id === $log3->id) {
            echo "✅ SUCCESS: Only completion log received points (required activities exist)\n";
        } else {
            echo "❌ FAILURE: Expected only completion log to have points, but got different result\n";
        }
    }
    
    // Test the new calculateAndCreateSessionPoints method directly
    echo "\n🧪 Testing calculateAndCreateSessionPoints method directly...\n";
    
    // Clear existing points
    UserPoint::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // Call the method on the completion log
    $log3->calculateAndCreateSessionPoints();
    
    $sessionPoints = UserPoint::where('user_id', $user->id)
        ->where('book_id', $book->id)
        ->where('point_type', UserPoint::POINT_TYPE_PAGE)
        ->get();
    
    echo "Points created by calculateAndCreateSessionPoints: " . $sessionPoints->count() . "\n";
    
    foreach ($readingLogs as $index => $log) {
        $logPoints = $sessionPoints->where('source_id', $log->id)->first();
        if ($logPoints) {
            echo "✅ Log " . ($index + 1) . ": {$logPoints->points} points\n";
        } else {
            echo "❌ Log " . ($index + 1) . ": No points\n";
        }
    }
    
    echo "\n🎉 Test completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
