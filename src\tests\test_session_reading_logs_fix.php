<?php

/**
 * Test getSessionReadingLogs Date Comparison Fix
 * 
 * This script tests the fix for getSessionReadingLogs method to properly handle
 * date comparison between UserReadingLog.log_date (timestamp) and 
 * UserBook.start_date/end_date (date-only).
 */

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Book;
use App\Models\UserBook;
use App\Models\UserReadingLog;
use Carbon\Carbon;

echo "=== getSessionReadingLogs DATE COMPARISON FIX TEST ===\n\n";

try {
    // Find a test user and book
    $user = User::first();
    $book = Book::where('active', true)->first();
    
    if (!$user || !$book) {
        echo "❌ No test data available\n";
        exit(1);
    }
    
    echo "📚 Test Setup:\n";
    echo "- User: {$user->name} (ID: {$user->id})\n";
    echo "- Book: {$book->name} (ID: {$book->id})\n\n";
    
    // Clean up any existing data
    echo "🧹 Cleaning up existing test data...\n";
    UserReadingLog::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserBook::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // STEP 1: Create a UserBook session with DATE-ONLY values
    echo "\n📅 STEP 1: Creating UserBook session with date-only values...\n";
    
    $sessionStartDate = '2024-01-15'; // Date only
    $sessionEndDate = '2024-01-17';   // Date only
    
    $userBook = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => $sessionStartDate,
        'end_date' => $sessionEndDate,
    ]);
    
    echo "✅ Created UserBook session:\n";
    echo "   ID: {$userBook->id}\n";
    echo "   Start Date: {$userBook->start_date}\n";
    echo "   End Date: {$userBook->end_date}\n";
    
    // STEP 2: Create reading logs with TIMESTAMP values
    echo "\n📖 STEP 2: Creating reading logs with timestamp values...\n";
    
    $testLogs = [
        [
            'name' => 'Log within session (start day)',
            'log_date' => '2024-01-15 10:30:00',
            'pages' => 10,
            'should_be_included' => true
        ],
        [
            'name' => 'Log within session (middle day)',
            'log_date' => '2024-01-16 14:45:00',
            'pages' => 15,
            'should_be_included' => true
        ],
        [
            'name' => 'Log within session (end day)',
            'log_date' => '2024-01-17 20:15:00',
            'pages' => 20,
            'should_be_included' => true
        ],
        [
            'name' => 'Log before session',
            'log_date' => '2024-01-14 12:00:00',
            'pages' => 5,
            'should_be_included' => false
        ],
        [
            'name' => 'Log after session',
            'log_date' => '2024-01-18 09:00:00',
            'pages' => 8,
            'should_be_included' => false
        ]
    ];
    
    $createdLogs = [];
    foreach ($testLogs as $index => $testLog) {
        $log = UserReadingLog::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'log_date' => $testLog['log_date'],
            'start_page' => 1 + ($index * 10),
            'end_page' => $testLog['pages'] + ($index * 10),
            'pages_read' => $testLog['pages'],
            'reading_duration' => 30,
            'book_completed' => false,
        ]);
        
        $createdLogs[] = [
            'log' => $log,
            'testData' => $testLog
        ];
        
        echo "✅ Created {$testLog['name']}:\n";
        echo "   ID: {$log->id}\n";
        echo "   Log Date: {$log->log_date}\n";
        echo "   Pages: {$testLog['pages']}\n";
        echo "   Should be included: " . ($testLog['should_be_included'] ? 'YES' : 'NO') . "\n\n";
    }
    
    // STEP 3: Test the getSessionReadingLogs method
    echo "🔍 STEP 3: Testing getSessionReadingLogs method...\n\n";
    
    $sessionLogs = $userBook->getSessionReadingLogs();
    
    echo "Session Date Range: {$userBook->start_date} to {$userBook->end_date}\n";
    echo "Found {$sessionLogs->count()} logs in session\n\n";
    
    // Check each found log
    $foundLogIds = $sessionLogs->pluck('id')->toArray();
    echo "Found Log Details:\n";
    foreach ($sessionLogs as $log) {
        echo "- Log ID {$log->id}: {$log->log_date} ({$log->pages_read} pages)\n";
    }
    echo "\n";
    
    // STEP 4: Verify results
    echo "📊 STEP 4: Verifying results...\n\n";
    
    $allCorrect = true;
    $expectedCount = 0;
    $expectedTotalPages = 0;
    
    foreach ($createdLogs as $logData) {
        $log = $logData['log'];
        $testData = $logData['testData'];
        $shouldBeIncluded = $testData['should_be_included'];
        $isIncluded = in_array($log->id, $foundLogIds);
        
        if ($shouldBeIncluded) {
            $expectedCount++;
            $expectedTotalPages += $testData['pages'];
        }
        
        if ($isIncluded === $shouldBeIncluded) {
            echo "✅ {$testData['name']}: " . ($isIncluded ? "Correctly included" : "Correctly excluded") . "\n";
        } else {
            echo "❌ {$testData['name']}: " . ($isIncluded ? "Incorrectly included" : "Incorrectly excluded") . "\n";
            $allCorrect = false;
        }
    }
    
    echo "\n";
    echo "Expected logs in session: {$expectedCount}\n";
    echo "Actually found logs: {$sessionLogs->count()}\n";
    echo "Expected total pages: {$expectedTotalPages}\n";
    echo "Actually found pages: {$sessionLogs->sum('pages_read')}\n";
    
    // STEP 5: Test related methods
    echo "\n🧪 STEP 5: Testing related session methods...\n";
    
    $hasLogs = $userBook->hasSessionReadingLogs();
    $totalPages = $userBook->getSessionPagesRead();
    $totalTime = $userBook->getSessionReadingTime();
    $hasCompletion = $userBook->hasSessionCompletionLog();
    
    echo "hasSessionReadingLogs(): " . ($hasLogs ? 'true' : 'false') . "\n";
    echo "getSessionPagesRead(): {$totalPages}\n";
    echo "getSessionReadingTime(): {$totalTime}\n";
    echo "hasSessionCompletionLog(): " . ($hasCompletion ? 'true' : 'false') . "\n";
    
    // STEP 6: Test with active session
    echo "\n🔄 STEP 6: Testing with active session (end_date = null)...\n";
    
    $activeSession = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => '2024-01-20',
        'end_date' => null,
    ]);
    
    $activeLog = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => '2024-01-21 15:30:00',
        'start_page' => 1,
        'end_page' => 10,
        'pages_read' => 10,
        'reading_duration' => 30,
        'book_completed' => false,
    ]);
    
    $activeSessionLogs = $activeSession->getSessionReadingLogs();
    
    if ($activeSessionLogs->count() === 1 && $activeSessionLogs->first()->id === $activeLog->id) {
        echo "✅ Active session test PASSED\n";
    } else {
        echo "❌ Active session test FAILED\n";
        echo "   Expected 1 log, found {$activeSessionLogs->count()}\n";
        $allCorrect = false;
    }
    
    // STEP 7: Overall results
    echo "\n🎉 STEP 7: Overall Test Results...\n";
    
    if ($allCorrect && $sessionLogs->count() === $expectedCount && $sessionLogs->sum('pages_read') === $expectedTotalPages) {
        echo "🎉 ✅ ALL TESTS PASSED!\n";
        echo "✅ getSessionReadingLogs date comparison fix is working correctly\n";
        echo "✅ Session filtering properly handles timestamp vs date-only comparison\n";
        echo "✅ All related methods work correctly\n";
    } else {
        echo "❌ SOME TESTS FAILED!\n";
        echo "❌ getSessionReadingLogs may need further adjustment\n";
        
        if ($sessionLogs->count() !== $expectedCount) {
            echo "❌ Count mismatch: expected {$expectedCount}, got {$sessionLogs->count()}\n";
        }
        if ($sessionLogs->sum('pages_read') !== $expectedTotalPages) {
            echo "❌ Pages mismatch: expected {$expectedTotalPages}, got {$sessionLogs->sum('pages_read')}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
