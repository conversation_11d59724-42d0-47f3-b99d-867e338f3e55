# Local Book Repository Enhancement

## Overview
Enhanced the BookDiscoveryService to support discovering books from a local repository stored in the `storage/app/private/book_repository` folder. This provides a fast, offline-first approach to book discovery before falling back to online providers.

## Implementation Date
September 29, 2025

## Local Repository Structure

### Directory Layout:
```
storage/app/private/book_repository/
├── json/
│   ├── 9786057611291.json
│   ├── 9786051234567.json
│   └── ...
└── covers/
    ├── 9786057611291.jpg
    ├── 9786051234567.jpg
    └── ...
```

### JSON File Format:
Each book's metadata is stored in a JSON file named `{isbn}.json` in the `json` subfolder.

**Sample JSON Structure:**
```json
{
    "name": "Mutlu Prens",
    "isbn": "9786057611291",
    "publisher": "İndigo Çocuk",
    "page_count": "96",
    "year_of_publish": "2019",
    "cover_image": "9786057611291.jpg",
    "author": {
        "0": "<PERSON>"
    },
    "category": {
        "0": "6-10 Yaş",
        "1": "Çocuk Kitapları",
        "2": "Roman-Öykü"
    }
}
```

**Field Descriptions:**
- `name` (required): Book title
- `isbn` (required): ISBN-10 or ISBN-13
- `publisher` (optional): Publisher name
- `page_count` (optional): Number of pages
- `year_of_publish` (optional): Publication year
- `cover_image` (optional): Cover image filename (not used, actual file is `{isbn}.jpg`)
- `author` (optional): Array of author names (indexed array)
- `category` (optional): Array of category names (indexed array)

### Cover Image Files:
- **Location:** `storage/app/private/book_repository/covers/`
- **Naming:** `{isbn}.jpg` (e.g., `9786057611291.jpg`)
- **Format:** JPEG images
- **Max Size:** 5MB (configurable via `book_discovery.settings.max_image_size`)

## Book Discovery Priority Order

When a user adds a book by ISBN, the system searches in this order:

1. **Database** - Check if book already exists in the local database
2. **Local Repository** - Search `storage/app/private/book_repository` folder
3. **Online Providers** - Fall back to external services (D&R, FidanKitap, Google Books)

This priority ensures:
- ✅ Fastest response for existing books (database)
- ✅ Fast offline discovery for pre-loaded books (local repository)
- ✅ Comprehensive coverage via online providers (fallback)

## Implementation Details

### New Method: `searchLocalRepository()`

**Purpose:** Search for book metadata in the local repository by ISBN.

**Process:**
1. Construct JSON file path: `private/book_repository/json/{isbn}.json`
2. Check if JSON file exists
3. Read and parse JSON content
4. Validate required fields (name)
5. Extract authors from `author` array
6. Extract categories from `category` array
7. Check if cover image exists: `private/book_repository/covers/{isbn}.jpg`
8. Format data for consistency with other providers
9. Return formatted book data or null if not found

**Return Format:**
```php
[
    'name' => 'Mutlu Prens',
    'isbn' => '9786057611291',
    'author' => ['Oscar Wilde'],
    'publisher' => 'İndigo Çocuk',
    'year' => '2019',
    'page_count' => '96',
    'cover_image' => 'private/book_repository/covers/9786057611291.jpg',
    'category' => ['6-10 Yaş', 'Çocuk Kitapları', 'Roman-Öykü'],
    'source' => 'local_repository',
    'local_cover_path' => 'private/book_repository/covers/9786057611291.jpg'
]
```

### New Method: `copyLocalCoverImage()`

**Purpose:** Copy cover image from local repository to public storage.

**Process:**
1. Verify source file exists in local repository
2. Read image data from storage
3. Validate image size (max 5MB)
4. Detect image type from binary data
5. Validate image type (jpg, jpeg, png, webp)
6. Generate unique filename: `book_{id}_{timestamp}.{ext}`
7. Copy to public storage: `storage/app/public/books/covers/`
8. Update book record with public path

**Error Handling:**
- Missing source file → Log warning, continue without cover
- File too large → Log warning, continue without cover
- Invalid image type → Log warning, continue without cover
- Copy failure → Log error with full trace

### New Method: `getImageExtensionFromData()`

**Purpose:** Detect image type from binary data (for local repository images).

**Process:**
1. Use `finfo_buffer()` to detect MIME type
2. Map MIME type to file extension
3. Return extension (default: 'jpg')

**Supported Types:**
- `image/jpeg` → `jpg`
- `image/png` → `png`
- `image/webp` → `webp`
- `image/gif` → `gif`

### Modified Method: `searchByIsbn()`

**Changes:**
- Added local repository search between database and online providers
- Updated method documentation to reflect new priority order
- Added logging for local repository discoveries

**Flow:**
```
searchByIsbn(isbn)
    ↓
cleanIsbn() → Normalize ISBN format
    ↓
isValidIsbn() → Validate ISBN-10 or ISBN-13
    ↓
searchLocalDatabase() → Check existing books
    ↓ (if not found)
searchLocalRepository() → Check local repository ← NEW
    ↓ (if not found)
Loop through online providers → D&R, FidanKitap, etc.
    ↓ (if not found)
searchGoogleBooks() → Final fallback
```

### Modified Method: `createBookFromData()`

**Changes:**
- Added logic to detect local repository source
- Routes local repository books to `copyLocalCoverImage()`
- Routes online provider books to `downloadAndAttachCoverImage()`

**Decision Logic:**
```php
if (source === 'local_repository' && local_cover_path exists) {
    copyLocalCoverImage(book, local_cover_path);
} else {
    downloadAndAttachCoverImage(book, cover_image_url);
}
```

## Data Extraction

### Authors: <AUTHORS>
```json
"author": {
    "0": "Oscar Wilde",
    "1": "Another Author"
}
```

**Extraction:**
```php
$authors = [];
if (isset($bookData['author']) && is_array($bookData['author'])) {
    $authors = array_values($bookData['author']);
}
```

**Result:** `['Oscar Wilde', 'Another Author']`

### Categories:
The JSON file contains a `category` array with numeric keys:
```json
"category": {
    "0": "6-10 Yaş",
    "1": "Çocuk Kitapları",
    "2": "Roman-Öykü"
}
```

**Extraction:**
```php
$categories = [];
if (isset($bookData['category']) && is_array($bookData['category'])) {
    $categories = array_values($bookData['category']);
}
```

**Result:** `['6-10 Yaş', 'Çocuk Kitapları', 'Roman-Öykü']`

## Error Handling

### JSON File Issues:
- **File not found:** Log debug message, return null, continue to next provider
- **Invalid JSON:** Log error with JSON error message, return null
- **Missing required fields:** Log error, return null

### Cover Image Issues:
- **Image not found:** Log warning, continue without cover (book still created)
- **Image too large:** Log warning, continue without cover
- **Invalid image type:** Log warning, continue without cover
- **Copy failure:** Log error with full trace, continue without cover

### Graceful Degradation:
- Missing cover image does NOT prevent book creation
- Invalid JSON does NOT crash the application
- Errors are logged for debugging but don't block the user

## Logging

### Success Logs:
```php
Log::info("Book found in local repository", [
    'isbn' => '9786057611291',
    'name' => 'Mutlu Prens'
]);

Log::info("Local repository: Book data loaded successfully", [
    'isbn' => '9786057611291',
    'name' => 'Mutlu Prens',
    'authors' => 1,
    'categories' => 3,
    'has_cover' => true
]);

Log::info('Local repository cover image copied successfully', [
    'book_id' => 123,
    'isbn' => '9786057611291',
    'source_path' => 'private/book_repository/covers/9786057611291.jpg',
    'public_path' => 'books/covers/book_123_1632847200.jpg'
]);
```

### Error/Warning Logs:
```php
Log::debug("Local repository: JSON file not found", [
    'isbn' => '9786057611291',
    'path' => 'private/book_repository/json/9786057611291.json'
]);

Log::error("Local repository: Invalid JSON format", [
    'isbn' => '9786057611291',
    'error' => 'Syntax error'
]);

Log::warning("Local repository: Cover image not found", [
    'isbn' => '9786057611291',
    'expected_path' => 'private/book_repository/covers/9786057611291.jpg'
]);
```

## Configuration

All settings are inherited from the existing `config/book_discovery.php`:

```php
'settings' => [
    'max_image_size' => 5 * 1024 * 1024, // 5MB
    'allowed_image_types' => ['jpg', 'jpeg', 'png', 'webp'],
    'image_storage_path' => 'books/covers', // Public storage path
    'image_download_timeout' => 30, // seconds
],
```

## Files Modified

1. **`src/app/Services/BookDiscovery/BookDiscoveryService.php`**
   - Added `searchLocalRepository()` method (93 lines)
   - Added `copyLocalCoverImage()` method (107 lines)
   - Added `getImageExtensionFromData()` method (17 lines)
   - Modified `searchByIsbn()` method (added local repository check)
   - Modified `createBookFromData()` method (added local cover handling)

## Benefits

### 1. Performance:
- **Fast Discovery:** Local repository search is instant (no network calls)
- **Reduced Load:** Fewer requests to external providers
- **Offline Capability:** Works without internet connection

### 2. Reliability:
- **No Rate Limits:** Local repository has no API rate limits
- **No Downtime:** Not affected by external provider outages
- **Consistent Data:** Pre-validated book metadata

### 3. Cost:
- **No API Costs:** No charges for local repository searches
- **Reduced Bandwidth:** Cover images copied locally, not downloaded

### 4. Control:
- **Curated Content:** Pre-load specific books for your users
- **Custom Metadata:** Add books not available in online providers
- **Data Quality:** Ensure accurate, complete book information

## Usage Example

### Adding a Book from Local Repository:

1. **Prepare Repository:**
   ```bash
   # Create directory structure
   mkdir -p storage/app/private/book_repository/json
   mkdir -p storage/app/private/book_repository/covers
   
   # Add book metadata
   echo '{"name":"Mutlu Prens","isbn":"9786057611291",...}' > \
     storage/app/private/book_repository/json/9786057611291.json
   
   # Add cover image
   cp mutlu_prens_cover.jpg \
     storage/app/private/book_repository/covers/9786057611291.jpg
   ```

2. **User Adds Book:**
   - User enters ISBN: `9786057611291`
   - System searches database → Not found
   - System searches local repository → **Found!**
   - System creates book record with metadata
   - System copies cover image to public storage
   - Book appears in user's library

3. **Result:**
   - Book created in < 100ms (no network calls)
   - Cover image available immediately
   - Authors and categories properly linked
   - User can start reading activities

## Testing Checklist

- [ ] Test with valid JSON file and cover image
- [ ] Test with valid JSON file but missing cover image
- [ ] Test with missing JSON file (should fall back to online)
- [ ] Test with malformed JSON file (should fall back to online)
- [ ] Test with JSON missing required 'name' field
- [ ] Test with cover image exceeding max size
- [ ] Test with unsupported cover image type
- [ ] Test priority order (DB → Local → Online)
- [ ] Verify authors are correctly extracted and linked
- [ ] Verify categories are correctly extracted and linked
- [ ] Verify cover image is copied to correct public path
- [ ] Verify book record is created with all fields
- [ ] Check logs for proper error/success messages

## Status
✅ **COMPLETE** - Local repository support fully implemented and ready for testing

