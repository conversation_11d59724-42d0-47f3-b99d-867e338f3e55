<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Team;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\UserClass;
use App\Models\UserSchool;
use App\Models\UserTeam;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TeamResourceRoleBasedFilteringTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_user_local_scope_filtering_works_for_team_member_selection()
    {
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        $school = School::factory()->create();
        $class = SchoolClass::factory()->create(['school_id' => $school->id]);

        // Assign teacher to class
        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        $student1 = User::factory()->create(['name' => 'Student One']);
        $student1->assignRole('student');
        $student2 = User::factory()->create(['name' => 'Student Two']);
        $student2->assignRole('student');
        $student3 = User::factory()->create(['name' => 'Student Three']);
        $student3->assignRole('student');

        // Assign student1 and student2 to teacher's class
        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // student3 is not in teacher's class

        $this->actingAs($teacher, 'moonshine');

        // Test that User::forCurrentUser() returns only users the teacher can access
        $accessibleUsers = User::forCurrentUser()->get();
        $userNames = $accessibleUsers->pluck('name')->toArray();

        // Teacher should see themselves + students in their class
        $this->assertCount(3, $accessibleUsers);
        $this->assertContains($teacher->name, $userNames);
        $this->assertContains('Student One', $userNames);
        $this->assertContains('Student Two', $userNames);
        $this->assertNotContains('Student Three', $userNames);
    }

    public function test_school_admin_can_access_students_from_their_schools()
    {
        $schoolAdmin = User::factory()->create();
        $schoolAdmin->assignRole('school_admin');

        $school1 = School::factory()->create();
        $school2 = School::factory()->create();

        // Assign school admin to school1
        UserSchool::create([
            'user_id' => $schoolAdmin->id,
            'school_id' => $school1->id,
            'role_id' => 1, // Default role
            'active' => true,
            'default' => true,
        ]);

        $class1 = SchoolClass::factory()->create(['school_id' => $school1->id]);
        $class2 = SchoolClass::factory()->create(['school_id' => $school2->id]);

        $student1 = User::factory()->create(['name' => 'Student One']);
        $student1->assignRole('student');
        $student2 = User::factory()->create(['name' => 'Student Two']);
        $student2->assignRole('student');

        // Assign students to classes
        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class1->id,
            'school_id' => $school1->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class2->id,
            'school_id' => $school2->id,
            'active' => true,
        ]);

        $this->actingAs($schoolAdmin, 'moonshine');

        // Test that User::forCurrentUser() returns only users the school admin can access
        $accessibleUsers = User::forCurrentUser()->get();
        $userNames = $accessibleUsers->pluck('name')->toArray();

        // School admin should see themselves + students in their school
        $this->assertCount(2, $accessibleUsers);
        $this->assertContains($schoolAdmin->name, $userNames);
        $this->assertContains('Student One', $userNames);
        $this->assertNotContains('Student Two', $userNames);
    }

    public function test_system_admin_can_access_all_users()
    {
        $admin = User::factory()->create();
        $admin->assignRole('system_admin');

        $student = User::factory()->create();
        $student->assignRole('student');

        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        // Authenticate as system admin
        $this->actingAs($admin, 'moonshine');

        // System admin should see all users using local scope
        $users = User::forCurrentUser()->get();
        $this->assertCount(3, $users);
    }
}
