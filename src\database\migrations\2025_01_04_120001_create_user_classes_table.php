<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_classes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('class_id')->constrained('school_classes')->onDelete('cascade');
            $table->foreignId('school_id')->constrained('schools')->onDelete('cascade');
            $table->boolean('active')->default(true);
            $table->boolean('default')->default(false);
            
            // Add indexes and constraints
            $table->unique(['user_id', 'class_id', 'school_id'], 'user_classes_unique');
            $table->index(['class_id', 'active']);
            $table->index(['school_id', 'active']);
            $table->index(['user_id', 'active']);
            $table->index('active');
            $table->index(['user_id', 'default']);
            $table->index(['user_id', 'active', 'default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_classes');
    }
};
