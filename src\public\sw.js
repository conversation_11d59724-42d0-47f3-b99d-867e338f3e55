// Minimal service worker focused on FCM functionality
console.log('Service worker starting...');

// FCM Configuration with Firebase SDK 12.4.0 - Compat API for Service Workers
console.log('Service worker loading...');

try {
  importScripts('https://www.gstatic.com/firebasejs/12.4.0/firebase-app-compat.js');
  console.log('Firebase app script loaded');

  importScripts('https://www.gstatic.com/firebasejs/12.4.0/firebase-messaging-compat.js');
  console.log('Firebase messaging script loaded');

  console.log('All Firebase scripts loaded successfully in service worker');
} catch (error) {
  console.error('Failed to load Firebase scripts in service worker:', error);
}

let messaging = null;

const firebaseConfig = {
  apiKey: "AIzaSyAR1qUdJdJFZuWUH2pqx0HTjcmCszuLMBc",
  authDomain: "okumobil-2025.firebaseapp.com",
  projectId: "okumobil-2025",
  storageBucket: "okumobil-2025.firebasestorage.app",
  messagingSenderId: "572159316141",
  appId: "1:572159316141:web:be0818d9518b853ef6b624"
};

try {
  if (firebase.apps.length > 0) {
    console.log('Firebase already initialized in service worker');
    messaging = firebase.messaging();
  } else {
    console.log('Initializing Firebase messaging in service worker...');
    firebase.initializeApp(firebaseConfig);
    messaging = firebase.messaging();
    console.log('Firebase messaging initialized successfully in service worker');
  }

  // Handle background messages
  messaging.onBackgroundMessage((payload) => {
    console.log('Background message received:', payload);

    const notificationTitle = payload.notification?.title || 'Okumobil';
    const notificationOptions = {
      body: payload.notification?.body || 'Okumobil\'den bir bildirim var!',
      icon: payload.notification?.image || '/images/icon-192x192.png',
      badge: '/images/icon-72x72.png',
      tag: payload.data?.type || 'general',
      data: {
        ...payload.data,
        dateOfArrival: Date.now(),
        deep_link_url: payload.data?.deep_link_url || '/mobile'
      },
      requireInteraction: true,
      actions: [
        {
          action: 'open',
          title: 'Aç',
          icon: '/images/icon-72x72.png'
        },
        {
          action: 'close',
          title: 'Kapat'
        }
      ]
    };

    return self.registration.showNotification(notificationTitle, notificationOptions);
  });

} catch (error) {
  console.error('Failed to initialize Firebase in service worker:', error);
}

console.log('Service worker loaded for FCM push notifications');

// Install event - Minimal for FCM
self.addEventListener('install', (event) => {
  console.log('Service worker installing...');
  // Skip waiting to activate immediately
  self.skipWaiting();
});

// Activate event - Minimal for FCM
self.addEventListener('activate', (event) => {
  console.log('Service worker activating...');
  // Take control of all clients immediately
  event.waitUntil(self.clients.claim());
  console.log('Service worker activated successfully');
});

// Native push event handler - Critical for push notifications
self.addEventListener('push', (event) => {
  console.log('Native push event received:', event);

  if (!event.data) {
    console.log('Push event has no data');
    return;
  }

  try {
    const payload = event.data.json();
    console.log('Push payload:', payload);

    const notificationTitle = payload.notification?.title || 'Okumobil';
    const notificationOptions = {
      body: payload.notification?.body || 'Okumobil\'den bir bildirim var!',
      icon: payload.notification?.image || '/images/icon-192x192.png',
      badge: '/images/icon-72x72.png',
      tag: payload.data?.type || 'general',
      data: {
        ...payload.data,
        dateOfArrival: Date.now(),
        deep_link_url: payload.data?.deep_link_url || '/'
      },
      requireInteraction: true,
      actions: [
        {
          action: 'open',
          title: 'Aç',
          icon: '/images/icon-72x72.png'
        },
        {
          action: 'close',
          title: 'Kapat'
        }
      ]
    };

    event.waitUntil(
      self.registration.showNotification(notificationTitle, notificationOptions)
    );
  } catch (error) {
    console.error('Error handling push event:', error);

    // Fallback notification
    event.waitUntil(
      self.registration.showNotification('Okumobil', {
        body: 'Yeni bir bildirim var!',
        icon: '/images/icon-192x192.png',
        badge: '/images/icon-72x72.png'
      })
    );
  }
});

// Notification click event with deep linking support
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  let targetUrl = '/mobile'; // Default URL

  // Handle deep linking
  if (event.notification.data && event.notification.data.deep_link_url) {
    targetUrl = event.notification.data.deep_link_url;
    // Ensure URL starts with / for relative paths
    if (!targetUrl.startsWith('/') && !targetUrl.startsWith('http')) {
      targetUrl = '/' + targetUrl;
    }
  }

  if (event.action === 'open' || !event.action) {
    // Open the app with deep link URL
    event.waitUntil(
      clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
        // Check if app is already open
        for (let client of clientList) {
          if (client.url.includes('/mobile') && 'focus' in client) {
            // Navigate to the target URL and focus
            client.navigate(targetUrl);
            return client.focus();
          }
        }

        // If no existing window, open new one
        if (clients.openWindow) {
          return clients.openWindow(targetUrl);
        }
      })
    );
  } else if (event.action === 'close') {
    // Just close the notification - no action needed
  }
});

// Background sync event (for offline functionality)
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

function doBackgroundSync() {
  // Implement background sync logic here
  // For example, sync reading logs when back online
  return Promise.resolve();
}

console.log('Service worker loaded completely');
