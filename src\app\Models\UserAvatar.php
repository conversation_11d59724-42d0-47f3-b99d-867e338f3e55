<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAvatar extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'avatar_id',
        'selected_at',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'selected_at' => 'datetime',
        ];
    }

    /**
     * Get the user who selected this avatar.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the selected avatar.
     */
    public function avatar(): BelongsTo
    {
        return $this->belongsTo(Avatar::class);
    }

    /**
     * Get the user who created this avatar selection.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by avatar.
     */
    public function scopeByAvatar($query, $avatarId)
    {
        return $query->where('avatar_id', $avatarId);
    }

    /**
     * Scope to get recent selections.
     */
    public function scopeRecentSelections($query, $days = 30)
    {
        return $query->where('selected_at', '>=', now()->subDays($days));
    }

    /**
     * Get the display name for the user avatar selection.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' - ' . $this->avatar->name;
    }

    /**
     * Get summary information for the user avatar selection.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            '%s selected %s on %s',
            $this->user->name,
            $this->avatar->name,
            $this->selected_at->format('M d, Y')
        );
    }

    /**
     * Get the current avatar image URL based on user's reading activity.
     */
    public function getCurrentImageUrlAttribute(): string
    {
        return $this->user->getAvatarDisplayImage();
    }

    /**
     * Check if this avatar selection is recent (within last 7 days).
     */
    public function isRecentSelection(): bool
    {
        return $this->selected_at->isAfter(now()->subDays(7));
    }

    /**
     * Get the selection age in days.
     */
    public function getSelectionAgeInDays(): int
    {
        return $this->selected_at->diffInDays(now());
    }

    /**
     * Get the selection age as formatted text.
     */
    public function getSelectionAgeTextAttribute(): string
    {
        $days = $this->getSelectionAgeInDays();
        
        if ($days === 0) {
            return __('admin.selected_today');
        } elseif ($days === 1) {
            return __('admin.selected_yesterday');
        } else {
            return __('admin.selected_days_ago', ['days' => $days]);
        }
    }

    /**
     * Select or update avatar for a user.
     */
    public static function selectAvatarForUser($userId, $avatarId): self
    {
        return self::updateOrCreate(
            ['user_id' => $userId],
            [
                'avatar_id' => $avatarId,
                'selected_at' => now(),
            ]
        );
    }

    /**
     * Get the current avatar selection for a user.
     */
    public static function getCurrentSelectionForUser($userId): ?self
    {
        return self::where('user_id', $userId)->first();
    }

    /**
     * Remove avatar selection for a user.
     */
    public static function removeSelectionForUser($userId): bool
    {
        return self::where('user_id', $userId)->delete() > 0;
    }
}
