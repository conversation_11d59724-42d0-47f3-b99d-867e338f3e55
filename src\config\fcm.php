<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Firebase Cloud Messaging Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for Firebase Cloud Messaging (FCM)
    | push notifications. All settings are kept simple and configurable via
    | environment variables.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Firebase Credentials
    |--------------------------------------------------------------------------
    |
    | Path to the Firebase service account JSON file. This file should be
    | placed in the storage/app/private/firebase/ directory for security.
    |
    */
    'credentials_path' => env('FIREBASE_CREDENTIALS_PATH', storage_path('app/private/firebase/service-account.json')),

    /*
    |--------------------------------------------------------------------------
    | Firebase Project Configuration
    |--------------------------------------------------------------------------
    |
    | Basic Firebase project configuration for client-side integration.
    |
    */
    'project_id' => env('FIREBASE_PROJECT_ID'),
    'web_api_key' => env('FIREBASE_WEB_API_KEY'),
    'vapid_key' => env('FIREBASE_VAPID_KEY'),
    'messaging_sender_id' => env('FIREBASE_MESSAGING_SENDER_ID'),
    'app_id' => env('FIREBASE_APP_ID'),

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for push notifications.
    |
    */
    'default_icon' => '/images/icon-192x192.png',
    'default_badge' => '/images/icon-72x72.png',
    
    /*
    |--------------------------------------------------------------------------
    | Inactivity Reminder Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for scheduled inactivity reminder notifications.
    |
    */
    'inactivity_days_threshold' => env('FCM_INACTIVITY_DAYS', 3),
    'inactivity_reminder_enabled' => env('FCM_INACTIVITY_ENABLED', true),
    'todays_reading_reminder_enabled' => env('FCM_TODAYS_READING_REMINDER_ENABLED', true),

   
    /*
    |--------------------------------------------------------------------------
    | Deep Link URL Patterns
    |--------------------------------------------------------------------------
    |
    | URL patterns for deep linking from notifications.
    |
    */
    'deep_link_patterns' => [
        'activity_approved' => '/books/{book_id}/activities',
        'activity_rejected' => '/books/{book_id}/activities',
        'activity_submitted' => '/teacher/activity-review/{user_activity_id}',
        'inactivity_reminder' => '/books',
        'todays_reading_reminder' => '/books',
        'admin_broadcast' => '/home',
    ],

    /*
    |--------------------------------------------------------------------------
    | Activity Log Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for logging FCM activities using Spatie ActivityLog.
    |
    */
    'activity_log' => [
        'log_name' => 'fcm_notifications',
        'enabled' => true,
    ],
];
