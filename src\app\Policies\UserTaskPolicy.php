<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\UserTask;
use App\Models\User;

class UserTaskPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, UserTask $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, UserTask $item): bool
    {
        return true;
    }

    public function delete(User $user, UserTask $item): bool
    {
        return true;
    }

    public function restore(User $user, UserTask $item): bool
    {
        return true;
    }

    public function forceDelete(User $user, UserTask $item): bool
    {
        return true;
    }

    public function massDelete(User $user): bool
    {
        return true;
    }
}
