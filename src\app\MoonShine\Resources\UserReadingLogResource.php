<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserReadingLog;
use App\Models\User;
use App\Models\Book;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('book-open')]
class UserReadingLogResource extends BaseResource
{
    protected string $model = UserReadingLog::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'book'];

    public function getTitle(): string
    {
        return __('admin.user_reading_logs');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: BookResource::class
            )
                ->sortable(),

            Date::make(__('admin.log_date'), 'log_date')
                ->sortable(),

            Number::make(__('admin.pages_read'), 'pages_read')
                ->sortable(),

            Number::make(__('admin.reading_duration'), 'reading_duration')
                ->sortable(),

            Switcher::make(__('admin.book_completed'), 'book_completed')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class
                    )
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.book'),
                        'book',
                        formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                        resource: BookResource::class
                    )
                        ->required()
                        ->searchable(),
                ]),

                Date::make(__('admin.log_date'), 'log_date')
                    ->required()
                    ->default(now()->format('Y-m-d')),

                Flex::make([
                    Number::make(__('admin.start_page'), 'start_page')
                        ->min(1)
                        ->hint(__('admin.start_page_hint')),

                    Number::make(__('admin.end_page'), 'end_page')
                        ->min(1)
                        ->hint(__('admin.end_page_hint')),
                ]),

                Flex::make([
                    Number::make(__('admin.pages_read'), 'pages_read')
                        ->required()
                        ->min(1)
                        ->hint(__('admin.pages_read_hint')),

                    Number::make(__('admin.reading_duration'), 'reading_duration')
                        ->min(1)
                        ->hint(__('admin.reading_duration_hint')),
                ]),

                Switcher::make(__('admin.book_completed'), 'book_completed')
                    ->default(false),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: BookResource::class
            ),

            Date::make(__('admin.log_date'), 'log_date'),
            Number::make(__('admin.start_page'), 'start_page'),
            Number::make(__('admin.end_page'), 'end_page'),
            Number::make(__('admin.pages_read'), 'pages_read'),
            Number::make(__('admin.reading_duration'), 'reading_duration'),
            Switcher::make(__('admin.book_completed'), 'book_completed'),

            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'book_id' => ['required', 'exists:books,id'],
            'log_date' => ['required', 'date', 'before_or_equal:today'],
            'start_page' => ['nullable', 'integer', 'min:1'],
            'end_page' => ['nullable', 'integer', 'min:1', 'gte:start_page'],
            'pages_read' => ['required', 'integer', 'min:1'],
            'reading_duration' => ['nullable', 'integer', 'min:1'],
            'book_completed' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'book.name', 'book.isbn'];
    }

    protected function getDefaultSort(): array
    {
        return ['log_date' => 'desc', 'user.name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all reading logs
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see logs for students in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            });
        }

        // Teachers can see logs for students in their assigned classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            });
        }

        // Students can only see their own reading logs
        if ($user->isStudent()) {
            return $builder->where('user_id', $user->id);
        }

        // Default: no access
        return $builder->where('id', 0);
    }
}
