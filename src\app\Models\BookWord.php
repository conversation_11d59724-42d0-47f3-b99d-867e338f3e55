<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BookWord extends BaseModel
{
    /**
     * Difficulty level constants.
     */
    const DIFFICULTY_EASY = 'easy';
    const DIFFICULTY_MEDIUM = 'medium';
    const DIFFICULTY_HARD = 'hard';

    /**
     * The table associated with the model.
     */
    protected $table = 'book_words';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'book_id',
        'word',
        'definition',
        'synonym',
        'antonym',
        'page_reference',
        'difficulty_level',
        'is_active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'page_reference' => 'integer',
        ];
    }

    /**
     * Get all difficulty levels.
     */
    public static function getDifficultyLevels(): array
    {
        return [
            self::DIFFICULTY_EASY => 'Easy',
            self::DIFFICULTY_MEDIUM => 'Medium',
            self::DIFFICULTY_HARD => 'Hard',
        ];
    }

    /**
     * Get difficulty level name.
     */
    public function getDifficultyLevelNameAttribute(): string
    {
        return self::getDifficultyLevels()[$this->difficulty_level] ?? 'Unknown';
    }

    /**
     * Get the book this word belongs to.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the user who created this word.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get active words.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by difficulty level.
     */
    public function scopeOfDifficulty($query, string $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeForBook($query, int $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to search by word.
     */
    public function scopeSearchWord($query, string $searchTerm)
    {
        return $query->where('word', 'like', "%{$searchTerm}%");
    }

    /**
     * Scope to filter by page reference.
     */
    public function scopeOnPage($query, int $page)
    {
        return $query->where('page_reference', $page);
    }

    /**
     * Scope to filter by page range.
     */
    public function scopeInPageRange($query, int $startPage, int $endPage)
    {
        return $query->whereBetween('page_reference', [$startPage, $endPage]);
    }

    /**
     * Check if word has definition.
     */
    public function hasDefinition(): bool
    {
        return !empty($this->definition);
    }

    /**
     * Check if word has synonym.
     */
    public function hasSynonym(): bool
    {
        return !empty($this->synonym);
    }

    /**
     * Check if word has antonym.
     */
    public function hasAntonym(): bool
    {
        return !empty($this->antonym);
    }

    /**
     * Get formatted word information.
     */
    public function getFormattedInfoAttribute(): array
    {
        return [
            'word' => $this->word,
            'definition' => $this->definition,
            'synonym' => $this->synonym,
            'antonym' => $this->antonym,
            'page' => $this->page_reference,
            'difficulty' => $this->difficulty_level_name,
        ];
    }
}
