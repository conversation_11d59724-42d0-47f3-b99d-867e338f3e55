<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class ChallengeTask extends BaseModel
{
    protected $fillable = [
        'challenge_id',
        'task_id',
        'start_date',
        'end_date',
        'reward_id',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * Get the challenge that this challenge task belongs to.
     */
    public function challenge(): BelongsTo
    {
        return $this->belongsTo(Challenge::class);
    }

    /**
     * Get the task that this challenge task belongs to.
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Get the user tasks for this challenge task.
     */
    public function userTasks(): HasMany
    {
        return $this->hasMany(UserTask::class)->where('task_type', UserTask::TASK_TYPE_CHALLENGE);
    }

    /**
     * Legacy method for backward compatibility.
     * @deprecated Use userTasks() instead
     */
    public function userChallengeTasks(): <PERSON><PERSON><PERSON>
    {
        return $this->userTasks();
    }

    /**
     * Get the reward associated with this challenge task (if any).
     */
    public function reward(): BelongsTo
    {
        return $this->belongsTo(Reward::class);
    }

    /**
     * Get the user who created this challenge task.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active challenge tasks (within date range).
     */
    public function scopeActive($query)
    {
        $now = Carbon::now()->toDateString();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now);
    }

    /**
     * Scope for upcoming challenge tasks.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', Carbon::now()->toDateString());
    }

    /**
     * Scope for past challenge tasks.
     */
    public function scopePast($query)
    {
        return $query->where('end_date', '<', Carbon::now()->toDateString());
    }

    /**
     * Check if challenge task is currently active (within date range).
     */
    public function isActive(): bool
    {
        $now = Carbon::now()->toDateString();
        return $this->start_date <= $now && $this->end_date >= $now;
    }

    /**
     * Check if challenge task is upcoming.
     */
    public function isUpcoming(): bool
    {
        return $this->start_date > Carbon::now()->toDateString();
    }

    /**
     * Check if challenge task is past.
     */
    public function isPast(): bool
    {
        return $this->end_date < Carbon::now()->toDateString();
    }

    /**
     * Get status display.
     */
    public function getStatusDisplayAttribute(): string
    {
        if ($this->isUpcoming()) {
            return __('admin.upcoming');
        }

        if ($this->isActive()) {
            return __('admin.active');
        }

        return __('admin.completed');
    }

    /**
     * Get date range display.
     */
    public function getDateRangeDisplayAttribute(): string
    {
        return $this->start_date->format('d/m/Y') . ' - ' . $this->end_date->format('d/m/Y');
    }

    /**
     * Get duration in days.
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Get participants count.
     */
    public function getParticipantsCountAttribute(): int
    {
        return $this->userTasks()->distinct('user_id')->count('user_id');
    }

    /**
     * Get completion count.
     */
    public function getCompletionCountAttribute(): int
    {
        return $this->userTasks()->where('completed', true)->count();
    }

    /**
     * Get completion rate.
     */
    public function getCompletionRateAttribute(): float
    {
        $totalParticipants = $this->participants_count;
        if ($totalParticipants === 0) {
            return 0.0;
        }

        $completedCount = $this->completion_count;
        return round(($completedCount / $totalParticipants) * 100, 2);
    }

    /**
     * Get completion rate display.
     */
    public function getCompletionRateDisplayAttribute(): string
    {
        return $this->completion_rate . '%';
    }

    /**
     * Get summary information.
     */
    public function getSummaryAttribute(): string
    {
        $participants = $this->participants_count;
        $completed = $this->completion_count;
        $rate = $this->completion_rate_display;

        return "Participants: {$participants}, Completed: {$completed}, Rate: {$rate}";
    }
}
