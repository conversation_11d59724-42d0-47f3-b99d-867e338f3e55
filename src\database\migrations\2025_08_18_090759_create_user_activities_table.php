<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->foreignId('activity_id')->constrained('activities')->onDelete('cascade');
            $table->timestamp('activity_date');
            $table->text('content')->nullable()->comment('Written content for writing activities');
            $table->integer('rating')->nullable()->comment('Rating value for rating activities');
            $table->string('media_url')->nullable()->comment('User-submitted media content');
            $table->tinyInteger('status')->default(0)->comment('0-pending, 1-approved, 2-rejected, 3-completed (no approval needed)');
            $table->foreignId('challenge_task_id')->nullable()->constrained('challenge_tasks')->onDelete('set null');
          
            // Add indexes for performance
            $table->index(['user_id', 'activity_date']);
            $table->index(['book_id', 'activity_date']);
            $table->index(['activity_id', 'status']);
            $table->index('status');
            $table->index('activity_date');
            $table->index(['challenge_task_id', 'user_id']);
            $table->index(['challenge_task_id', 'status']);
            
            // Unique constraint to prevent duplicate submissions
            // Removed at 06.09.2025 due to allowing multiple tries
//            $table->unique(['user_id', 'book_id', 'activity_id'], 'user_book_activity_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_activities');
    }
};
