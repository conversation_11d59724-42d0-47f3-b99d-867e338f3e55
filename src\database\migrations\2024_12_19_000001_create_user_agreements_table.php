<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_agreements', function (Blueprint $table) {
            $table->id();
            
            // Core agreement information
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('agreement_type')->default('privacy_policy')
                  ->comment('Type of agreement (privacy_policy, terms_of_service, etc.)');
            $table->string('version')->default('1.0')
                  ->comment('Version of the agreement accepted');
            $table->timestamp('accepted_at')
                  ->comment('When the user accepted the agreement');
            $table->string('ip_address', 45)->nullable()
                  ->comment('IP address of the user when accepting');
            
            // Standard audit fields
            
            // Add indexes for performance
            $table->index(['user_id', 'agreement_type']);
            $table->index(['agreement_type', 'version']);
            $table->index('accepted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_agreements');
    }
};
