<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Level extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'nr',
        'name',
        'image',
        'books_count',
        'page_points',
        'all_required',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'nr' => 'integer',
            'books_count' => 'integer',
            'page_points' => 'integer',
            'all_required' => 'boolean',
        ];
    }

    /**
     * Get the user levels for this level.
     */
    public function userLevels(): HasMany
    {
        return $this->hasMany(UserLevel::class);
    }

    /**
     * Get users who have achieved this level.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_levels')
            ->withPivot(['level_date', 'reading_log_id']);
    }

    /**
     * Check if a user qualifies for this level based on their stats.
     */
    public function userQualifies(User $user): bool
    {
        $userBooksCompleted = $user->getTotalBooksCompleted();
        $userPagePoints = $user->getTotalPagePoints();

        if ($this->all_required) {
            // Both conditions must be met
            return $userBooksCompleted >= $this->books_count && $userPagePoints >= $this->page_points;
        } else {
            // Either condition can be met
            return $userBooksCompleted >= $this->books_count || $userPagePoints >= $this->page_points;
        }
    }

    /**
     * Get the next level after this one.
     */
    public function getNextLevel(): ?Level
    {
        return static::where('nr', '>', $this->nr)
            ->orderBy('nr', 'asc')
            ->first();
    }

    /**
     * Get the previous level before this one.
     */
    public function getPreviousLevel(): ?Level
    {
        return static::where('nr', '<', $this->nr)
            ->orderBy('nr', 'desc')
            ->first();
    }

    /**
     * Scope to get levels ordered by level number.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('nr', 'asc');
    }

    /**
     * Get the display name for the level.
     */
    public function getDisplayNameAttribute(): string
    {
        return "Level {$this->nr}: {$this->name}";
    }

    /**
     * Get the summary for the level.
     */
    public function getSummaryAttribute(): string
    {
        $requirements = [];
        
        if ($this->books_count > 0) {
            $requirements[] = "{$this->books_count} books";
        }
        
        if ($this->page_points > 0) {
            $requirements[] = "{$this->page_points} page points";
        }

        $connector = $this->all_required ? ' and ' : ' or ';
        
        return $this->display_name . ' (Requires: ' . implode($connector, $requirements) . ')';
    }
}
