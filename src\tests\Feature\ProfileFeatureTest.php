<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Avatar;
use App\Models\UserAvatar;
use App\Models\UserReadingLog;
use App\Models\UserPoint;
use App\Models\UserReward;
use App\Models\Reward;
use App\Models\Book;
use App\Models\Role;
use App\Services\ReadingStreakAnalyzer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Carbon\Carbon;

class ProfileFeatureTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Avatar $avatar;
    private Book $book;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user with student role
        $this->user = User::factory()->create();
        $studentRole = Role::factory()->create(['name' => 'student']);
        $this->user->assignRole($studentRole);
        
        // Create test avatar
        $this->avatar = Avatar::factory()->create([
            'name' => 'Test Avatar',
            'required_points' => 100,
            'active' => true,
        ]);
        
        // Create test book
        $this->book = Book::factory()->create();
    }

    /** @test */
    public function user_can_view_profile_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('mobile.me'));

        $response->assertStatus(200);
        $response->assertSeeLivewire('mobile.profile');
        $response->assertSee($this->user->name);
    }

    /** @test */
    public function profile_displays_correct_user_statistics()
    {
        // Create some test data
        UserPoint::factory()->create([
            'user_id' => $this->user->id,
            'point_type' => UserPoint::POINT_TYPE_PAGE,
            'points' => 50,
        ]);
        
        UserPoint::factory()->create([
            'user_id' => $this->user->id,
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
            'points' => 100,
        ]);

        Livewire::actingAs($this->user)
            ->test('mobile.profile')
            ->assertSet('stats.page_points', 50)
            ->assertSet('stats.activity_points', 100);
    }

    /** @test */
    public function profile_displays_reading_streak_data()
    {
        // Create reading logs for streak
        UserReadingLog::factory()->create([
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'log_date' => Carbon::today(),
            'pages_read' => 10,
        ]);

        UserReadingLog::factory()->create([
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'log_date' => Carbon::yesterday(),
            'pages_read' => 15,
        ]);

        Livewire::actingAs($this->user)
            ->test('mobile.profile')
            ->assertSet('readingStreak', function ($streak) {
                return count($streak) === 30 && 
                       collect($streak)->where('has_reading', true)->count() >= 2;
            });
    }

    /** @test */
    public function user_can_navigate_to_avatar_selection()
    {
        Livewire::actingAs($this->user)
            ->test('mobile.profile')
            ->call('navigateToAvatarSelection')
            ->assertRedirect(route('mobile.choose-avatar'));
    }

    /** @test */
    public function user_can_view_avatar_selection_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('mobile.choose-avatar'));

        $response->assertStatus(200);
        $response->assertSeeLivewire('mobile.choose-avatar');
    }

    /** @test */
    public function avatar_selection_displays_available_and_locked_avatars()
    {
        // Create available avatar (low points requirement)
        $availableAvatar = Avatar::factory()->create([
            'name' => 'Available Avatar',
            'required_points' => 0,
            'active' => true,
        ]);

        // Create locked avatar (high points requirement)
        $lockedAvatar = Avatar::factory()->create([
            'name' => 'Locked Avatar',
            'required_points' => 1000,
            'active' => true,
        ]);

        Livewire::actingAs($this->user)
            ->test('mobile.choose-avatar')
            ->assertSet('availableAvatars', function ($avatars) use ($availableAvatar) {
                return $avatars->contains('id', $availableAvatar->id);
            })
            ->assertSet('lockedAvatars', function ($avatars) use ($lockedAvatar) {
                return $avatars->contains('id', $lockedAvatar->id);
            });
    }

    /** @test */
    public function user_can_select_available_avatar()
    {
        // Create available avatar
        $availableAvatar = Avatar::factory()->create([
            'required_points' => 0,
            'active' => true,
        ]);

        Livewire::actingAs($this->user)
            ->test('mobile.choose-avatar')
            ->call('selectAvatar', $availableAvatar->id)
            ->assertHasNoErrors();

        // Verify avatar was selected
        $this->assertDatabaseHas('user_avatars', [
            'user_id' => $this->user->id,
            'avatar_id' => $availableAvatar->id,
        ]);
    }

    /** @test */
    public function user_cannot_select_locked_avatar()
    {
        // Create locked avatar
        $lockedAvatar = Avatar::factory()->create([
            'required_points' => 1000,
            'active' => true,
        ]);

        Livewire::actingAs($this->user)
            ->test('mobile.choose-avatar')
            ->call('selectAvatar', $lockedAvatar->id);

        // Verify avatar was not selected
        $this->assertDatabaseMissing('user_avatars', [
            'user_id' => $this->user->id,
            'avatar_id' => $lockedAvatar->id,
        ]);
    }

    /** @test */
    public function user_can_view_my_badges_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('mobile.my-badges'));

        $response->assertStatus(200);
        $response->assertSeeLivewire('mobile.my-badges');
    }

    /** @test */
    public function my_badges_displays_earned_badges()
    {
        // Create badge reward
        $reward = Reward::factory()->create([
            'type' => Reward::TYPE_BADGE,
            'name' => 'Test Badge',
        ]);

        UserReward::factory()->create([
            'user_id' => $this->user->id,
            'reward_id' => $reward->id,
            'awarded_date' => Carbon::now(),
        ]);

        Livewire::actingAs($this->user)
            ->test('mobile.my-badges')
            ->assertSet('totalBadges', 1)
            ->assertSet('badges', function ($badges) use ($reward) {
                return $badges->contains('reward_id', $reward->id);
            });
    }

    /** @test */
    public function reading_streak_analyzer_identifies_unstoppable_streaker()
    {
        // Create 30 consecutive days of reading
        for ($i = 29; $i >= 0; $i--) {
            UserReadingLog::factory()->create([
                'user_id' => $this->user->id,
                'book_id' => $this->book->id,
                'log_date' => Carbon::today()->subDays($i),
                'pages_read' => 10,
            ]);
        }

        $analyzer = new ReadingStreakAnalyzer();
        $analysis = $analyzer->analyzeReadingPattern($this->user->id);

        $this->assertEquals('unstoppable_streaker', $analysis['pattern']);
        $this->assertArrayHasKey('message', $analysis);
        $this->assertArrayHasKey('en', $analysis['message']);
        $this->assertArrayHasKey('tr', $analysis['message']);
    }

    /** @test */
    public function reading_streak_analyzer_identifies_recent_recruit()
    {
        // Create reading logs for last 3 days only
        for ($i = 2; $i >= 0; $i--) {
            UserReadingLog::factory()->create([
                'user_id' => $this->user->id,
                'book_id' => $this->book->id,
                'log_date' => Carbon::today()->subDays($i),
                'pages_read' => 10,
            ]);
        }

        $analyzer = new ReadingStreakAnalyzer();
        $analysis = $analyzer->analyzeReadingPattern($this->user->id);

        $this->assertEquals('recent_recruit', $analysis['pattern']);
    }

    /** @test */
    public function reading_streak_analyzer_provides_fallback_message()
    {
        // Create minimal reading data that doesn't match specific patterns
        UserReadingLog::factory()->create([
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'log_date' => Carbon::today()->subDays(15),
            'pages_read' => 10,
        ]);

        $analyzer = new ReadingStreakAnalyzer();
        $analysis = $analyzer->analyzeReadingPattern($this->user->id);

        $this->assertEquals('general', $analysis['pattern']);
        $this->assertArrayHasKey('message', $analysis);
    }
}
