<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('task_type_id')->constrained('enum_task_types')->onDelete('cascade');
            $table->foreignId('task_cycle_id')->constrained('enum_task_cycles')->onDelete('cascade');
            $table->integer('task_value')->nullable();
            $table->foreignId('activity_id')->nullable()->constrained('activities')->onDelete('set null');
            $table->boolean('active')->default(true);

            // Indexes for performance
            $table->index('task_type_id');
            $table->index('task_cycle_id');
            $table->index('activity_id');
            $table->index('active');
            $table->index(['active', 'task_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
