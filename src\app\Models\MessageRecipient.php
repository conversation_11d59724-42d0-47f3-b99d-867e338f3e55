<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MessageRecipient extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'message_id',
        'user_id',
        'read',
        'sent_date',
        'read_date',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'read' => 'boolean',
            'sent_date' => 'datetime',
            'read_date' => 'datetime',
        ];
    }

    /**
     * Get the message that this recipient belongs to.
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    /**
     * Get the user who received this message.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark this message as read.
     */
    public function markAsRead(): void
    {
        if (!$this->read) {
            $this->update([
                'read' => true,
                'read_date' => now(),
            ]);
        }
    }

    /**
     * Mark this message as unread.
     */
    public function markAsUnread(): void
    {
        $this->update([
            'read' => false,
            'read_date' => null,
        ]);
    }

    /**
     * Scope to get read messages.
     */
    public function scopeRead($query)
    {
        return $query->where('read', true);
    }

    /**
     * Scope to get unread messages.
     */
    public function scopeUnread($query)
    {
        return $query->where('read', false);
    }

    /**
     * Scope to get messages for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}

