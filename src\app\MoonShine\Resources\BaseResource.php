<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use MoonShine\Laravel\Resources\ModelResource;
use MoonShine\Support\ListOf;
use MoonShine\UI\Components\ActionButton;
use MoonShine\Support\Enums\ClickAction;


use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

abstract class BaseResource extends ModelResource
{
    use WithRolePermissions;

    protected ?ClickAction $clickAction = ClickAction::DETAIL;

    /**
     * Get common index fields.
     */
    protected function getCommonIndexFields(): array
    {
        return [
            //            ID::make()->sortable(),
        ];
    }

    /**
     * Get common detail fields.
     */
    protected function getCommonDetailFields(): array
    {
        return [    
        ];
    }

    /**
     * Common validation rules.
     */
    protected function getCommonRules(mixed $item = null): array
    {
        return [
        ];
    }

    /**
     * Rules method with proper signature.
     */
    public function rules(mixed $item): array
    {
        return $this->getCommonRules($item);
    }

    /**
     * Get translatable title.
     */
    public function getTitle(): string
    {
        return __('admin.' . strtolower(class_basename($this->getModel())));
    }

    /**
     * Get search fields for the resource.
     */
    protected function search(): array
    {
        return ['name'];
    }

    /**
     * Apply search to query.
     */
    protected function applySearch($query, string $search)
    {
        $searchFields = $this->search();

        $query->where(function ($q) use ($search, $searchFields) {
            foreach ($searchFields as $field) {
                $q->orWhere($field, 'like', '%' . $search . '%');
            }
        });

        return $query;
    }

    /**
     * Get items per page.
     */
    protected function getItemsPerPage(): int
    {
        return 25;
    }

    /**
     * Get default sort field.
     */
    protected function getDefaultSort(): array
    {
        return ['id' => 'desc'];
    }

    protected function formBuilderButtons(): ListOf
    {
        return parent::formBuilderButtons()
            ->add(
                ActionButton::make(__('admin.cancel'), fn() => $this->getIndexPageUrl())->secondary()->class('btn-lg')
            );
    }

    /**
     * Redirect to index page after save.
     */
    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
