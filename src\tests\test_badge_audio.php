<?php

/**
 * Badge Unlock Audio Test Script
 * 
 * This script verifies that the badge unlock audio functionality is properly configured.
 * It checks file accessibility, generates test URLs, and provides debugging information.
 */

require __DIR__ . '/../vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "\n=== Badge Unlock Audio Test ===\n\n";

// Test 1: Check if audio file exists
echo "1. Checking audio file accessibility...\n";

$audioFilePath = public_path('audio/badge_unlocked.mp3');
$audioFileExists = file_exists($audioFilePath);

if ($audioFileExists) {
    $fileSize = filesize($audioFilePath);
    $fileSizeKB = round($fileSize / 1024, 2);
    echo "   ✓ Audio file found: {$audioFilePath}\n";
    echo "   ✓ File size: {$fileSizeKB} KB\n";
    
    // Check if file is readable
    if (is_readable($audioFilePath)) {
        echo "   ✓ File is readable\n";
    } else {
        echo "   ✗ File is not readable - check permissions\n";
    }
} else {
    echo "   ✗ Audio file NOT found: {$audioFilePath}\n";
    echo "   ℹ  Please ensure the audio file exists at this location\n";
}

// Test 2: Generate and test asset URL
echo "\n2. Testing asset URL generation...\n";

try {
    $audioUrl = asset('audio/badge_unlocked.mp3');
    echo "   ✓ Asset URL generated: {$audioUrl}\n";
    
    // Test if URL is accessible (basic check)
    $urlParts = parse_url($audioUrl);
    if ($urlParts) {
        echo "   ✓ URL format is valid\n";
        echo "   - Scheme: " . ($urlParts['scheme'] ?? 'none') . "\n";
        echo "   - Host: " . ($urlParts['host'] ?? 'localhost') . "\n";
        echo "   - Path: " . ($urlParts['path'] ?? 'none') . "\n";
    } else {
        echo "   ✗ Invalid URL format\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error generating asset URL: " . $e->getMessage() . "\n";
}

// Test 3: Check public directory structure
echo "\n3. Checking public directory structure...\n";

$publicPath = public_path();
$audioDir = public_path('audio');

echo "   - Public path: {$publicPath}\n";
echo "   - Audio directory: {$audioDir}\n";

if (is_dir($audioDir)) {
    echo "   ✓ Audio directory exists\n";
    
    // List audio files
    $audioFiles = glob($audioDir . '/*');
    if (!empty($audioFiles)) {
        echo "   ✓ Audio files found:\n";
        foreach ($audioFiles as $file) {
            $filename = basename($file);
            $size = filesize($file);
            $sizeKB = round($size / 1024, 2);
            echo "     - {$filename} ({$sizeKB} KB)\n";
        }
    } else {
        echo "   ℹ  No audio files found in directory\n";
    }
} else {
    echo "   ✗ Audio directory does not exist\n";
    echo "   ℹ  You may need to create: {$audioDir}\n";
}

// Test 4: Check Blade template
echo "\n4. Checking Blade template integration...\n";

$bladeFile = resource_path('views/livewire/mobile/badge-unlocked.blade.php');

if (file_exists($bladeFile)) {
    echo "   ✓ Blade template found: {$bladeFile}\n";
    
    $bladeContent = file_get_contents($bladeFile);
    
    // Check for audio element
    if (strpos($bladeContent, 'id="badgeUnlockAudio"') !== false) {
        echo "   ✓ Audio element found in template\n";
    } else {
        echo "   ✗ Audio element NOT found in template\n";
    }
    
    // Check for audio toggle button
    if (strpos($bladeContent, 'id="audioToggle"') !== false) {
        echo "   ✓ Audio toggle button found in template\n";
    } else {
        echo "   ✗ Audio toggle button NOT found in template\n";
    }
    
    // Check for JavaScript audio handling
    if (strpos($bladeContent, 'playBadgeUnlockAudio') !== false) {
        echo "   ✓ Audio JavaScript functions found in template\n";
    } else {
        echo "   ✗ Audio JavaScript functions NOT found in template\n";
    }
    
    // Check for asset URL usage
    if (strpos($bladeContent, "asset('audio/badge_unlocked.mp3')") !== false) {
        echo "   ✓ Correct asset URL usage found in template\n";
    } else {
        echo "   ✗ Asset URL usage NOT found or incorrect in template\n";
    }
} else {
    echo "   ✗ Blade template NOT found: {$bladeFile}\n";
}

// Test 5: Environment and configuration checks
echo "\n5. Environment and configuration checks...\n";

// Check APP_URL
$appUrl = config('app.url');
echo "   - APP_URL: {$appUrl}\n";

// Check if we're in local development
$environment = config('app.env');
echo "   - Environment: {$environment}\n";

// Check asset URL configuration
$assetUrl = config('app.asset_url');
if ($assetUrl) {
    echo "   - Asset URL: {$assetUrl}\n";
} else {
    echo "   - Asset URL: Not configured (using APP_URL)\n";
}

// Test 6: Generate test HTML for manual testing
echo "\n6. Generating test HTML...\n";

$testHtml = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Badge Audio Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .test-container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        button { padding: 10px 20px; margin: 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Badge Unlock Audio Test</h1>
        <p>This page tests the badge unlock audio functionality.</p>
        
        <audio id="testAudio" preload="auto">
            <source src="' . asset('audio/badge_unlocked.mp3') . '" type="audio/mpeg">
            Your browser does not support the audio element.
        </audio>
        
        <div>
            <button onclick="playAudio()">Play Badge Audio</button>
            <button onclick="testAutoplay()">Test Autoplay</button>
            <button onclick="checkAudioSupport()">Check Audio Support</button>
        </div>
        
        <div id="status"></div>
        
        <script>
            const audio = document.getElementById("testAudio");
            const status = document.getElementById("status");
            
            function showStatus(message, type = "success") {
                status.innerHTML = `<div class="${type}">${message}</div>`;
            }
            
            function playAudio() {
                audio.currentTime = 0;
                const playPromise = audio.play();
                
                if (playPromise !== undefined) {
                    playPromise
                        .then(() => showStatus("✓ Audio played successfully!", "success"))
                        .catch(error => showStatus("✗ Audio play failed: " + error.message, "error"));
                }
            }
            
            function testAutoplay() {
                audio.currentTime = 0;
                audio.play()
                    .then(() => showStatus("✓ Autoplay works in this browser!", "success"))
                    .catch(() => showStatus("ℹ Autoplay blocked - this is normal for mobile browsers", "error"));
            }
            
            function checkAudioSupport() {
                const canPlayMP3 = audio.canPlayType("audio/mpeg");
                const audioSupported = !!window.Audio;
                
                let message = `Audio Support: ${audioSupported ? "✓" : "✗"}<br>`;
                message += `MP3 Support: ${canPlayMP3 ? "✓ " + canPlayMP3 : "✗"}<br>`;
                message += `Audio Source: ${audio.src}<br>`;
                message += `Ready State: ${audio.readyState}`;
                
                showStatus(message, audioSupported && canPlayMP3 ? "success" : "error");
            }
            
            // Auto-check on load
            window.addEventListener("load", checkAudioSupport);
        </script>
    </div>
</body>
</html>';

$testHtmlFile = public_path('badge_audio_test.html');
file_put_contents($testHtmlFile, $testHtml);

echo "   ✓ Test HTML file created: {$testHtmlFile}\n";
echo "   ℹ  You can access it at: " . url('badge_audio_test.html') . "\n";

// Summary
echo "\n=== Test Summary ===\n";

$issues = [];
if (!$audioFileExists) {
    $issues[] = "Audio file missing";
}

if (empty($issues)) {
    echo "🎉 All tests passed! Badge unlock audio should work correctly.\n";
    echo "\nNext steps:\n";
    echo "1. Visit the badge unlock page to test audio playback\n";
    echo "2. Test on mobile devices (iOS Safari, Android Chrome)\n";
    echo "3. Verify mute/unmute toggle functionality\n";
    echo "4. Test with multiple rewards to ensure audio plays for each\n";
} else {
    echo "⚠️  Issues found:\n";
    foreach ($issues as $issue) {
        echo "   - {$issue}\n";
    }
    echo "\nPlease resolve these issues before testing.\n";
}

echo "\n=== Test Complete ===\n\n";
