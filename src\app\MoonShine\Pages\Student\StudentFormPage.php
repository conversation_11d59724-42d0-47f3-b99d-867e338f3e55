<?php

declare(strict_types=1);

namespace App\MoonShine\Pages\Student;

use MoonShine\Laravel\Pages\Crud\FormPage;

use MoonShine\UI\Components\{Layout\Box, Layout\Flex, ActionButton};
use MoonShine\UI\Fields\{Field, Password, PasswordRepeat, Text};

class StudentFormPage extends FormPage
{
    protected function fields(): iterable
    {
        return [
            Box::make(__('admin.basic_information'), [
                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),

                Flex::make([
                    Text::make(__('admin.username'), 'username')
                        ->when(fn() => $this->getResource()->isCreateFormPage(), fn(Field $field) => $field->required())
//                        ->unless(fn() => $this->getResource()->isCreateFormPage(), fn(Field $field) => $field->readonly())
                        ->hint(__('admin.unique_username_hint'))
                        ->placeholder(__('admin.enter_username')),
                        
                    ActionButton::make(__('admin.check_username'))
                            ->method('checkUsername') 
                            ->withSelectorsParams(['username' => 'input[name="username"]'])
                            ->secondary()
                            ->icon('check-circle')
//                            ->canSee(fn () => $this->getResource()->isCreateFormPage()),

/*
                    Email::make(__('admin.email'), 'email')
                        ->required()
                        ->placeholder(__('admin.enter_email')),
*/
                ]),

                Flex::make([
                    Password::make(__('admin.password'), 'password')
                        ->customAttributes(['autocomplete' => 'new-password'])
                        ->required(!$this->getResource()->getItem()),

                    PasswordRepeat::make(__('admin.password_repeat'), 'password_confirmation')
                        ->customAttributes(['autocomplete' => 'confirm-password'])
                        ->required(!$this->getResource()->getItem()),
                ]),
            ]),
        ];
    }
}
