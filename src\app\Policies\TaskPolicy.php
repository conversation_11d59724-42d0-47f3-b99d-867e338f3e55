<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Task;
use App\Models\User;

class TaskPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function view(User $user, Task $item): bool
    {
        //TODO: School admin or teacher can see only tasks they are part of (their schools or classes)
        
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function create(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function update(User $user, Task $item): bool
    {
        return ($user->isSystemAdmin()) || ($item->created_by === $user->id);
    }

    public function delete(User $user, Task $item): bool
    {
        return $this->update($user, $item);
    }

    public function restore(User $user, Task $item): bool
    {
        return $this->update($user, $item);
    }

    public function forceDelete(User $user, Task $item): bool
    {
        return $this->update($user, $item);
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }
}
