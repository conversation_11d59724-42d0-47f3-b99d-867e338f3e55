<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\BookType;
use App\Models\EnumClassLevel;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\RelationRepeater;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\{ID, Text, Image, Number};
use MoonShine\Support\Attributes\Icon;

#[Icon('tag')]
class BookTypeResource extends BaseResource
{
    protected string $model = BookType::class;

    protected string $column = 'name';

    public function getTitle(): string
    {
        return __('admin.book_types');
    }

    public function indexFields(): array
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),
        ];
    }

    public function formFields(): array
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),
                Text::make(__('admin.description'), 'description')
                    ->placeholder(__('admin.enter_description')),
                Image::make(__('admin.thumbnail'), 'thumbnail'),
            ]),
            Box::make([
                RelationRepeater::make(__('admin.page_points'), 'pagePoints', resource: PagePointResource::class)
                    ->removable()
                    ->fields([
                        ID::make(),
                        BelongsTo::make(
                            __('admin.class_level'),
                            'classLevel',
                            formatted: fn(EnumClassLevel $classLevel) => $classLevel->name,
                            resource: EnumClassLevelResource::class
                        ),

                        Number::make(__('admin.point'), 'point')
                        ->min(0)
                        ->max(100)
                        ->step(0.01),
                    ]),
            ]),
        ];
    }

    public function detailFields(): array
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.description'), 'description'),
            Image::make(__('admin.thumbnail'), 'thumbnail'),
            RelationRepeater::make(__('admin.page_points'), 'pagePoints', resource: PagePointResource::class)
                ->fields([
                    BelongsTo::make(
                        __('admin.class_level'),
                        'classLevel',
                        formatted: fn(EnumClassLevel $classLevel) => $classLevel->name,
                        resource: EnumClassLevelResource::class
                    ),

                    Number::make(__('admin.point'), 'point'),
                ]),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'unique:book_types,name,' . $item?->id],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
