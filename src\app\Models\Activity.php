<?php

namespace App\Models;

use App\Models\Scopes\ClassActivityResolutionScope;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Activity extends BaseModel
{
    /**
     * Activity type constants.
     */
    const ACTIVITY_TYPE_WRITING = 1;
    const ACTIVITY_TYPE_RATING = 2;
    const ACTIVITY_TYPE_MEDIA = 3;
//    const ACTIVITY_TYPE_PHYSICAL = 4;
//    const ACTIVITY_TYPE_GAME = 5;
    const ACTIVITY_TYPE_QUIZ = 6;
    const ACTIVITY_TYPE_VOCABULARY_TEST = 7;

    /**
     * Media type constants.
     */
    const MEDIA_TYPE_IMAGE = 1;
    const MEDIA_TYPE_AUDIO = 2;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'category_id',
        'activity_type',
        'media_type',
        'name',
        'description',
        'question_count',
        'choices_count',
        'min_grade',
        'required',
        'allowed_tries',
        'min_word_count',
        'min_rating',
        'max_rating',
        'media_url',
        'points',
        'need_approval',
        'active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'activity_type' => 'integer',
            'media_type' => 'integer',
            'question_count' => 'integer',
            'choices_count' => 'integer',
            'min_grade' => 'integer',
            'required' => 'boolean',
            'allowed_tries' => 'integer',
            'min_word_count' => 'integer',
            'min_rating' => 'integer',
            'max_rating' => 'integer',
            'points' => 'integer',
            'need_approval' => 'boolean',
            'active' => 'boolean',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // When a new activity is created, generate ClassActivity records for all existing classes
        static::created(function ($activity) {
            \App\Models\ClassActivity::createForNewActivity($activity->id);
        });
    }

    protected static function booted()
    {
        static::addGlobalScope(new ClassActivityResolutionScope);
    }

    /**
     * Get the category this activity belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ActivityCategory::class);
    }

    /**
     * Get user activities for this activity.
     */
    public function userActivities(): HasMany
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * Get class-specific configurations for this activity.
     */
    public function classActivities(): HasMany
    {
        return $this->hasMany(\App\Models\ClassActivity::class);
    }

    /**
     * Scope to filter active activities.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to filter by activity type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Scope to filter activities that need approval.
     */
    public function scopeNeedApproval($query, $needApproval = true)
    {
        return $query->where('need_approval', $needApproval);
    }

    /**
     * Get the activity type name.
     */
    public function getActivityTypeNameAttribute(): string
    {
        return match($this->activity_type) {
            self::ACTIVITY_TYPE_WRITING => 'Writing',
            self::ACTIVITY_TYPE_RATING => 'Rating',
            self::ACTIVITY_TYPE_MEDIA => 'Media',
//            self::ACTIVITY_TYPE_PHYSICAL => 'Physical',
//            self::ACTIVITY_TYPE_GAME => 'Game',
            self::ACTIVITY_TYPE_QUIZ => 'Quiz',
            self::ACTIVITY_TYPE_VOCABULARY_TEST => 'Vocabulary Test',
            default => 'Unknown',
        };
    }

    /**
     * Get the localized activity type name.
     */
    public function getLocalizedActivityTypeNameAttribute(): string
    {
        return match($this->activity_type) {
            self::ACTIVITY_TYPE_WRITING => __('admin.activity_type_writing'),
            self::ACTIVITY_TYPE_RATING => __('admin.activity_type_rating'),
            self::ACTIVITY_TYPE_MEDIA => __('admin.activity_type_media'),
//            self::ACTIVITY_TYPE_PHYSICAL => __('admin.activity_type_physical'),
//            self::ACTIVITY_TYPE_GAME => __('admin.activity_type_game'),
            self::ACTIVITY_TYPE_QUIZ => __('admin.activity_type_quiz'),
            self::ACTIVITY_TYPE_VOCABULARY_TEST => __('admin.activity_type_vocabulary_test'),
            default => __('admin.activity_type_unknown'),
        };
    }

    /**
     * Get the display name for the activity.
     */
    public function getDisplayNameAttribute(): string
    {
        $status = $this->active ? '' : ' (Inactive)';
        $approval = $this->need_approval ? ' [Approval Required]' : '';
        return $this->name . $status . $approval;
    }

    /**
     * Get all activity type options for forms.
     */
    public static function getActivityTypeOptions(): array
    {
        return [
            self::ACTIVITY_TYPE_WRITING => __('admin.activity_type_writing'),
            self::ACTIVITY_TYPE_RATING => __('admin.activity_type_rating'),
            self::ACTIVITY_TYPE_MEDIA => __('admin.activity_type_media'),
//            self::ACTIVITY_TYPE_PHYSICAL => __('admin.activity_type_physical'),
//            self::ACTIVITY_TYPE_GAME => __('admin.activity_type_game'),
            self::ACTIVITY_TYPE_QUIZ => __('admin.activity_type_quiz'),
            self::ACTIVITY_TYPE_VOCABULARY_TEST => __('admin.activity_type_vocabulary_test'),
        ];
    }

    /**
     * Get all media type options for forms.
     */
    public static function getMediaTypeOptions(): array
    {
        return [
            self::MEDIA_TYPE_IMAGE => __('admin.media_type_image'),
            self::MEDIA_TYPE_AUDIO => __('admin.media_type_audio'),
        ];
    }

    /**
     * Get the media type name.
     */
    public function getMediaTypeNameAttribute(): string
    {
        return match($this->media_type) {
            self::MEDIA_TYPE_IMAGE => 'Image',
            self::MEDIA_TYPE_AUDIO => 'Audio',
            default => 'Unknown',
        };
    }

    /**
     * Check if this activity requires content validation.
     */
    public function requiresContentValidation(): bool
    {
        return in_array($this->activity_type, [
            self::ACTIVITY_TYPE_WRITING,
            self::ACTIVITY_TYPE_RATING,
            self::ACTIVITY_TYPE_MEDIA
        ]);
    }

    /**
     * Validate user activity content based on activity type.
     */
    public function validateUserContent($content, $rating = null, $mediaUrl = null): array
    {
        $errors = [];

        switch ($this->activity_type) {
            case self::ACTIVITY_TYPE_WRITING:
                if (empty($content)) {
                    $errors[] = __('admin.content_required_for_writing');
                } elseif ($this->min_word_count && str_word_count($content, 0, 'şŞçÇğĞıİöÖüÜ') < $this->min_word_count) {
                    $errors[] = __('admin.min_word_count', ['count' => $this->min_word_count]);
                }
                break;

            case self::ACTIVITY_TYPE_RATING:
                if (is_null($rating)) {
                    $errors[] = __('admin.rating_required');
                } elseif ($this->min_rating && $rating < $this->min_rating) {
                    $errors[] = __('admin.min_rating', ['min' => $this->min_rating]);
                } elseif ($this->max_rating && $rating > $this->max_rating) {
                    $errors[] = __('admin.max_rating', ['max' => $this->max_rating]);
                }
                break;

            case self::ACTIVITY_TYPE_MEDIA:
                if (empty($mediaUrl)) {
                    $errors[] = __('admin.media_url_required');
                } elseif (!filter_var($mediaUrl, FILTER_VALIDATE_URL)) {
                    $errors[] = __('admin.media_url_invalid');
                }
                break;
        }

        return $errors;
    }

    /**
     * Check if this activity is a test activity (quiz or vocabulary test).
     */
    public function isTestActivity(): bool
    {
        return in_array($this->activity_type, [
            self::ACTIVITY_TYPE_QUIZ,
            self::ACTIVITY_TYPE_VOCABULARY_TEST
        ]);
    }

    /**
     * Check if this activity is a quiz.
     */
    public function isQuiz(): bool
    {
        return $this->activity_type === self::ACTIVITY_TYPE_QUIZ;
    }

    /**
     * Check if this activity is a vocabulary test.
     */
    public function isVocabularyTest(): bool
    {
        return $this->activity_type === self::ACTIVITY_TYPE_VOCABULARY_TEST;
    }

    /**
     * Validate test activity configuration.
     */
    public function validateTestConfiguration(): array
    {
        $errors = [];

        if (!$this->isTestActivity()) {
            return $errors;
        }

        // Validate question count
        if (!$this->question_count || $this->question_count < 1 || $this->question_count > 10) {
            $errors[] = __('admin.question_count_range_error');
        }

        // Validate choices count
        if (!$this->choices_count || $this->choices_count < 1 || $this->choices_count > 6) {
            $errors[] = __('admin.choices_count_range_error');
        }

        // Validate min grade
        if (!is_null($this->min_grade) && ($this->min_grade < 0 || $this->min_grade > 100)) {
            $errors[] = __('admin.min_grade_range_error');
        }

        // Validate allowed tries
        if (!$this->allowed_tries || $this->allowed_tries < 1) {
            $errors[] = __('admin.allowed_tries_min_error');
        }

        return $errors;
    }

    /**
     * Get default values for test activities.
     */
    public static function getTestActivityDefaults(): array
    {
        return [
            'question_count' => 4,
            'choices_count' => 4,
            'min_grade' => 70,
            'required' => false,
            'allowed_tries' => 1,
        ];
    }

    /**
     * Scope to filter required activities.
     */
    public function scopeRequired($query, $required = true)
    {
        return $query->where('required', $required);
    }

    /**
     * Scope to filter test activities.
     */
    public function scopeTestActivities($query)
    {
        return $query->whereIn('activity_type', [
            self::ACTIVITY_TYPE_QUIZ,
            self::ACTIVITY_TYPE_VOCABULARY_TEST
        ]);
    }

    /**
     * Scope to resolve activity settings with class-specific overrides for a user.
     * Uses LEFT JOIN with class_activities table and COALESCE to merge values.
     */
/*    
    public function scopeResolvedForUser($query, \App\Models\User $user)
    {
        $defaultClass = $user->getDefaultClass();

        if (!$defaultClass) {
            // User has no class assignment, return activities as-is
            return $query;
        }

        return $query->leftJoin('class_activities', function ($join) use ($defaultClass) {
            $join->on('activities.id', '=', 'class_activities.activity_id')
                 ->where('class_activities.class_id', '=', $defaultClass->class_id);
        })
        ->select([
            'activities.id',
            'activities.category_id',
            'activities.activity_type',
            'activities.name',
            'activities.description',
            'activities.choices_count',
            'activities.min_rating',
            'activities.max_rating',
            'activities.media_url',
            // Use COALESCE to prefer ClassActivity values over Activity values
            DB::raw('COALESCE(class_activities.question_count, activities.question_count) as question_count'),
            DB::raw('COALESCE(class_activities.min_grade, activities.min_grade) as min_grade'),
            DB::raw('COALESCE(class_activities.allowed_tries, activities.allowed_tries) as allowed_tries'),
            DB::raw('COALESCE(class_activities.min_word_count, activities.min_word_count) as min_word_count'),
            DB::raw('COALESCE(class_activities.points, activities.points) as points'),
            DB::raw('COALESCE(class_activities.required, activities.required) as required'),
            DB::raw('COALESCE(class_activities.need_approval, activities.need_approval) as need_approval'),
            DB::raw('COALESCE(class_activities.active, activities.active) as active'),
        ]);
    }
        */
}
