// Okullar için severek kitap okuma uygulaması

# Özet

 Okullarda öğrencilere kitap okuma alışkanlığının kazandırılması ve kitap okuma takibinin yapılabilmesi için, oyunlaş<PERSON><PERSON>rma esaslı, web ve mobil uygulamaları üzerinden kullanılabilen bir okuma uygulaması.
 Sistemin kendisinin dijital içerik sağlaması gerekmiyor ancak böyle bir özellik eklenebilir. (Aksi halde e-kitap platformlarının sağladığı özellikler uygulamanın kullanımını azaltacaktır.)
 Kitap önermesi gerekmiyor ancak eklenebilir.
 Öğretmenlerin tamamen kendilerinin belirledikleri ya da sistemde önceden sunulan kitap listelerinin takibinin sağlanması ve oyunlaştırma ile eğlenceli şekilde motivasyonun sağlanması ana amaç. 
 <PERSON><PERSON><PERSON> odaklandığı kısım oyunlaştırma (fiziksel-dijital). Diğer dijital platformlardan ayrıldığı nokta burası.
 Asıl müşteri kitlesi okullar. Bireysel ve veliler için eğlenceli okuma takibi ikincil amaç.
 Fiyatlandırma öğrenci bazlı yıllık ödeme olarak okullara toplu teklif şeklinde. (Ya da içerik/kitap satışıyla ücretsiz bir platform)
 
 Daha üst seviyede, ilçe/il içi programlar düzenlenerek okullar arası yarışma ortamı sağlanabilir. Uygulamanın satışı anlamında farklı MEB seviyeleriyle anlaşmalar yapılabilir.
 Özel okullarda aynı gruba bağlı okullar arasında programlar düzenlenebilir.
 Satış ilişkileri anlamında farklı yaklaşımlar geliştirilmesi ya da yönlere gidilmesi gerekecektir.
 
 Teknopark içinde bir şirket kurulması, KOSGEB desteği alınması, AR-GE projeleri yapılması hem maliyetler hem de pazarlama açısından mantıklı kararlar.

---

# Pazarlama 

 [+] Alan adı önerileri
	[x] okuokuoku.com
		
	[x] okumaku.com
	
	[x] kitapu.com
	
	[x] kitapsevgim.com
	[x] kitapulkem.com / kitapulkesi.com
	
	[x]okumi.tr 
	
	[x] kitapsayar.com 
	
	[+] okumobil.com
	- Kitap taşıyan sevimli bir otomobil logosu
	- Uygulamanın girişinde animasyon
	
 [-] Patent çalışması yap.
 
 [-] Yapay zeka desteğiyle makaleler yaz. (blog ya da daha akademik)
 
 [+] Logo 
	 Design a playful and colorful logo for a children's book reading tracker mobile application called "okumobil" (Turkish for "read mobile," pronounced like "automobile"). 
	 A cute, 2D cartoon-style car made from or incorporating books (e.g., books as wheels, a book as the car body, or books stacked to form a vehicle)
	- Primary color scheme featuring various shades of violet/purple as the dominant color
	- Appeal to both children (ages 4-12) and their parents
	- Convey the concepts of reading, mobility, and fun learning
	- Be scalable and legible at various sizes (from app icons to marketing banners)
	- Work effectively on both light and dark backgrounds
	- Suitable for use across multiple marketing materials (flyers, posters, digital ads, app store listings)
	- Cartoon/illustration style rather than photorealistic
	- Friendly, approachable aesthetic
	- Clear visual connection between books and transportation/movement
	- Avoid overly complex details that might not render well at small sizes

 [+] Kullanım videosu 
	- Ekranlar
		- Login
		- Avatar seçimi 
		- Ana ekrana ekleme 
		- Kitap Ekleme
		- Ana sayfa 
		- Kitap okuma 
		- Kitabı tamamlama 
		- Ödül ekranları 
		- Kitaplarım 
		- Aktivite yapma 
		- Arkadaşlar 
		- Profil sayfası 
		- Avatar değiştirme 
		
	- Ekranlarda resim üzerinde bilgilendirmeler 
	- AI seslendirme?

---
	
	
# Rakip Araştırmaları	
	
 - Benzer siteler 
	- https://www.uppyforkids.com/
		Dijital kitap okuma uygulaması, kendi serileri ve interaktif içerikleri var.
	
	- https://okuyayplatformu.com
		AB fonu projesi, okumayı sevdirmek için etkinlikler yapıyor ve STK'ları destekliyor. Teknolojik bir çalışması yok.
		
	- kitapokuyorum.org
		Yurtdışında yaşayan öğrenciler için Türkçe ve İngilizce kitap okumalarını sağlamak amacıyla kurulmuş.
		Beanstack benzeri ama fazla oyunlaştırma yok. 
		https://www.youtube.com/watch?v=u13WUCSOIVE
	
	- https://okumaplatformu.com/
		Dijital kitap okuma uygulaması, kendi serileri ve interaktif içerikleri var. Okul ve veli takip ekranları var.
		
	- Okul yönetim sistemleri kitap okuma takibi modülleri 
		https://abc.net.tr/kitap-okuma-takibi/
		
	- https://okuvaryum.com.tr
		Dijital kitap okuma uygulaması, kendi serileri ve interaktif içerikleri var. Okul ve veli takip ekranları var.
	
	- https://gokidly.com/tr/education
		Dijital kitap okuma uygulaması, kendi serileri ve interaktif içerikleri var. Okul ve veli takip ekranları var.
		https://www.youtube.com/watch?v=s0pGZee8ygk
	
	- İngilizce siteler 
		https://www.beanstack.com (En yakın fikir)
		
		https://www.raz-kids.com/ (dijital içerik)
		
		https://www.renaissance.com/products/accelerated-reader/ Accelerated Reader
		30 yıllık okuma takip sistemi, amacı çocuğun okuyacağı kitabı sistemin verdiği listeden kendisinin seçip bunun motivasyonuyla daha çok kitap okuması. 
		Okumayı geliştirmek için üretilmiş ancak zamanla tekele dönüşmüş.	
		Quizler sınıfta çözülmesi gerekiyor. Evde istisnai durumlar hariç quiz istemiyorlar. 
		Eleştiriler: https://blog-penningtonpublishing-com.translate.goog/the-18-reasons-not-to-use-accelerated-reader/?_x_tr_sl=en&_x_tr_tl=tr&_x_tr_hl=tr&_x_tr_pto=tc		
		
		https://www.hmhco.com HMH 
		Eskiden Reading Counts adında Accelerated Reader benzeri bir uygulamaları varmış. Şimdi komple okul yayınlarına dönüşmüşler. 
		Bir okuldaki tüm dersler için basılı ve dijital içerik sağlıyorlar. 
		
		https://bookadventure.com/ Beanstack benzeri, önemli bir rakip
			https://booktaco.com Yeni hali ( bayağı ilerlemiş.)
			https://booknacho.com/ E-kitap, makale için aynı sistem
			https://vocabloco.com/ Kelime öğretmek için aynı sistem (eski hali https://vocabclass.com/)

		https://endopro.tr/ Fiziki ulusal etkinlik
		
		 READsquare https://www.readsquared.org/oursolution 
		 The Great Reading Adventure https://manual.greatreadingadventure.com/en/latest/introduction/overview/ 
		 Reader Zone https://www.readerzone.com 
		 Book Points https://bookpoints.org/ -> https://easysrp.com/
		 Wandoo Reader https://www.demcosoftware.com/products/learning/wandoo-reader/
		 Bibionasium https://www.biblionasium.com https://www.youtube.com/@BiblioNasium
		 LevelUp  https://levelupreader.net
		 BookRoo https://bookroo.com/classrooms
		 https://library.highlights.com
---

# Akademik çalışmalar 
	 
	- Kitap Okuma Etkinliklerine Yönelik Çevrimiçi Takip, Değerlendirme ve Tavsiye Sistemlerinin Karşılaştırılması 
	 https://dergipark.org.tr/tr/download/article-file/300532

	- Okuma Becerilerinin Okuma Ortamı Açısından Karşılaştırılması: Ekran mı kâğıt mı? 
	 https://dergipark.org.tr/en/download/article-file/32340
 
	- Bir Okuma Deneyimi Olarak Elektronik Çocuk Kitapları
	https://dergipark.org.tr/en/download/article-file/3328554

	- Basılı mı, Dijital mi?
	https://www.rulmakkagit.com/blog/basili-mi-dijital-mi-okuma-aliskanliklarimiz-uzerine-bilim-ne-diyor
	
	- A Comparison of Children’s Reading on Paper Versus Screen: A Meta-Analysis
	https://www.uis.no/en/meta-analysis-digital-books-may-harm-childrens-learning
	https://journals.sagepub.com/stoken/default+domain/PEGGPPJDJUFXRI6RSHHR/full

	- Which gamification elements to use for your future gamification project?
	https://mambo.io/blog/gamification-elements-and-mechanics	

	- Sınıf Öğretmenlerinin Öğrencilerine Okuma Alışkanlığını Kazandırma Stratejileri
	https://dergipark.org.tr/en/download/article-file/2076628
	
	Key Design Mistakes to Avoid in Gamification Features
	https://medium.com/@design_geek/key-design-mistakes-to-avoid-in-gamification-features-55d20405c23b
	
	https://tr.wikipedia.org/wiki/%C3%96%C4%9Frenmenin_oyunla%C5%9Ft%C4%B1r%C4%B1lmas%C4%B1
	
	https://egitimpedia.com/ogretmen/bir-ogretmen-anlatiyor-ogrencilerime-kitap-okumayi-nasil-sevdiriyorum/
	
	Grup Çemberi tekniği
	https://dergipark.org.tr/tr/download/article-file/1668
	https://www.anadiliegitimi.com/tr/download/article-file/515198

	Okuma aktiviteleri 
	https://www.thethinkerbuilder.com/2017/10/the-tower-of-books-challenge-reading.html
	https://www.thethinkerbuilder.com/2021/01/what-do-your-students-do-while-reading.html
	
	Reading Activity Strategies
	https://www.readingquest.org/a-z-strategies.html
	
	Page Count Goal
	https://growingwisertogether.com/blog-post-3/
	
	Reading Logs are Bad
	https://pernillesripp.com/?s=reading+log
	https://pernillesripp.com/2018/11/03/lets-talk-about-reading-logs-again/
	
	Hexad Framework - six key user types—Players, Achievers, Socialisers, Free Spirits, Philanthropists, and Disruptors
    https://www.gamified.uk/2024/12/02/creating-a-balanced-system-for-all-user-hexad-types-an-evolving-perspective/	

    https://barisozcan.com/kitap-okumak-beynimizi-nasil-etkiliyor/
	
	
	https://www.trthaber.com/haber/cocuk/kitap-okumayan-bir-kusak-mi-yetisiyor-921855.html
	
---

# Genel


 [+] Sistem oyunlaştırma üzerine çalışacak. Rozetler, sürpriz başarılar, puanlar, seviyeler, leaderboard vb. olacak.
	- Temel birim puan. Puan kazandıran aktiviteler var. (Kitap bitirmek, x sayfa okumak, x saat/dakika/gün okumak)		
	
	[+] Herkesin bir avatarı olacak. 
		[-] item mantığında çalışılabilir mi? Okudukça rozet yerine itemler geliyor ve onları giyebiliyor.
			[-] Giyilebilir özellikler ve collectible itemler farklı olabilir.
			[-] Karakter listesi standart olmalı. Kazanılan itemler üzerine takılabilmesi için.
			[-] Erkek/kız ayrı karakterler olmalı. Her karaktere uygun itemler olabilir.
		[+] Belirli bir süre kitap okumadığında karakter üzülebilir, canı sıkılabilir ya da okuduğu kısımlardan bir şey hatırlatabilir.
		[x] Grafikleri game asset satan yerlerden almak daha mantıklı. (Google AI Studio ile yaptık)
			https://admurin.itch.io/mega-admurins-items
			https://danielthomasart.itch.io/2d-customizable-characters (unity :( )
			https://shubibubi.itch.io/cozy-people
			https://visustella.itch.io/stella-character-generator
			https://craftpix.net/
			https://liberatedpixelcup.github.io/Universal-LPC-Spritesheet-Character-Generator
			https://www.gamedeveloperstudio.com/search.php?searchterm=pack
			https://itch.io/game-assets/free/tag-pixel-art/tag-user-interface
			https://pixel-vault.itch.io/pixel-book-ui-v1
			https://kenney.nl/assets
			https://assetstore.unity.com/publishers/5232
			https://assetstore.unity.com/packages/2d/gui/gui-fantasy-2d-ui-fantasy-pack-87689#description
			https://opengameart.org/content/game-ui-pack
			https://opengameart.org/content/jungle-cartoon-gui
			https://www.titanui.com/?s=game&ct_post_type=post%3Apage

		[+] Karakter puan üzerinden çalışıyor. Programla direkt ilgisi yok. 
		
		[+] Rozetler belirli durumlarda tetikleniyor. Puana göre, görevi tamamladığında vb.

	[-] Her programın bir hikayesi olması güzel olur. 
		[-] Hikaye bir map, tamamlanacak bir yapı ya da gelişen bir varlık içerebilir. (Büyüyen kitap ağacı, kitaplardan yapılan bina vb.) Amaç çocuğun sanal dünyasını inşa etmek.
		[-] Kullanıcı bazlı hikayeler olabilir mi? Her öğrenci istediği hikayeyi seçiyor. Bir şeyler savaşçısı, çiçek bahçesi, lego koleksiyoncusu, aşçı, dedektif, bilgisayar uzmanı, uzay kaşifi, dinozor bilimcisi
			[-] Her hikayenin bir karakter tasarımı olmalı. Bir gün karakterler gerçek tasarımlara dönüşebilir.
			
	[+]	Oyun mekaniği
		[+] Temel işlem görev tamamlama. Tek, tekrarlanan, isteğe bağlı ekstra, öğretmen tarafından anlık.
		[+] Temel birim puan. Her aktivite puan kazandırıyor. 
		[+] Belirli bir puan toplandığında, bir eşya/toplanabilir bir şey açılıyor.
		[+] Belirli kurallar sağlandığında, rozet dengi bir şey kazanılıyor. Her hikayenin kendi rozet yapısı olmalı.
		[-] Kitaplara özel karakterler, hikayeler, kazanılan şeyler ve rozetler olabilir. Kitap bazlı bir program olması lazım bu durumda.
		[+] Herkesin bir envanteri olur ve kazandıkları orada görünür. Rozetler ayrıca listelenir.
		[+] Puana bağlı ya da bir sonuca bağlı ödüller olabilir. Ödüller fiziki olarak tanımlanabilir.
		[+] Bir kurala bağlı olarak seviyeler tanımlanabilir. Her şu kadar puanı kazanınca, her x adet kitabı bitirince
		[+] Günlük görevi tamamlayan için ekstra görev talebi olmalı. Belirli görev türleri: Daha fazla okuma, özet yazma, 10 adet sıfat bulma, öğretmenin imtihan etmesi challenge vb. 
			Ekstra görevlerde onay mekanizması olması gerekecek. Öğretmen/Veli?
	

 [-] Sistemde hazır oyunlaştırma şablon ve paketleri olmalı. Direkt seçerek kolay uygulama yapılabilir. 
	- İhtiyaç olan görsel ve malzemeleri paketler halinde sağlamak lazım. 
	- Hazır şablonların belirli karakter/avatarları olur. Kazanılan item ve eşyalar vb. karaktere özel olabilir. Erkek/kız ayrımı ilgiyi arttırmak için işe yarayabilir.
	- Kitaplar da hazır şablonun içinde yer alabilir. Yayınevi işbirlikleri ve ücretli paket satışlarıyla komple sistem olarak okulda çalışılabilir. 


 [-] Fiziksel materyaller olabildiğince çok desteklenmeli.
	- Yayınevleriyle birlikte çalışılarak kitaplardaki karakterlerin karton halleri, 3 boyutlu halleri, kuklaları hazırlatılabilir.
	- Kitapla birlikte gelen bir bulmaca/yapboz vb. etkinlik çözülerek çıkan kelime sisteme girildiğinde puan kazanılabilir.
	

 [+] Sistemin kullanıcıları: Admin, Grup yöneticileri, Okul yöneticileri, Öğretmenler, Veliler

 [+] Web Yönetim Ekranı
	[+] Sistemin web üzerinden yönetimi yapılabilecek.
	[+] Teknoloji seçimi
		[] Laravel - php
		https://github.com/topics/laravel-admin-panel
		https://madewithlaravel.com/boilerplate
		https://github.com/siubie/kaido-kit
		https://github.com/thedevdojo/wave
		https://filamentphp.com
			https://github.com/andrewdwallo/erpsaas
			
	
 [+] Mobil Uygulama 
	- Kullanım çoğunlukla mobil uygulama üzerinden olacak. 
	[+] Teknoloji seçimi
		[x] Flutter - Flutter Flow
		[+] HTML5-Hybrid App - PWA
			
			https://whatpwacando.today/
			https://www.pwabuilder.com/
					
			
		[x] Öğrenci uygulaması için game engine?
			- Uygulamaya hikaye eklerken html parçaları kullanacağız, nasıl olacak?
			ChatGPT phaser.js öneriyor. Hikaye için tilesetler kullanıp map oluşturabilirsin diyor.
			HTML parçaları yerine resim ve video yüklemeyi desteklemek gerekebilir.
			https://blog.logrocket.com/best-javascript-html5-game-engines-2025/
			https://github.com/blurymind/tilemap-editor


 [+] Sistemin iki temel unsuru: kitap ve öğrenci 
	[+] Aynı kitabı okuyan kişiler arasında bir ilişki kurulabilmesi için kitap sisteme kayıtlı olmalı. 
		Kitaplar farklı yayınevlerinden farklı sayfalarda olabiliyor. Ya hepsine destek verilmeli ya da belirli yayınevlerinin içerikleriyle sınırlanmalı. Ama bu durumda herkese hitap edilemez.
		Okullara satılan yayınlarla birlikte çalışılarak kitaplar önceden sisteme tanımlanabilir.
		
	[x] Kitaplar bir setin parçası olabiliyor ancak tüm setin tek ISBN numarası oluyor. Bir set seçildiğinde, setteki her kitap ayrı ayrı kütüphaneye eklenmeli.
		Elle kitap girişinde ait olduğu set ayrıca girilebilir?
		[+] Setteki her kitabın ayrı ISBN numarası var. Ama set toplu olarak seçilebilmeli.
	
	[+] MEB'de sınıf kütüphanesi oluşturma kavramı var. Sistem kütüphane üzerinden gidecekse öncelikle onu oluşturma seçeneği olabilir.
		MEB de çok kullanılan kitaplar
		https://www.sorubak.com/blog/e-okul-kitap-listesi-ve-sayfa-sayilari-5-6-7-8-sinif-lise.html
	
	[+] Kitap ISBN sorgulama 
		https://www.googleapis.com/books/v1/volumes?q=isbn:8680628431466
		https://isbnsearch.org/
		https://isbndb.com/book/9789752452718
		https://openlibrary.org/search.json?isbn=9789752452718&fields=*&limit=1
		bunlar Türkçe karakterleri kaldırıyor. Alttaki kütüphane kullanılarak Türkçeye çevirme denenebilir.
			https://github.com/yusufusta/DeAsciifier		
	
		
		https://www.dr.com.tr/search?q=9786050322033
		https://www.nadirkitap.com/kitapara.php?ara=aramayap&isbn=9786255978189
		- En güzeli üstteki kaynakları tek tek tarayıp bilgileri bulan ara bir servis yazmak. Yapay zeka yeterli değil.
		https://www.siyasalkitap.com/search?p=Products&q=9789754034929
		https://www.fidankitap.com/search?p=Products&q=9789754034929
		https://www.kitabinabak.com/sorgu/Kitap?bak=9789754034929

	
	
	[-] Sistemde olmayan kitaplar eklendiğinde öneri olarak yapay zeka tarafından soruları hazırlatılabilir.
	
	
	[+] Öğrenciler bir takımın parçası olurlar. Sınıf yerine takım var. Öğretmen isterse takıma sınıfın adını verebilir. Bir sınıfta birden çok takımla rekabet de oluşturulabilir.
	
	[-] Sınıf öğrenci listesinin Excel olarak içeri aktarımı sağlanabilir. (E-okul formatı?)

	

 [+] Okuma kaydı
	- Sistemde okuma kaydı öğretmen ya da veli tarafından yapılır.  
		- Öğrenci tarafından da velinin telefonundan yapılabilir. 
		- Her halükarda okuma kaydı bilgisi öğretmene bildirilir.
		
	- Bir kitabın bitmesi için öncelikle kitapla ilgili sorular sorulmalı
		- Sorular öğretmen/veli ekranına gelir, soran kişi öğrencinin okuduğuna kanaat getirdiyse giriş yapar. 
		- Öğrenci tarafından yapılabilecekse, sorular/test öğrenciye gösterilir. Bu durumda bir onay mekanizması olmalı mı? 
		
	- Sistemin neye göre işleyeceği seçeneklere bağlı olabilir. Sisteme kayıt bu seçeneğe göre yapılır.
		- Belirli bir süre arka arkaya kitap okuduğunda  (ör. 5 gün boyunca. takvim üzerinde gösterim ?)
		- Her kitap bitirdiğinde 
		- Sayfa okuduğunda 
			- sona yaklaştığında "az kaldı" motivasyonu uygula
			- en az sayfa sayısı sınırı olmalı. 10 sayfa okumadan kayıt yapamaz gibi.
			- Süre sınırı da olabilir. En erken 1 saatte bir sayfa kaydı yapabilir gibi.

 [+] Uygulama görev tabanlı olabilir. 
	- Görev mantığı küçük sınıflar için önemli, hoca şu sayfaları oku diyor. 
	- Görevlerin puanı olur. 
	- Görevlerin başlangıç-bitiş tarihleri olur. Saat hep 00.00 üzerinden.
	- Görev tamamlandığında öğretmene bildirim gidebilir.
	- Tekrarlanan görevler olabilir. Her gün 5 sayfa oku. Her bir görev puan kazandırır, yapılmazsa süresi biter, puan vermez.
	- Görev olarak soru sorulabilir, bitiş tarihine kadar.
	- Görev olarak farklı fiziki etkinlikler verilebilir. Kütüphaneye üye ol. 
	- Görevin tamamlanması için gereken işlemde gösterilen metin değiştirililebilir. (Varsayılan: Kaç sayfa okudun?)
	- Günlük görev tamamlandığı halde, daha fazla kitap okumak isteyen çocuğun önüne ne çıkarılacak? Okuma kaydı görevden bağımsız mı olmalı?
		- Ekstra görev talebi
	- Öğretmenin işini kolaylaştırmak için hazır görev önerileri iyi olabilir.
	

 [+] Kısa süreli challenge/etkinlik/yarışma düzenleme. (Belirli kitapları bitirme, bir kitaba en iyi yorumu yazma, belirli temalar, dönemler için etkinlik düzenleme)
 
 [-] Sistem içinde mini oyunlar olabilir. Ya da  fiziki mini oyun/kod bulma/hazine avı gibi etkinlikler için bulunan kodun girildiği bir ekran olabilir.
	- Kitap bittiğinde resmini yapma
	- fiziki etkinlikler daha çok puan kazandırır.

 [+] Meydan okuma talebi. Kitap bittiğinde öğrenci öğretmeninden kendisini imtihan etmesini/soru sormasını talep eder. Öğretmen sorar, bilirse iyi bir puan kazanır.

 [-] Sınıfta birlikte okuma yaklaşımına destek seçenekleri sağlanabilir. 
	- Sitede blog bölümünde uygulamayla birlikte öğretmenlerin düzenleyebbilecği aktivitelere dair yazılar yayınla. Birlikte okuyun, kitap çemberi kurun, kulüp faaliyeti yapın vb.
 
 [-] Aynı kitabı okuyanlar arasında okuma/tartışma kulübü tarzı bir iletişim oluşturulabilir. Burası biraz sosyal medya ortamı gibi, içeriği düşünülmeli.

 [-] Uygulamada gezi/buluşma vb. fiziki aktiviteler tanımlanıp duyurulabilir. Bu etkinliklere katılım bir puan ya da rozet vb. kazandırabilir. Katılanlar bir ekrandan girilebilir, qr kod okutulabilir vb.
	 
 [-] Rastgele ödüller olabilir, direkt puan verebilir, ufak bir bulmacanın karşılığında puan verebilir. (kilit açma)
	- Bir kitabı bitirdiğinde, en hızlı okuyan kişi, sorulara en doğru cevap veren kişi gibi ödüller gelebilir.
 
 [-] Belirli günlere özel ödüller olabilir. Doğum günü, bayram vb. 


 [+] Ödül önerileri 
	- Accelerated Reader için yazılmış öneriler:
		Yerel bir parka bir gezi
		Yerel şişme bir yere bir gezi
		Patlamış mısır, meşrubat ve film partisi
		Dondurma partisi 
		Pizza partisi
		Dışarıda baloncuklar ve kaldırım tebeşiriyle ekstra oyun zamanı
		Spor salonunda çoraptan zıplama
		Kütüphanede özel öğle yemeği
		Müdürle kahvaltı
	- Hediye çeki, indirim vb. işyerleriyle yapılan anlaşmalar olabilir. Okul/sınıf bazında da düşünülebilir, sistemin hediyeleri de olabilir. (Sistem hediyeleri uygulamanın kullanım sıklığına bağlı olabilir?)
 
 [x] Belirli kısıtlar olabilir, kitabı 7 gün içinde bitirmezse arada aldığı puanların da gitmesi gibi... Ya da, zaman kısıtlı bir ön puan verilebilir.100 puanla başlamak gibi.
 
 [-] Kazanılan puanlarla karaktere yeni özellikler satın alınabilir?
  
 [-] Birinci vb. seçmek yerine belirli seviyeler olabilir, o seviyeye girmek için çalışılabilir. (Altın madalyalılar, master okuyucular, belirli konu/yayın guruları, tarih profesörleri)

 [-] Televizyonlarda/akıllı tahtalarda göstermek üzere pano ekranları olmalı. Okullardaki belli seviyedekiler, bir challenge liderleri vb. okul bazında da filtrelenebilmeli.
	- Sistemin en önemli bileşenlerinden biri. Öğrenciler okulda programla ilgili en güncel bilgiyi akıllı tahtadaki ekrandan alırlar.

 [-] Sistemde her öğrenci için bir kimlik/katılımcı kartı oluşturulup yazdırılabilir. Kart öğrencinin avatarını, o sırada katıldığı yarışmanın bilgisini vb. içerebilir.
 
 [-] Sistemde belirli durumlarda alınan başarı belgesi ve sertifikalar yazdırılabilir olmalıdır.

 [-] Öğretmenler kendi tanımladıkları programı (etkinlikleri, ödülleri vb. dahil, kişiler hariç) paketleyebilir ve başkalarının kullanımına sunabilir. Çamlıca okuma paketi vb.
     Bunun için bir market kavramı oluşturmak gerekir. Ücreti karşılığında marketten satış yapılabilir. Bu durumda gelir paylaşımının, finansal konuların ve altyapıların vb. yönetilmesi gerekir.
	- !! Kullanıcıdan alınan her içeriğin bir moderasyondan geçmesi gerekir.
	 
 [-] Sistem direkt kitap setleriyle ve bunlarla oluşturulmuş paketlerle gelebilir. Öğretmenler yayınevlerinden kitap alıyor. Yayınevleriyle anlaşılıp setleri sisteme ve ücrete dahil edilebilir.
	 Sistem ücretsiz olup paket satın alınarak da ilerleyebilir.  
 
 [-] Okul içi sınıflar arası, ilçe okullar arası vb. yarışma organizasyonları
	- Kitapları yarışmayı düzenleyen belirler
	- Yarışmaya öğretmen tüm sınıfını ya da isteyen öğrencileri ekleyebilir, belki öğrenci kendisi de katılabilir.
	- Kitap sonu soruları yine sorulabilir ancak genel ayrı bir sınav da yapılabilir.
	- Genel bir sınav için soruları yarışmayı düzenleyen hazırlar, istediği zaman yayına alır.
		- Sorulara bir sınava dahil olma özelliği eklemek lazım.
	- Bu tür etkinlikler için sistemde haber/duyuru gibi bir bölüme ihtiyaç var.
	
 [-] Sistemde içerik girilebilen, resim yüklenebilen bir duyuru/haber bölümü olmalı. (Yetkilendirmeler ?)
 
 [-] Hazır program şablonları
	- 1. sınıf okuma programı 
	- En çok kitap okuyan kazanır.
	- Her ay bir kitap. Kitaplar eklenir, her ay için başlangıç bitiş tarihli bir görev tanımlanır. Görev ayın kitabıyla ilişkilendirilir. Görevin/kitabın sonunda sorular sorulur?
	- Birlikte kitap okuyoruz. Bir kitap eklenir, sınıfta/evde herkes kitaptan belirli bir bölüm okur. Görev olarak tanımlanır. Öğretmen toplu olarak tamamlandı yapabilir.
	- Kitabını oku, hayatta kal. Başta tüm öğrencilere 100 puan verilir. Öğrenciye kitap atanır, bir görev tanımlanır. Görev zamanında yapılmadığında puanı düşer.
	- Çanakkale temalı kitap okuma programı
	- Çamlıca kitapları 
	- Kitap kumbarası. Sisteme kitap eklenmez, görev tanımlanır. Günlük görevlerde kumbaraya kaç lira attığı sorulur. Program bittiğinde kumbarayla söylediği miktar karşılaştırılır.
	- Ailemle okuyorum. Sisteme kitap eklenebilir ya da eklenmeyebilir, görev tanımlanır. "Toplam okuduğu sayfa sayısı" sorulur.
	- Kitabımı canlandırıyorum. Herkese kitap tanımlanır. Görev olarak kitabı fiziki olarak bir resime dönüştürme, materyallerle canlandırma, dijital videosunu yapma vb. hedefi verilir.
	- Kitap ağacı. Herkesin bir dijital/fiziki ağacı olur, okuduğu kitabı getirir dalına asar. 
	- Kitap ağacımız. Sınıfa bir kitap ağacı kurulur. Herkes okuduğu kitabı getirir, hediye eder. Onun adıyla ağaca asılır. En çok kitabı getiren kazanabilir? Kitaplar bir yere hediye edilebilir.
	• Read a book based on a true story.
	• Watch a movie based on a book.
	• Read outside.
	• Read a book out loud. You could read to a sibling, parent, grandparent, or pet.
	• Create a reading nook: Be creative! Closets, corners, and trees all make for great nooks.
	
    https://candidcover.net/2025-reading-challenges-list/	
	Örnek çalışmalar: http://sefaatli.gov.tr/kurumlar/sefaatli.gov.tr/%27Bana%20da%20Zaman%20Ay%C4%B1r%27%20E%C4%9Fitim%20Projesipdf.pdf	

 [+] Oyunlaştırma özeti
	Sistemde oyunlaştırma iki düzlemde gerçekleşir:
	1- Dış motivasyon kaynakları: 
		- Puanlar
			Her işlem puan kazandırır. 
		- Rozetler 
			Belirli puanlara ulaşmak yeni rozetler kazandırır.
			Farklı sebeplerle elde edilen başarılar yeni rozetler kazandırır. (Düzenli okumak, belirli kitapları okumak vb.)  
		- Liderlik tabloları
			Tüm sınıfta liderlik
			Takım içinde liderlik
			Takım olarak liderlik

	2- İç motivasyon kaynakları: 
		- Karakter geliştirme (Kendini gerçekleştirme, duygusal motivasyon) 
			Bazı kazanımlar karakterin görünüşünü ya da gücünü değiştirebilir.
			Tamamlanmayan görevler karakterin üzülmesine ya da güç kaybetmesine sebep olabilir.
		- Bitirdiği kitaplar ( sahip olma)
			Belirlenen kitap listesinden tamamlananlar ve o kitaplardan elde edilen başarılar
		- Seviyeler ve hediyelerin kilidini açma (başarı hissi)
			Program içinde ya da uygulanan hikayede seviyeler tanımlanabilir ve belirli puanlara ulaştıkça yeni seviyelere geçilebilir.
			Görev tamamlama ya da belirli kitapları okuma farklı hediyelerin kilidini açabilir. 
		- Takım puanları ve başarıları (sosyal aidiyet) 
			Takım bazında toplu puan gösterimi
			Takımlara puan kazandıran aktivite, görev ve challengeler
		- Hedefler (başarıya ulaşma)
			Kişiye, takıma ya da tüm sınıfa hedef atanabilir. Hedefe ulaşma motivasyonu ve teşviki sağlanabilir.
			Hedefler kişiye özel olduğundan, kişisel farklılık ve okuma hızlarını destekler.			
		- Anlık görev ve challengeler (zorlukları yenme)
			Kitaptan istenen bilgileri bulup gönderme
			Kitapta okunan yerle ilgili anlık sorular
			Sesli okuma kaydı gönderme 
			Fiziksel görevler (Sınıfta saklanan bir şeyi bulma-hazine avı, halk kütüphanesine üye olma, yazar buluşmalarına katılma, kütüphane/fuar ziyareti, kulüp çalışmaları vb.)
			Öğrenciden gelen challenge talepleri (öğretmenden soru sormasını isteme vb.)
		- Puanla satın alınabilir oyun içi nesneler (kazandığını harcama yetkisi)
			Belirli puan karşılığı alınabilir eşya ve tanımlı nesneler (puanları düşürmeli mi?)
		- Düzenli okuma kazanımları (istikrar)
			streak seviyelerine göre tetiklenen ekstra puanlar kazanma
			streak göstergesi
		- Bir hikayenin parçası olma (kahraman olma)
			Hikayeler bir program şablonu gibi çalışabilir. Hikayenin kendi karakterleri, seviyeleri, kitapları, başarı ve kazanımları, kuralları olabilir
			Hikaye bir harita içerebilir, haritanın üzerine kazanılan eşya vb. yerleştirilebilir.
			Okuma programı bir hikayeye bağlıysa işleyiş hikaye üzerinden yürüyebilir.
		- Teşvik edici sistem mesajları (Başarıya yakınlık)
			Kitabın bitmesine yakın "az kaldı" mesajları, streak için teşvik,
			Günlük motivasyon mesajları
		- Fiziki ödüller (Gerçek hayat kazanımları)
			Hediye çekleri, yemek ödülleri, toplu parti, eğlence vb.
		- Sosyal motivasyon (Değer görme)
			Arkadaşlar ve okuma bildirimleri 
			Beğendiği kitapları yayınlama 
			
		

---
 

# İş/Satış/Gelir/Finans

 - Fiyatlandırma öğrenci bazlı yıllık ödeme olarak okullara toplu teklif şeklinde.
	(Sınıf bazlı olması gerekebilir. Öğretmenler sınıfından para toplayıp sisteme ödeme yapması daha çok tercih ediliyor.)
 - İlk yıl indirimi düşünülebilir.
 - Öğretmenlere ücretsiz (Sınırlı sayıda olabilir)
 - Öğrenci mevcudu belirli bir sayının altındaki okullara ücretsiz olabilir.
 - Köy okullarına ücretsiz olabilir.
 - Freemium mantığında çalışabilir. Belirli özellikler ücretsiz, tam kullanım için ücret ödenir.
 - Okullara ücretsiz ve süreli deneme hesabı açılabilir.
 - Sistem ücretsiz olup, yayınevleriyle anlaşılarak fiziki setler bir paket haline getirilip sistemden satın alınabilir.
 - Kitap satınalma ayrı bir özellik olabilir, kitap alınmasa bile sisteme kitaplarla ilgili özel rozetler, sorular paket olarak tanımlanabilir. 
   Öğretmen bir kitabı listeden seçtiğinde, o kitaba ait paket öğretmenin programına otomatik eklenir.
   Kitabı sistemten satın aldıysa, hem kitap hem de ilgili içerik paketi programa eklenir.
   Kitap içerik paketi ayrı bir fiyatla da satılabilir.
 - Öğretmenlere demo hesabı oluşturulması gerekebilir. Ya da bir deneme süresi tanımlanabilir.
   Deneme süresi içinde tüm içerikler ücretsiz kullanılabilir. Süre sonunda devam edilmediyse hesap silinebilir ya da devam edilecekse eklenen içerikler fatura edilebilir.
   Eğer içerikler ücretsiz olacaksa, sabit üyelik ücreti olması gerekir.

---   
   

Betül hoca randevu
Nurettin hoca randevu
Okul bitmeden uygulamayı duyurmalı


# Yetkiler
Grup yöneticileri, Okul yöneticileri, Öğretmenler, Veliler, Öğrenciler

 - Grup yöneticileri
	- Grup geneli raporları alabilir. 
	- Yeni challenge başlatabilir.
	- Rozet ve ödülleri belirleyebilir.
	
 - Okul yöneticileri
	- Okul geneli raporları alabilir. 
	- Yeni challenge başlatabilir.
	- Rozet ve ödülleri belirleyebilir.


 - Öğretmenler
	- Kitap listesini oluşturabilir
	- Yeni challenge başlatabilir.
	- Rozet ve ödülleri belirleyebilir.	
	- Öğrenci kaydı yapabilir
	- Kendi öğrencilerinin raporlarını alabilir
	- Öğrencilerin ilerlemesini girebilir.

 - Veliler 
	- Ayrı giriş yok. Öğrenci girişi kullanacak. Birden çok öğrencisi varsa hepsi için ayrı ayrı girecek.

---

# Akışlar
Grup yöneticileri, Okul yöneticileri, Öğretmenler, Veliler, Öğrenciler

İşlemleri sihirbazlar üzerinden yürüt.

 [-] Yönetim Paneli
	[-] Sistem Yöneticisi 
		[-] Kurumlar bölümünden yeni bir kurum ekler	
		[-] Kullanıcılar bölümünden bir Okul yöneticisi ekler.
		

	[-] Grup yöneticileri	
	[-] Okul yöneticileri

	[-] Öğretmenler	

 [-] Veliler 
	[+] Öğrenci girişinde daha önce girmiş öğrencileri ekranda liste olarak tut. Veli için birden çok öğrenci girişini kolaylaştır.
---

 
# Tanımlar

 - Tüm kayıtlarda sabit 
 
 [-] Genel Ayarlar

 [x] Dönemler (Eğitim yılı)
 [x] Etiketler (Serbest tanımlanır, yetkilere göre, kurum etiketleri sadece sistem yöneticilerinde olabilir. ) 
 [+] Kurum/Birim (Hiyerarşik olarak eklenebilir. )		
 [+] Kullanıcılar
 [+] Roller
 [+] Yetkiler
 [-] Kullanıcı Tercihleri
 [+] Karakterler 
 [x] Oyuncu
 [-] Itemler 
 [+] Rozetler  
 [+] Kitap 
	[+] Kitap yorumu 
 [-] Okuma Programı
	- Şablon mu? 
	[-] Hikaye 
		[-] Hikaye bölümleri 		
		[-] Oyuncu hikaye durumu 
	[-] Takım	
		[-] Takım oyuncuları
	
	[-] Programa dahil kitaplar

	[-] Okuma kaydı	
	
	[-] Sınav  		
		[-] Sınav soruları 
		[-] Katılımcılar 
		[-] Cevap kayıtları 
   
 [+] Program Rozetleri	
	
 [+] Görevler 
	
 [+] Seviyeler 
	
 [+] Sorular 		
	
 [+] Etkinlik (kod girme vb.?)
		- Türü (kod, bulmaca, fiziki)??			
	
---

 [+] Adalet için kitaplara puan ver sayfa  ya da resim yoğunluğu, yazı yoğunluğu direkt kitap türü de seçilebilir. Büyük resimli kitap, full yazılı kitap
	Kitap türleri:
	 - Çok resimli az yazılı
	 - Resim ve yazı dengeli
	 - Çok yazılı az resimli
	 - Tamamen yazılı büyük puntolu
	 - Tamamen yazılı küçük puntolu 
 
 sınıf seviyesine göre puanı değişebilir
okuma güçlüğü olan öğrenci için de aynı puan mı olacak?
 
Kitabı öğrenci nasıl ekleyecek
Aynı sorular ona da sorulabilir sonra kitap onaya gider kitabın kısa videosu ya da birkaç resim?
zxing js ile barkod okut. 


Programdan bağımsız olarak çocuğun puanı gücü artar
Sayfa gücü, kitap puanı 
Arka arkaya okuma rozetleri sayısı
Yorum puanı
Etkinlik puanı
Full doğru çözme rozetleri

Karakter sadece avatar
Yeni yarışmalar çocuğun puanını attırır
Öğretmenlere de puan ver
10 soru ekleyince soru rozeti
Soruların doğrulttuğu ölçülecek mi
Sadece yapay zeka onayı?
Kaç kitap eklemiş
Başka öğretmenler soruları puanlar
Her şey oyun herkes oyunda
Sonraki sene hayat nasıl başlayacak, sıfırdan mı
Hikayeler nerede, isteyen hikaye mi oluşturacak kendine
Türkiye haritası hikayesine göre kurgu
Ya da bağımsız olarak bir hikaye devreye girecek hikaye başladı okuduğun her kitap hikayede şunu yapacak. Hikaye değil yarışma
Yarışmanın hikayeleri olabilir hava katmak için 

Yönetim paneli yerine daha oyuna yönelik ekranlar doğrultulu ağırlıklı oluyor bu durumda

Görevler ne olacak?
Öğretmenin ekstra katkısı?

Yabancı dildeki kitaplar için destek. Orada seviyeler farklı. Türkçe iyi okuyabilir ama İngilizce kötüdür







Sanal kitap evreni/dünyası/ülkesi
Kitap okuma oyunu
Açılışta kendine bir takma ad ve bir karakter seç (yeni karakterler zamanla açılabiliyor) (yetişkinlere uygun karakterler de lazım.)
İlk sayfalarını oku yaşamaya başla.
Sürekli artan sayfa puan altınları, sayfa sayısı da yanında 
Bitirdiği kitap puanı
Dikkatli kitap okuma gücü (Hatasız çözdüğü kitap soruları)
İstikrarlı kitap okuma gücü (en yüksek streak günü)
Cesaret gücü (Öğretmenden soru sormasını isteyip başardığı puanlar)
Aktivite puanı (Kitap sonu aktiviteleri)
Kazandığı rozetler 
Harita seviyeleri (Açılan yeni bölge sayısı)
Kazandığı eşyalar (kilidini açtığı)
Kitap okuma yaşı (x dakika)
Kitap Kulesi (Okuduğu kitapların sayfa sayılarından oluşan kitap kulesi)
Sosyallikle ilgili puanlar 

Arkadaşlar 
 - Takımlarım (dahil olduğu takımlar)
 - Arkadaşlarım (sınıf arkadaşları)
 - Ülkedaşlar (Aynı uygulamayı kullanan diğer arkadaşları)

Mesajlar 
 - Ürün içi bildirim ve öğretmen mesajları sistemi 
 - Mesajlar görev bildirimi olabilir. Görev linki içerebilir.
 - Ürün bildirimleri teşvik edici ama anonim olabilir. (Bugün sınıfından 4 kişi kitap okudu. Hadi sen de..., Okumayan bir sen kaldın, Bugün 2 kişi x rozeti kazandı. )
	- Günde bir kez olmalı ?
	
	
 
 
 
Sosyal
 - Bir arkadaş mesaj (friend feed) sistemi uygula. Bir öğrenci arkadaşını takip ettiğinde, hangi tür bildirimlerin iletileceğini sor.
	- Beni şu konularda bilgilendir:
		Arkadaş kitap okuma kaydı yaptığında
		Arkadaş bir kitabı bitirdiğinde
		Arkadaş bir rozet kazandığında
		Arkadaş cesaret puanı kazandığında
	- takip türleri ve kullanıcı takip tablosu tut.
	- takibi bıraktığında tüm takip veritabanı kayıtlarını sil.
	- sadece bir feedi takipten kaldırdığında ilgili tip kaydını sil. 
	- Tüm ilgili aktiviteler kullanıcının feed tablosuna kayıt eklesin. Diğer kullanıcılara gidecek mesaj da ilgili tabloda yazsın.
	- Kullanıcı kendi feedinde reaksiyonları sayı olarak görsün. Başkalarının feedinde sadece yapılan reaksiyon görünsün/kaldırılabilsin.
	- Mesajlar tablosu şimdilik sabit metin olarak kaydetsin. Bir aksiyon içermesin.
	- Beğenme emojileri 
	- Kimse beğenmezse psikolojik etki?
	- Tersten bakarak yapılabilir, isteyen paylaşır, herkes görür. (Beğenme ve psikolojik etki geçerli)
 - Mesaj sistemi yerine arkadaşlarının profiline bakarak gelişmeleri görmesi sağlanabilir.
 
 

	
 
Hedef bazlı okumayı ekle
 - Öğretmen öğrenciye hedef verebilir
 - Sınıfa hedef verebilir
 - Takıma hedef verebilir
 - Toplam/Günlük/haftalık/aylık sayfa/aktivite puanı/kitap hedefi  (hedefler ölçülebilir olmalı)
 - Başlangıç bitiş tarihi var
 - Öğretmen motivasyon cümlesi 
 - Öğretmen ekranlarında öğrencinin durumunu gösteren bir liste/dashboard üzerinden direkt hedef atanabilmeli.
 - Ortak bir görev mekanizmasından görev seçilebilir.

Yarışmalara fazla odaklanmaya gerek yok. v2.0
Yarışma sadece belirli bir sayfayı-kitabı okuma olmasın. item collection tamamlama.
Mesela Türkiye haritası için, belirlenen itemleri toplama şeklinde. 
En iyi yorumu kim yazacak yarışması 


Hikayelere katılmayı öğrencilerin kendilerine bırakalım.
Özellik olarak hikayeler gelsin. Öğretmenin hangi hikayelerin aktif olacağını seçme imkanı verilebilir.
Öğrenciler bir hikayeye katılmak isterlerse katılırlar.

Hayvanat bahçesi 
Aktivite olarak hayvanat bahçesi tasarla. Akıllı tahta ekranlarında izlenebilsin. Öğrenci uygulamasından durumu görülebilsin.
Öğrenci kendi hayvanını seçer. Her gün kitap okudukça hayvanına besin gider. 2 gün okumazsa hastalanır. 1 hafta okumazsa hayvanı ölür.
Farklı yaptıkları için (yorum yazmak vb. writing prompts olabilir. ) hayvanı daha eğlenceli şeyler yapabilir.

Okuma kaydı 
 - Sınıf kitaplığından ya da evden herhangi bir kitabı okuyabilir
 - Okuma günlüğünü writing prompt mantığında yap, öğretmene onaya gönder. Onaylanan bilgiler puan kazandırır.  Puanlar oyun genelinde standart olmalı.
 - Günlük bilgileri ses kaydı olabilir, elle yazıp fotoğraf çekip gönderebilir, resim çizebilir.
 - kitap aktiviteleri tablosunda puan vb. detaylar.
 
Kitap kartları ve albüm
- Her kitapla ilgili kartlar hazırlayıp sisteme tanımla. Bir yöntemle çocuk kazansın ve albüm yapsın.
- Özel okumobil kartları da  olabilir. 

Öğretmen kontrolleri
 - Kitap soruları zorunlu/soru varsa isteğe bağlı/sorma  - kaç tane? - geçme puanı
 - Hangi kitap aktiviteleri aktif olacak?

Sınıf motivasyonunu destekleyici aktiviteler 
 - Kitaplara soru/kelime hazırlama bir sınıf aktivitesi olabilir ve katılan herkese öğretmen puan verir.
 - Sisteme kitap ekleme puan verebilir. 
 (Bunlar öğretmenler arası bir rekabet ortamı olabilir mi? Puanlar öğretmene verilecek, diğer öğretmenler onaylayacak filan.)

App ekranları
 - Öğrenci ekranı yerine Mobil Uygulama /mobile
 - Livewire ile tasarla 
 x Komple moonshine yapısıyla üretsek (esnek değil) 
  dokümanlar
	- https://moonshine-laravel.com/en/docs/3.x/model-resource/pages
    - https://moonshine-laravel.com/en/docs/3.x/appearance/layout
	- https://moonshine-laravel.com/en/docs/3.x/model-resource/metrics
    - https://moonshine-laravel.com/en/docs/3.x/page/index
    - https://moonshine-laravel.com/en/docs/3.x/recipes/profile 
    - https://moonshine-laravel.com/en/docs/3.x/components/attributes#class
    - https://moonshine-laravel.com/en/docs/3.x/appearance/assets
	- https://moonshine-laravel.com/en/docs/3.x/frontend/js client side interactivity
	- https://moonshine-laravel.com/en/docs/3.x/recipes/form-with-events more events 
	- https://moonshine-laravel.com/en/docs/3.x/recipes/async-remove-on-click more js async
	
  - standart bir css class yapısı oluştursun tüm öğrenci bileşenlerinde kullansın. 
  - Öğrenci ekranlarında ilk kullanımlar wizard olarak yapılsa...
  - Var olan model yapısını ve içinde oluşturulmuş helper metotları kullansın.
  - Birden çok çocuğu olanlar için login ekranında daha önce giriş yapmış hesaplarla girme düğmeleri

Ebeveyn takibi ekle. 
- Ebeveyn rolü
- Ebeveyn çocuk tablosu (user_relations)
- Ebeveyn rolünde bir kullanıcı eklendiğinde o ebeveyne ait öğrenciler seçilir. Yetkiye bağlı olarak farklı sınıflar (okullar ?) da...
- Öğretmen öğrencileri seçtiği her yerde o öğrencilerin ebeveynlerini de seçebilir.
   

Akış  
Kurumu sistem yöneticisi tanımlar. 
Öğretmen hesabını sistem yöneticisi ekler. Kuruma ilk tanımlanan öğretmen okul yöneticisi olur.
 a) Öğretmenin e-mailine link gider. Tıklayıp hesabını aktive eder.
 b) Öğretmenin e-mailine ilk kullanım şifresi gider, ilk girişte şifresini değiştirir.


Öğretmen giriş yapar. 
Tanımlar wizard şeklinde?
https://spatie.be/docs/laravel-livewire-wizard/v2/introduction
Sınıfını ekler. 
Sınıfına öğrenci ekler. 
 - Öğrencinin adını soyadını yazar. Eğer okulunda öğrenci varsa, (yazarken çıksın ?) seçer. 
 - Sistem öğrenciye bir kullanıcı numarası üretebilir. Her zaman o kullanıcı adıyla girer. Öğretmen gerekirse kendi kullanıcı adını içeren bir kağıdı öğrenciye verir.
	https://github.com/unleashlive/human-readable-ids
	- Öğrenci giriş yaptığında şifresini belirler. Şifrelerde çok seçici olmayız.
Rehber öğretmen, ders öğretmeni vb. birden çok sınıf için basit bir erişim şekli olmalı.

Hiyerarşik yarışma akışı
 - Mahal yöneticisi bir yarışma ekler. Yarışmaya katılacak okulları seçer. İlgili okul yöneticilerine davet gider.
 - Okul yöneticileri daveti kabul eder. 
 
- Ödül sistemi 
 - Sistem tarafından tanımlanan rozetler (ileride kartlar ve itemler) 
 - Öğretmen tarafından tanımlanan ve sadece kendi sınıfında kullanabildiği rozetler
 - Öğretmen tarafından tanımlanan ve sadece kendi sınıfında kullanabildiği hediyeler (fiziki hediye olabilir, hediye çeki olabilir)
 - Öğretmen tarafından tanımlanan ve sadece kendi sınıfında kullanabildiği kupalar
 - Eğer bir ödül bir göreve bağlı değilse manuel verilen ödüldür. 
 - Yarışmalarda görev tamamlandıkça verilen ödüller manuel ödüllerden seçim yapılır.
 - Öğretmen ödüller bölümünde kendi tanımladığı ödülleri ve (readonly olarak) sistem tarafından tanımlanmış ödülleri görür.
 - Öğretmen manuel ya da bir göreve bağlı ödüller oluşturabilir. 
 - Öğretmen ödül vermek istediğinde bir göreve bağlı olmayan ödüllerden verebilir. Ödülü sınıfın tamamı, bir takım ya da bir kişiye verebilir.
 - Öğretmenin oluşturduğu göreve bağlı ödüller sistem tarafından sadece öğretmenin sınıfındaki öğrencilere otomatik işletilir.
 
- Seviye sistemi 
 - Öğrenciler okudukları kitap sayısına ve okuma puanına göre seviyelerini yükseltebilir.
 - Her seviye için okunan kitap sayısı ve/veya okuma puanı sınırı konur.
 - Kitap okudukça kazanılan seviyeler hesaplanıp gösterilir.
 


Programın yaklaşımları 
- Sabit bir karakter, her kitap okuma yeni bir şeyler kazandırıyor. 
  Koleksiyon yaklaşımı
  Sistem komple puan üzerine kurulu, kitap okudukça/ilgili etkinlikleriyle puan artıyor, puan arttıkça yeni karakter/item/rozet etc. 
  Farklı program/challenge lar puan artışını sağlıyor. İlgili programa bağlı okuma kayıtları ayrıca raporlanabiliyor.
  
- Her program/challenge kendi karakterleri, ödül yapılarıyla geliyor. 
  Çocuk sürekli farklı rollere bürünüyor.
  Aktif birden çok program varsa her birinin ilerlemesi için ayrı ayrı kayıtlar girmek gerekir.
  
- Sistem direkt bir macera oyunu şeklinde.   
 
---


# Yapılacak işler listesi 

# Yönetim Paneli 

 [+] Badge yapısını task tabanlı olarak yeniden yaz. 
	(Reward olarak yeniden yazdık. Reward türleri: Badge, Gift, Trophy, Card, Item)
	[x] Önce görevler eklenmeden reward tanımında seçilemiyor. (Ekle düğmesiyle görev ekliyoruz.)
	[+] Mobilde sadece okuma kaydı rozet veriyor. Aktivite için kod yok.
	[+] UserReadingLog içindeki ödül verme mekanizması tüm task türleri için çalışmıyor. Eklenmesi gerekir.
	[+] Daha önce verilmiş ödülü yeniden veriyor. (Aynı ödül daha önce verilmişse tekrar verilemez. mi?)
		[+] Ödüllere bir kez/birden çok kez verilebilme özelliği ekle
	[+] Öğretmen tarafından oluşturulmuş görev bazlı ödüller sadece o öğretmenin öğrencilerine veriliyor. Kontrol et!
		[-] Yarışmalarda ilgili görev ödülleri sadece o öğretmenin öğrencilerine değil, yarışmaya katılanlara verilmeli!
		[-] Birden çok sınıf desteğini ekle
	[-] Otomatik takım ödüllerini tekrar gözden geçir. Kullanıcı ödülleriyle tekrara düşüyor.
	[+] Tekrarlanan ödüller kendi döngüsüne göre kontrol edilmeli. Aylıksa aynı ay içinde bir kez verilebilir.
	[+] Haftada x gün okudun hatalı. Muhtemelen bir günde birden çok okuma varsa onları sayıyor. 
	[+] Öğretmen panelinde ödülü kazananlar listesinde başka sınıflar da görünüyor.
	[+] Öğretmen panelinde sınıf kitaplığında kitapları alanlarda başka sınıflardan öğrenciler görünüyor.
	
	

 [+] Image alanları için klasör yapısını netleştir. book/covers vb.
	[+] Karakterler: avatars 
	[+] Kitap kapakları: books/covers 
	[+] Yarışma afişleri: challenges 
	[+] Seviyeler: levels 
	[+] Ödüller : rewards 
	[+] Takımlar: teams 
	[+] Kullanıcı aktivite dosyaları : user-activities 
	
 [-] Resource ekranları için filtreler ekle
	(Arama kutucukları var, çok öncelikli değil.)
	
 [-] Öğretmen için öğrenci hesabı oluşturma 
	[+] Kullanıcı adının müsait olduğunu kontrol et 
	[+] Oluşturulmuş kullanıcının kullanıcı adını değiştirmeye izin verme 
	[+] Öğrenciler için şifreyi 6 karakter yap. (harf + sayı)
	[-] Otomatik kullanıcı adı / şifre 
		[-] Şifre barkodları 
	[+] e-mail adresi otomatik olarak kullanıcıadı@okumobil.com şeklinde oluşsun.
	[-] Öğrenci silindiğinde o öğrenci ile ilgili tüm kayıtların silinmesi gerekiyor. (!!! uyarı göster)
	
 [+] Hedef kavramını kaldır. Hedef yerine görevleri kullan.
	[+] Görevlere açıklama ekle 
	[+] usertask a due date ve classid ekle.
	[+] Tüm görev türlerinin doğru şekilde çalıştığını ve hesaplandığını kontrol et. Görev-ödül, Görev-challenge ilişkilerinin doğru çalıştığını kontrol et.		
	[+] Görevlere ödül ekle. Sadece manuel ödüllerden seçim yapabilsin. 
	[+] Panelde görev detay ekranında linkleri kapat
	[+] Evet/Hayır görevlerini kaldır, yerine fiziksel kitap aktivitelerini kullan.
		[-] Görevler menüsünde hala duruyor.
	
 

 [-] Birden çok kurumda çalışanlar/ birden çok sınıf
	[+] Öğretmen panelinde üstten okul seçiliyor, öğretmenler sadece kendi asıl sınıfını görüyor, raporlarda yetkili olduğu sınıfları görüp filtreleyebiliyor
	[-] Asıl sınıf direkt olarak userclass tablosunda tek kayıt için yapılıyor. Okul + sınıf olmalı?
	[-] Sınıf isimleri karışıyor. Her okulda aynı sınıf var. Okul adıyla birlikte görünmeli.

 [-] Kurumlar için ülke, il, ilçe bilgileri ekle 

 [-] Öğretmenler için şifremi unuttum

 [+] Sınıf kitaplığı oluştur. 
	[+] Class Books resource üzerinde değişiklik yap. Kitabın durumu (müsait, öğrencide) Son okuyan öğrenci bilgisi, hala okuyorsa da....
		[+] Öğrenciye Ver linki, UserBooks penceresini açıp kitabı verebilsin.
	(Öğretmen paneli için yaptık)
 
 [+] Kitaplar sadece sınıf kitaplığından seçilebilsin. 
 
 [-] Bir kitap sadece bir kez okuma listesine eklenebiliyor. Birden çok kez okunamıyor. Altyapıda desteği var.

 [+] Kitap sorularının ve kelimelerinin öğretmenler tarafından eklenebilmesi

 [+] Kitap bulma ve ekleme servisi
	[+] Kitaplar form penceresinde kitap bulup ekleme 
	[-] Kitap bulunamadığında, öğretmene/öğrenciye bilgi versin, eklendiğinde haber versin.
 
 [+] Görev atamaları (Hedef ve yarışmalardan bağımsız, anlık görev atamaları)
	[+] Kişiye, takıma ya da tüm sınıfa atanabilir 
	(UserTask modeli ekledik. UserGoalTask ve UserChallengeTask modellerini kaldırdık. Hepsi UserTask türü olarak takip ediliyor.)

 [-] Challenge için UserReadingLog, UserActivity, UserBook modellerinde gerekli işlemlerin yapıldığına emin ol.
	[+] Tablolardan challenge_task_id kaldırdık. Hesaplama dinamik yapılacak.
 
 [-] Okuma takibi düzeltmeleri
	[-] Öğrencinin okuduğu kitap silindiğinde, tüm okuma kayıtları, aktiviteler, puanlar, ilgili okumadan kaynaklı rozetler vb. silinmeli. (Özel uyarı!)
	[-] Son okuma kaydı dışındaki kayıtlar silinememeli 
	[-] Bir okuma kaydı silindiğinde, o okuma kaydına bağlı puanlar silinmeli. (reward ve level siliniyor)
	[-] Son aktivite kaydı dışındaki aktiviteler silinmemeli 
	[-] Bir aktivite silindiğinde, o aktiviteye bağlı puanlar silinmeli 
	[-] Bir aktivite silindiğinde, o aktiviteye bağlı avatarlar silinmeli 
	[-] Mobilde de son aktivite ve son okuma kaydını silme özelliği getir

 [+] Zorunlu aktiviteler tamamlanmadığında kitaplar tamamlanmış sayılmasın

 [-] Rozetler verilirken son 2 dakika hesabı yapılıyor. Kitap bitince hemen aktivite yaptığında bu rozetler tekrar gösteriliyor.
	

 [-] Sınıf ayarları ekle
	[-] sayfa dakika sınırı
	[-] günlük kitap okuma kaydı sınırı
	[-] kitapların eklenebilme/sınıf kitaplığından seçme ayarı
	
	
  
 	

 [+] İşlemler kullanıcıya göre kısıtlanmalı (created_by / global scopes / policies)
	[+] Tüm gerekli modellere created_by ekle 
	[+] Tüm gerekli modellere global scope ekle 
	[+] Tüm gerekli policy dosyalarına yetkileri ekle 
	[+] Kitap ekleme işlemi arkada bir servis tarafından yapılırken created_by alanı doldurulmalı.  (Kitabı kim eklemiş?)
		[+] İlgili tüm tablolar için yap. (Author, Book, Publisher)
	[-] Policy dosyalarında TODO düzeltmelerine ihtiyaç var.
	[x] Global scope kodlarının gözden geçirilmesi gerekiyor. Mesela UserSchool hatalı çalışıyor. Devredışı bıraktık. (yetki için global scope kullanmayacağız.)
		[-] Global scope yetki kontrollerini kaldırdık. Doğru çalıştığını kontrol et.
		

 [x] Yarışma katılım süreci 
	[-] Öğretmen panelindeki işlemleri wizard olarak tasarla
	[-] challenge_schools ve challenge_classes tablolarına joined (default false) ve join_date alanları ekle. 
	[-] Eklenen okulların okul yöneticilerine mesaj gönder. 
	[-] Okul yöneticileri kendi okulları için katılır ve katılacak sınıfları seçer. Seçilen sınıfların öğretmenlerine mesaj gönder. 
	[-] Öğretmenler kendi sınıfları için katılır ve katılacak öğrencileri seçer. Seçilen öğrencilere görev kaydı oluşur. 
	[-] Okul yöneticisi isterse katılan öğrenciler için bir takım oluşturur ya da oluşturulmuş takıma katılır
	[-] Öğretmen isterse katılan öğrenciler için bir takım oluşturur ya da oluşturulmuş takıma katılır
	[-] Katılmayan okullar ve sınıflar görülebilir. 
	[-] Tüm yarışma görev hesapları katılan sınıflar ve katılan öğrenciler için yapılır. 
	


 [-] Dashboard ve rapor ekranları 
	[-] Yarışma raporları 
	[-] Hedef raporları 
	[+] Günlük aktivite dashboard (motive amaçlı)
	
 [+] İstatistik amaçlı metotları bir serviste birleştir, kod tekrarı olmasın.
	
 [+] Kitap aktiviteleri 
	[+] Aktiviteler için tekrar yapma sayısı ver. Quiz gibi aktiviteler tekrar çözülebilir.
	
	[+] Kitap testi aktivitesi ekle. 
		[+] Kaç soru sorulacak? Öğretmen değiştirilebilecek mi?
		[+] Geçme puanı nedir? Öğretmen değiştirilebilecek mi?
		[+] (Mobil) Kitap için soru tanımlı değilse hiç görünmesin
	
	[+] Kitap kelime testi aktivitesi ekle 
		[+] Kaç soru sorulacak? Öğretmen değiştirilebilecek mi?
		[+] Geçme puanı nedir? Öğretmen değiştirilebilecek mi?
		[+] (Mobil) Kitap için kelime tanımlı değilse hiç görünmesin
		
	[+] Reddedilen aktivite tekrar gönderildiğinde Bekliyor konumuna geçmiyor.
	[+] Ses aktivitesi dinlenemiyor 
		(File olarak ayarladık. Audio için bir alan yok ama düzenlenebiliyor. https://github.com/orgs/moonshine-software/discussions/1803)

 [+] Öğretmen kontrolleri 
	[+] Hangi aktiviteler sınıf için aktif? 
	
 [-] Ebeveyn takibi 
	[+] Ebeveyn rolü 
	[-] Ebeveyn çocuk tablosu (user_relations)
	[-] Ebeveyn rolünde bir kullanıcı eklendiğinde o ebeveyne ait öğrenciler seçilir. Yetkiye bağlı olarak farklı sınıflar (okullar ?) da...
	[-] Öğretmen öğrencileri seçtiği her yerde o öğrencilerin ebeveynlerini de seçebilir. (Rolü yanında gösterilebilir)
	Ebeveyn olarak girdiğinde 

 [-] Öğrencilerin içeri toplu aktarımı 
	[-] Daha sonra dışa aktarım

 [?] Challenge için dijital ödül/map yaklaşımı  (Hikaye mi olmalı, level vb. nasıl olacak?)
	[-] Challenge map ekle 
	[-] Task için item kazanma ekle 
	[-] Item için koordinat ekle 
	[-] Harita üzerinde kazanılan itemleri göster.

 [-] Abonelik yapısını oluştur 
	- Okul ve sınıfa göre olmalı.
	- 12 aylık. İlk yıl indirimli olabilir. 
	- Eğer aboneliği öğretmen aldıysa veritabanında okul+sınıf kaydı tutulur.
	- Aboneliği okul aldıysa sadece okul bilgisi tutulur. Bir öğretmenin hakkı her zaman okul toplamı üzerinden hesaplanır. Birden çok kez abonelik alınabilir, hepsi toplama eklenir.
		A okulu 3-A sınıfı 30 lisans + A okulu 100 lisans =	A okulu toplam 130 lisans 
		A okulu 50 ekstra farklı tarihli lisans = İlk lisansın bitimine kadar 180 lisans, ilk lisansın süresi bittiğinde 50 lisans. (Bu 50 lisansı kim kullanacak?)
		
	- Okul için okul yöneticisi kullanıcının alması gerekir.
	- veritabanı tasarımı: subscriptions (id, school_id, class_id, seats, purchased_by, purchase_date, expire_date) 
	- Lisans süresi bitiminde yapılacaklar 
		- Öğrenci giriş yaptığında bir uyarıyla karşılaşır. (Uygulamanın abonelik bilgileriyle ilgili bir sorun oluştu. Lütfen öğretmeninizle iletişime geçiniz)
		- Öğretmen giriş yaptığında bir uyarıyla karşılaşır. (Uygulamanın abonelik süresi dolmuştur. Lütfen aboneliğinizi yenileyiniz.)
		- Lisans süresi bitimine 1-2 hafta kala panelde uyarı gösterilir. (Uygulamanın abonelik süresi 15 gün sonra bitecektir. Lütfen aboneliğinizi yenileyiniz.)
		
 


---

# Mobil-Öğrenci

 [+] Okuma kaydında kitabın sayfa sayısından fazla girişi engelle.
 
 [+] Ana ekranda eğer hiç kitap yoksa kitap ekle kartını göster. (Kitap tamamlandığında boş kalıyor)

 [+] Ana ekranda avatar gösterilmiyor. (user->avatar bilgisi boş.)
 
 [+] Giriş ekranı düzenle 
 
	[+] Önce kullanıcı adı sonra şifre iki ekran 
	[+] Daha önce girmiş olanları kart olarak göster. Ayrıca manuel kullanıcı adı giriş
	[-] KVKK rıza metni

 [x] Aktivite puanları başka ne işe yarayabilir?
	(Avatar yeterli oldu. Sayfa puanlarıyla seviye atlıyoruz.)
 
 [+] Kitap soruları nasıl sorulacak? ( Kitap bittiğinde mi, aktivite mi?) 
 [+] Öğretmene kitapla ilgili meydan okuma fiziksel aktivite 

 [+] Kitap ekleme ekranında Internet'ten kitap bulup getirme 

 [-] Profil ekranı 
    [+] Avatar ve avatar seçimi 
	[+] Puanlar + Sayfa puanı göster
	[+] Aylık streak-github gibi 
	[-] Challenge listesi 
	[x] Hedef listesi 
	[+] Kazandığı ödüller 
	[-] Kitap kulesi 
	[+] Seviye bilgisi

 [+] Arkadaşlarım ekranı 
 
 [+] Kitap kartlarına "az kaldı" motivasyonu?
 
 [+] Girişte home ekranına gitmiyor, avatar seçilmemişse avatar seçme ekranı gelmiyor.
 
 [-] PWA özelliklerinin doğru çalıştığını test et, düzelt
	[+] kamera çalışmıyor, siyah ekran gösteriyor. 
	[-] Install as app çalışmıyor.  (Bildirim/mesaj altyapısı oluşturup mesajınız var gibi gösterelim.)
	[-] Kamera okumuyor
 
 [+] kitap bitti hata veriyor 
	[+] Kitabın tamamını  okuyup bitti demeyince kitap bitmiyor.
 
 [+] kapak resmi gönderme takılıyor
 
 [+] Translation bilgisi olmayan yerler var.

 [+] Genişlik sınırını kaldırabiliriz, tabletlerde daha kullanışlı olur.

 [+] Rozet geçen yerleri ödül olarak değiştir, tüm ödül türlerini destekle 
 
 [+] Kitap aramada Call to undefined method GuzzleHttp\Psr7\Response::withoutVerifying() veriyor. Muhtemelen google books a bakarken.
 
 [+] Kitap eklemede 500 
 
 [+] Rating aktivitesi gönderilemedi hatası 
 
 [+] Son 30 gün göstermiyor
 
 [+] Seviyelerin ismini de göster 
 
 [+] Ödül kazanıldığında bir müzik çal.
 [+] Ödül sayfasında resmi büyüt
 
 [+] Dosya yükleme aktivitelerinde m4a ses dosyası validasyon hatası veriyor.
 [+] Aktivite onaylarında tüm leveller için unlock sayfası gösteriyor. Aktivitede level kazanma yok. 
 [+] Loglarda ödül servisinde gün bilgilerini alırken sorgu hatası var. 
 
 [-] Kitap tamamlanmış ama sayfa sayısı 0 - kontrol et ( aynı kitap için farklı zamanlarda iki kayıt oluşmuş. biri 0 süre aynı)
	(15.10.2025 Bir düzeltme yaptık ama hala oluşuyor.)
 
 
 
 
 [+] Profil sayfasında avatara tıklandığında daha büyük göster
 
 [+] Mobil ekranda en fazla günde 600 sayfa, 360 dakika okuma limiti ekle. (Daha sonra sınıf ayarları eklendiğinde oradan değiştirilebilecek.)
 
 [+] Aktivite listesinde onaylı aktivitelerin üstüne onaylanması gerektiğini yaz.

 [-] Kitabı listeye yeni ekleyip "dün okudum" deyince kitap tamamlanmıyor.
	(Kitabın başlangıç tarihi okuma tarihinden yeni!)
 
---

# Mobil- Öğretmen  

 [x] Öğretmene mobilde tüm işleri yaptırabilme?
	(Responsive öğretmen paneli yapacağız.)
	
 [+] Mobil öğretmen sayfaları oluştur 
	[+] Öğretmen ana ekranı
		[+] Son 24 Saat: Kitap sayısı, okuyan/toplam öğrenci sayısı, okunan sayfa sayısı, tamamlanan etkinlikler gibi istatistik kutuları. 
		    Ayrıca 3 öğrenci kitap etkinliği (tamamlanan kitap, okumaya başlanan kitap) için kartlar ve "Tümünü Gör" bağlantısı.
		[+] Bekleyen Etkinlikler: Tüm bekleyen kullanıcı etkinlikleri listelenir. Her biri için "İncele" butonu bulunur.
	[+] Son 24 Saat Detay Ekranı
		Tüm öğrenci kitap etkinlikleri için kartlar (tamamlanan kitap, okumaya başlanan kitap) gösterilir.
	
	[+] Bireysel Etkinlik İnceleme Ekranı
		Kullanıcı, Kitap, Aktivite:Yazı, Puanlama, Yükleme (görsel/ses) içeriği, Kabul Et ve Reddet butonları 

---

# Öğretmen Paneli 

 [x] Responsive, PWA öğretmen paneli oluştur. 
	[x] PWA özelliklerinin doğru çalıştığını test et, düzelt

 [x] Rollere göre menüler ve yetkilendirme 
	[x] Okul yöneticisi için ayrı menüler. Ortak menülerde kayıt filtreleme
	[x] Bölge yöneticisi için ayrı menüler. Ortak menülerde kayıt filtreleme

	
 [x] Wizard olarak yapılacak işlemler 
	[x] Excel'den toplu öğrenci ekleme
 (Şimdilik moonshine ile devam ediyoruz. Öğretmene özel menüler.)	

 [-] Öğretmen paneli menüler 
	[+] Dashboard 
		[+] İstatistikler
			[+] Metrikler 
				[+] Onay Bekleyen aktivite sayısı
				[+] Son 24 saatte kitap okuyan öğrenci sayısı / sınıf mevcudu
				[+] Son 24 saatte okunan sayfa sayısı 
				[+] Son 24 saatte yapılan aktivite sayısı 
				
			[+] En çok kitap okuyan 5 kişi (Adı-Kitap sayısı)
			[+] En az kitap okuyan 5 kişi (Adı-Kitap sayısı)
			[+] En yüksek seviyeli 5 öğrenci
			[+] En düşük seviyeli 5 öğrenci
			[+] En çok rozete sahip 5 öğrenci
			[+] En az rozete sahip 5 öğrenci
			[+] En uzun süredir kitap okuyan 5 kişi (Adı - bugünden geriye en uzun streak)
			[+] En uzun süredir kitap okumayan 5 kişi (Adı - bugünden geriye en uzun okumadığı gün)
			[+] En yüksek aktivite puanına sahip 5 kişi (Adı - puanı)
			[+] En düşük aktivite puanına sahip 5 kişi (Adı - puanı)			
		[x] Bekleyen aktivite onayları
	[+] Öğrenciler (detaylı bilgi ve istatistikler, kitap işlemleri)
	[+] Takımlar 
	[+] Kitaplar (inceleme ve soru/kelime tanımlama)
		[+] Kitap soruları
		[+] Kitap kelimeleri 		
	[+] Sınıf Kitaplığı 
	[+] Sınıf Aktiviteleri (aktiviteleri inceleme modunda görebilir, sınıf aktivitesi oluşturabilir)
	[+] Öğrenci Okuma Kayıtları 	
	[+] Öğrenci Kitap Aktiviteleri 
	[+] Görevler (kendi oluşturduğu görevler)
	[+] Öğrenci Görevleri 
	[+] Ödüller (manuel ödül verme, kendi öğrencileri için geçerli rozetler tanımlama)
		[+] Öğrenciler genel sistem ödülleri yanında öğretmenin tanımladığı ödülleri alabilir, yönet.
		[-] Öğrencilere verilen ödüller silinemiyor
	[+] Öğrenci Ödülleri  (Sadece kendi oluşturduğu ödülleri verebilmeli, diğerleri otomatik vb.)
	[+] Takım Ödülleri (Sadece kendi oluşturduğu ödülleri verebilmeli , diğerleri otomatik vb.)
	[x] Hedefler 
	[x] Öğrenci Hedefleri 
		[x] Öğrenci hedef görevleri
	[-] Yarışmalar 
		[-] Öğrenci Yarışma Görevleri 
	
	
 [+] Kitap eklendiğinde sayfa sayısı yoksa pasif kalıyor, değiştirilemiyor.
	(varsayılan olarak aktif getirdik)
 
 [+] Sınıf aktiviteleri görünmüyor. (modifyQueryBuilder düzeltmesi)
 [-] Sınıf aktivitesi eklerken sınıf görünmüyor. 
 [+] Sınıf kitaplığında başkasının kitapları görünüyor
 [+] Okuma kayıtlarında öğrenci ismine tıklayınca detail değil form açılıyor

 [+] Sınıf kitaplığı sayfasına kitapların okunma raporunu Excel'e aktarma özelliği ekle.


---

# İçerik işleri 

 [+] Kitapları sisteme yükle / Yazarlar / Yayınevleri 
	[+] Sayfa sayıları tanımlı olmayanlar var, başka yerden tamamla.
	[-] Yeni kitaplar, hocalarla çalış.

 [+] Kitap sayfa puanlarını oluştur 
 

	
 [-] Belirli kitaplar için soru ve kelimeler gir
 [+] Kitap aktivitelerini tanımla 
	[+] Kitapla ilgili soruları çöz 
	[+] Kitapla ilgili kelime testi çöz 
	[+] Öğretmenden soru sormasını iste 
	
 [+] Karakterleri oluştur / al (AI, Freepik, asset siteleri, karakter duygu durumları)
	- Robot Kiraz
	- Okucan
	- Tatlı Aşçı
	- Kitty
	- Son Sürat
	- Kipo
	- Akıllı Muz
	- Topitop
	- Uzaylı Nanu
	- Aslancık	
	- Okumofil 
	- Kitap Kahramanı 
	- Leydi 
	- Pandi 
	
	- Kaplumbağa (Tospik)
	- Kirpi (Dikenli)
	- Helikopter Böceği (Yusufçuk)
	- Baykuş (Bay Baykuş)
	- Unicorn (Uni)
	- Meyve suyu bardağı içinde limon (Limo)
	
	
 
 [+] Rozetleri tanımla 
	[+] Kullanıcılara rozet oluşturma yetkisi verecek miyiz? 
	[+] Manuel rozet türleri ekle.
	
 
 [+] Seviyeleri tanımla 
	[+] Seviye simgelerini oluştur 

 [+] 3-A daha önce okunmuş kitapları sisteme aktar
 
---

# Hosting vb. işler 
 
 [+] Hüseyin'le konuş, VPS ayarla. Kurulumlar vb. 
 [+] Uygulamayı deploy et. 
 [+] Sayfalar sık sık 500 veriyor. 
	[+] (10.10.2025) Hüseyin'le sunucuda php thread sayısını 550 yaptık. 
		Ama yine de hata veriyor. Sorun sunucunun erişimiyle ilgili olabilir?
		(19.10.2025 Cloudflare proxy ile sunucu arasında sorun oluyor. Hüseyin proxy gereksiz dedi kaldırdık, DNS only çalışıyor. Sistem hızlandı ve hatalar gitti.)
 [-] Bir performans aracı kullan 
	[+] Laravel Pulse ile lokalde test ediyoruz. Ciddi bir performans problemi yok.
	
 

 [-] Ana site tasarımı
	[-] Yapay zeka resimleriyle tanıtım
	[-] Blog yazıları 
		https://barisozcan.com/kitap-okumak-beynimizi-nasil-etkiliyor/
	[-] Woocommerce üzerinden satış 

---



# Teknik todo

 [-] Veritabanı yapısı migrationlardan farklı. indeksleri vb. optimize et.
 [-] Kayıtlarda aktif-pasif yapısı olacakları gözden geçir. Silme yerine pasif yapma kavramı.
 [-] Kayıtlarda her yerde aktif olanların kullanıldığına emin ol!
 
 [x] modifyQueryBuilder kodları her yerde aynı değil. Sorguların rollere göre filtrelenmesi gerekir. 
	(Moonshine panel sadece super admin için kullanılacak)
 [x] Resource RBAC izinleri üretilmedi. (Moonshine panel sadece super admin için kullanılacak)
 [-] Moonshine panel kodlarını sadece super admin kullanımına göre temizle
 [+] Modeller üzerinden policy ve permission yapılarını oturt. Mobil uygulama ve öğretmen paneli rol yetkilerine göre çalışmalı.
	[-] Augment hata yapabiliyor, güzel test et.

 [+] ActivityLog yapısını devreye al. (Şimdilik sadece login için devreye aldık.)
	[-] .env içinde QUEUE_CONNECTION=sync kullanıyoruz. Login işlemlerini kayıt altına alan LogAuthEvent için performans sorunu olursa kontrol et.

 [-] Online log servisi entegrasyonu yap.

!!!! 404 hatası durumunda php artisan config:cache php artisan config:clear gerekiyor

 [+] Push notifications 
	[+] Token cache süresi 30 gün 
	[+] Her kullanıcının ayrı tokenı var. Cache nasıl yönetilecek? Loginde kontrol edilmeli ?
	 (Kullanıcının tokeni yok, device token var. Her kullanıcı aynı tokeni kullanıyor.) Aynı cihazı kullanan herkese mesaj geliyor.
	[-] Mesajlar kullanıcılara gitmiyor.

 [-] Ana ekrana ekle 
	[+] Chrome'da Yükle seçeneği çıkıyor. Uygulama olarak yükleniyor.
 
---

 + Kullanıcı tanımlama ve kullanıcı adı seçimi 
 + Sınıf aktiviteleri
 + Başlangıç için, eski kitapları girmelerini sağlayacak bir işlem yapmalı mı?
	+ Günlük/Haftalık ödülleri devre dışı bırakmak?
	+ Panelde kitap girişi var.
	+ Panelde toplu kitap giriş modu?
	
 - Günlük işleri kolaylaştıracak ne yapılabilir?
 - Kalan özellikler (
	- Yarışmalar 
	+ Kitap soruları/kelimeleri
	+ Bildirimler
	- Ebeveyn takibi
	- Öğretmenlerin kitap okuma takibi
	- Raporlar
	- Birden çok sınıf desteği (Okul geneli yönetim)
	- Manuel kitap ekleme
	- Başka ?
 - Destek Whatsapp grubu	

+ Yiğit Rana çekişme 
+ Günlük giriş sınırı (dakika,sayfa, kitap, okuma kaydı)
+ Toplu giriş için bir ekran 
+ Sınıfın önceki kayıtlarını ben gireyim
+ Zorunlu aktiviteleri tamamlamadıkları için kitaplar 0, seviyeler 1
 


