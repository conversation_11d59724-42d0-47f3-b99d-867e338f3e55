<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use App\Models\Traits\BypassesPermissionScopes;

class UserReward extends BaseModel
{
    use BypassesPermissionScopes;

    protected $fillable = [
        'user_id',
        'reward_id',
        'awarded_date',
        'awarded_by',
        'reading_log_id',
        'user_activity_id',
        'created_by',
    ];

    protected $casts = [
        'awarded_date' => 'datetime',
    ];

    /**
     * Get the user who earned this reward.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the reward that was earned.
     */
    public function reward(): BelongsTo
    {
        return $this->belongsTo(Reward::class);
    }

    /**
     * Get the user who awarded this reward (for manual awards).
     */
    public function awarder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'awarded_by');
    }

    /**
     * Get the reading log that triggered this reward award (for automatic awards).
     */
    public function readingLog(): BelongsTo
    {
        return $this->belongsTo(UserReadingLog::class, 'reading_log_id');
    }

    /**
     * Get the user activity that triggered this reward award (for automatic awards).
     */
    public function userActivity(): BelongsTo
    {
        return $this->belongsTo(UserActivity::class, 'user_activity_id');
    }

    /**
     * Get the user who created this reward record.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by reward.
     */
    public function scopeByReward($query, int $rewardId)
    {
        return $query->where('reward_id', $rewardId);
    }

    /**
     * Scope to filter by reward type.
     */
    public function scopeByRewardType($query, int $rewardType)
    {
        return $query->whereHas('reward', function ($q) use ($rewardType) {
            $q->where('reward_type', $rewardType);
        });
    }

    /**
     * Scope to get recent awards.
     */
    public function scopeRecentAwards($query, int $days = 30)
    {
        return $query->where('awarded_date', '>=', now()->subDays($days));
    }

    /**
     * Scope to get manual awards.
     */
    public function scopeManualAwards($query)
    {
        return $query->whereNotNull('awarded_by');
    }

    /**
     * Scope to get automatic awards.
     */
    public function scopeAutomaticAwards($query)
    {
        return $query->whereNull('awarded_by');
    }

    /**
     * Scope to get awards triggered by reading logs.
     */
    public function scopeTriggeredByReadingLog($query)
    {
        return $query->whereNotNull('reading_log_id');
    }

    /**
     * Scope to get awards triggered by user activities.
     */
    public function scopeTriggeredByActivity($query)
    {
        return $query->whereNotNull('user_activity_id');
    }

    /**
     * Get the display name for this user reward.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' - ' . $this->reward->name;
    }

    /**
     * Apply role-based filtering based on current user's access to users.
     */
    public function scopeForCurrentUser($query)
    {
        return $query->whereHas('user', function ($userQuery) {
            $userQuery->forCurrentUser();
        });
    }

    /**
     * Get the award type (manual or automatic).
     */
    public function getAwardTypeAttribute(): string
    {
        return $this->awarded_by ? __('admin.manual') : __('admin.automatic');
    }

    /**
     * Get the trigger source for this reward.
     */
    public function getTriggerSourceAttribute(): string
    {
        if ($this->reading_log_id) {
            return __('admin.reading_log_trigger');
        }
        
        if ($this->user_activity_id) {
            return __('admin.activity_trigger');
        }
        
        return $this->awarded_by ? __('admin.manual_award') : __('admin.automatic_award');
    }

    /**
     * Get the awarder name.
     */
    public function getAwarderNameAttribute(): string
    {
        return $this->awarder ? $this->awarder->name : __('admin.system');
    }

    /**
     * Get award age in human readable format.
     */
    public function getAwardAgeTextAttribute(): string
    {
        $diffInDays = $this->awarded_date->diffInDays(now());
        
        if ($diffInDays === 0) {
            return __('admin.awarded_today');
        } elseif ($diffInDays === 1) {
            return __('admin.awarded_yesterday');
        } else {
            return __('admin.awarded_days_ago', ['days' => $diffInDays]);
        }
    }

    /**
     * Check if this reward was triggered by a reading log.
     */
    public function isTriggeredByReadingLog(): bool
    {
        return !is_null($this->reading_log_id);
    }

    /**
     * Check if this reward was triggered by a user activity.
     */
    public function isTriggeredByActivity(): bool
    {
        return !is_null($this->user_activity_id);
    }

    /**
     * Get summary information.
     */
    public function getSummaryAttribute(): string
    {
        $rewardType = $this->reward->reward_type_display;
        $awardType = $this->award_type;
        $triggerSource = $this->trigger_source;

        return "Type: {$rewardType}, Award: {$awardType}, Source: {$triggerSource}";
    }

    /**
     * Award a reward to a user.
     */
    public static function awardRewardToUser(int $userId, int $rewardId, ?int $awardedBy = null, ?int $readingLogId = null, ?int $userActivityId = null): ?self
    {
        // Check if user already has this reward (bypass scopes for system check)
        if (self::withoutGlobalScopes()->where('user_id', $userId)->where('reward_id', $rewardId)->exists()) {
            return null;
        }

        // Create reward without scopes (automatic system operation)
        return self::createWithoutScopes([
            'user_id' => $userId,
            'reward_id' => $rewardId,
            'awarded_date' => now(),
            'awarded_by' => $awardedBy,
            'reading_log_id' => $readingLogId,
            'user_activity_id' => $userActivityId,
        ]);
    }

    /**
     * Get rewards earned by a user.
     */
    public static function getRewardsForUser(int $userId)
    {
        return self::where('user_id', $userId)
            ->with(['reward', 'awarder', 'readingLog', 'userActivity'])
            ->orderBy('awarded_date', 'desc')
            ->get();
    }

    /**
     * Get reward statistics for a user.
     */
    public static function getRewardStatsForUser(int $userId): array
    {
        $userRewards = self::byUser($userId)->with('reward');
        
        return [
            'total_rewards' => $userRewards->count(),
            'badges' => $userRewards->byRewardType(Reward::TYPE_BADGE)->count(),
            'gifts' => $userRewards->byRewardType(Reward::TYPE_GIFT)->count(),
            'trophies' => $userRewards->byRewardType(Reward::TYPE_TROPHY)->count(),
            'cards' => $userRewards->byRewardType(Reward::TYPE_CARD)->count(),
            'items' => $userRewards->byRewardType(Reward::TYPE_ITEM)->count(),
            'recent_rewards' => $userRewards->recentAwards(7)->count(),
        ];
    }
}
