<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\Traits\BypassesPermissionScopes;

class UserReadingLog extends BaseModel
{
    use BypassesPermissionScopes;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'book_id',
        'log_date',
        'start_page',
        'end_page',
        'pages_read',
        'reading_duration',
        'book_completed',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'log_date' => 'datetime',
            'book_completed' => 'boolean',
            'start_page' => 'integer',
            'end_page' => 'integer',
            'pages_read' => 'integer',
            'reading_duration' => 'integer',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Calculate pages_read if start_page and end_page are provided
        static::saving(function ($log) {
            if ($log->start_page && $log->end_page) {
                $log->pages_read = $log->end_page - $log->start_page + 1;
            }

            // Prevent duplicate book completion entries within the same session
            if ($log->book_completed) {
                // Get the current active session for this user-book combination
                $activeSession = UserBook::getCurrentSession($log->user_id, $log->book_id);

                if ($activeSession) {
                    // Check if there's already a completion log in this session
                    $existingCompletion = UserReadingLog::where('user_id', $log->user_id)
                        ->where('book_id', $log->book_id)
                        ->where('book_completed', true)
                        ->where('log_date', '>=', $activeSession->start_date)
                        ->where('id', '!=', $log->id ?? 0) // Exclude current log if updating
                        ->exists();

                    if ($existingCompletion) {
                        throw new \Exception(__('mobile.book_already_completed_in_session'));
                    }
                } else {
                    // No active session - this shouldn't happen in normal flow
                    // But check for any existing completion to be safe
                    $existingCompletion = UserReadingLog::where('user_id', $log->user_id)
                        ->where('book_id', $log->book_id)
                        ->where('book_completed', true)
                        ->where('id', '!=', $log->id ?? 0)
                        ->exists();

                    if ($existingCompletion) {
                        throw new \Exception(__('mobile.book_already_completed'));
                    }
                }
            }

            // Handle automatic book completion when pages exceed book's total page count
            if ($log->pages_read && $log->book && $log->book->page_count && !$log->book_completed) {
                $totalPagesRead = UserReadingLog::where('user_id', $log->user_id)
                    ->where('book_id', $log->book_id)
                    ->where('id', '!=', $log->id ?? 0) // Exclude current log if updating
                    ->sum('pages_read');

                $newTotalPages = $totalPagesRead + $log->pages_read;

                // If new total would exceed book's page count, adjust and mark as completed
                if ($newTotalPages > $log->book->page_count) {
                    $remainingPages = max(0, $log->book->page_count - $totalPagesRead);
                    if ($remainingPages > 0) {
                        $log->pages_read = $remainingPages;
                        $log->book_completed = true;
                    } else {
                        // Book was already completed, don't allow this log
                        throw new \Exception(__('mobile.book_already_completed'));
                    }
                } elseif ($newTotalPages == $log->book->page_count) {
                    // Auto-complete when pages equal total pages
                    $log->book_completed = true;
                }
            }
        });

        // Consolidated event handler for reading log creation
        static::created(function ($readingLog) {
            // Handle book completion first - update UserBook session
            if ($readingLog->book_completed) {
                $readingLog->updateUserBookSession();
            }

            // ALWAYS award immediate reading rewards (READ_PAGES, READ_MINUTES, READ_DAYS, READ_STREAK)
            // These rewards track ongoing reading behavior and should not be withheld
            $readingLog->checkAndAwardImmediateReadingRewards();

            // Only proceed with book-completion rewards/points/tasks if all required activities are completed
            if ($readingLog->allRequiredActivitiesCompleted()) {
                // Calculate and create reading points
                $readingLog->calculateAndCreatePoints();

                // Check and award book-completion rewards (READ_BOOKS, EARN_READING_POINTS)
                // These rewards depend on book completion and should be withheld until required activities are complete
                $readingLog->checkAndAwardBookCompletionRewards();

                // Check for level progression after creating reading log
                $readingLog->checkAndAwardLevels();

                // Check for task completion after creating reading log
                $readingLog->checkAndCompleteUserTasks();

                // Check and award team rewards for all user's teams
// 05.10.2025 review team awarding logic again
//                $readingLog->checkAndAwardTeamRewards();
            }
        });

        static::updated(function ($readingLog) {
            // Check if book_completed was changed to true
            if ($readingLog->book_completed && $readingLog->wasChanged('book_completed')) {
                $readingLog->updateUserBookSession();
            }

            // ALWAYS check and award rewards for any significant reading log update
            // This enables incremental rewards like daily pages read, weekly minutes read, etc.
            // Only check if significant fields changed (pages_read, reading_duration, book_completed)
            if ($readingLog->wasChanged(['pages_read', 'reading_duration', 'book_completed'])) {
                // ALWAYS award immediate reading rewards (READ_PAGES, READ_MINUTES, READ_DAYS, READ_STREAK)
                // These rewards track ongoing reading behavior and should not be withheld
                $readingLog->checkAndAwardImmediateReadingRewards();

                // Only trigger book-completion rewards/tasks if all required activities are completed
                if ($readingLog->allRequiredActivitiesCompleted()) {
                    // Check and award book-completion rewards (READ_BOOKS, EARN_READING_POINTS)
                    $readingLog->checkAndAwardBookCompletionRewards();

                    // Check for level progression after any update
                    $readingLog->checkAndAwardLevels();

                    // Check for task completion after updating reading log
                    $readingLog->checkAndCompleteUserTasks();

                    // Check and award team rewards for all user's teams
// 05.10.2025 review team awarding logic again
//                    $readingLog->checkAndAwardTeamRewards();
                }
            }

        });

        // Handle level regression when reading log is deleted
        static::deleting(function ($readingLog) {
            // Remove any user levels that were triggered by this specific reading log
            UserLevel::where('reading_log_id', $readingLog->id)->delete();
        });
    }

    /**
     * Get the user who created this reading log.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book for this reading log.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the rewards that were awarded based on this reading log.
     */
    public function userRewards(): HasMany
    {
        return $this->hasMany(UserReward::class, 'reading_log_id');
    }

    /**
     * Get the team rewards that were awarded based on this reading log.
     */
    public function teamRewards(): HasMany
    {
        return $this->hasMany(TeamReward::class, 'reading_log_id');
    }

    /**
     * Check if this reading log has associated reward awards.
     */
    public function hasAssociatedRewards(): bool
    {
        return $this->userRewards()->exists() || $this->teamRewards()->exists();
    }

    /**
     * Get the count of rewards awarded from this reading log.
     */
    public function getRewardCountAttribute(): int
    {
        return $this->userRewards()->count() + $this->teamRewards()->count();
    }

    /**
     * Get the names of rewards awarded from this reading log.
     */
    public function getAwardedRewardNamesAttribute(): string
    {
        $userRewardNames = $this->userRewards()
            ->with('reward')
            ->get()
            ->pluck('reward.name')
            ->toArray();

        $teamRewardNames = $this->teamRewards()
            ->with('reward')
            ->get()
            ->pluck('reward.name')
            ->toArray();

        $allRewardNames = array_merge($userRewardNames, $teamRewardNames);

        return empty($allRewardNames) ? __('admin.no_rewards') : implode(', ', $allRewardNames);
    }

    /**
     * Check and award rewards based on this reading log.
     */
    public function checkAndAwardRewards(): array
    {
        $awardedRewards = [];

        // Use RewardCalculationService for automatic reward awarding
        $service = app(\App\Services\RewardCalculationService::class);
        $awardedRewards = $service->checkAndAwardUserRewards($this->user_id, $this->id, null);

        return $awardedRewards;
    }

    /**
     * Check and award immediate reading rewards based on this reading log.
     *
     * Immediate rewards are awarded regardless of required activity status:
     * - READ_PAGES (1) - Cumulative page reading
     * - READ_MINUTES (3) - Cumulative time reading
     * - READ_DAYS (4) - Daily reading tracking
     * - READ_STREAK (5) - Consecutive day streaks
     */
    public function checkAndAwardImmediateReadingRewards(): array
    {
        $awardedRewards = [];

        // Use RewardCalculationService for immediate reading reward awarding
        $service = app(\App\Services\RewardCalculationService::class);
        $awardedRewards = $service->checkAndAwardImmediateReadingRewards($this->user_id, $this->id);

        return $awardedRewards;
    }

    /**
     * Check and award book-completion-dependent rewards based on this reading log.
     *
     * Book-completion rewards are only awarded when all required activities are completed:
     * - READ_BOOKS (2) - Requires book completion
     * - EARN_READING_POINTS (6) - Tied to book completion points
     */
    public function checkAndAwardBookCompletionRewards(): array
    {
        $awardedRewards = [];

        // Use RewardCalculationService for book completion reward awarding
        $service = app(\App\Services\RewardCalculationService::class);
        $awardedRewards = $service->checkAndAwardBookCompletionRewards($this->user_id, $this->id);

        return $awardedRewards;
    }

    /**
     * Check and award team rewards for all user's teams.
     */
    public function checkAndAwardTeamRewards(): array
    {
        // Use RewardCalculationService for automatic team reward awarding
        $service = app(\App\Services\RewardCalculationService::class);
        return $service->checkAndAwardTeamRewards($this->user_id, $this->id, null);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('log_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by completion status.
     */
    public function scopeCompleted($query, $completed = true)
    {
        return $query->where('book_completed', $completed);
    }

    /**
     * Scope to get recent logs.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('log_date', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Check if a book is already completed by a user in their current session.
     * This method considers multiple reading sessions - a user can complete the same book multiple times.
     */
    public static function isBookCompletedByUser(int $userId, int $bookId): bool
    {
        // Get the current active session for this user-book combination
        $activeSession = UserBook::getCurrentSession($userId, $bookId);

        if (!$activeSession) {
            // No active session means no current reading in progress
            // Check if there are any sessions that have finished reading (user might have completed all sessions)
            // Use completedSessions() scope to check for sessions with end_date, not truly completed sessions
            $completedSessions = UserBook::where('user_id', $userId)
                ->where('book_id', $bookId)
                ->completedSessions()
                ->exists();

            return $completedSessions;
        }

        // Check if the current active session has a completion log
        return $activeSession->hasSessionCompletionLog();
    }

    /**
     * Calculate and create points for this reading log.
     */
    public function calculateAndCreatePoints()
    {
        $points = $this->calculatePoints();

        if ($points > 0) {
            UserPoint::create([
                'point_date' => now(),
                'user_id' => $this->user_id,
                'book_id' => $this->book_id,
                'source_id' => $this->id,
                'point_type' => UserPoint::POINT_TYPE_PAGE,
                'points' => $points,
            ]);
        }
    }

    /**
     * Find the session that this reading log belongs to (active or completed).
     * This is used for retroactive processing when sessions may already be completed.
     */
    public function findSessionForLog(): ?UserBook
    {
        // Find all sessions for this user-book combination, ordered by start date
        $sessions = UserBook::where('user_id', $this->user_id)
            ->where('book_id', $this->book_id)
            ->orderBy('start_date', 'desc')
            ->get();

        // Convert log_date to date-only format for comparison (UserBook dates are date-only)
        $logDateOnly = Carbon::parse($this->log_date)->format('Y-m-d');

        foreach ($sessions as $session) {
            $sessionStartDate = Carbon::parse($session->start_date)->format('Y-m-d');

            // Check if this log falls within the session's date range
            if ($logDateOnly >= $sessionStartDate) {
                // If session is completed, check if log is within end date
                if ($session->end_date) {
                    $sessionEndDate = Carbon::parse($session->end_date)->format('Y-m-d');
                    if ($logDateOnly <= $sessionEndDate) {
                        return $session;
                    }
                } else {
                    // Active session - this log belongs here
                    return $session;
                }
            }
        }

        return null;
    }

    /**
     * Award points for ALL reading logs in a specific session that don't have points yet.
     * This method works for both active and completed sessions.
     */
    public function awardPointsForSession(UserBook $session): void
    {
        // Get all reading logs for this session
        $sessionLogs = $session->getSessionReadingLogs();

        foreach ($sessionLogs as $log) {
            // Check if this log already has reading points awarded
            $existingPoints = UserPoint::where('user_id', $this->user_id)
                ->where('book_id', $this->book_id)
                ->where('source_id', $log->id)
                ->where('point_type', UserPoint::POINT_TYPE_PAGE)
                ->exists();

            // If no points exist, create them
            if (!$existingPoints) {
                $log->calculateAndCreatePoints();
            }
        }
    }

    /**
     * Calculate points based on pages read and book/class level.
     */
    public function calculatePoints(): int
    {
        if (!$this->pages_read || $this->pages_read <= 0) {
            return 0;
        }

        // Get user's active class level through user_classes
        $userClass = $this->user->activeUserClasses()->first();
        if (!$userClass || !$userClass->schoolClass) {
            return 0;
        }

        $classLevelId = $userClass->schoolClass->class_level_id;
        $bookTypeId = $this->book->book_type_id;

        // Get page points from page_points table
        $pagePoint = PagePoint::where('book_type_id', $bookTypeId)
            ->where('class_level_id', $classLevelId)
            ->first();

        if (!$pagePoint) {
            return 0;
        }

        return (int) ($this->pages_read * $pagePoint->point);
    }

    /**
     * Get the display name for the reading log.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' - ' . $this->book->name . ' (' . $this->log_date->format('Y-m-d') . ')';
    }

    /**
     * Get summary information for the reading log.
     */
    public function getSummaryAttribute(): string
    {
        $duration = $this->reading_duration ? " ({$this->reading_duration} min)" : '';
        $completed = $this->book_completed ? ' [Completed]' : '';
        
        return sprintf(
            '%s pages%s%s on %s',
            $this->pages_read,
            $duration,
            $completed,
            $this->log_date->format('M d, Y')
        );
    }

    /**
     * Validation rules for the model.
     */
    public static function validationRules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'book_id' => [
                'required',
                'exists:books,id',
                function ($attribute, $value, $fail) {
                    $book = Book::find($value);
                    if ($book && !$book->canCreateReadingLogs()) {
                        $fail(__('admin.cannot_create_reading_log_inactive_book'));
                    }
                }
            ],
            'log_date' => ['required', 'date', 'before_or_equal:today'],
            'start_page' => ['nullable', 'integer', 'min:1'],
            'end_page' => ['nullable', 'integer', 'min:1', 'gte:start_page'],
            'pages_read' => ['required', 'integer', 'min:1'],
            'reading_duration' => ['nullable', 'integer', 'min:1'],
            'book_completed' => ['boolean'],
        ];
    }

    /**
     * Update the corresponding UserBook session when book is marked as completed.
     */
    public function updateUserBookSession(): void
    {
        // Find the current active session for this user-book combination
        $activeSession = UserBook::getCurrentSession($this->user_id, $this->book_id);

        if (!$activeSession) {
            // No active session found - this might be a reading log without a session
            // or the session was already completed
            return;
        }

        // Validate that the reading log date is within the session's date range
        if ($this->log_date < $activeSession->start_date) {
            // Reading log date is before session start - invalid
            return;
        }

        // If session already has an end date, check if this log date is earlier
        if ($activeSession->end_date && $this->log_date > $activeSession->end_date) {
            // Reading log date is after session end - invalid
            return;
        }

        // Update the session's end_date and set completed based on required activities
        $updateData = [
            'end_date' => $this->log_date
        ];

        // Check if all required activities are completed to determine if book should be marked as truly completed
        if ($this->allRequiredActivitiesCompleted()) {
            $updateData['completed'] = true;
        } else {
            $updateData['completed'] = false;
        }

        $activeSession->update($updateData);

        // Log the automatic session completion for debugging/auditing
        Log::info('UserBook session automatically completed', [
            'user_id' => $this->user_id,
            'book_id' => $this->book_id,
            'session_id' => $activeSession->id,
            'completion_date' => $this->log_date,
            'reading_log_id' => $this->id
        ]);
    }

    /**
     * Check if all required activities for this book are completed by the user.
     */
    public function allRequiredActivitiesCompleted(): bool
    {
        // Use class-specific resolution to get activities with proper required status
        $requiredActivities = Activity::where('activities.active', true)
            ->get()
            ->where('required', true); // Filter after resolution to use resolved values

        if ($requiredActivities->isEmpty()) {
            // No required activities, so completion is allowed
            return true;
        }

        foreach ($requiredActivities as $activity) {
            // For quiz and vocabulary test activities, check if book has sufficient questions/words
            if ($activity->isTestActivity()) {
                $hasEnoughContent = $this->bookHasEnoughContentForActivity($activity);
                if (!$hasEnoughContent) {
                    // Skip this activity if book doesn't have enough content
                    continue;
                }
            }

            // Check if user has completed this activity for this book
            // Activities are considered complete if they have STATUS_COMPLETED (no approval needed)
            // or STATUS_APPROVED (needed approval and was approved)
            $userActivity = UserActivity::where('user_id', $this->user_id)
                ->where('book_id', $this->book_id)
                ->where('activity_id', $activity->id)
                ->whereIn('status', [UserActivity::STATUS_COMPLETED, UserActivity::STATUS_APPROVED])
                ->first();

            if (!$userActivity) {
                // Required activity not completed
                return false;
            }

            // For test activities, check if they passed the minimum grade
            if ($activity->isTestActivity() && $activity->min_grade) {
                $content =  (is_string($userActivity->content)) ? json_decode($userActivity->content, true) : $userActivity->content;
                $score = $content['score_percentage'] ?? 0;

                if ($score < $activity->min_grade) {
                    // User didn't pass the minimum grade
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if book has enough content for a test activity.
     */
    private function bookHasEnoughContentForActivity(Activity $activity): bool
    {
        if ($activity->isQuiz()) {
            // Check if book has at least 10 questions
            $questionCount = BookQuestion::where('book_id', $this->book_id)
                ->where('is_active', true)
                ->count();
            return $questionCount >= 10;
        }

        if ($activity->isVocabularyTest()) {
            // Check if book has at least 10 words
            $wordCount = BookWord::where('book_id', $this->book_id)
                ->where('is_active', true)
                ->count();
            return $wordCount >= 10;
        }

        return true;
    }

    /**
     * Get incomplete required activities for this book and user.
     */
    public function getIncompleteRequiredActivities(): array
    {
        // Use class-specific resolution to get activities with proper required status
        $requiredActivities = Activity::where('activities.active', true)
            ->get()
            ->where('required', true); // Filter after resolution to use resolved values

        $incompleteActivities = [];

        foreach ($requiredActivities as $activity) {
            // For quiz and vocabulary test activities, check if book has sufficient questions/words
            if ($activity->isTestActivity()) {
                $hasEnoughContent = $this->bookHasEnoughContentForActivity($activity);
                if (!$hasEnoughContent) {
                    // Skip this activity if book doesn't have enough content
                    continue;
                }
            }

            // Check if user has completed this activity for this book
            // Activities are considered complete if they have STATUS_COMPLETED (no approval needed)
            // or STATUS_APPROVED (needed approval and was approved)
            $userActivity = UserActivity::where('user_id', $this->user_id)
                ->where('book_id', $this->book_id)
                ->where('activity_id', $activity->id)
                ->whereIn('status', [UserActivity::STATUS_COMPLETED, UserActivity::STATUS_APPROVED])
                ->first();

            $isCompleted = false;
            if ($userActivity && $activity->isTestActivity() && $activity->min_grade) {
                $content =  (is_string($userActivity->content)) ? json_decode($userActivity->content, true) : $userActivity->content;
                $score = $content['score_percentage'] ?? 0;
                $isCompleted = $score >= $activity->min_grade;
            } elseif ($userActivity) {
                $isCompleted = true;
            }

            if (!$isCompleted) {
                $incompleteActivities[] = $activity;
            }
        }

        return $incompleteActivities;
    }

    /**
     * Check and update UserBook completion status when required activities are completed.
     * This method should be called when a required activity is completed.
     */
    public static function checkAndUpdateBookCompletion(int $userId, int $bookId): void
    {
        // Find all UserBook sessions for this user-book combination that have end_date but not completed
        $incompleteSessions = UserBook::where('user_id', $userId)
            ->where('book_id', $bookId)
            ->whereNotNull('end_date')
            ->where('completed', false)
            ->get();

        if ($incompleteSessions->isEmpty()) {
            return;
        }

        // Create a temporary UserReadingLog instance to check required activities
        $tempLog = new self([
            'user_id' => $userId,
            'book_id' => $bookId,
        ]);

        // Check if all required activities are now completed
        if ($tempLog->allRequiredActivitiesCompleted()) {
            // Mark all incomplete sessions as completed
            foreach ($incompleteSessions as $session) {
                $session->update(['completed' => true]);
            }

            // Award withheld rewards for this book
            self::awardWithheldRewardsForBook($userId, $bookId);
        }
    }

    /**
     * Award previously withheld reading points and rewards for completed books.
     * This should be called when a required activity is completed.
     */
    public static function awardWithheldRewardsForBook(int $userId, int $bookId): void
    {
        // Find all completed reading logs for this user and book that haven't awarded points yet
        $completedLogs = self::where('user_id', $userId)
            ->where('book_id', $bookId)
            ->where('book_completed', true)
            ->get();

        $rewardsTriggered = false;
        $processedSessions = [];

        foreach ($completedLogs as $log) {
            // Check if this log already has reading points awarded
            $existingPoints = UserPoint::where('user_id', $userId)
                ->where('book_id', $bookId)
                ->where('source_id', $log->id)
                ->where('point_type', UserPoint::POINT_TYPE_PAGE)
                ->exists();

            // If no points exist and all required activities are now completed, award them
            if (!$existingPoints && $log->allRequiredActivitiesCompleted()) {
                // Find the session that this reading log belongs to (active or completed)
                $logSession = $log->findSessionForLog();

                if ($logSession) {
                    $sessionKey = $logSession->id;

                    // If we haven't processed this session yet, award points for all logs in the session
                    if (!in_array($sessionKey, $processedSessions)) {
                        $log->awardPointsForSession($logSession);
                        $processedSessions[] = $sessionKey;
                    }
                } else {
                    // No session found - just create points for this specific log
                    $log->calculateAndCreatePoints();
                }

                $rewardsTriggered = true;
            }
        }

        // Only trigger rewards once after all points are awarded
        // This prevents duplicate reward awarding
        if ($rewardsTriggered) {
            // Use the most recent completed log for reward checking
            $mostRecentLog = $completedLogs->sortByDesc('log_date')->first();
            if ($mostRecentLog) {
                // Trigger ONLY book-completion rewards that were withheld
                // Immediate reading rewards (READ_PAGES, READ_MINUTES, READ_DAYS, READ_STREAK)
                // were already awarded when the reading logs were created, so we only need
                // to award book-completion rewards (READ_BOOKS, EARN_READING_POINTS)
                $mostRecentLog->checkAndAwardBookCompletionRewards();

                // Trigger task completion that was withheld
                $mostRecentLog->checkAndCompleteUserTasks();

                // Trigger level progression that was withheld
                // Since levels are only awarded when allRequiredActivitiesCompleted() is true,
                // we need to award them retroactively when required activities are completed
                $mostRecentLog->checkAndAwardLevels();

// 05.10.2025 review team awarding logic again
//                $mostRecentLog->checkAndAwardTeamRewards();
            }
        }
    }

    /**
     * Check and award new levels for the user based on their current progress.
     */
    public function checkAndAwardLevels()
    {
        $user = $this->user;
        $currentLevelNumber = $user->getCurrentLevelNumber();

        // Get all levels higher than the user's current level
        $availableLevels = Level::where('nr', '>', $currentLevelNumber)
            ->ordered()
            ->get();

        foreach ($availableLevels as $level) {
            // Check if user qualifies for this level and hasn't achieved it yet
            if ($level->userQualifies($user) && !$user->hasAchievedLevel($level)) {
                // Award the level
                UserLevel::create([
                    'user_id' => $user->id,
                    'level_id' => $level->id,
                    'level_date' => now(),
                    'reading_log_id' => $this->id,
                ]);
            }
        }
    }

    /**
     * Check and complete any UserTask instances that should be marked as completed
     * based on the current reading activity.
     *
     * This method uses the TaskProgressCalculationService to determine if any
     * assigned tasks for this user should be marked as completed based on their
     * current progress including this reading log.
     *
     * @return array Array of UserTask instances that were marked as completed
     */
    public function checkAndCompleteUserTasks(): array
    {
        $service = app(\App\Services\TaskProgressCalculationService::class);
        return $service->checkAndCompleteUserTasks($this->user_id, $this->book_id);
    }

    /**
     * Check if this reading log can be deleted (only most recent log can be deleted).
     */
    public function canBeDeleted(): bool
    {
        // Get the most recent reading log for this user
        $mostRecentLog = UserReadingLog::where('user_id', $this->user_id)
            ->orderBy('log_date', 'desc')
            ->orderBy('id', 'desc')
            ->first();

        return $mostRecentLog && $mostRecentLog->id === $this->id;
    }

    /**
     * Get validation rules for deletion.
     */
    public static function deletionRules(): array
    {
        return [
            'can_delete' => function ($attribute, $value, $fail) {
                if ($value instanceof UserReadingLog && !$value->canBeDeleted()) {
                    $fail(__('admin.can_only_delete_most_recent_reading_log'));
                }
            }
        ];
    }
}
