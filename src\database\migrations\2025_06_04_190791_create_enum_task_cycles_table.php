<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enum_task_cycles', function (Blueprint $table) {
            $table->id();
            $table->integer('nr')->unique();
            $table->string('name');

            // Index for performance
            $table->index('nr');
        });

        // Insert the predefined task cycles
        DB::table('enum_task_cycles')->insert([
            ['nr' => 1, 'name' => 'Total'],
            ['nr' => 2, 'name' => 'Daily'],
            ['nr' => 3, 'name' => 'Weekly'],
            ['nr' => 4, 'name' => 'Monthly'],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enum_task_cycles');
    }
};
