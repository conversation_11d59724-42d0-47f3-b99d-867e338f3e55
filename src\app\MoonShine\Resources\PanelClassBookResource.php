<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ClassBook;
use App\Models\SchoolClass;
use App\Models\Book;
use App\Models\User;
use App\Models\UserBook;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Support\AlpineJs;
use MoonShine\Support\Enums\JsEvent;
use MoonShine\UI\Components\Badge;
use MoonShine\UI\Components\CardsBuilder;
use MoonShine\UI\Fields\Date;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;
use MoonShine\UI\Components\ActionButton;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;
use MoonShine\Laravel\MoonShineRequest;
use MoonShine\Support\Enums\ToastType;
use MoonShine\Support\ListOf;
use MoonShine\Contracts\UI\ActionButtonContract;
use OpenSpout\Common\Entity\Style\CellAlignment;
use OpenSpout\Common\Entity\Style\Style;
use Rap2hpoutre\FastExcel\FastExcel;

#[Icon('view-columns')]
class PanelClassBookResource extends ClassBookResource
{
    use WithRolePermissions;
    
    protected function indexFields(): iterable
    {
        return [
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.book'),
                    'book',
                    formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                    resource: PanelBookResource::class
                )
                    ->required()
                    ->searchable()
                    ->asyncSearch(),
            ]),

        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name . ' (' . $class->school->name . ')',
                resource: SchoolClassResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            ),

            Text::make(__('admin.isbn'), 'book.isbn'),
            Text::make(__('admin.publisher'), 'book.publisher.name'),
            Text::make(__('admin.book_type'), 'book.bookType.name'),
            Text::make(__('admin.page_count'), 'book.page_count'),
            Text::make(__('admin.year_of_publish'), 'book.year_of_publish'),
            ...parent::getCommonDetailFields(),
            HasMany::make(__('admin.reading_history'), 'userBooks', PanelUserBookResource::class)
                ->async()
                ->creatable()
                ->fields([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: StudentResource::class
                    ),
                    Date::make(__('admin.start_date'), 'start_date')
                    ->format('d.m.Y'),
                    Date::make(__('admin.end_date'), 'end_date')
                    ->format('d.m.Y'),
                    Text::make(__('admin.reading_status'), 'localized_reading_status'),
                ]),
        ];
    }

    public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();

        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }

    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), [])
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
            ->title( fn($classBook) => html_entity_decode($classBook->book->name))
            ->subtitle('book.author_names')
            // if class book have any user books with enddate is null, show badge of reading user name else show badge of available
            ->header(fn($classBook) => $this->getClassBookHeaderBadge($classBook))
            ->url(fn($classBook) => $this->getDetailPageUrl($classBook->getKey()))
            ->thumbnail(fn($classBook) => asset('storage/' . $classBook->book->cover_image))
            ->fields([
                Text::make(__('admin.isbn'), 'book.isbn'),
                    ]);
    }

    private function getClassBookHeaderBadge($classBook)
    {
        $user = auth('moonshine')->user();
        $userBookQuery = $classBook->userBooks()->whereNull('end_date')->forCurrentUser();

        if ($userBookQuery->exists()) {
            $firstUserBook = $userBookQuery->first();
            return Badge::make($firstUserBook->user->name, 'info');
        }
        return Badge::make(__('admin.available'), 'success');
    }



    // override rules to exclude class selection, it will be added on create
    public function rules(mixed $item): array
    {
        return [
            'book_id' => ['required', 'exists:books,id'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function topButtons(): ListOf
    {
        return parent::topButtons()
            ->add(
                ActionButton::make(__('admin.export_reading_matrix'), fn() => '#')
                    ->method('exportClassBooksMatrix')
                    ->secondary()
                    ->icon('table-cells')
            );
    }

    public function exportClassBooksMatrix(MoonShineRequest $request): MoonShineJsonResponse
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User) || (!$user->isTeacher() && !$user->isSystemAdmin())) {
            return MoonShineJsonResponse::make()->toast(__('admin.unauthorized_action'), ToastType::ERROR);
        }

        try {
            // Get teacher's default class
            $defaultClass = $user->getDefaultClass();
            if (!$defaultClass) {
                return MoonShineJsonResponse::make()->toast(__('admin.no_default_class'), ToastType::ERROR);
            }

            $classId = $defaultClass->class_id;
            $schoolClass = SchoolClass::find($classId);

            if (!$schoolClass) {
                return MoonShineJsonResponse::make()->toast(__('admin.class_not_found'), ToastType::ERROR);
            }

            // Get class books for this class
            $classBooks = ClassBook::where('class_id', $classId)
                ->with(['book'])
                ->get();

            if ($classBooks->isEmpty()) {
                return MoonShineJsonResponse::make()->toast(__('admin.no_books_in_class'), ToastType::ERROR);
            }

            // Get students in this class
            $students = User::whereHas('activeUserClasses', function ($q) use ($classId) {
                $q->where('class_id', $classId);
            })
            ->whereHas('roles', function ($q) {
                $q->where('name', 'student');
            })
            ->orderBy('name')
            ->get();

            if ($students->isEmpty()) {
                return MoonShineJsonResponse::make()->toast(__('admin.no_students_in_class'), ToastType::ERROR);
            }

            // Get user books data for completed books (end_date IS NOT NULL)
            $userBooks = UserBook::whereIn('book_id', $classBooks->pluck('book_id'))
                ->whereIn('user_id', $students->pluck('id'))
                ->whereNotNull('end_date')
                ->get()
                ->groupBy(['book_id', 'user_id']);

            // Generate Excel file
            $fileName = 'kitap_okuma_raporu_' . str_replace(' ', '_', $schoolClass->name) . '_' . now()->format('d.m.Y') . '.xlsx';

            // Prepare data for Excel
            $data = [];

            // Title row (FastExcel uses keys as column headers)
            $data[] = [ $schoolClass->name . ' ' . __('admin.class_books_reading_report') => ''];

            // Header row
            $header = [__('admin.book')];
            foreach ($students as $student) {
                $header[] = $student->name;
            }
            $data[] = $header;

            // Data rows
            foreach ($classBooks as $classBook) {
                $row = [html_entity_decode($classBook->book->name)];

                foreach ($students as $student) {
                    // Check if student has completed this book
                    $hasCompleted = isset($userBooks[$classBook->book_id][$student->id]);
                    $row[] = $hasCompleted ? '+' : '';
                }

                $data[] = $row;
            }

            // Create Excel file and save to public storage
            $publicPath = 'exports/' . $fileName;
            $fullPath = storage_path('app/public/' . $publicPath);

            // Ensure exports directory exists
            if (!file_exists(dirname($fullPath))) {
                mkdir(dirname($fullPath), 0755, true);
            }

            // Create and save Excel file
            $header_style = (new Style())->setFontBold();
            $fastExcel = new FastExcel($data);
            $fastExcel->headerStyle($header_style);

            $columnStyles = [];
            $studentsColumnCount = count($students) + 1; 
            for ($i = 0; $i < $studentsColumnCount; $i++) {
                $style = new Style();
                if ($i > 0) {
                    $style->setCellAlignment(CellAlignment::CENTER);
                }
                $columnStyles[$i] = $style;
            }
            $fastExcel->setColumnStyles($columnStyles);

            $fastExcel->export($fullPath);

            // Return success response with download URL
            return MoonShineJsonResponse::make()
                ->toast(__('admin.export_successful'), ToastType::SUCCESS)
                ->redirect(asset('storage/' . $publicPath));

        } catch (\Exception $e) {
            return MoonShineJsonResponse::make()->toast(__('admin.export_failed') . ': ' . $e->getMessage(), ToastType::ERROR);
        }
    }
}
