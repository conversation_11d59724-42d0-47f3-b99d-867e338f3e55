<?php

namespace App\Services\BookDiscovery\Providers;

use App\Services\BookDiscovery\AbstractBookDiscoveryProvider;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use DOMXPath;

class AmazonTRProvider extends AbstractBookDiscoveryProvider
{
    /**
     * Turkish month names mapping for date parsing.
     */
    protected array $turkishMonths = [
        'Ocak' => 1, 'Şubat' => 2, 'Mart' => 3, 'Nisan' => 4,
        'Mayıs' => 5, 'Haziran' => 6, 'Temmuz' => 7, 'Ağustos' => 8,
        'Eylül' => 9, 'Ekim' => 10, 'Kasım' => 11, 'Aralık' => 12
    ];

    /**
     * Build search URL for the given ISBN.
     */
    protected function buildSearchUrl(string $isbn): string
    {
        return "https://www.amazon.com.tr/s?k={$isbn}&i=stripbooks";
    }

    /**
     * Check if the page indicates book not found.
     */
    protected function isBookNotFound(string $html): bool
    {
        // Check for "sonuç bulunamadı" text pattern
        if (stripos($html, 'sonuç bulunamadı') !== false) {
            return true;
        }

        // If "Sonuçlar" is found, books are available
        if (stripos($html, 'Sonuçlar') !== false) {
            return false;
        }

        // Also check parent's generic logic
        return parent::isBookNotFound($html);
    }

    /**
     * Parse book data from HTML.
     */
    protected function parseBookData(string $html): ?array
    {
        // First, try to extract detail URL from search results
        $detailUrl = $this->extractDetailUrl($html);
        
        if (!$detailUrl) {
            Log::warning("AmazonTR: Could not extract detail URL from search results");
            return null;
        }

        // Fetch the detail page
        $detailHtml = $this->fetchDetailPage($detailUrl);
        
        if (!$detailHtml) {
            Log::warning("AmazonTR: Could not fetch detail page", ['url' => $detailUrl]);
            return null;
        }

        // Parse book data from detail page
        $bookData = $this->parseDetailPage($detailHtml);

        if (!$bookData) {
            return null;
        }

        // Post-process the data
        $bookData = $this->postProcessAmazonTRData($bookData);

        return $bookData;
    }

    /**
     * Extract detail URL from search results page.
     */
    protected function extractDetailUrl(string $html): ?string
    {
        $dom = $this->createDomDocument($html);
        if (!$dom) {
            return null;
        }

        $xpath = new DOMXPath($dom);
        
        // Look for the first div[data-cy="title-recipe"]
        $titleRecipeNodes = $xpath->query('//div[@data-cy="title-recipe"]');
        
        if ($titleRecipeNodes->length === 0) {
            return null;
        }

        // Find the first a element within the title recipe
        $linkNodes = $xpath->query('.//a', $titleRecipeNodes->item(0));
        
        if ($linkNodes->length === 0) {
            return null;
        }

        $href = $linkNodes->item(0)->getAttribute('href');
        
        if (empty($href)) {
            return null;
        }

        // If it's a relative URL, make it absolute
        if (strpos($href, 'http') !== 0) {
            $href = 'https://www.amazon.com.tr' . $href;
        }

        return $href;
    }

    /**
     * Fetch detail page content.
     */
    protected function fetchDetailPage(string $url): ?string
    {
        try {
            $response = Http::timeout($this->config['timeout'])
                ->withHeaders([
                    'User-Agent' => $this->getRandomUserAgent(),
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language' => 'tr-TR,tr;q=0.9,en;q=0.8',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                    'Upgrade-Insecure-Requests' => '1',
                ])
                ->withoutVerifying()
                ->get($url);

            if ($response->successful()) {
                return $response->body();
            }

            Log::warning("AmazonTR: HTTP error fetching detail page", [
                'url' => $url,
                'status' => $response->status()
            ]);

        } catch (\Exception $e) {
            Log::error("AmazonTR: Exception fetching detail page", [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Parse book data from detail page HTML.
     */
    protected function parseDetailPage(string $html): ?array
    {
        $dom = $this->createDomDocument($html);
        if (!$dom) {
            return null;
        }

        $xpath = new DOMXPath($dom);
        $bookData = [];

        // Extract book title from span#productTitle
        $titleNodes = $xpath->query('//span[@id="productTitle"]');
        if ($titleNodes->length > 0) {
            $title = trim($titleNodes->item(0)->textContent);
            if (!empty($title)) {
                $bookData['name'] = html_entity_decode($title, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
        }

        // Extract cover image from img#landingImage
        $imageNodes = $xpath->query('//img[@id="landingImage"]');
        if ($imageNodes->length > 0) {
            $src = $imageNodes->item(0)->getAttribute('src');
            if (!empty($src)) {
                $bookData['cover_image'] = $src;
            }
        }

        // Extract ISBN
        $this->extractISBN($xpath, $bookData);

        // Extract authors
        $this->extractAuthors($xpath, $bookData);

        // Extract publisher
        $this->extractPublisher($xpath, $bookData);

        // Extract page count
        $this->extractPageCount($xpath, $bookData);

        // Extract publication year
        $this->extractPublicationYear($xpath, $bookData);

        return !empty($bookData) ? $bookData : null;
    }

    /**
     * Extract ISBN from detail page.
     */
    protected function extractISBN(DOMXPath $xpath, array &$bookData): void
    {
        // Look for ISBN-13 in list items
        $isbnNodes = $xpath->query('//span[@class="a-list-item"]//span[@class="a-text-bold"][contains(text(), "ISBN-13")]/following-sibling::span');
        
        if ($isbnNodes->length > 0) {
            $isbn = trim($isbnNodes->item(0)->textContent);
            if (!empty($isbn)) {
                // Remove hyphens and spaces
                $isbn = preg_replace('/[-\s]/', '', $isbn);
                $bookData['isbn'] = $isbn;
            }
        }
    }

    /**
     * Extract authors from detail page.
     */
    protected function extractAuthors(DOMXPath $xpath, array &$bookData): void
    {
        // Look for all span.author elements and extract a elements within them
        $authorNodes = $xpath->query('//span[contains(@class, "author")]//a');
        
        if ($authorNodes->length > 0) {
            $authors = [];
            foreach ($authorNodes as $authorNode) {
                $authorName = trim($authorNode->textContent);
                if (!empty($authorName)) {
                    $authors[] = html_entity_decode($authorName, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                }
            }
            
            if (!empty($authors)) {
                $bookData['author'] = array_unique($authors); // Remove duplicates
            }
        }
    }

    /**
     * Extract publisher from detail page.
     */
    protected function extractPublisher(DOMXPath $xpath, array &$bookData): void
    {
        // Look for publisher in list items
        $publisherNodes = $xpath->query('//span[@class="a-list-item"]//span[@class="a-text-bold"][contains(text(), "Yayıncı")]/following-sibling::span');
        
        if ($publisherNodes->length > 0) {
            $publisher = trim($publisherNodes->item(0)->textContent);
            if (!empty($publisher)) {
                $bookData['publisher'] = html_entity_decode($publisher, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
        }
    }

    /**
     * Extract page count from detail page.
     */
    protected function extractPageCount(DOMXPath $xpath, array &$bookData): void
    {
        $pageNodes = $xpath->query('//span[@class="a-list-item"]//span[@class="a-text-bold"][contains(text(), "Baskı Uzunluğu")]/following-sibling::span');

        if ($pageNodes->length > 0) {
            $pageText = trim($pageNodes->item(0)->textContent);
            if (!empty($pageText)) {
                if (preg_match('/(\d+)\s*sayfa/i', $pageText, $matches)) {
                    $pageCount = (int) $matches[1];
                    if ($pageCount > 0 && $pageCount <= 10000) { // Reasonable page count validation
                        $bookData['page_count'] = $pageCount;
                    }
                }
            }
        }
    }

    /**
     * Extract publication year from detail page.
     */
    protected function extractPublicationYear(DOMXPath $xpath, array &$bookData): void
    {
        // Look for publication date in list items
        $dateNodes = $xpath->query('//span[@class="a-list-item"]//span[@class="a-text-bold"][contains(text(), "Yayınlanma Tarihi")]/following-sibling::span');
        
        if ($dateNodes->length > 0) {
            $dateText = trim($dateNodes->item(0)->textContent);
            if (!empty($dateText)) {
                $year = $this->parseTurkishDate($dateText);
                if ($year) {
                    $bookData['year_of_publish'] = $year;
                }
            }
        }
    }

    /**
     * Parse Turkish date format and extract year.
     */
    protected function parseTurkishDate(string $dateText): ?int
    {
        // Try to extract 4-digit year first (simple approach)
        if (preg_match('/(\d{4})/', $dateText, $matches)) {
            $year = (int) $matches[1];
            if ($year >= 1000 && $year <= (date('Y') + 1)) {
                return $year;
            }
        }

        // Try to parse Turkish date format: "28 Ocak 2025"
        foreach ($this->turkishMonths as $monthName => $monthNumber) {
            if (stripos($dateText, $monthName) !== false) {
                // Extract day, month, year pattern
                $pattern = '/(\d{1,2})\s+' . preg_quote($monthName, '/') . '\s+(\d{4})/i';
                if (preg_match($pattern, $dateText, $matches)) {
                    $year = (int) $matches[2];
                    if ($year >= 1000 && $year <= (date('Y') + 1)) {
                        return $year;
                    }
                }
                break; // Found the month, no need to continue
            }
        }

        return null;
    }

    /**
     * Post-process Amazon TR specific data.
     */
    protected function postProcessAmazonTRData(array $bookData): array
    {
        // Clean and validate book title
        if (isset($bookData['name'])) {
            $bookData['name'] = $this->cleanText($bookData['name']);
        }

        // Clean author names
        if (isset($bookData['author'])) {
            if (is_array($bookData['author'])) {
                $bookData['author'] = array_map([$this, 'cleanText'], $bookData['author']);
                $bookData['author'] = array_filter($bookData['author']); // Remove empty values
            } else {
                $bookData['author'] = [$this->cleanText($bookData['author'])];
            }
        }

        // Clean publisher name
        if (isset($bookData['publisher'])) {
            $bookData['publisher'] = $this->cleanText($bookData['publisher']);
        }

        // Add source information
        $bookData['source'] = 'Amazon TR';

        return $bookData;
    }

    /**
     * Clean text content.
     */
    protected function cleanText(string $text): string
    {
        // Remove extra whitespace and decode HTML entities
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * Create DOM document from HTML with proper error handling.
     */
    protected function createDomDocument(string $html): ?DOMDocument
    {
        $dom = new DOMDocument();
        
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
        
        // Load HTML with UTF-8 encoding
        $success = @$dom->loadHTML('<?xml encoding="UTF-8">' . $html);
        
        // Clear libxml errors
        libxml_clear_errors();
        
        return $success ? $dom : null;
    }

    /**
     * Get random user agent from config.
     */
    protected function getRandomUserAgent(): string
    {
        $userAgents = $this->config['user_agents'] ?? [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ];
        
        return $userAgents[array_rand($userAgents)];
    }
}
