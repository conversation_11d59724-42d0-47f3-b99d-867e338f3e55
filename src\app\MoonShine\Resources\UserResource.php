<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\School;
use App\Models\User;
use App\Models\Role;
use App\Models\SchoolClass;


use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;

use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Email;
use MoonShine\UI\Fields\Password;
use MoonShine\UI\Fields\PasswordRepeat;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Sweet1s\MoonshineRBAC\Traits\WithRoleFormComponent;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Http\Request;
use MoonShine\Laravel\Fields\Relationships\RelationRepeater;
use MoonShine\UI\Fields\Field;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Switcher;

#[Icon('users')]
class UserResource extends BaseResource
{
    use WithRoleFormComponent;

    //WARN: We modify getRoles method in vendor\sweet1s\moonshine-roles-permissions\src\FormComponents\RoleFormComponent.php to have role description
    // TODO: Teachers can only see their own students
    // TODO: Simplify role, entry, class assignments
    // TODO: What will happen to general role management? Permissions check general roles.

    protected string $model = User::class;

    protected string $column = 'username';

    protected $guard_name = "moonshine";

    public function getTitle(): string
    {
        return __('admin.users');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.username'), 'username')
                ->sortable(),

            Text::make(__('admin.name_surname'), 'name')
                ->sortable(),

            Text::make(__('admin.user_title'), 'title')
                ->sortable(),

            Email::make(__('admin.email'), 'email')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.username'), 'username')
                        ->required()
                        ->placeholder(__('admin.enter_username')),

                    Text::make(__('admin.name_surname'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name_surname')),

                    Text::make(__('admin.user_title'), 'title')
                        ->placeholder(__('admin.enter_user_title')),

                    Email::make(__('admin.email'), 'email')
                        ->required()
                        ->placeholder(__('admin.enter_email')),
                ]),

                Flex::make([
                    Password::make(__('admin.password'), 'password')
                        ->customAttributes(['autocomplete' => 'new-password'])
                        ->required($this->isCreateFormPage()),

                    PasswordRepeat::make(__('admin.password_repeat'), 'password_confirmation')
                        ->customAttributes(['autocomplete' => 'confirm-password'])
                        ->required($this->isCreateFormPage()),
                ]),
            ]),
            Box::make([
                RelationRepeater::make(__('admin.school_assignments'), 'userSchools', resource: UserSchoolResource::class)
                    ->removable()
                    ->fields([
                        ID::make(),
                        BelongsTo::make(
                            __('admin.school'),
                            'school',
                            formatted: fn(School $org) => $org->name,
                            resource: SchoolResource::class
                        ),
                        BelongsTo::make(
                            __('admin.role'),
                            'role',
                            formatted: fn(Role $role) => $role->description,
                            resource: RoleResource::class
                        ),
                        Switcher::make(__('admin.default'), 'default')
                            ->default(false)
                    ]),
            ]),
            Box::make([
                RelationRepeater::make(__('admin.class_assignments'), 'userClasses', resource: UserClassResource::class)
                    ->removable()
                    ->fields([
                        ID::make(),
                        BelongsTo::make(
                            __('admin.school'),
                            'school',
                            formatted: fn(School $org) => $org->name,
                            resource: SchoolResource::class
                        ),
                        BelongsTo::make(
                            __('admin.class'),
                            'schoolClass',
                            formatted: fn(SchoolClass $class) => $class->name,
                            resource: SchoolClassResource::class
                        )                        
                            ->asyncSearch(
                                'name',
                                searchQuery: function (Builder $query, Request $request, Field $field): Builder {
                                    return $query->where('school_id', $request->get('school.id'));
                                }
                            ),
                        Switcher::make(__('admin.default'), 'default')
                            ->default(false),
                    ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.username'), 'username'),
            Text::make(__('admin.name_surname'), 'name'),
            Text::make(__('admin.user_title'), 'title'),
            Email::make(__('admin.email'), 'email'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'username' => ['required', 'string', 'max:255'],
            'name' => ['required', 'string', 'max:255'],
            'title' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $item?->id],
            ...parent::getCommonRules($item),
        ];

        // Password rules
        if (!$item) { // Creating new user
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
        } elseif (request()->filled('password')) {
            $rules['password'] = ['string', 'min:8', 'confirmed'];
        }

        return $rules;
    }

    protected function search(): array
    {
        return ['username', 'name', 'email'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        // Cast to concrete Eloquent Builder to access query methods
        if (!$builder instanceof EloquentBuilder) {
            return $builder;
        }

        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all users
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see users in their assigned schools
        if ($user->isSchoolAdmin()) {
            $schoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($schoolIds)) {
                // No schools assigned - only access to themselves
                return $builder->where('id', $user->id);
            }

            // Access users who have active assignments in the admin's schools
            return $builder->where(function ($q) use ($schoolIds, $user) {
                $q->where('id', $user->id) // Include themselves
                  ->orWhereHas('activeUserSchools', function ($subQuery) use ($schoolIds) {
                      $subQuery->whereIn('school_id', $schoolIds);
                  })
                  ->orWhereHas('activeUserClasses', function ($subQuery) use ($schoolIds) {
                      $subQuery->whereIn('school_id', $schoolIds);
                  });
            });
        }

        // Teachers can see students in their assigned classes
        if ($user->isTeacher()) {
            $classIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($classIds)) {
                // No classes assigned - only access to themselves
                return $builder->where('id', $user->id);
            }

            // Access students who have active assignments in the teacher's classes
            return $builder->where(function ($q) use ($classIds, $user) {
                $q->where('id', $user->id) // Include themselves
                  ->orWhere(function ($subQuery) use ($classIds) {
                      $subQuery->whereHas('activeUserClasses', function ($classQuery) use ($classIds) {
                          $classQuery->whereIn('class_id', $classIds);
                      })
                      ->whereHas('roles', function ($roleQuery) {
                          $roleQuery->where('name', 'student');
                      });
                  });
            });
        }

        // Parents can access their own children (students)
        if ($user->isParent()) {
            // For now, parents can only access themselves
            return $builder->where('id', $user->id);
        }

        // Students can only access themselves
        if ($user->isStudent()) {
            return $builder->where('id', $user->id);
        }

        // Unknown role or no role - no access
        return $builder->where('id', 0);
    }
}
