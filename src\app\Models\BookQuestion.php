<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BookQuestion extends BaseModel
{
    /**
     * Difficulty level constants.
     */
    const DIFFICULTY_EASY = 'easy';
    const DIFFICULTY_MEDIUM = 'medium';
    const DIFFICULTY_HARD = 'hard';

    /**
     * The table associated with the model.
     */
    protected $table = 'book_questions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'book_id',
        'question_text',
        'question_image_url',
        'question_audio_url',
        'question_video_url',
        'correct_answer',
        'incorrect_answer_1',
        'incorrect_answer_2',
        'incorrect_answer_3',
        'incorrect_answer_4',
        'incorrect_answer_5',
        'page_start',
        'page_end',
        'difficulty_level',
        'is_active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'page_start' => 'integer',
            'page_end' => 'integer',
        ];
    }

    /**
     * Get all difficulty levels.
     */
    public static function getDifficultyLevels(): array
    {
        return [
            self::DIFFICULTY_EASY => 'Easy',
            self::DIFFICULTY_MEDIUM => 'Medium',
            self::DIFFICULTY_HARD => 'Hard',
        ];
    }

    /**
     * Get difficulty level name.
     */
    public function getDifficultyLevelNameAttribute(): string
    {
        return self::getDifficultyLevels()[$this->difficulty_level] ?? 'Unknown';
    }

    /**
     * Get the book this question belongs to.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the user who created this question.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

  
    /**
     * Scope to get active questions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by difficulty level.
     */
    public function scopeOfDifficulty($query, string $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeForBook($query, int $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by page range.
     */
    public function scopeInPageRange($query, ?int $startPage = null, ?int $endPage = null)
    {
        if ($startPage && $endPage) {
            return $query->where(function($q) use ($startPage, $endPage) {
                $q->where(function($subQ) use ($startPage, $endPage) {
                    // Question covers the specified range
                    $subQ->where('page_start', '<=', $startPage)
                         ->where('page_end', '>=', $endPage);
                })->orWhere(function($subQ) use ($startPage, $endPage) {
                    // Question overlaps with the specified range
                    $subQ->where('page_start', '<=', $endPage)
                         ->where('page_end', '>=', $startPage);
                })->orWhere(function($subQ) {
                    // Questions without page restrictions
                    $subQ->whereNull('page_start')
                         ->whereNull('page_end');
                });
            });
        }
        
        return $query;
    }

    /**
     * Get all answer options as an array.
     */
    public function getAnswerOptionsAttribute(): array
    {
        $options = [$this->correct_answer];
        
        for ($i = 1; $i <= 5; $i++) {
            $incorrectAnswer = $this->{"incorrect_answer_$i"};
            if (!empty($incorrectAnswer)) {
                $options[] = $incorrectAnswer;
            }
        }
        
        return $options;
    }


    /**
     * Check if an answer is correct.
     */
    public function isCorrectAnswer(string $answer): bool
    {
        return trim($answer) === trim($this->correct_answer);
    }

    /**
     * Get page range display.
     */
    public function getPageRangeAttribute(): ?string
    {
        if ($this->page_start && $this->page_end) {
            if ($this->page_start === $this->page_end) {
                return "Page {$this->page_start}";
            }
            return "Pages {$this->page_start}-{$this->page_end}";
        } elseif ($this->page_start) {
            return "Page {$this->page_start}+";
        }
        return __('admin.not_specified');
    }

    /**
     * Check if question has media content.
     */
/*    
    public function hasMedia(): bool
    {
        return !empty($this->question_image_url) || 
               !empty($this->question_audio_url) || 
               !empty($this->question_video_url);
    }
*/
    /**
     * Get media type.
     */
/*    
    public function getMediaTypeAttribute(): ?string
    {
        if (!empty($this->question_video_url)) return 'video';
        if (!empty($this->question_audio_url)) return 'audio';
        if (!empty($this->question_image_url)) return 'image';
        return null;
    }
*/
    /**
     * Get primary media URL.
     */
/*    
    public function getPrimaryMediaUrlAttribute(): ?string
    {
        return $this->question_video_url ?? 
               $this->question_audio_url ?? 
               $this->question_image_url;
    }
               */
}
