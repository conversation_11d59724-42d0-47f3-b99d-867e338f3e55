<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('class_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('class_id')->constrained('school_classes')->onDelete('cascade');
            $table->foreignId('activity_id')->constrained('activities')->onDelete('cascade');
            
            // Test Activity Settings (overrides Activity defaults)
            $table->integer('question_count')->nullable()->comment('Number of questions for quiz/vocabulary tests (1-50)');
            $table->integer('min_grade')->nullable()->comment('Minimum passing grade 0-100 for tests');
            $table->integer('allowed_tries')->nullable()->comment('Maximum retry attempts for test activities');
            
            // Writing Activity Settings
            $table->integer('min_word_count')->nullable()->comment('Minimum word count for writing activities');
            
            // General Activity Settings
            $table->integer('points')->nullable()->comment('Activity points for this class');
            $table->boolean('required')->default(false)->comment('Whether activity is required for book completion');
            $table->boolean('need_approval')->default(false)->comment('Whether activity needs teacher approval');
            $table->boolean('active')->default(true)->comment('Whether activity is enabled for this class');
            
            // Indexes and constraints
            $table->unique(['class_id', 'activity_id'], 'class_activities_unique');
            $table->index(['class_id', 'active']);
            $table->index(['activity_id', 'active']);
            $table->index('active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('class_activities');
    }
};
