<?php

namespace App\Services;

use App\Models\{User<PERSON><PERSON><PERSON>, User<PERSON><PERSON><PERSON>, TeamReward, Team};
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * Service for handling mobile reward display logic.
 * 
 * This service provides unified reward checking and display preparation
 * for both reading logs and activity completions on mobile interface.
 */
class MobileRewardDisplayService
{
    /**
     * Check for newly unlocked rewards and levels after a user action.
     * 
     * @param int|null $readingLogId Reading log ID that triggered the check
     * @param int|null $userActivityId User activity ID that triggered the check
     * @param bool $bookCompleted Whether a book was completed
     * @return array|null Redirect information if rewards were found, null otherwise
     */
    public function checkForRewards(?int $readingLogId = null, ?int $userActivityId = null, bool $bookCompleted = false): ?array
    {
        $userId = Auth::id();
        
        // Check for newly awarded individual rewards
        $recentRewards = $this->getRecentUserRewards($userId, $readingLogId, $userActivityId);
        
        // Check for newly awarded team rewards
// 05.10.2025 review team awarding logic again        
        $recentTeamRewards = collect();
//        $recentTeamRewards = $this->getRecentTeamRewards($userId, $readingLogId, $userActivityId);
        
        // Check for newly achieved levels (only from reading logs)
        $recentLevels = $this->getRecentUserLevels($userId, $readingLogId);

        // Check for newly unlocked avatars (from activity completions)
        $recentAvatars = $this->getRecentUnlockedAvatars($userId, $userActivityId);

        // If any rewards, levels, or avatars were found, prepare for display
        if ($recentRewards->count() > 0 || $recentTeamRewards->count() > 0 || $recentLevels->count() > 0 || $recentAvatars->count() > 0) {
            return $this->prepareRewardDisplay($recentRewards, $recentTeamRewards, $recentLevels, $recentAvatars, $bookCompleted, $readingLogId, $userActivityId);
        }

        return null;
    }

    /**
     * Get recently awarded individual rewards for the user.
     */
    private function getRecentUserRewards(int $userId, ?int $readingLogId, ?int $userActivityId)
    {
        $query = UserReward::where('user_id', $userId);
        
        if ($readingLogId) {
            $query->where('reading_log_id', $readingLogId);
        }
        
        if ($userActivityId) {
            $query->where('user_activity_id', $userActivityId);
        }
        
        return $query->with('reward:id,name,description,image,reward_type')->get();
    }

    /**
     * Get recently awarded team rewards for teams the user belongs to.
     */
    private function getRecentTeamRewards(int $userId, ?int $readingLogId, ?int $userActivityId)
    {
        // Get all active teams the user belongs to
        $userTeamIds = Team::whereHas('users', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('active', true)->pluck('id');

        if ($userTeamIds->isEmpty()) {
            return collect();
        }

        $query = TeamReward::whereIn('team_id', $userTeamIds);
        
        if ($readingLogId) {
            $query->where('reading_log_id', $readingLogId);
        }
        
        if ($userActivityId) {
            $query->where('user_activity_id', $userActivityId);
        }
        
        return $query->with(['reward:id,name,description,image,reward_type', 'team:id,name'])
                    ->get();
    }

    /**
     * Get recently achieved levels for the user.
     */
    private function getRecentUserLevels(int $userId, ?int $readingLogId)
    {
        // levels are only achieved when reading logs are created/updated,
        if ($readingLogId) {
            $query = UserLevel::where('user_id', $userId);
            $query->where('reading_log_id', $readingLogId);
            return $query->with('level:id,nr,name,image')->get();
        }
        else {
            return collect();
        }
    }

    /**
     * Get recently unlocked avatars for the user.
     */
    private function getRecentUnlockedAvatars(int $userId, ?int $userActivityId)
    {
        // Only check for avatar unlocking when an activity was completed
        if (!$userActivityId) {
            return collect();
        }

        // Get the user activity that triggered this check
        $userActivity = \App\Models\UserActivity::find($userActivityId);
        if (!$userActivity) {
            return collect();
        }

        // Get user's current activity points (which may already include this activity's points)
        $currentActivityPoints = $userActivity->user->getActivityPoints();

        // Get activity points before this activity was completed
        $activityPointsValue = $userActivity->getActivityPointsValue();
        $previousActivityPoints = $currentActivityPoints - $activityPointsValue;

        // Get avatars that would be unlocked with the current total but weren't unlocked before
        $newlyUnlockedAvatars = \App\Models\Avatar::where('required_points', '>', $previousActivityPoints)
            ->where('required_points', '<=', $currentActivityPoints)
            ->where('active', true)
            ->orderBy('required_points', 'asc')
            ->get();

        return $newlyUnlockedAvatars;
    }

    /**
     * Prepare reward display data and session variables.
     */
    private function prepareRewardDisplay($recentRewards, $recentTeamRewards, $recentLevels, $recentAvatars, bool $bookCompleted, ?int $readingLogId, ?int $userActivityId): array
    {
        // Store individual rewards in session
        if ($recentRewards->count() > 0) {
            session(['unlocked_rewards' => $recentRewards->pluck('id')->toArray()]);
        }

        // Store team rewards in session
        if ($recentTeamRewards->count() > 0) {
            session(['unlocked_team_rewards' => $recentTeamRewards->pluck('id')->toArray()]);
        }

        // Store levels in session
        if ($recentLevels->count() > 0) {
            session(['achieved_levels' => $recentLevels->pluck('id')->toArray()]);
        }

        // Store avatars in session
        if ($recentAvatars->count() > 0) {
            session(['unlocked_avatars' => $recentAvatars->pluck('id')->toArray()]);
        }

        // Log reward display for debugging
        Log::info('Mobile rewards prepared for display', [
            'user_id' => Auth::id(),
            'individual_rewards' => $recentRewards->count(),
            'team_rewards' => $recentTeamRewards->count(),
            'levels' => $recentLevels->count(),
            'avatars' => $recentAvatars->count(),
            'reading_log_id' => $readingLogId,
            'user_activity_id' => $userActivityId,
            'book_completed' => $bookCompleted,
        ]);

        return [
            'redirect_to_celebration' => true,
            'book_completed' => $bookCompleted,
            'reading_log_id' => $readingLogId,
            'user_activity_id' => $userActivityId,
        ];
    }

    /**
     * Set redirect route after reward celebration.
     */
    public function setRedirectRoute(string $route, array $params = []): void
    {
        session([
            'reward_redirect_route' => $route,
            'reward_redirect_params' => $params
        ]);
    }

    /**
     * Get redirect route information from session.
     */
    public function getRedirectRoute(): array
    {
        return [
            'route' => session('reward_redirect_route', 'mobile.home'),
            'params' => session('reward_redirect_params', [])
        ];
    }

    /**
     * Check for all recent rewards/levels for a user within the last few minutes.
     * This method is used to capture both activity-related and retroactive rewards.
     */
    public function checkForAllRecentRewards(?int $userActivityId = null): ?array
    {
        $userId = Auth::id();
        $timeThreshold = now()->subMinutes(2); // Look for rewards/levels awarded in last 2 minutes

        // Get all recent individual rewards (regardless of source)
        $recentRewards = UserReward::where('user_id', $userId)
            ->where('awarded_date', '>=', $timeThreshold)
            ->with('reward:id,name,description,image,reward_type')
            ->get();

        // Get all recent team rewards (regardless of source)
        // disabled for now
        $recentTeamRewards = collect();
/*        
        $userTeamIds = \App\Models\Team::whereHas('users', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->where('active', true)->pluck('id');

        if ($userTeamIds->isNotEmpty()) {
            $recentTeamRewards = \App\Models\TeamReward::whereIn('team_id', $userTeamIds)
                ->where('awarded_date', '>=', $timeThreshold)
                ->with(['reward:id,name,description,image,reward_type', 'team:id,name'])
                ->get();
        }
*/
        // Get all recent levels (regardless of source)
        $recentLevels = \App\Models\UserLevel::where('user_id', $userId)
            ->where('level_date', '>=', $timeThreshold)
            ->with('level:id,nr,name,image')
            ->get();

        // Get all recent avatars (from activity completions)
        $recentAvatars = $this->getRecentUnlockedAvatars($userId, $userActivityId);

        // If any rewards, levels, or avatars were found, prepare for display
        if ($recentRewards->count() > 0 || $recentTeamRewards->count() > 0 || $recentLevels->count() > 0 || $recentAvatars->count() > 0) {
            return $this->prepareRewardDisplay($recentRewards, $recentTeamRewards, $recentLevels, $recentAvatars, false, null, $userActivityId);

//        if ($recentRewards->count() > 0 || $recentTeamRewards->count() > 0 || $recentLevels->count() > 0 || $recentAvatars->count() > 0) {
//            return $this->prepareRewardDisplay($recentRewards, $recentTeamRewards, $recentLevels, $recentAvatars, false, null, $userActivityId);
        }

        return null;
    }

    /**
     * Clear all reward-related session data.
     */
    public function clearRewardSession(): void
    {
        session()->forget([
            'unlocked_rewards',
            'unlocked_team_rewards',
            'achieved_levels',
            'unlocked_avatars',
            'reward_redirect_route',
            'reward_redirect_params'
        ]);
    }
}
