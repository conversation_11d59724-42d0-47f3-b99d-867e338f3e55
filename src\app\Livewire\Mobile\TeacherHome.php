<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Services\StatisticsService;

class TeacherHome extends Component
{
    public $user;
    public $greeting;
    public $avatarImage;
    public $unreadMessagesCount = 0;
    
    // Statistics
    public $stats = [];
    public $recentActivities = [];
    public $pendingActivities = [];
    
    public function mount()
    {
        $this->user = Auth::user();
        
        // Ensure user is a teacher
        if (!$this->user->isTeacher()) {
            return redirect()->route('mobile.home');
        }
        
        $this->greeting = StatisticsService::getTimeBasedGreeting();
        $this->avatarImage = $this->user->avatar;
        $this->loadTeacherStats();
        $this->loadRecentActivities();
        $this->loadPendingActivities();
        $this->loadUnreadMessagesCount();
    }
    

    
    /**
     * Load teacher-specific statistics for the last 24 hours.
     */
    private function loadTeacherStats()
    {
        $this->stats = StatisticsService::getTeacherLast24HourStats($this->user);
    }
    
    /**
     * Load recent student book activities (last 3 for preview).
     */
    private function loadRecentActivities()
    {
        $this->recentActivities = StatisticsService::getRecentBookActivities($this->user, 3);
    }
    
    /**
     * Load pending activities that require teacher review.
     */
    private function loadPendingActivities()
    {
        $this->pendingActivities = StatisticsService::getPendingActivities($this->user, 10);
    }
    

    
    /**
     * Load unread messages count.
     */
    private function loadUnreadMessagesCount()
    {
        // This would be implemented based on the existing message system
        // For now, set to 0
        $this->unreadMessagesCount = 0;
    }

    public function logout()
    {
        Auth::logout();
        session()->invalidate();
        session()->regenerateToken();

        return redirect()->route('mobile.login');
    }
    
    public function render()
    {
        return view('livewire.mobile.teacher-home');
    }
}
