<x-layouts.mobile title="{{ __('mobile.welcome') }} - {{ __('mobile.app_name') }}">
    <div class="min-h-screen bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600 flex items-center justify-center p-4 relative overflow-hidden">
        <!-- Confetti Container -->
        <div id="confetti-container" class="mobile-confetti"></div>
        
        <!-- Celebration Content -->
        <div class="text-center z-10">
            <!-- Stars Animation -->
            <div class="relative mb-8">
                <div class="absolute -top-4 -left-4 w-8 h-8 text-yellow-300 animate-bounce" style="animation-delay: 0s;">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div class="absolute -top-2 -right-6 w-6 h-6 text-yellow-300 animate-bounce" style="animation-delay: 0.5s;">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div class="absolute -bottom-2 -left-2 w-10 h-10 text-yellow-300 animate-bounce" style="animation-delay: 1s;">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div class="absolute -bottom-4 -right-4 w-7 h-7 text-yellow-300 animate-bounce" style="animation-delay: 1.5s;">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
            </div>

            <!-- Congratulations Text -->
            <h1 class="text-2xl font-bold text-white mb-4">{{ __('mobile.congratulations') }}</h1>
            <h2 class="text-4xl font-bold text-white mb-6">{{ Auth::user()->name }}!</h2>
            <p class="text-xl text-white opacity-90 mb-12">{{ __('mobile.all_set') }}</p>

            <!-- Start Button -->
            <a href="{{ route('mobile.home') }}" class="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-12 rounded-2xl text-xl shadow-2xl transform transition-all duration-200 hover:scale-105">
                {{ __('mobile.start') }}
            </a>
        </div>

        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <!-- Floating particles -->
            @for($i = 0; $i < 20; $i++)
                <div class="absolute w-2 h-2 bg-white opacity-30 rounded-full animate-pulse" 
                     style="left: {{ rand(0, 100) }}%; top: {{ rand(0, 100) }}%; animation-delay: {{ rand(0, 3000) }}ms; animation-duration: {{ rand(2000, 4000) }}ms;"></div>
            @endfor
        </div>
    </div>

    <x-slot name="scripts">
        <script>
            // Trigger confetti animation on page load
            document.addEventListener('DOMContentLoaded', function() {
                const showConfettiWhenReady = () => {
                    if (typeof MobileApp !== 'undefined' && MobileApp.showConfetti) {
                        // Initial confetti burst
                        MobileApp.showConfetti();

                        // Additional confetti bursts
                        setTimeout(() => MobileApp.showConfetti(), 1000);
                        setTimeout(() => MobileApp.showConfetti(), 2000);
                    } else {
                        // Retry after a short delay if MobileApp is not ready
                        setTimeout(showConfettiWhenReady, 100);
                    }
                };

                showConfettiWhenReady();
            });
        </script>
    </x-slot>
</x-layouts.mobile>
