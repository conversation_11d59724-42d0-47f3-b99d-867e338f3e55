<div class="min-h-screen bg-base-200">
    <x-mobile-page-header route="{{ route('mobile.books.activities', $book->id) }}" header="⭐ {{ __('mobile.rating_activity_title') }}" />

    <div class="p-4">
        <!-- Activity Info -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
            <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-yellow-100 rounded-2xl flex items-center justify-center">
                    <span class="text-3xl">⭐</span>
                </div>

                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-1">{{ $activity->name }}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ $activity->description }}</p>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="mobile-badge">{{ $activity->points }} {{ __('mobile.points') }}</span>
                        <span class="text-gray-500">📖 {!! $book->name !!}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <!-- Rating Form -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <form wire:submit="submitActivity" class="space-y-6">
                <!-- Star Rating -->
                <div class="text-center">
                    <h4 class="font-semibold text-gray-900 mb-2"> {{ __('mobile.how_rate_book') }}</h4>
                    <p class="text-sm text-gray-600 mb-4">
                        {{ __('mobile.rate_from_min_to_max_stars', ['min' => $activity->min_rating ?? 1, 'max' => $activity->max_rating ?? 5]) }}
                    </p>
                    @php
                        $minRating = $activity->min_rating ?? 1;
                        $maxRating = $activity->max_rating ?? 5;
                        $totalStars = $maxRating - $minRating + 1;
                        $starSize = $totalStars > 5 ? 'text-4xl' : 'text-5xl';
                        $spacing = $totalStars > 7 ? 'space-x-1' : 'space-x-2';
                    @endphp
                    <div class="flex justify-center {{ $spacing }} mb-4 flex-wrap">
                        @for($i = $minRating; $i <= $maxRating; $i++)
                            <button
                                type="button"
                                wire:click="setRating({{ $i }})"
                                class="{{ $starSize }} transition-all duration-200 {{ $rating >= $i ? 'text-yellow-400' : 'text-gray-300' }} hover:text-yellow-400 touch-manipulation"
                                @if($mode === 'view') disabled @endif
                            >
                                ★
                            </button>
                        @endfor
                    </div>

                    <p class="text-gray-500">{{ __('mobile.tap_stars_to_rate') }}</p>

                    @error('rating')
                        <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Comment -->
                <div>
                    <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('mobile.why_rating') }}
                    </label>
                    <textarea
                        id="comment"
                        wire:model="comment"
                        class="mobile-input @error('comment') border-red-500 @enderror"
                        placeholder=" {{ __('mobile.what_like_dislike') }}"
                        rows="4"
                        maxlength="500"
                    ></textarea>
                    @error('comment')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">{{ strlen($comment) }}/500 {{ __('mobile.characters') }}</p>
                </div>

                <!-- Submit Button -->
                @if($mode !== 'view')
                    <button
                        type="submit"
                        class="mobile-button {{ $isLoading || $rating == 0 ? 'opacity-50 cursor-not-allowed' : '' }}"
                        wire:loading.attr="disabled"
                        {{ $rating == 0 ? 'disabled' : '' }}
                    >
                        <span wire:loading.remove>
                            @if($mode === 'edit')
                                🔄 {{ __('mobile.update_rating') }}
                            @else
                                ⭐ {{ __('mobile.submit_rating') }}
                            @endif
                        </span>
                    <span wire:loading class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                            {{ $mode === 'edit' ? __('mobile.updating') : __('mobile.submitting') }}
                    </span>
                    </button>

                    @if($rating == 0)
                        <p class="text-sm text-gray-500 text-center">
                            {{ __('mobile.select_rating_to_submit') }}
                        </p>
                    @endif
                @endif
            </form>
        </div>
    </div>
</div>
