<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class Avatar extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'base_image',
        'happy_image',
        'sad_image',
        'sleepy_image',
        'required_points',
        'active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
        ];
    }

    /**
     * Scope to get active avatars.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to search avatars by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
            ->orWhere('description', 'like', '%' . $search . '%');
    }

    /**
     * Get the display image (base_image).
     */
    public function getDisplayImageAttribute(): string
    {
        return $this->base_image;
    }

    /**
     * Get users who have selected this avatar.
     */
    public function userAvatars(): HasMany
    {
        return $this->hasMany(UserAvatar::class);
    }

    /**
     * Get users who currently have this avatar selected.
     */
    public function users(): HasMany
    {
        return $this->hasMany(UserAvatar::class);
    }

    /**
     * Get the count of users who have selected this avatar.
     */
    public function getUserCountAttribute(): int
    {
        return $this->userAvatars()->count();
    }

    /**
     * Check if this avatar is unlocked for a specific user.
     */
    public function isUnlockedForUser($userId): bool
    {
        $user = User::find($userId);
        return $user ? $user->canSelectAvatar($this->id) : false;
    }
}
