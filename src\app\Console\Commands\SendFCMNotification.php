<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\SchoolClass;
use App\Models\Team;
use App\Services\FCMService;
use Illuminate\Console\Command;

class SendFCMNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fcm:send
                            {recipient : User ID, "class:ID", "team:ID", or "all"}
                            {title : Notification title}
                            {body : Notification body}
                            {--type=admin_broadcast : Notification type}
                            {--url= : Deep link URL}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send FCM push notification to users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $fcmService = app(FCMService::class);

        $recipient = $this->argument('recipient');
        $title = $this->argument('title');
        $body = $this->argument('body');
        $type = $this->option('type');
        $url = $this->option('url');

        try {
            if ($recipient === 'all') {
                // Send to all users with FCM tokens
                $users = User::whereNotNull('fcm_token')->get();
                $results = $fcmService->sendToUsers($users, $title, $body, $type, $url);
                $successCount = array_sum($results);
                $this->info("Sent notification to {$successCount} out of {$users->count()} users");

            } elseif (str_starts_with($recipient, 'class:')) {
                // Send to all students in a class
                $classId = substr($recipient, 6);
                $class = SchoolClass::findOrFail($classId);
                $results = $fcmService->sendToClass($class, $title, $body, $type, $url);
                $successCount = array_sum($results);
                $this->info("Sent notification to {$successCount} students in class '{$class->name}'");

            } elseif (str_starts_with($recipient, 'team:')) {
                // Send to all members of a team
                $teamId = substr($recipient, 5);
                $team = Team::findOrFail($teamId);
                $results = $fcmService->sendToTeam($team, $title, $body, $type, $url);
                $successCount = array_sum($results);
                $this->info("Sent notification to {$successCount} members of team '{$team->name}'");

            } else {
                // Send to individual user
                $user = User::findOrFail($recipient);
                $success = $fcmService->sendToUser($user, $title, $body, $type, $url);

                if ($success) {
                    $this->info("Notification sent successfully to user '{$user->name}'");
                } else {
                    $this->error("Failed to send notification to user '{$user->name}' (no FCM token or send failed)");
                }
            }

        } catch (\Exception $e) {
            $this->error("Error sending notification: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
