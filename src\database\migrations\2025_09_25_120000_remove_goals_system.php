<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {   // Drop goal-related tables in reverse dependency order
        // These tables may not exist if the goal system was never fully implemented

        Schema::table('user_books', function (Blueprint $table) {
            $table->dropForeign(['goal_task_id']);
        });

        Schema::table('user_reading_logs', function (Blueprint $table) {
            $table->dropForeign(['goal_task_id']);
        });

        Schema::table('user_activities', function (Blueprint $table) {
            $table->dropForeign(['goal_task_id']);
        });

        Schema::table('user_tasks', function (Blueprint $table) {
            $table->dropForeign(['user_goal_id']);
            $table->dropForeign(['goal_task_id']);
        });

        Schema::dropIfExists('user_goals');
        Schema::dropIfExists('goal_tasks');
        Schema::dropIfExists('goals');
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate goals table
        Schema::create('goals', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('motto')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();

            // Indexes for performance
            $table->index('name');
            $table->index('active');
            $table->index(['active', 'name']);
        });

        // Recreate goal_tasks table
        Schema::create('goal_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('goal_id')->constrained('goals')->onDelete('cascade');
            $table->foreignId('task_id')->constrained('tasks')->onDelete('cascade');
            $table->date('start_date');
            $table->date('end_date');
            $table->timestamps();

            // Indexes for performance
            $table->index(['goal_id', 'task_id']);
            $table->index(['start_date', 'end_date']);
            $table->index(['goal_id', 'start_date', 'end_date']);
            
            // Unique constraint to prevent duplicate task assignments to same goal
            $table->unique(['goal_id', 'task_id', 'start_date', 'end_date']);
        });

        // Recreate user_goals table
        Schema::create('user_goals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('goal_id')->constrained('goals')->onDelete('cascade');
            $table->foreignId('team_id')->nullable()->constrained('teams')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('assign_date')->nullable();
            $table->text('comment')->nullable();
            $table->boolean('achieved')->default(false);
            $table->timestamp('achieve_date')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['goal_id', 'user_id']);
            $table->index(['goal_id', 'team_id']);
            $table->index(['user_id', 'achieved']);
            $table->index(['team_id', 'achieved']);
            $table->index(['assigned_by']);
            $table->index(['assign_date']);
            $table->index(['achieve_date']);
            
            // Unique constraint to prevent duplicate assignments
            $table->unique(['goal_id', 'user_id'], 'unique_user_goal');
            $table->unique(['goal_id', 'team_id'], 'unique_team_goal');
        });

        // Restore foreign key columns to existing tables
        Schema::table('user_books', function (Blueprint $table) {
            $table->foreignId('goal_task_id')->nullable()->constrained('goal_tasks')->onDelete('set null');
        });

        Schema::table('user_reading_logs', function (Blueprint $table) {
            $table->foreignId('goal_task_id')->nullable()->constrained('goal_tasks')->onDelete('set null');
        });

        Schema::table('user_activities', function (Blueprint $table) {
            $table->foreignId('goal_task_id')->nullable()->constrained('goal_tasks')->onDelete('set null');
        });

        Schema::table('user_tasks', function (Blueprint $table) {
            $table->foreignId('user_goal_id')->nullable()->constrained('user_goals')->onDelete('cascade');
            $table->foreignId('goal_task_id')->nullable()->constrained('goal_tasks')->onDelete('cascade');
        });
    }
};
