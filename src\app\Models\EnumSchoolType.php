<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EnumSchoolType extends BaseModel
{
    use HasFactory;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get schools that have this type.
     */
    public function schools(): HasMany
    {
        return $this->hasMany(School::class, 'school_type_id');
    }
}
