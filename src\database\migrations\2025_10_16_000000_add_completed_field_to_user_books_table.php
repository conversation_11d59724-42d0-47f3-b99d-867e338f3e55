<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\UserBook;
use App\Models\Activity;
use App\Models\UserActivity;
use App\Models\BookQuestion;
use App\Models\BookWord;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         // Step 1: Add the completed field with indexes
        Schema::table('user_books', function (Blueprint $table) {
            // Add completed boolean field with default false
            $table->boolean('completed')->default(false)->after('end_date');

            // Add index for performance on completed field queries
            $table->index('completed');
            $table->index(['user_id', 'completed']);
            $table->index(['book_id', 'completed']);
        });

        // Step 2: Migrate existing data - set completed=true for eligible records
        $this->migrateExistingData();
    }

    /**
     * Migrate existing UserBook records to set completed=true for eligible records.
     * A UserBook is eligible if:
     * - It has an end_date (finished reading), AND
     * - Either no required activities exist, OR all required activities are completed
     */
    private function migrateExistingData(): void
    {
       $userBooksToCheck = UserBook::whereNotNull('end_date')
            ->where('completed', false)
            ->with(['user', 'book'])
            ->get();

        if ($userBooksToCheck->isEmpty()) {
            echo('No UserBook records need to be migrated.');
            return;
        }

        echo("Found {$userBooksToCheck->count()} UserBook records to check...");

        // Process in chunks to avoid memory issues
        $userBooksToCheck->chunk(100)->each(function ($chunk) {
            foreach ($chunk as $userBook) {
                if ($this->shouldMarkAsCompleted($userBook->user_id, $userBook->book_id)) {
                    $userBook->update(['completed' => true]);
                }
            }
        });

        echo "Data migration completed.\n";
    }

    /**
     * Check if a UserBook should be marked as completed based on required activities logic.
     * Replicates the logic from UserReadingLog::allRequiredActivitiesCompleted()
     * Uses class-specific activity resolution to determine proper required status.
     */
     private function shouldMarkAsCompleted(int $userId, int $bookId): bool
    {
        // Get user's default class for class-specific activity resolution
        $user = \App\Models\User::find($userId);
        if (!$user) {
            return false;
        }

        $defaultClass = $user->getDefaultClass();

        // Get all active activities with class-specific resolution for required status
        $activitiesQuery = Activity::query()
            ->where('activities.active', true);

        if ($defaultClass) {
            // Apply class-specific resolution using LEFT JOIN and COALESCE
            $activitiesQuery->leftJoin('class_activities', function ($join) use ($defaultClass) {
                $join->on('activities.id', '=', 'class_activities.activity_id')
                     ->where('class_activities.class_id', '=', $defaultClass->class_id);
            })
            ->select([
                'activities.*',
                DB::raw('COALESCE(class_activities.required, activities.required) as required'),
                DB::raw('COALESCE(class_activities.min_grade, activities.min_grade) as min_grade'),
                DB::raw('COALESCE(class_activities.active, activities.active) as resolved_active'),
            ]);
        }

        $requiredActivities = $activitiesQuery->get()
            ->where('required', true)
            ->where('resolved_active', true);

        if ($requiredActivities->isEmpty()) {
            // No required activities exist, so book can be marked as completed
            return true;
        }

        foreach ($requiredActivities as $activity) {
            // For quiz and vocabulary test activities, check if book has sufficient content
            if ($activity->isTestActivity()) {
                if (!$this->bookHasEnoughContentForActivity($bookId, $activity)) {
                    // Skip this activity if book doesn't have enough content
                    continue;
                }
            }

            // Check if user has completed this activity for this book
            $userActivity = UserActivity::where('user_id', $userId)
                ->where('book_id', $bookId)
                ->where('activity_id', $activity->id)
                ->whereIn('status', [UserActivity::STATUS_APPROVED, UserActivity::STATUS_COMPLETED])
                ->first();

            if (!$userActivity) {
                // Required activity not completed
                return false;
            }

            // For test activities, check if they passed the minimum grade
            if ($activity->isTestActivity() && $activity->min_grade) {
                $content = is_string($userActivity->content) ? json_decode($userActivity->content, true) : $userActivity->content;
                $score = $content['score_percentage'] ?? 0;

                if ($score < $activity->min_grade) {
                    // User didn't pass the minimum grade
                    return false;
                }
            }
        }

        return true;
    }

   
    /**
     * Check if book has enough content for a test activity.
     */
    private function bookHasEnoughContentForActivity(int $bookId, Activity $activity): bool
    {
        if ($activity->isQuiz()) {
            // Check if book has at least 10 questions
            $questionCount = BookQuestion::where('book_id', $bookId)
                ->where('is_active', true)
                ->count();
            return $questionCount >= 10;
        }

        if ($activity->isVocabularyTest()) {
            // Check if book has at least 10 words
            $wordCount = BookWord::where('book_id', $bookId)
                ->where('is_active', true)
                ->count();
            return $wordCount >= 10;
        }

        return true;
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_books', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['book_id', 'completed']);
            $table->dropIndex(['user_id', 'completed']);
            $table->dropIndex(['completed']);
            
            // Drop the completed column
            $table->dropColumn('completed');
        });
    }
};
