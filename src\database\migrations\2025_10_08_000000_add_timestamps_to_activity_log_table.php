<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/**
 * Add timestamps to the activity_log table.
 * 
 * The Spatie ActivityLog package requires created_at and updated_at columns
 * for proper functionality, but the original migration didn't include them.
 * This migration adds the missing timestamp columns.
 */
class AddTimestampsToActivityLogTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection(config('activitylog.database_connection'))->table(config('activitylog.table_name'), function (Blueprint $table) {
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection(config('activitylog.database_connection'))->table(config('activitylog.table_name'), function (Blueprint $table) {
            $table->dropTimestamps();
        });
    }
}
