<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Task;
use App\Models\EnumTaskType;
use App\Models\EnumTaskCycle;
use App\Models\TaskBook;
use App\Models\TaskBookCategory;
use App\Models\Book;
use App\Models\Category;
use App\Models\Activity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class TaskManagementSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    /** @test */
    public function it_creates_enum_task_types_with_predefined_data()
    {
        $this->assertDatabaseCount('enum_task_types', 10);
        
        $readPages = EnumTaskType::where('nr', EnumTaskType::READ_PAGES)->first();
        $this->assertEquals('Read Pages', $readPages->name);
        $this->assertTrue($readPages->isQuantitative());
        $this->assertFalse($readPages->isQualitative());
    }

    /** @test */
    public function it_creates_enum_task_cycles_with_predefined_data()
    {
        $this->assertDatabaseCount('enum_task_cycles', 4);
        
        $daily = EnumTaskCycle::where('nr', EnumTaskCycle::DAILY)->first();
        $this->assertEquals('Daily', $daily->name);
        $this->assertTrue($daily->isTimeBased());
        $this->assertFalse($daily->isCumulative());
    }

    /** @test */
    public function it_creates_quantitative_task_with_task_value()
    {
        $taskType = EnumTaskType::where('nr', EnumTaskType::READ_PAGES)->first();
        $taskCycle = EnumTaskCycle::where('nr', EnumTaskCycle::DAILY)->first();

        $task = Task::create([
            'name' => 'Read 10 Pages Daily',
            'task_type_id' => $taskType->id,
            'task_cycle_id' => $taskCycle->id,
            'task_value' => 10,
            'active' => true,
        ]);

        $this->assertDatabaseHas('tasks', [
            'name' => 'Read 10 Pages Daily',
            'task_value' => 10,
        ]);

        $this->assertTrue($task->isQuantitative());
        $this->assertFalse($task->isQualitative());
        $this->assertEquals('10 pages', $task->task_value_with_unit);
    }


    /** @test */
    public function it_creates_activity_based_task()
    {
        $activity = Activity::factory()->create(['name' => 'Book Quiz']);
        $taskType = EnumTaskType::where('nr', EnumTaskType::COMPLETE_BOOK_ACTIVITY)->first();
        $taskCycle = EnumTaskCycle::where('nr', EnumTaskCycle::WEEKLY)->first();

        $task = Task::create([
            'name' => 'Complete Weekly Quiz',
            'task_type_id' => $taskType->id,
            'task_cycle_id' => $taskCycle->id,
            'task_value' => 1,
            'activity_id' => $activity->id,
            'active' => true,
        ]);

        $this->assertDatabaseHas('tasks', [
            'name' => 'Complete Weekly Quiz',
            'activity_id' => $activity->id,
        ]);

        $this->assertTrue($task->requiresActivity());
        $this->assertEquals('Book Quiz', $task->activity_name);
    }

    /** @test */
    public function it_creates_book_list_task_with_books()
    {
        $books = Book::factory()->count(3)->create();
        $taskType = EnumTaskType::where('nr', EnumTaskType::COMPLETE_BOOK_LIST)->first();
        $taskCycle = EnumTaskCycle::where('nr', EnumTaskCycle::TOTAL)->first();

        $task = Task::create([
            'name' => 'Complete Classic Books',
            'task_type_id' => $taskType->id,
            'task_cycle_id' => $taskCycle->id,
            'active' => true,
        ]);

        // Add books to task
        foreach ($books as $book) {
            TaskBook::create([
                'task_id' => $task->id,
                'book_id' => $book->id,
            ]);
        }

        $this->assertTrue($task->requiresBookList());
        $this->assertEquals(3, $task->books_count);
        $this->assertCount(3, $task->books);
    }

    /** @test */
    public function it_creates_category_based_task()
    {
        $categories = Category::factory()->count(2)->create();
        $taskType = EnumTaskType::where('nr', EnumTaskType::READ_BOOKS)->first();
        $taskCycle = EnumTaskCycle::where('nr', EnumTaskCycle::MONTHLY)->first();

        $task = Task::create([
            'name' => 'Read Science Fiction Books',
            'task_type_id' => $taskType->id,
            'task_cycle_id' => $taskCycle->id,
            'task_value' => 2,
            'active' => true,
        ]);

        // Add categories to task
        foreach ($categories as $category) {
            TaskBookCategory::create([
                'task_id' => $task->id,
                'category_id' => $category->id,
            ]);
        }

        $this->assertEquals(2, $task->categories_count);
        $this->assertCount(2, $task->categories);
    }

    /** @test */
    public function it_calculates_task_cycle_periods_correctly()
    {
        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');

        $daily = EnumTaskCycle::where('nr', EnumTaskCycle::DAILY)->first();
        $weekly = EnumTaskCycle::where('nr', EnumTaskCycle::WEEKLY)->first();
        $monthly = EnumTaskCycle::where('nr', EnumTaskCycle::MONTHLY)->first();
        $total = EnumTaskCycle::where('nr', EnumTaskCycle::TOTAL)->first();

        $this->assertEquals(31, $daily->getPeriodsInRange($startDate, $endDate));
        $this->assertEquals(5, $weekly->getPeriodsInRange($startDate, $endDate));
        $this->assertEquals(1, $monthly->getPeriodsInRange($startDate, $endDate));
        $this->assertEquals(1, $total->getPeriodsInRange($startDate, $endDate));
    }

    /** @test */
    public function it_calculates_expected_progress_percentage()
    {
        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');
        $currentDate = Carbon::parse('2024-01-16'); // Halfway through month

        $daily = EnumTaskCycle::where('nr', EnumTaskCycle::DAILY)->first();
        $total = EnumTaskCycle::where('nr', EnumTaskCycle::TOTAL)->first();

        // For daily cycle, 16 days out of 31 days = ~51.6%
        $dailyProgress = $daily->getExpectedProgressPercentage($startDate, $endDate, $currentDate);
        $this->assertEqualsWithDelta(51.6, $dailyProgress, 0.1);

        // For total cycle, 16 days out of 31 days = ~51.6%
        $totalProgress = $total->getExpectedProgressPercentage($startDate, $endDate, $currentDate);
        $this->assertEqualsWithDelta(51.6, $totalProgress, 0.1);
    }

    /** @test */
    public function it_manages_task_book_relationships()
    {
        $task = Task::factory()->create();
        $book = Book::factory()->create();

        // Add book to task
        $taskBook = TaskBook::addBookToTask($task->id, $book->id);
        $this->assertNotNull($taskBook);
        $this->assertTrue(TaskBook::isBookInTask($task->id, $book->id));

        // Try to add same book again (should return null)
        $duplicate = TaskBook::addBookToTask($task->id, $book->id);
        $this->assertNull($duplicate);

        // Remove book from task
        $removed = TaskBook::removeBookFromTask($task->id, $book->id);
        $this->assertTrue($removed);
        $this->assertFalse(TaskBook::isBookInTask($task->id, $book->id));
    }

    /** @test */
    public function it_manages_task_category_relationships()
    {
        $task = Task::factory()->create();
        $category = Category::factory()->create();

        // Add category to task
        $taskCategory = TaskBookCategory::addCategoryToTask($task->id, $category->id);
        $this->assertNotNull($taskCategory);
        $this->assertTrue(TaskBookCategory::isCategoryInTask($task->id, $category->id));

        // Try to add same category again (should return null)
        $duplicate = TaskBookCategory::addCategoryToTask($task->id, $category->id);
        $this->assertNull($duplicate);

        // Remove category from task
        $removed = TaskBookCategory::removeCategoryFromTask($task->id, $category->id);
        $this->assertTrue($removed);
        $this->assertFalse(TaskBookCategory::isCategoryInTask($task->id, $category->id));
    }

    /** @test */
    public function it_generates_configuration_summary()
    {
        $taskType = EnumTaskType::where('nr', EnumTaskType::READ_PAGES)->first();
        $taskCycle = EnumTaskCycle::where('nr', EnumTaskCycle::DAILY)->first();

        $task = Task::create([
            'name' => 'Daily Reading',
            'task_type_id' => $taskType->id,
            'task_cycle_id' => $taskCycle->id,
            'task_value' => 10,
            'active' => true,
        ]);

        $summary = $task->configuration_summary;
        $this->assertStringContainsString('Read Pages', $summary);
        $this->assertStringContainsString('Daily', $summary);
        $this->assertStringContainsString('10 pages', $summary);
    }

    /** @test */
    public function it_validates_task_type_classifications()
    {
        $quantitativeTypes = EnumTaskType::getQuantitativeTypes();
        $qualitativeTypes = EnumTaskType::getQualitativeTypes();
        $bookListTypes = EnumTaskType::getBookListTypes();
        $activityTypes = EnumTaskType::getActivityTypes();

        $this->assertCount(8, $quantitativeTypes);
        $this->assertCount(2, $qualitativeTypes);
        $this->assertCount(1, $bookListTypes);
        $this->assertCount(1, $activityTypes);

        $this->assertContains(EnumTaskType::READ_PAGES, $quantitativeTypes);
        $this->assertContains(EnumTaskType::COMPLETE_BOOK_LIST, $bookListTypes);
        $this->assertContains(EnumTaskType::COMPLETE_BOOK_ACTIVITY, $activityTypes);
    }

    /** @test */
    public function it_validates_task_cycle_classifications()
    {
        $timeBasedCycles = EnumTaskCycle::getTimeBasedCycles();
        $cumulativeCycles = EnumTaskCycle::getCumulativeCycles();

        $this->assertCount(3, $timeBasedCycles);
        $this->assertCount(1, $cumulativeCycles);

        $this->assertContains(EnumTaskCycle::DAILY, $timeBasedCycles);
        $this->assertContains(EnumTaskCycle::WEEKLY, $timeBasedCycles);
        $this->assertContains(EnumTaskCycle::MONTHLY, $timeBasedCycles);
        $this->assertContains(EnumTaskCycle::TOTAL, $cumulativeCycles);
    }
}
