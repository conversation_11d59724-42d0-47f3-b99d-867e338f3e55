<?php

declare(strict_types=1);

namespace App\MoonShine\Handlers;

use MoonShine\Contracts\UI\ActionButtonContract;
use MoonShine\ImportExport\ImportHandler;
use MoonShine\UI\Exceptions\ActionButtonException;
use MoonShine\UI\Fields\File;

use Moon<PERSON>hine\UI\Components\{ActionButton, Alert, Components, FormBuilder, Link};

class AppImportHandler extends ImportHandler
{
    protected string $templateLink = '';

    public function templateLink(string $link): static
    {
        $this->templateLink = $link;

        return $this;
    }

    public function getInstructions(): string
    {
        return __('admin.import_template_hint', [ 'template' => Link::make($this->templateLink, __('admin.template')) ]);
    }

    public function getButton(): ActionButtonContract
    {
        if (! $this->hasResource()) {
            throw ActionButtonException::resourceRequired();
        }
        $form = FormBuilder::make($this->getUrl())->fields([
                        File::make(column: $this->getInputName())->required(),
        ])
            ->class('js-change-query')
            ->customAttributes([
                'data-original-url' => $this->getUrl(),
            ])
            ->submit(__('moonshine::ui.confirm'));

        return $this->prepareButton(
            ActionButton::make(
                $this->getLabel(),
                '#'
            )
                ->success()
                ->icon($this->getIconValue(), $this->isCustomIcon(), $this->getIconPath())
                ->inOffCanvas(
                    fn (): string => $this->getLabel(),
                    fn (): Components => Components::make([
                        Alert::make(type: 'info')->content( $this->getInstructions() ),
                        $form,
                    ]),                    
                    name: 'import-off-canvas'
                )
        );
    }
}
