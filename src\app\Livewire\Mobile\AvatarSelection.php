<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Avatar;
use App\Models\UserAvatar;

class AvatarSelection extends Component
{
    public $selectedAvatarId = null;
    public $avatars = [];
    public $isLoading = false;

    public function mount()
    {
        $user = Auth::user();

        // Check if user already has an avatar
        if (UserAvatar::where('user_id', $user->id)->exists()) {
            return redirect()->route('mobile.home');
        }

        // Get available avatars (required_points = 0)
        $this->avatars = Avatar::where('required_points', 0)
                              ->where('active', true)
                              ->get()
                              ->toArray();
    }

    public function selectAvatar($avatarId)
    {
        $this->selectedAvatarId = $avatarId;
    }

    public function confirmSelection()
    {
        if (!$this->selectedAvatarId) {
            return;
        }

        $this->isLoading = true;
        $user = Auth::user();

        try {
            // Create user avatar selection
            UserAvatar::create([
                'user_id' => $user->id,
                'avatar_id' => $this->selectedAvatarId,
                'selected_at' => now(),
            ]);

            // Redirect to welcome screen
            return redirect()->route('mobile.welcome');
        } catch (\Exception) {
            $this->isLoading = false;
            session()->flash('error', __('mobile.avatar_selection_error'));
        }
    }

    public function chooseLater()
    {
        return redirect()->route('mobile.welcome');
    }

    public function render()
    {
        return view('livewire.mobile.avatar-selection');
    }
}
