<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\EnumTaskCycle;
use App\Models\User;

class EnumTaskCyclePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, EnumTaskCycle $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, EnumTaskCycle $item): bool
    {
        return true;
    }

    public function delete(User $user, EnumTaskCycle $item): bool
    {
        return true;
    }

    public function restore(User $user, EnumTaskCycle $item): bool
    {
        return true;
    }

    public function forceDelete(User $user, EnumTaskCycle $item): bool
    {
        return true;
    }

    public function massDelete(User $user): bool
    {
        return true;
    }
}
