<?php

/**
 * Test script to verify activity completion doesn't cause duplicate rewards/levels
 * 
 * This script tests the fix for the issue where completing an activity would:
 * 1. Unlock awards related to reading logs multiple times
 * 2. Re-award all past levels up to current level
 * 
 * Run with: php _augment/test_activity_completion_rewards.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Models\User;
use App\Models\Book;
use App\Models\Activity;
use App\Models\UserActivity;
use App\Models\UserReadingLog;
use App\Models\UserReward;
use App\Models\UserLevel;
use App\Models\UserPoint;
use App\Models\Reward;
use App\Models\Level;
use Illuminate\Support\Facades\DB;

// Initialize Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testing Activity Completion Rewards Fix\n";
echo "==========================================\n\n";

try {
    // Test 1: Verify awardWithheldRewardsForBook doesn't cause duplicates
    echo "📋 Test 1: Withheld Rewards Logic\n";
    echo "--------------------------------\n";
    
    // Find a user with reading logs and activities
    $user = User::whereHas('readingLogs')
        ->whereHas('userActivities')
        ->first();
    
    if (!$user) {
        echo "❌ No user found with both reading logs and activities\n";
        echo "   Create test data first\n\n";
    } else {
        echo "✅ Testing with user: {$user->name} (ID: {$user->id})\n";
        
        // Get user's current rewards and levels count
        $initialRewards = $user->userRewards()->count();
        $initialLevels = $user->userLevels()->count();
        $currentLevelNumber = $user->getCurrentLevelNumber();
        
        echo "   Initial rewards: {$initialRewards}\n";
        echo "   Initial levels: {$initialLevels}\n";
        echo "   Current level: {$currentLevelNumber}\n";
        
        // Find a book with completed reading logs and required activities
        $book = Book::whereHas('readingLogs', function($q) use ($user) {
            $q->where('user_id', $user->id)->where('book_completed', true);
        })->whereHas('userActivities', function($q) {
            $q->whereHas('activity', function($subQ) {
                $subQ->where('required', true);
            });
        })->first();
        
        if ($book) {
            echo "   Testing with book: {$book->name}\n";
            
            // Simulate calling awardWithheldRewardsForBook multiple times
            echo "   Calling awardWithheldRewardsForBook 3 times...\n";
            
            for ($i = 1; $i <= 3; $i++) {
                UserReadingLog::awardWithheldRewardsForBook($user->id, $book->id);
                echo "   Call {$i} completed\n";
            }
            
            // Check if rewards/levels increased
            $finalRewards = $user->fresh()->userRewards()->count();
            $finalLevels = $user->fresh()->userLevels()->count();
            
            echo "   Final rewards: {$finalRewards}\n";
            echo "   Final levels: {$finalLevels}\n";
            
            if ($finalRewards == $initialRewards && $finalLevels == $initialLevels) {
                echo "✅ No duplicate rewards/levels awarded\n";
            } else {
                echo "❌ Duplicate rewards/levels detected!\n";
                echo "   Rewards increased by: " . ($finalRewards - $initialRewards) . "\n";
                echo "   Levels increased by: " . ($finalLevels - $initialLevels) . "\n";
            }
        } else {
            echo "   No suitable book found for testing\n";
        }
    }
    
    echo "\n";
    
    // Test 2: Verify activity status handling
    echo "📋 Test 2: Activity Status Handling\n";
    echo "-----------------------------------\n";
    
    // Find activities with different statuses
    $completedActivity = UserActivity::where('status', UserActivity::STATUS_COMPLETED)->first();
    $approvedActivity = UserActivity::where('status', UserActivity::STATUS_APPROVED)->first();
    
    echo "✅ STATUS_COMPLETED activities: " . UserActivity::where('status', UserActivity::STATUS_COMPLETED)->count() . "\n";
    echo "✅ STATUS_APPROVED activities: " . UserActivity::where('status', UserActivity::STATUS_APPROVED)->count() . "\n";
    echo "✅ STATUS_PENDING activities: " . UserActivity::where('status', UserActivity::STATUS_PENDING)->count() . "\n";
    echo "✅ STATUS_REJECTED activities: " . UserActivity::where('status', UserActivity::STATUS_REJECTED)->count() . "\n";
    echo "✅ STATUS_FAILED activities: " . UserActivity::where('status', UserActivity::STATUS_FAILED)->count() . "\n";
    
    echo "\n";
    
    // Test 3: Check level awarding logic
    echo "📋 Test 3: Level Awarding Logic\n";
    echo "-------------------------------\n";
    
    // Get level statistics
    $totalLevels = Level::count();
    $usersWithLevels = User::whereHas('userLevels')->count();
    $totalUserLevels = UserLevel::count();
    
    echo "✅ Total levels in system: {$totalLevels}\n";
    echo "✅ Users with levels: {$usersWithLevels}\n";
    echo "✅ Total user level achievements: {$totalUserLevels}\n";
    
    // Check for potential duplicate levels (same user, same level, multiple times)
    $duplicateLevels = DB::select("
        SELECT user_id, level_id, COUNT(*) as count 
        FROM user_levels 
        GROUP BY user_id, level_id 
        HAVING COUNT(*) > 1
    ");
    
    if (empty($duplicateLevels)) {
        echo "✅ No duplicate level achievements found\n";
    } else {
        echo "❌ Found " . count($duplicateLevels) . " duplicate level achievements:\n";
        foreach ($duplicateLevels as $duplicate) {
            echo "   User {$duplicate->user_id}, Level {$duplicate->level_id}: {$duplicate->count} times\n";
        }
    }
    
    echo "\n";
    
    // Test 4: Check reward awarding patterns
    echo "📋 Test 4: Reward Awarding Patterns\n";
    echo "-----------------------------------\n";
    
    // Check for rewards awarded multiple times to same user
    $rewardStats = DB::select("
        SELECT reward_id, COUNT(*) as total_awards, COUNT(DISTINCT user_id) as unique_users
        FROM user_rewards 
        GROUP BY reward_id 
        ORDER BY total_awards DESC 
        LIMIT 10
    ");
    
    echo "✅ Top 10 most awarded rewards:\n";
    foreach ($rewardStats as $stat) {
        $reward = Reward::find($stat->reward_id);
        $rewardName = $reward ? $reward->name : "Unknown";
        echo "   {$rewardName}: {$stat->total_awards} awards to {$stat->unique_users} users\n";
    }
    
    // Check for non-repeatable rewards awarded multiple times to same user
    $duplicateRewards = DB::select("
        SELECT ur.user_id, ur.reward_id, COUNT(*) as count
        FROM user_rewards ur
        JOIN rewards r ON ur.reward_id = r.id
        WHERE r.repeatable = 0
        GROUP BY ur.user_id, ur.reward_id
        HAVING COUNT(*) > 1
    ");
    
    if (empty($duplicateRewards)) {
        echo "✅ No non-repeatable rewards awarded multiple times\n";
    } else {
        echo "❌ Found " . count($duplicateRewards) . " non-repeatable rewards awarded multiple times:\n";
        foreach ($duplicateRewards as $duplicate) {
            $reward = Reward::find($duplicate->reward_id);
            $rewardName = $reward ? $reward->name : "Unknown";
            echo "   User {$duplicate->user_id}, Reward '{$rewardName}': {$duplicate->count} times\n";
        }
    }
    
    echo "\n";
    
    // Test 5: Activity completion flow simulation
    echo "📋 Test 5: Activity Completion Flow\n";
    echo "-----------------------------------\n";
    
    // Find a pending activity that can be approved
    $pendingActivity = UserActivity::where('status', UserActivity::STATUS_PENDING)
        ->whereHas('activity', function($q) {
            $q->where('need_approval', true);
        })
        ->first();
    
    if ($pendingActivity) {
        echo "✅ Found pending activity for testing: ID {$pendingActivity->id}\n";
        
        $user = $pendingActivity->user;
        $initialRewards = $user->userRewards()->count();
        $initialLevels = $user->userLevels()->count();
        
        echo "   User: {$user->name}\n";
        echo "   Initial rewards: {$initialRewards}\n";
        echo "   Initial levels: {$initialLevels}\n";
        echo "   Activity required: " . ($pendingActivity->activity->required ? 'Yes' : 'No') . "\n";
        
        // Note: We won't actually approve the activity to avoid affecting real data
        echo "   (Simulation only - not actually approving to preserve data)\n";
        
    } else {
        echo "   No pending activities found for testing\n";
    }
    
    echo "\n";
    
    // Summary
    echo "📊 Test Summary\n";
    echo "===============\n";
    echo "✅ Withheld rewards logic tested\n";
    echo "✅ Activity status handling verified\n";
    echo "✅ Level awarding logic checked\n";
    echo "✅ Reward patterns analyzed\n";
    echo "✅ Activity completion flow reviewed\n";
    
    echo "\n🎉 Activity completion rewards fix testing completed!\n";
    echo "   Review the results above for any issues.\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "   Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
