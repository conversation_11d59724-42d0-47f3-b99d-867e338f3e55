<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\BookWord;
use App\Models\User;

class BookWordPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // System admin, school admin, and teacher can view book questions
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function view(User $user, BookWord $item): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function create(User $user): bool
    {
        // System admin, school admin, and teacher can create book questions
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function update(User $user, BookWord $item): bool
    {
        return ($user->isSystemAdmin()) || ($item->created_by === $user->id);
    }

    public function delete(User $user, BookWord $item): bool
    {
        return ($user->isSystemAdmin()) || ($item->created_by === $user->id);
    }

    public function restore(User $user, BookWord $item): bool
    {
        return ($user->isSystemAdmin()) || ($item->created_by === $user->id);
    }

    public function forceDelete(User $user, BookWord $item): bool
    {
        return ($user->isSystemAdmin()) || ($item->created_by === $user->id);
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }
}
