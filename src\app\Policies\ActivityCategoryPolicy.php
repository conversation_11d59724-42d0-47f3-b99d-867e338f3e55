<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\ActivityCategory;
use App\Models\User;

class ActivityCategoryPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, ActivityCategory $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return true;
    }

    public function update(User $user, ActivityCategory $item): bool
    {
        return true;
    }

    public function delete(User $user, ActivityCategory $item): bool
    {
        return true;
    }

    public function restore(User $user, ActivityCategory $item): bool
    {
        return true;
    }

    public function forceDelete(User $user, ActivityCategory $item): bool
    {
        return true;
    }

    public function massDelete(User $user): bool
    {
        return true;
    }
}
