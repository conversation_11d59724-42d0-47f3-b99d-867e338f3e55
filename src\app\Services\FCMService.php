<?php

namespace App\Services;

use App\Models\User;
use App\Models\SchoolClass;
use App\Models\Team;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Spatie\Activitylog\Models\Activity;

class FCMService
{
    protected $config;
    protected $accessToken;

    public function __construct()
    {
        $this->config = config('fcm');
    }

    /**
     * Get OAuth2 access token for Firebase API.
     */
    protected function getAccessToken(): ?string
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        try {
            if (!file_exists($this->config['credentials_path'])) {
                Log::warning('Firebase credentials file not found: ' . $this->config['credentials_path']);
                return null;
            }

            $credentials = json_decode(file_get_contents($this->config['credentials_path']), true);

            if (!$credentials || !isset($credentials['private_key'], $credentials['client_email'])) {
                Log::error('Invalid Firebase credentials file format');
                return null;
            }

            // Create JWT for OAuth2
            $jwt = $this->createJWT($credentials);

            // Exchange JWT for access token
            $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $this->accessToken = $data['access_token'];
                return $this->accessToken;
            }

            Log::error('Failed to get Firebase access token: ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('Error getting Firebase access token: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create JWT for Firebase authentication.
     */
    protected function createJWT(array $credentials): string
    {
        $header = json_encode(['alg' => 'RS256', 'typ' => 'JWT']);
        $now = time();

        $payload = json_encode([
            'iss' => $credentials['client_email'],
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => 'https://oauth2.googleapis.com/token',
            'exp' => $now + 3600,
            'iat' => $now
        ]);

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = '';
        openssl_sign($base64Header . '.' . $base64Payload, $signature, $credentials['private_key'], OPENSSL_ALGO_SHA256);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
    }

    /**
     * Send notification to a single user.
     */
    public function sendToUser(User $user, string $title, string $body, string $type = 'general', ?string $deepLinkUrl = null): bool
    {
        if (!$user->fcm_token) {
            $this->logActivity('fcm_send_failed', $user, [
                'reason' => 'No FCM token',
                'title' => $title,
                'type' => $type
            ]);
            return false;
        }

        return $this->sendToToken($user->fcm_token, $title, $body, $type, $deepLinkUrl, $user);
    }

    /**
     * Send notification to multiple users.
     */
    public function sendToUsers(Collection $users, string $title, string $body, string $type = 'general', ?string $deepLinkUrl = null): array
    {
        $results = [];
        
        foreach ($users as $user) {
            $results[$user->id] = $this->sendToUser($user, $title, $body, $type, $deepLinkUrl);
        }

        $this->logActivity('fcm_bulk_send', null, [
            'total_users' => $users->count(),
            'successful_sends' => array_sum($results),
            'title' => $title,
            'type' => $type
        ]);

        return $results;
    }

    /**
     * Send notification to all students in a class.
     */
    public function sendToClass(SchoolClass $class, string $title, string $body, string $type = 'general', ?string $deepLinkUrl = null): array
    {
        $students = $class->users()->whereHas('roles', function ($query) {
            $query->where('name', 'student');
        })->whereNotNull('fcm_token')->get();

        $this->logActivity('fcm_class_send', $class, [
            'class_name' => $class->name,
            'student_count' => $students->count(),
            'title' => $title,
            'type' => $type
        ]);

        return $this->sendToUsers($students, $title, $body, $type, $deepLinkUrl);
    }

    /**
     * Send notification to all members of a team.
     */
    public function sendToTeam(Team $team, string $title, string $body, string $type = 'general', ?string $deepLinkUrl = null): array
    {
        $members = $team->users()->whereNotNull('fcm_token')->get();

        $this->logActivity('fcm_team_send', $team, [
            'team_name' => $team->name,
            'member_count' => $members->count(),
            'title' => $title,
            'type' => $type
        ]);

        return $this->sendToUsers($members, $title, $body, $type, $deepLinkUrl);
    }

    /**
     * Send notification using FCM token via HTTP API.
     */
    protected function sendToToken(string $token, string $title, string $body, string $type, ?string $deepLinkUrl = null, ?User $user = null): bool
    {
        $accessToken = $this->getAccessToken();
        if (!$accessToken) {
            Log::warning('Firebase access token not available');
            return false;
        }

        try {
            $projectId = $this->config['project_id'] ?? null;
            if (!$projectId) {
                Log::error('Firebase project ID not configured');
                return false;
            }

            $data = [
                'type' => $type,
                'timestamp' => now()->toISOString(),
            ];

            if ($deepLinkUrl) {
                $data['deep_link_url'] = $deepLinkUrl;
            }

            $payload = [
                'message' => [
                    'token' => $token,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                        'image' => $this->config['default_icon'] ?? null
                    ],
                    'data' => $data,
                    'android' => [
                        'notification' => [
                            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                        ]
                    ],
                    'webpush' => [
                        'fcm_options' => [
                            'link' => $deepLinkUrl ?: '/'
                        ]
                    ]
                ]
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json'
            ])->post("https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send", $payload);

            if ($response->successful()) {
                $this->logActivity('fcm_send_success', $user, [
                    'title' => $title,
                    'type' => $type,
                    'deep_link_url' => $deepLinkUrl,
                    'token_preview' => substr($token, 0, 20) . '...'
                ]);
                return true;
            } else {
                Log::error('FCM API error: ' . $response->body());
                $this->logActivity('fcm_send_failed', $user, [
                    'title' => $title,
                    'type' => $type,
                    'error' => $response->body(),
                    'token_preview' => substr($token, 0, 20) . '...'
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error('FCM send failed: ' . $e->getMessage(), [
                'token' => substr($token, 0, 20) . '...',
                'title' => $title,
                'type' => $type
            ]);

            $this->logActivity('fcm_send_failed', $user, [
                'title' => $title,
                'type' => $type,
                'error' => $e->getMessage(),
                'token_preview' => substr($token, 0, 20) . '...'
            ]);

            return false;
        }
    }

    /**
     * Get default notification content for a type.
     */
    public function getDefaultNotification(string $type, User $user): array
    {
        $notification_types = [
            'activity_approved' => [
                'title' => __('admin.activity_approved_notification_title'), //'Activity Approved! 🎉'
                'body' => __('admin.activity_approved_notification_body', [ 'student_name' => $user->name ]), //'Your activity has been approved by your teacher.'
                'icon' => '✅',
            ],
            'activity_rejected' => [
                'title' => __('admin.activity_rejected_notification_title'),// 'Activity Needs Revision 📝',
                'body' => __('admin.activity_rejected_notification_body', [ 'student_name' => $user->name ]), // 'Your teacher has provided feedback on your activity.',
                'icon' => '📝',
            ],
            'activity_submitted' => [
                'title' => __('admin.activity_submitted_notification_title'), // 'New Activity Submission 📚',
                'body' => __('admin.activity_submitted_notification_body', [ 'student_name' => $user->name ]), // 'A student has submitted an activity for review.',
                'icon' => '📚',
            ],
            'todays_reading_reminder' => [
                'title' => __('admin.todays_reading_reminder_notification_title', [ 'student_name' => $user->name ]),
                'body' => __('admin.todays_reading_reminder_notification_body'),
                'icon' => '📖',
            ],
            'inactivity_reminder' => [
                'title' => __('admin.inactivity_reminder_notification_title', [ 'student_name' => $user->name, 'avatar_name' => $user->getCurrentAvatar()->name ]), // 'We Miss You! 📖',
                'body' => __('admin.inactivity_reminder_notification_body'), // 'Come back and continue your reading journey.',
                'icon' => '📖',
            ],
            'admin_broadcast' => [
                'title' => __('admin.admin_broadcast_notification_title'), // 'Important Announcement 📢',
                'body' => __('admin.admin_broadcast_notification_body'), // 'You have a new message from the administration.',
                'icon' => '📢',
            ],
        ];

        $defaults = $notification_types[$type] ?? [
            'title' => 'Notification',
            'body' => 'You have a new notification.',
            'icon' => '📱',
        ];

        return $defaults;
    }

    /**
     * Generate deep link URL for notification type.
     */
    public function generateDeepLinkUrl(string $type, array $parameters = []): ?string
    {
        $pattern = $this->config['deep_link_patterns'][$type] ?? null;
        
        if (!$pattern) {
            return null;
        }

        $url = $pattern;
        foreach ($parameters as $key => $value) {
            $url = str_replace('{' . $key . '}', $value, $url);
        }

        return $url;
    }

    /**
     * Send activity approval notification.
     */
    public function sendActivityApprovalNotification(User $student, $userActivity): bool
    {
        $defaults = $this->getDefaultNotification('activity_approved', $student);
        $deepLinkUrl = $this->generateDeepLinkUrl('activity_approved', [
            'book_id' => $userActivity->book_id,
            'activity_id' => $userActivity->activity_id
        ]);

        return $this->sendToUser($student, $defaults['title'], $defaults['body'], 'activity_approved', $deepLinkUrl);
    }

    /**
     * Send activity rejection notification.
     */
    public function sendActivityRejectionNotification(User $student, $userActivity, string $feedback = ''): bool
    {
        $defaults = $this->getDefaultNotification('activity_rejected', $student);
        $body = $feedback ? $feedback : $defaults['body'];
        
        $deepLinkUrl = $this->generateDeepLinkUrl('activity_rejected', [
            'book_id' => $userActivity->book_id,
            'activity_id' => $userActivity->activity_id
        ]);

        return $this->sendToUser($student, $defaults['title'], $body, 'activity_rejected', $deepLinkUrl);
    }

    /**
     * Send activity submission notification to teachers.
     */
    public function sendActivitySubmissionNotification($userActivity): array
    {
        // Get teachers assigned to the student's classes
        $student = $userActivity->user;
        $teachers = collect();

        foreach ($student->activeClasses as $class) {
            $classTeachers = $class->users()->whereHas('roles', function ($query) {
                $query->where('name', 'teacher');
            })->whereNotNull('fcm_token')->get();
            
            $teachers = $teachers->merge($classTeachers);
        }

        $teachers = $teachers->unique('id');

        if ($teachers->isEmpty()) {
            return [];
        }

        $defaults = $this->getDefaultNotification('activity_submitted', $student);
        $title = $defaults['title'];
        $body = $defaults['body'];
        
        $deepLinkUrl = $this->generateDeepLinkUrl('activity_submitted', [
            'user_activity_id' => $userActivity->id
        ]);

        return $this->sendToUsers($teachers, $title, $body, 'activity_submitted', $deepLinkUrl);
    }
    // send todays reading reminder
    public function sendTodaysReadingReminder(User $user): bool
    {
        $defaults = $this->getDefaultNotification('todays_reading_reminder', $user);
        $deepLinkUrl = $this->generateDeepLinkUrl('todays_reading_reminder');

        return $this->sendToUser($user, $defaults['title'], $defaults['body'], 'todays_reading_reminder', $deepLinkUrl);
    }
    

    /**
     * Send inactivity reminder notification.
     */
    public function sendInactivityReminder(User $user): bool
    {
        $defaults = $this->getDefaultNotification('inactivity_reminder', $user);
        $deepLinkUrl = $this->generateDeepLinkUrl('inactivity_reminder');

        return $this->sendToUser($user, $defaults['title'], $defaults['body'], 'inactivity_reminder', $deepLinkUrl);
    }

    /**
     * Log FCM activity using Spatie ActivityLog.
     */
    protected function logActivity(string $description, $subject = null, array $properties = []): void
    {
        if (!$this->config['activity_log']['enabled']) {
            return;
        }

        try {
            Activity::create([
                'log_name' => $this->config['activity_log']['log_name'],
                'description' => $description,
                'subject_type' => $subject ? get_class($subject) : null,
                'subject_id' => $subject ? $subject->id : null,
                'properties' => $properties,
                'causer_type' => null,
                'causer_id' => null,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log FCM activity: ' . $e->getMessage());
        }
    }
}
