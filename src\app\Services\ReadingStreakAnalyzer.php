<?php

namespace App\Services;

use App\Models\UserReadingLog;
use Carbon\Carbon;

class ReadingStreakAnalyzer
{
    /**
     * Analyze 30-day reading pattern and return motivational message.
     */
    public function analyzeReadingPattern(int $userId): array
    {
        $readingData = $this->getReadingStreakData($userId, 30);
        $pattern = $this->identifyPattern($readingData);
        
        return [
            'pattern' => $pattern,
            'message' => __('mobile.message_'.$pattern),
            'reading_data' => $readingData,
        ];
    }

    /**
     * Get reading streak data for the last N days.
     */
    public function getReadingStreakData(int $userId, int $days = 30): array
    {
        setlocale(LC_TIME, config('app.locale'));
        $today = Carbon::today();
        $streak = [];

        // Get reading logs for the period
        // include time 
        $readingLogs = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [Carbon::now()->subDays($days - 1), Carbon::now()])
            ->pluck('log_date')
            ->map(fn($date) => Carbon::parse($date)->format('Y-m-d'))
            ->unique()
            ->toArray();

        // Build 30-day array
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = $today->copy()->subDays($i);
            $dateString = $date->format('Y-m-d');
            
            $streak[] = [
                'date' => $date,
                'day' => $date->translatedFormat('j M'),
                'short_day' => $date->format('j'),
                'has_reading' => in_array($dateString, $readingLogs),
                'is_today' => $date->isToday(),
            ];
        }

        return $streak;
    }

    /**
     * Identify reading pattern from streak data.
     */
    private function identifyPattern(array $readingData): string
    {
        $totalDays = count($readingData);
        $readingDays = array_filter($readingData, fn($day) => $day['has_reading']);
        $readingCount = count($readingDays);

        // Pattern 1: The Unstoppable Streaker (30 consecutive days)
        if ($readingCount === $totalDays) {
            return 'unstoppable_streaker';
        }

        // Get recent activity (last 15 days)
        $recentData = array_slice($readingData, -15);
        $recentReadingCount = count(array_filter($recentData, fn($day) => $day['has_reading']));
        
        // Pattern 2: The Consistent Comebacker (missed early days, then 15+ day recent streak)
        if ($recentReadingCount >= 15 && $readingCount < $totalDays) {
            return 'consistent_comebacker';
        }

        // Get very recent activity (last 3 days)
        $veryRecentData = array_slice($readingData, -3);
        $veryRecentReadingCount = count(array_filter($veryRecentData, fn($day) => $day['has_reading']));

        // Pattern 7: The Recent Recruit (started in last 3-5 days)
        $last5Days = array_slice($readingData, -5);
        $last5DaysReading = count(array_filter($last5Days, fn($day) => $day['has_reading']));
        if ($last5DaysReading >= 3 && $readingCount <= 7) {
            return 'recent_recruit';
        }

        // Pattern 9: The Lost & Found (weeks of inactivity, returned in last 2 days)
        $last2Days = array_slice($readingData, -2);
        $last2DaysReading = count(array_filter($last2Days, fn($day) => $day['has_reading']));
        $previousWeeks = array_slice($readingData, 0, -7);
        $previousWeeksReading = count(array_filter($previousWeeks, fn($day) => $day['has_reading']));
        
        if ($last2DaysReading >= 1 && $previousWeeksReading <= 2) {
            return 'lost_and_found';
        }

        // Pattern 4: The Restart Hero (long break followed by recent restart)
        if ($veryRecentReadingCount >= 2 && $readingCount <= 8) {
            return 'restart_hero';
        }

        // Pattern 10: The Power Starter (active first 10-15 days, then inactive)
        $firstHalf = array_slice($readingData, 0, 15);
        $firstHalfReading = count(array_filter($firstHalf, fn($day) => $day['has_reading']));
        $secondHalf = array_slice($readingData, 15);
        $secondHalfReading = count(array_filter($secondHalf, fn($day) => $day['has_reading']));
        
        if ($firstHalfReading >= 10 && $secondHalfReading === 0) {
            return 'power_starter';
        }

        // Pattern 5: The Burnout Survivor (early consistency, then stopped)
        if ($firstHalfReading >= 8 && $veryRecentReadingCount === 0) {
            return 'burnout_survivor';
        }

        // Pattern 8: The Almost-Streaker (multiple 5-6 day streaks broken by single days)
        $streaks = $this->findStreaks($readingData);
        $longStreaks = array_filter($streaks, fn($streak) => $streak >= 5);
        if (count($longStreaks) >= 2 && $readingCount >= 15) {
            return 'almost_streaker';
        }

        // Pattern 3: The Weekend Warrior (irregular but consistent returns)
        if ($readingCount >= 8 && $readingCount <= 15) {
            return 'weekend_warrior';
        }

        // Pattern 6: The Explorer (scattered reading days)
        if ($readingCount >= 5 && $readingCount <= 12) {
            return 'explorer';
        }

        // Default fallback
        return 'streak_default';
    }

    /**
     * Find consecutive reading streaks in the data.
     */
    private function findStreaks(array $readingData): array
    {
        $streaks = [];
        $currentStreak = 0;

        foreach ($readingData as $day) {
            if ($day['has_reading']) {
                $currentStreak++;
            } else {
                if ($currentStreak > 0) {
                    $streaks[] = $currentStreak;
                    $currentStreak = 0;
                }
            }
        }

        // Add final streak if it exists
        if ($currentStreak > 0) {
            $streaks[] = $currentStreak;
        }

        return $streaks;
    }
}
