<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\UserClass;
use App\Models\Team;
use App\Models\UserTeam;
use App\Models\School;
use App\Models\UserSchool;
use App\Models\UserBook;
use App\Models\Book;
use App\Models\UserReadingLog;
use App\Models\UserPoint;
use App\Models\UserReward;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FriendsFeatureTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_get_classmates()
    {
        // Create school and class
        $school = School::factory()->create();
        $schoolClass = SchoolClass::factory()->create(['school_id' => $school->id]);

        // Create users
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create(); // Not in same class
        $teacher = User::factory()->create(); // Teacher in same class (should be filtered out)

        // Assign student role to students
        $user1->assignRole('student');
        $user2->assignRole('student');
        $user3->assignRole('student');
        $teacher->assignRole('teacher');

        // Assign users to class
        UserClass::create([
            'user_id' => $user1->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true,
            'default' => true,
        ]);

        UserClass::create([
            'user_id' => $user2->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true,
            'default' => true,
        ]);

        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true,
            'default' => true,
        ]);

        // Get classmates for user1
        $classmates = $user1->getClassmates();

        $this->assertCount(1, $classmates);
        $this->assertEquals($user2->id, $classmates->first()->id);
        $this->assertNotContains($user3->id, $classmates->pluck('id'));
        $this->assertNotContains($teacher->id, $classmates->pluck('id')); // Teacher should be filtered out
    }

    public function test_user_can_get_teammates()
    {
        // Create team
        $team = Team::factory()->create();

        // Create users
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $user3 = User::factory()->create(); // Not in same team
        $teacher = User::factory()->create(); // Teacher in same team (should be filtered out)

        // Assign student role to students
        $user1->assignRole('student');
        $user2->assignRole('student');
        $user3->assignRole('student');
        $teacher->assignRole('teacher');

        // Assign users to team
        UserTeam::create(['user_id' => $user1->id, 'team_id' => $team->id]);
        UserTeam::create(['user_id' => $user2->id, 'team_id' => $team->id]);
        UserTeam::create(['user_id' => $teacher->id, 'team_id' => $team->id]);

        // Get teammates for user1
        $teammates = $user1->getTeammates();

        $this->assertCount(1, $teammates);
        $this->assertEquals($user2->id, $teammates->first()->id);
        $this->assertNotContains($user3->id, $teammates->pluck('id'));
        $this->assertNotContains($teacher->id, $teammates->pluck('id')); // Teacher should be filtered out
    }

    public function test_user_can_get_all_friends()
    {
        // Create school, class, and team
        $school = School::factory()->create();
        $schoolClass = SchoolClass::factory()->create(['school_id' => $school->id]);
        $team = Team::factory()->create();

        // Create users
        $user1 = User::factory()->create();
        $user2 = User::factory()->create(); // Classmate
        $user3 = User::factory()->create(); // Teammate
        $user4 = User::factory()->create(); // Both classmate and teammate
        $user5 = User::factory()->create(); // Neither
        $teacher = User::factory()->create(); // Teacher (should be filtered out)

        // Assign student role to students
        $user1->assignRole('student');
        $user2->assignRole('student');
        $user3->assignRole('student');
        $user4->assignRole('student');
        $user5->assignRole('student');
        $teacher->assignRole('teacher');

        // Assign to class
        UserClass::create([
            'user_id' => $user1->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true,
            'default' => true,
        ]);
        UserClass::create([
            'user_id' => $user2->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true,
            'default' => true,
        ]);
        UserClass::create([
            'user_id' => $user4->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true,
            'default' => true,
        ]);
        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $schoolClass->id,
            'school_id' => $school->id,
            'active' => true,
            'default' => true,
        ]);

        // Assign to team
        UserTeam::create(['user_id' => $user1->id, 'team_id' => $team->id]);
        UserTeam::create(['user_id' => $user3->id, 'team_id' => $team->id]);
        UserTeam::create(['user_id' => $user4->id, 'team_id' => $team->id]);
        UserTeam::create(['user_id' => $teacher->id, 'team_id' => $team->id]);

        // Get all friends for user1
        $friends = $user1->getFriends();

        $this->assertCount(3, $friends); // user2, user3, user4 (user4 counted once despite being both classmate and teammate)
        $this->assertContains($user2->id, $friends->pluck('id'));
        $this->assertContains($user3->id, $friends->pluck('id'));
        $this->assertContains($user4->id, $friends->pluck('id'));
        $this->assertNotContains($user5->id, $friends->pluck('id'));
        $this->assertNotContains($teacher->id, $friends->pluck('id')); // Teacher should be filtered out
    }

    public function test_user_statistics_methods()
    {
        $user = User::factory()->create();
        $book = Book::factory()->create();
        
        // Create completed reading log
        UserReadingLog::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'log_date' => now(),
            'pages_read' => 100,
            'book_completed' => true,
        ]);
        
        // Create activity points
        UserPoint::create([
            'user_id' => $user->id,
            'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
            'points' => 50,
            'point_date' => now(),
        ]);
        
        // Create reward (badge)
        UserReward::create([
            'user_id' => $user->id,
            'reward_id' => 1,
            'awarded_date' => now(),
        ]);
        
        // Test statistics methods
        $this->assertEquals(1, $user->getTotalBooksCompleted());
        $this->assertEquals(50, $user->getTotalActivityPoints());
        $this->assertEquals(1, $user->getTotalBadges());
    }

    public function test_user_can_get_currently_reading_book()
    {
        $user = User::factory()->create();
        $book1 = Book::factory()->create();
        $book2 = Book::factory()->create();
        
        // Create finished book
        UserBook::create([
            'user_id' => $user->id,
            'book_id' => $book1->id,
            'start_date' => now()->subDays(10),
            'end_date' => now()->subDays(5),
        ]);
        
        // Create currently reading book
        UserBook::create([
            'user_id' => $user->id,
            'book_id' => $book2->id,
            'start_date' => now()->subDays(3),
            'end_date' => null, // Still reading
        ]);
        
        $currentBook = $user->getCurrentlyReadingBook();
        
        $this->assertNotNull($currentBook);
        $this->assertEquals($book2->id, $currentBook->book_id);
    }
}
