<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserAvatar;

class Login extends Component
{
    public $username = '';
    public $password = '';
    public $remember = true;
    public $showPassword = false;
    public $previousUsernames = [];
    public $showUsernameDropdown = false;
    public $isLoading = false;
    public $errorMessage = '';
    public $currentStep = 1; // 1 = username selection, 2 = password entry

    public function mount()
    {
        // If already authenticated, redirect
        if (Auth::check()) {
            return $this->redirectAfterLogin();
        }

        // Previous usernames will be loaded from localStorage via JavaScript
        $this->previousUsernames = [];
    }

    public function togglePasswordVisibility()
    {
        $this->showPassword = !$this->showPassword;
    }

    public function selectUsername($username)
    {
        $this->username = $username;
        $this->showUsernameDropdown = false;
        $this->proceedToPasswordStep();
    }

    public function proceedToPasswordStep()
    {
        $this->validate([
            'username' => 'required|string',
        ],
        [
            'username.required' => __('mobile.field_required'),
        ]);

        $this->currentStep = 2;
        $this->errorMessage = '';
    }

    public function goBackToUsernameStep()
    {
        $this->currentStep = 1;
        $this->password = '';
        $this->errorMessage = '';
    }

    public function loadPreviousUsernames($usernames)
    {
        $this->previousUsernames = $usernames;

        // Auto-fill last used username if available
        if (!empty($this->previousUsernames) && empty($this->username)) {
            $this->username = $this->previousUsernames[0];
        }
    }

    public function login()
    {
        $this->isLoading = true;
        $this->errorMessage = '';

        $this->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ],
        [
            'username.required' => __('mobile.field_required'),
            'password.required' => __('mobile.field_required'),
        ]);

        $credentials = [
            'username' => $this->username,
            'password' => $this->password,
        ];

        if (Auth::attempt($credentials, $this->remember)) {
            session()->regenerate();

            // Store username for future use
            $this->storeUsernameForDropdown();

            return $this->redirectAfterLogin();
        }

        $this->isLoading = false;
        $this->errorMessage = __('mobile.invalid_credentials');
        $this->password = '';
    }

    private function storeUsernameForDropdown()
    {
        // Username will be stored in localStorage via JavaScript
        $this->dispatch('store-username', username: $this->username);
    }

    private function redirectAfterLogin()
    {
        $user = Auth::user();

        // Check for intended URL first
        $intendedUrl = session('mobile_intended_url');
        if ($intendedUrl) {
            session()->forget('mobile_intended_url');
            return redirect($intendedUrl);
        }

        // Role-based navigation after login
        if ($user->isTeacher()) {
            return redirect()->route('mobile.teacher.home');
        }

        // Check if user has avatar
        if (!UserAvatar::where('user_id', $user->id)->exists()) {
            return redirect()->route('mobile.avatar-selection');
        }

        // Default to student home screen
        return redirect()->route('mobile.home');
    }

    public function render()
    {
        return view('livewire.mobile.login');
    }
}
