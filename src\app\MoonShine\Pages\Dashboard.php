<?php

declare(strict_types=1);

namespace App\MoonShine\Pages;

use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Laravel\Pages\Page;
use MoonShine\UI\Components\Metrics\Wrapped\ValueMetric;

use App\Models\{Reward, User};
use App\Services\StatisticsService;
use MoonShine\Contracts\Core\TypeCasts\DataWrapperContract;
use MoonShine\UI\Components\{Layout\Box, Layout\Column, Layout\Grid, Table\TableBuilder, Heading};
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\{Fieldset, Image, Text};

#[\MoonShine\MenuManager\Attributes\SkipMenu]

class Dashboard extends Page
{
    /**
     * @return array<string, string>
     */
    public function getBreadcrumbs(): array
    {
        return [
            '#' => $this->getTitle()
        ];
    }

    public function getTitle(): string
    {
        return $this->title ?: __('admin.dashboard');
    }

    /**
     * @return list<ComponentContract>
     */
    protected function components(): iterable
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return [];
        }

        // Return role-specific dashboard components
        if ($user->isTeacher()) {
            return $this->getTeacherDashboardComponents($user);
        }

        if ($user->isSystemAdmin()) {
            return $this->getSystemAdminDashboardComponents($user);
        }

        if ($user->isSchoolAdmin()) {
            return $this->getSchoolAdminDashboardComponents($user);
        }

        if ($user->isParent()) {
            return $this->getParentDashboardComponents($user);
        }

        return [];
    }

    /**
     * Get dashboard components for teachers.
     */
    protected function getTeacherDashboardComponents(User $teacher): array
    {
        // Get teacher class IDs using StatisticsService
        $teacherClassIds = StatisticsService::getTeacherClassIds($teacher);

        // Pre-fetch all students with necessary relationships for ranking calculations
        $allStudents = StatisticsService::getTeacherStudentsWithRelationships($teacherClassIds);

        return [
            // Dashboard Metrics
            Grid::make([
                    ValueMetric::make(__('admin.activities_pending_approval'))
                        ->value(fn() => StatisticsService::getActivitiesPendingApprovalCount($teacherClassIds))
                        ->icon('clipboard-document-check')
                        ->columnSpan(3),
                    ValueMetric::make(__('admin.students_read_last_24h'))
                        ->value(fn() => StatisticsService::getStudentsReadLast24Hours($teacherClassIds))
                        ->icon('users')
                        ->columnSpan(3),
                    ValueMetric::make(__('admin.pages_read_last_24h'))
                        ->value(fn() => StatisticsService::getPagesReadLast24Hours($teacherClassIds))
                        ->icon('book-open')
                        ->columnSpan(3),
                    ValueMetric::make(__('admin.activities_done_last_24h'))
                        ->value(fn() => StatisticsService::getActivitiesDoneLast24Hours($teacherClassIds))
                        ->icon('pencil-square')
                        ->columnSpan(3),
            ]),

            // Statistical Tables
            Heading::make(__('admin.student_rankings'), 2)->class('mt-8 mb-4'),
            Grid::make([
                // Left Column - Top Rankings
                Column::make([
                    Box::make(__('admin.most_books_read'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getTopStudentsByBooksRead($allStudents),'books_completed', 'admin.books_count', 'primary')
                    ]),

                    Box::make(__('admin.highest_level_students'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getTopStudentsByLevel($allStudents),'current_level_number', 'admin.level', 'success')
                    ]),

                    Box::make(__('admin.most_badges'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getTopStudentsByBadges($allStudents),'badges_count', 'admin.badges_count', 'warning')
                    ]),

                    Box::make(__('admin.longest_reading_streak'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getTopStudentsByReadingStreak($allStudents),'reading_streak', 'admin.streak_days', 'info')
                    ]),

                    Box::make(__('admin.highest_activity_score'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getTopStudentsByActivityScore($allStudents),'activity_points', 'admin.activity_points', 'primary')
                    ]),
                ])->columnSpan(6)->class('mb-8'),

                // Right Column - Bottom Rankings & Gaps
                Column::make([
                    Box::make(__('admin.fewest_books_read'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getBottomStudentsByBooksRead($allStudents),'books_completed', 'admin.books_count', 'secondary')
                    ]),

                    Box::make(__('admin.lowest_level_students'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getBottomStudentsByLevel($allStudents),'current_level_number', 'admin.level', 'secondary')
                    ]),

                    Box::make(__('admin.fewest_badges'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getBottomStudentsByBadges($allStudents),'badges_count', 'admin.badges_count', 'secondary')
                    ]),

                    Box::make(__('admin.longest_reading_gap'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getStudentsWithLongestReadingGap($allStudents),'days_since_reading', 'admin.days_since_reading', 'secondary')
                    ]),

                    Box::make(__('admin.lowest_activity_score'), [
                        $this->getStudentRankingsTable(
                            StatisticsService::getBottomStudentsByActivityScore($allStudents),'activity_points', 'admin.activity_points', 'secondary')
                    ]),
                ])->columnSpan(6),
            ]),
        ];
    }



    // ========== STUDENT RANKING METHODS ==========

    /**
     * Get students filtered by teacher's classes.
     */
    protected function getTeacherStudents(array $teacherClassIds)
    {
        if (empty($teacherClassIds)) {
            return User::where('id', 0); // Return empty query
        }

        return User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            });
    }

    /**
     * Get all teacher's students with necessary relationships loaded for ranking calculations.
     * This reduces database queries by eager loading all needed data at once.
     */
    protected function getTeacherStudentsWithRelationships(array $teacherClassIds)
    {
        if (empty($teacherClassIds)) {
            return collect(); // Return empty collection
        }

        return User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->withCount([
                'userBooks as books_completed' => function ($q) {
                    $q->where('completed', true);
                },
                'userRewards as badges_count' => function ($q) {
                    $q->whereHas('reward', function ($subQ) {
                        $subQ->where('reward_type', Reward::TYPE_BADGE);
                    });
                }
            ])
            ->get()
            ->map(function ($student) {
                // Calculate complex metrics that require method calls
                $student->current_level_number = $student->getCurrentLevel() ? $student->getCurrentLevel()->nr : 0;
                $student->reading_streak = $student->getCurrentReadingStreak();
                $student->activity_points = $student->getTotalActivityPoints();
                $student->days_since_reading = $student->getDaysSinceLastReading();
                return $student;
            });
    }

    // ========== OPTIMIZED RANKING METHODS (Using Pre-fetched Data) ==========

    // get ranking tablebuilder 

    protected function getStudentRankingsTable($students, $metric, $label, $badgeColor): TableBuilder
    {
        return TableBuilder::make()
            ->fields([
                Fieldset::make(__('admin.student_name'), [
                    Flex::make([
                        Image::make(__('admin.avatar'), 'avatar_display_image')
                        ->class('w-12 h-12 rounded-full'),
                        Text::make(__('admin.student_name'), 'name'),
                    ])->justifyAlign('start'),
                ]),
                Text::make(__($label), $metric)->badge($badgeColor),
            ])
            ->trAttributes(fn(?DataWrapperContract $data, int $row): array => ['class' => $row % 2 ? 'bg-gray-100' : ''])        
            ->tdAttributes(fn(?DataWrapperContract $data, int $row, int $cell): array => ['class' => 'py-1'])        
            ->items($students);
    }






    /**
     * Get dashboard components for system admins.
     */
    protected function getSystemAdminDashboardComponents(User $admin): array
    {
        return [
            Box::make(__('admin.system_admin_dashboard'), [
                Heading::make(__('admin.system_overview'))
                    ->h(2)
                    ->class('mb-4'),
                // Add system admin specific components here
            ])
        ];
    }

    /**
     * Get dashboard components for school admins.
     */
    protected function getSchoolAdminDashboardComponents(User $admin): array
    {
        return [
            Box::make(__('admin.school_admin_dashboard'), [
                Heading::make(__('admin.school_overview'))
                    ->h(2)
                    ->class('mb-4'),
                // Add school admin specific components here
            ])
        ];
    }

    /**
     * Get dashboard components for parents.
     */
    protected function getParentDashboardComponents(User $parent): array
    {
        return [
            Box::make(__('admin.parent_dashboard'), [
                Heading::make(__('admin.students'))
                    ->h(2)
                    ->class('mb-4'),
                // Add parent specific components here
            ])
        ];
    }    
}
