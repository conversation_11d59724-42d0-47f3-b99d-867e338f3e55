<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Book;
use App\Models\BookType;
use App\Models\Publisher;
use App\Models\User;
use App\Models\UserBook;
use App\Services\BookDiscovery\BookDiscoveryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Livewire\Livewire;

class BookDiscoveryIntegrationFixesTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected BookType $bookType;
    protected Publisher $publisher;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'username' => 'testuser',
            'password' => bcrypt('password'),
        ]);

        // Create test book type
        $this->bookType = BookType::create([
            'name' => 'Test Book Type',
            'description' => 'Test book type description',
            'thumbnail' => 'test-thumbnail.jpg',
            'created_by' => $this->user->id,
        ]);

        // Create test publisher
        $this->publisher = Publisher::create([
            'name' => 'Test Publisher',
            'created_by' => $this->user->id,
        ]);
    }

    /** @test */
    public function book_type_selection_creates_user_book_relationship()
    {
        $this->actingAs($this->user);

        $bookData = [
            'name' => 'Test Book for User Relationship',
            'isbn' => '9781234567890',
            'author' => ['Test Author'],
            'publisher' => 'Test Publisher',
            'year' => 2023,
            'page_count' => 200,
            'source' => 'Test Source',
        ];

        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->set('selectedBookTypeId', $this->bookType->id)
            ->call('createBook');

        $component->assertHasNoErrors()
                  ->assertSet('successMessage', __('mobile.book_created_success'));

        // Verify book was created in database
        $book = Book::where('isbn', '9781234567890')->first();
        $this->assertNotNull($book);
        $this->assertEquals('Test Book for User Relationship', $book->name);
        $this->assertEquals($this->bookType->id, $book->book_type_id);

        // Verify UserBook relationship was created
        $userBook = UserBook::where('user_id', $this->user->id)
                           ->where('book_id', $book->id)
                           ->first();
        $this->assertNotNull($userBook);
        $this->assertEquals($this->user->id, $userBook->user_id);
        $this->assertEquals($book->id, $userBook->book_id);
        $this->assertNotNull($userBook->start_date);
        $this->assertNull($userBook->end_date); // Should be in progress
    }

    /** @test */
    public function book_type_selection_handles_existing_user_book()
    {
        $this->actingAs($this->user);

        // Create a book first
        $book = Book::create([
            'name' => 'Existing Book',
            'isbn' => '9780987654321',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => 150,
            'year_of_publish' => 2022,
            'created_by' => $this->user->id,
        ]);

        // Create existing UserBook relationship
        UserBook::create([
            'user_id' => $this->user->id,
            'book_id' => $book->id,
            'start_date' => now(),
            'created_by' => $this->user->id,
        ]);

        $bookData = [
            'name' => 'Existing Book',
            'isbn' => '9780987654321',
            'author' => ['Test Author'],
            'publisher' => 'Test Publisher',
            'year' => 2022,
            'page_count' => 150,
            'source' => 'Test Source',
        ];

        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->set('selectedBookTypeId', $this->bookType->id)
            ->call('createBook');

        $component->assertHasNoErrors()
                  ->assertSet('successMessage', __('mobile.book_created_success'));

        // Verify only one UserBook relationship exists
        $userBookCount = UserBook::where('user_id', $this->user->id)
                                ->where('book_id', $book->id)
                                ->count();
        $this->assertEquals(1, $userBookCount);
    }

    /** @test */
    public function book_discovery_service_uses_provided_book_type_id()
    {
        $bookData = [
            'name' => 'Book with Custom Type',
            'isbn' => '9785555555555',
            'author' => ['Custom Author'],
            'publisher' => 'Custom Publisher',
            'year' => 2024,
            'page_count' => 300,
            'book_type_id' => $this->bookType->id, // Explicitly set book type
        ];

        $discoveryService = app(BookDiscoveryService::class);
        $book = $discoveryService->createBookFromData($bookData, $this->user->id);

        $this->assertNotNull($book);
        $this->assertEquals($this->bookType->id, $book->book_type_id);
        $this->assertEquals('Book with Custom Type', $book->name);
    }

    /** @test */
    public function book_discovery_service_falls_back_to_default_book_type()
    {
        $bookData = [
            'name' => 'Book with Default Type',
            'isbn' => '9786666666666',
            'author' => ['Default Author'],
            'publisher' => 'Default Publisher',
            'year' => 2024,
            'page_count' => 250,
            // No book_type_id provided
        ];

        $discoveryService = app(BookDiscoveryService::class);
        $book = $discoveryService->createBookFromData($bookData, $this->user->id);

        $this->assertNotNull($book);
        $this->assertNotNull($book->book_type_id);
        $this->assertEquals('Book with Default Type', $book->name);
    }

    /** @test */
    public function validation_rules_are_consistent()
    {
        $this->actingAs($this->user);

        $bookData = [
            'name' => 'Test Book',
            'isbn' => '9781111111111',
            'author' => ['Test Author'],
            'publisher' => 'Test Publisher',
            'year' => 2023,
            'page_count' => 100,
            'source' => 'Test Source',
        ];

        // Test with page count over 1000 (should fail)
        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->set('selectedBookTypeId', $this->bookType->id)
            ->set('manualPageCount', 1500)
            ->call('createBook');

        $component->assertHasErrors(['manualPageCount']);

        // Test with valid page count (should pass)
        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->set('selectedBookTypeId', $this->bookType->id)
            ->set('manualPageCount', 500)
            ->call('createBook');

        $component->assertHasNoErrors();
    }

    /** @test */
    public function book_type_selection_component_loads_book_types_correctly()
    {
        $this->actingAs($this->user);

        // Create additional book types
        $bookType2 = BookType::create([
            'name' => 'Second Book Type',
            'description' => 'Second test book type',
            'thumbnail' => 'second-thumbnail.jpg',
            'created_by' => $this->user->id,
        ]);

        $bookData = [
            'name' => 'Test Book',
            'isbn' => '9782222222222',
            'author' => ['Test Author'],
            'publisher' => 'Test Publisher',
            'year' => 2023,
            'page_count' => 200,
            'source' => 'Test Source',
        ];

        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ]);

        // Check that book types are loaded
        $this->assertCount(2, $component->get('bookTypes'));
        
        // Check that book types are ordered by ID
        $bookTypes = $component->get('bookTypes');
        $this->assertEquals($this->bookType->id, $bookTypes[0]['id']);
        $this->assertEquals($bookType2->id, $bookTypes[1]['id']);
    }

    /** @test */
    public function redirect_after_book_creation_works()
    {
        $this->actingAs($this->user);

        $bookData = [
            'name' => 'Redirect Test Book',
            'isbn' => '9783333333333',
            'author' => ['Redirect Author'],
            'publisher' => 'Redirect Publisher',
            'year' => 2023,
            'page_count' => 180,
            'source' => 'Test Source',
        ];

        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->set('selectedBookTypeId', $this->bookType->id)
            ->call('createBook');

        // Check that redirect event was dispatched
        $component->assertDispatched('redirect-after-delay');
    }
}
