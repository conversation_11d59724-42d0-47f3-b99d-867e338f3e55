<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\UserBook;
use App\Models\UserReadingLog;
use App\Models\User;
use App\Models\Book;

try {
    echo "=== TESTING PROGRESS PERCENTAGE CALCULATION ===\n\n";
    
    // Test Case 1: Book with completion flag
    echo "TEST CASE 1: Book marked as completed in reading logs\n";
    echo "Expected: 100% regardless of pages read vs page count\n";
    
    $completedLog = UserReadingLog::where('book_completed', true)->first();
    if ($completedLog) {
        $userBook = UserBook::where('user_id', $completedLog->user_id)
            ->where('book_id', $completedLog->book_id)
            ->first();
        
        if ($userBook) {
            $progress = $userBook->getProgressPercentage();
            echo "- User: {$userBook->user->name}\n";
            echo "- Book: {$userBook->book->name}\n";
            echo "- Book Page Count: {$userBook->book->page_count}\n";
            echo "- Has Completed Log: YES\n";
            echo "- Progress: {$progress}%\n";
            echo "✅ " . ($progress == 100.0 ? "PASS" : "FAIL") . "\n\n";
        } else {
            echo "❌ No UserBook found for completed reading log\n\n";
        }
    } else {
        echo "❌ No completed reading logs found\n\n";
    }
    
    // Test Case 2: Book without completion flag - sum pages
    echo "TEST CASE 2: Book not marked as completed - sum pages calculation\n";
    echo "Expected: (sum of pages_read / book.page_count) * 100\n";
    
    $incompleteLog = UserReadingLog::where('book_completed', false)->first();
    if ($incompleteLog) {
        $userBook = UserBook::where('user_id', $incompleteLog->user_id)
            ->where('book_id', $incompleteLog->book_id)
            ->first();
        
        if ($userBook) {
            $totalPagesRead = UserReadingLog::where('user_id', $userBook->user_id)
                ->where('book_id', $userBook->book_id)
                ->sum('pages_read');
            
            $expectedProgress = $userBook->book->page_count > 0 
                ? min(100.0, ($totalPagesRead / $userBook->book->page_count) * 100)
                : 0.0;
            
            $actualProgress = $userBook->getProgressPercentage();
            
            echo "- User: {$userBook->user->name}\n";
            echo "- Book: {$userBook->book->name}\n";
            echo "- Book Page Count: {$userBook->book->page_count}\n";
            echo "- Total Pages Read: {$totalPagesRead}\n";
            echo "- Expected Progress: " . number_format($expectedProgress, 2) . "%\n";
            echo "- Actual Progress: " . number_format($actualProgress, 2) . "%\n";
            echo "✅ " . (abs($expectedProgress - $actualProgress) < 0.01 ? "PASS" : "FAIL") . "\n\n";
        } else {
            echo "❌ No UserBook found for incomplete reading log\n\n";
        }
    } else {
        echo "❌ No incomplete reading logs found\n\n";
    }
    
    // Test Case 3: Book with zero or null page count
    echo "TEST CASE 3: Book with zero or null page count\n";
    echo "Expected: 0% regardless of pages read\n";
    
    $bookWithZeroPages = Book::where('page_count', 0)->orWhereNull('page_count')->first();
    if ($bookWithZeroPages) {
        $userBook = UserBook::where('book_id', $bookWithZeroPages->id)->first();
        
        if ($userBook) {
            $progress = $userBook->getProgressPercentage();
            echo "- User: {$userBook->user->name}\n";
            echo "- Book: {$userBook->book->name}\n";
            echo "- Book Page Count: " . ($userBook->book->page_count ?? 'NULL') . "\n";
            echo "- Progress: {$progress}%\n";
            echo "✅ " . ($progress == 0.0 ? "PASS" : "FAIL") . "\n\n";
        } else {
            echo "❌ No UserBook found for book with zero/null pages\n\n";
        }
    } else {
        echo "❌ No books with zero or null page count found\n\n";
    }
    
    // Test Case 4: Progress capped at 100%
    echo "TEST CASE 4: Progress capped at 100%\n";
    echo "Expected: Maximum 100% even if pages read > book page count\n";
    
    // Find a case where total pages read might exceed book page count
    $userBooks = UserBook::with(['book', 'user'])->get();
    $foundOverreadCase = false;
    
    foreach ($userBooks as $userBook) {
        if ($userBook->book->page_count > 0) {
            $totalPagesRead = UserReadingLog::where('user_id', $userBook->user_id)
                ->where('book_id', $userBook->book_id)
                ->sum('pages_read');
            
            if ($totalPagesRead > $userBook->book->page_count) {
                $progress = $userBook->getProgressPercentage();
                echo "- User: {$userBook->user->name}\n";
                echo "- Book: {$userBook->book->name}\n";
                echo "- Book Page Count: {$userBook->book->page_count}\n";
                echo "- Total Pages Read: {$totalPagesRead}\n";
                echo "- Progress: {$progress}%\n";
                echo "✅ " . ($progress <= 100.0 ? "PASS" : "FAIL") . " (Capped at 100%)\n\n";
                $foundOverreadCase = true;
                break;
            }
        }
    }
    
    if (!$foundOverreadCase) {
        echo "ℹ️ No cases found where pages read > book page count\n\n";
    }
    
    echo "✅ Progress percentage calculation tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
