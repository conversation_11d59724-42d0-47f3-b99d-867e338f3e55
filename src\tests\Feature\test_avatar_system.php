<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Avatar;
use App\Models\UserAvatar;
use App\Models\UserPoint;
use App\Models\UserReadingLog;
use App\Models\UserActivity;

try {
    echo "=== TESTING COMPREHENSIVE AVATAR SYSTEM ===\n\n";
    
    // Get test user and avatars
    $user = User::first();
    $lowPointAvatar = Avatar::where('required_points', '<=', 50)->first();
    $highPointAvatar = Avatar::where('required_points', '>', 100)->first();
    
    if (!$user || !$lowPointAvatar || !$highPointAvatar) {
        echo "❌ Missing test data. Need user and avatars with different point requirements\n";
        exit;
    }
    
    echo "Test User: {$user->name}\n";
    echo "Low Point Avatar: {$lowPointAvatar->name} ({$lowPointAvatar->required_points} pts)\n";
    echo "High Point Avatar: {$highPointAvatar->name} ({$highPointAvatar->required_points} pts)\n\n";
    
    // Clean up existing data
    UserAvatar::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserReadingLog::where('user_id', $user->id)->delete();
    UserActivity::where('user_id', $user->id)->delete();
    
    // Test Case 1: Check initial state (no points, no avatar)
    echo "TEST CASE 1: Initial state (no points, no avatar)\n";
    $currentAvatar = $user->getCurrentAvatar();
    $totalPoints = $user->total_points;
    $canSelectLow = $user->canSelectAvatar($lowPointAvatar->id);
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    
    echo "- Current Avatar: " . ($currentAvatar ? $currentAvatar->name : 'None') . "\n";
    echo "- Total Points: {$totalPoints}\n";
    echo "- Can Select Low Point Avatar: " . ($canSelectLow ? 'YES' : 'NO') . "\n";
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "✅ " . (is_null($currentAvatar) && $totalPoints === 0 && !$canSelectLow && !$canSelectHigh ? "PASS" : "FAIL") . "\n\n";
    
    // Test Case 2: Add points and test avatar selection
    echo "TEST CASE 2: Add points and test avatar selection\n";
    UserPoint::create([
        'point_date' => now(),
        'user_id' => $user->id,
        'book_id' => null,
        'source_id' => null,
        'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        'points' => 75, // Enough for low point avatar, not enough for high point
    ]);
    
    // Refresh user data
    $user->refresh();
    $totalPoints = $user->total_points;
    $canSelectLow = $user->canSelectAvatar($lowPointAvatar->id);
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    
    echo "- Total Points After Adding: {$totalPoints}\n";
    echo "- Can Select Low Point Avatar: " . ($canSelectLow ? 'YES' : 'NO') . "\n";
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($totalPoints === 75 && $canSelectLow && !$canSelectHigh ? "PASS" : "FAIL") . "\n\n";
    
    // Test Case 3: Select avatar
    echo "TEST CASE 3: Select avatar\n";
    $selectionResult = $user->selectAvatar($lowPointAvatar->id);
    $currentAvatar = $user->getCurrentAvatar();
    
    echo "- Selection Result: " . ($selectionResult ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Current Avatar: " . ($currentAvatar ? $currentAvatar->name : 'None') . "\n";
    echo "✅ " . ($selectionResult && $currentAvatar && $currentAvatar->id === $lowPointAvatar->id ? "PASS" : "FAIL") . "\n\n";
    
    // Test Case 4: Test avatar mood display (no recent activity - should show base image)
    echo "TEST CASE 4: Avatar mood display (no recent activity)\n";
    $avatarImage = $user->getAvatarDisplayImage();
    $hasActivityToday = $user->hasReadingActivityToday();
    $daysSinceReading = $user->getDaysSinceLastReading();
    
    echo "- Has Activity Today: " . ($hasActivityToday ? 'YES' : 'NO') . "\n";
    echo "- Days Since Last Reading: {$daysSinceReading}\n";
    echo "- Avatar Image URL: {$avatarImage}\n";
    echo "- Expected: Base Image ({$lowPointAvatar->base_image})\n";
    echo "✅ " . ($avatarImage === $lowPointAvatar->base_image ? "PASS" : "FAIL") . "\n\n";
    
    // Test Case 5: Add reading activity for today (should show happy image)
    echo "TEST CASE 5: Add reading activity for today (should show happy image)\n";
    $book = \App\Models\Book::first();
    if ($book) {
        UserReadingLog::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'log_date' => now()->toDateString(),
            'pages_read' => 10,
            'book_completed' => false,
        ]);
        
        $hasActivityToday = $user->hasReadingActivityToday();
        $avatarImage = $user->getAvatarDisplayImage();
        $expectedImage = $lowPointAvatar->happy_image ?: $lowPointAvatar->base_image;
        
        echo "- Has Activity Today: " . ($hasActivityToday ? 'YES' : 'NO') . "\n";
        echo "- Avatar Image URL: {$avatarImage}\n";
        echo "- Expected: Happy Image ({$expectedImage})\n";
        echo "✅ " . ($hasActivityToday && $avatarImage === $expectedImage ? "PASS" : "FAIL") . "\n\n";
    }
    
    // Test Case 6: Test insufficient points for high point avatar
    echo "TEST CASE 6: Test insufficient points for high point avatar\n";
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    $selectionResult = $user->selectAvatar($highPointAvatar->id);
    $currentAvatar = $user->getCurrentAvatar();
    
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "- Selection Result: " . ($selectionResult ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Current Avatar (should remain unchanged): " . ($currentAvatar ? $currentAvatar->name : 'None') . "\n";
    echo "✅ " . (!$canSelectHigh && !$selectionResult && $currentAvatar->id === $lowPointAvatar->id ? "PASS" : "FAIL") . "\n\n";
    
    // Test Case 7: Test avatar change (update existing selection)
    echo "TEST CASE 7: Test avatar change (update existing selection)\n";
    
    // Add more points
    UserPoint::create([
        'point_date' => now(),
        'user_id' => $user->id,
        'book_id' => null,
        'source_id' => null,
        'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
        'points' => 50, // Total will be 125
    ]);
    
    $user->refresh();
    $totalPoints = $user->total_points;
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    $selectionResult = $user->selectAvatar($highPointAvatar->id);
    $currentAvatar = $user->getCurrentAvatar();
    
    // Check that only one UserAvatar record exists
    $avatarCount = UserAvatar::where('user_id', $user->id)->count();
    
    echo "- Total Points: {$totalPoints}\n";
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "- Selection Result: " . ($selectionResult ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Current Avatar: " . ($currentAvatar ? $currentAvatar->name : 'None') . "\n";
    echo "- Avatar Records Count: {$avatarCount}\n";
    echo "✅ " . ($totalPoints === 125 && $canSelectHigh && $selectionResult && 
                 $currentAvatar->id === $highPointAvatar->id && $avatarCount === 1 ? "PASS" : "FAIL") . "\n\n";
    
    // Test Case 8: Test available and locked avatars
    echo "TEST CASE 8: Test available and locked avatars\n";
    $availableAvatars = $user->getAvailableAvatars();
    $lockedAvatars = $user->getLockedAvatars();
    
    echo "- Available Avatars: {$availableAvatars->count()}\n";
    echo "- Locked Avatars: {$lockedAvatars->count()}\n";
    
    foreach ($availableAvatars as $avatar) {
        echo "  - Available: {$avatar->name} ({$avatar->required_points} pts)\n";
    }
    
    foreach ($lockedAvatars as $avatar) {
        echo "  - Locked: {$avatar->name} ({$avatar->required_points} pts)\n";
    }
    
    echo "✅ PASS - Avatar lists generated\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    UserAvatar::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserReadingLog::where('user_id', $user->id)->delete();
    UserActivity::where('user_id', $user->id)->delete();
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Comprehensive avatar system tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
