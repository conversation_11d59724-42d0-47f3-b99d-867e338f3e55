<?php

namespace App\Services\BookDiscovery;

use App\Services\BookDiscovery\Contracts\BookDiscoveryProviderInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use DOMDocument;
use DOMXPath;

abstract class AbstractBookDiscoveryProvider implements BookDiscoveryProviderInterface
{
    protected array $config;
    protected string $providerKey;

    public function __construct(string $providerKey, array $config)
    {
        $this->providerKey = $providerKey;
        $this->config = $config;
    }

    /**
     * Search for a book by ISBN.
     */
    public function searchByIsbn(string $isbn): ?array
    {
        $cacheKey = "book_discovery_{$this->providerKey}_{$isbn}";
        
        // Check cache first
        if (config('book_discovery.settings.cache_duration') > 0) {
            $cached = Cache::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        try {
            $url = $this->buildSearchUrl($isbn);
            $html = $this->fetchPageContent($url);
            
            if (!$html) {
                return null;
            }

            if ($this->isBookNotFound($html)) {
                $this->cacheResult($cacheKey, null);
                return null;
            }

            $bookData = $this->parseBookData($html);
            
            if ($bookData && $this->validateBookData($bookData)) {
                $this->cacheResult($cacheKey, $bookData);
                return $bookData;
            }

        } catch (\Exception $e) {
            Log::error("Book discovery error for provider {$this->providerKey}", [
                'isbn' => $isbn,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        $this->cacheResult($cacheKey, null);
        return null;
    }

    /**
     * Fetch page content with retry mechanism.
     */
    protected function fetchPageContent(string $url): ?string
    {
        $maxRetries = config('book_discovery.settings.max_retries', 3);
        $retryDelay = config('book_discovery.settings.retry_delay', 2);
        
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $response = Http::timeout($this->config['timeout'])
                    ->withHeaders([
                        'User-Agent' => $this->getRandomUserAgent(),
                        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language' => 'tr-TR,tr;q=0.9,en;q=0.8',
                        'Accept-Encoding' => 'gzip, deflate',
                        'Connection' => 'keep-alive',
                        'Upgrade-Insecure-Requests' => '1',
                    ])
                    ->withoutVerifying() 
                    ->get($url);

                if ($response->successful()) {
                    return $response->body();
                }

                Log::warning("HTTP error for {$this->providerKey}", [
                    'url' => $url,
                    'status' => $response->status(),
                    'attempt' => $attempt
                ]);

            } catch (\Exception $e) {
                Log::warning("Request failed for {$this->providerKey}", [
                    'url' => $url,
                    'error' => $e->getMessage(),
                    'attempt' => $attempt
                ]);
            }

            if ($attempt < $maxRetries) {
                sleep($retryDelay);
            }
        }

        return null;
    }

    /**
     * Build search URL for the given ISBN.
     */
    protected function buildSearchUrl(string $isbn): string
    {
        return str_replace('{isbn}', urlencode($isbn), $this->config['search_url']);
    }

    /**
     * Check if the page indicates book not found.
     */
    protected function isBookNotFound(string $html): bool
    {
        $indicators = $this->config['not_found_indicators'];
        
        // Check text patterns
        foreach ($indicators['text_patterns'] as $pattern) {
            if (stripos($html, $pattern) !== false) {
                return true;
            }
        }

        // Check empty selectors
        $dom = $this->createDomDocument($html);
        if (!$dom) {
            return false;
        }

        $xpath = new DOMXPath($dom);
        
        foreach ($indicators['empty_selectors'] as $selector) {
            $elements = $xpath->query($this->cssToXpath($selector));
            if ($elements->length === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Parse book data from HTML.
     */
    protected function parseBookData(string $html): ?array
    {
        $dom = $this->createDomDocument($html);
        if (!$dom) {
            return null;
        }

        $xpath = new DOMXPath($dom);
        $bookData = [];
        $parsing = $this->config['parsing'];

        foreach ($parsing as $field => $rules) {
            $value = $this->extractFieldValue($xpath, $rules);
            if ($value !== null) {
                $bookData[$field] = $value;
            }
        }

        return empty($bookData) ? null : $bookData;
    }

    /**
     * Extract field value using parsing rules.
     */
    protected function extractFieldValue(DOMXPath $xpath, array $rules)
    {
        foreach ($rules['selectors'] as $selector) {
            $elements = $xpath->query($this->cssToXpath($selector));
            
            if ($elements->length > 0) {
                $values = [];
                
                foreach ($elements as $element) {
                    $value = $this->getElementValue($element, $rules['attribute']);
                    
                    if (!empty($value)) {
                        // Apply cleanup regex if specified
                        if (isset($rules['cleanup_regex'])) {
                            if (preg_match($rules['cleanup_regex'], $value, $matches)) {
                                $value = $matches[1] ?? $value;
                            }
                        }
                        
                        $value = trim($value);
                        if (!empty($value)) {
                            $values[] = $value;
                        }
                    }
                }

                if (!empty($values)) {
                    if (isset($rules['multiple']) && $rules['multiple']) {
                        return $values;
                    } else {
                        return $values[0];
                    }
                }
            }
        }

        return null;
    }

    /**
     * Get element value by attribute.
     */
    protected function getElementValue($element, string $attribute): string
    {
        switch ($attribute) {
            case 'text':
                return $element->textContent;
            case 'html':
                return $element->ownerDocument->saveHTML($element);
            default:
                return $element->getAttribute($attribute);
        }
    }

    /**
     * Convert CSS selector to XPath.
     */
    protected function cssToXpath(string $selector): string
    {
        // Basic CSS to XPath conversion
        $selector = trim($selector);
        
        // Handle class selectors
        $selector = preg_replace('/\.([a-zA-Z0-9_-]+)/', '[contains(@class,"$1")]', $selector);
        
        // Handle ID selectors
        $selector = preg_replace('/#([a-zA-Z0-9_-]+)/', '[@id="$1"]', $selector);
        
        // Handle descendant selectors
        $selector = str_replace(' ', '//', $selector);
        
        // Handle direct child selectors
        $selector = str_replace('>', '/', $selector);
        
        // Add leading // if not present
        if (!str_starts_with($selector, '//')) {
            $selector = '//' . $selector;
        }

        return $selector;
    }

    /**
     * Create DOM document from HTML.
     */
    protected function createDomDocument(string $html): ?DOMDocument
    {
        $dom = new DOMDocument();
        
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
        
        $success = $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        
        libxml_clear_errors();
        
        return $success ? $dom : null;
    }

    /**
     * Get random user agent.
     */
    protected function getRandomUserAgent(): string
    {
        $userAgents = $this->config['user_agents'];
        return $userAgents[array_rand($userAgents)];
    }

    /**
     * Cache the result.
     */
    protected function cacheResult(string $key, $value): void
    {
        $duration = config('book_discovery.settings.cache_duration', 3600);
        if ($duration > 0) {
            Cache::put($key, $value, $duration);
        }
    }

    /**
     * Get provider name.
     */
    public function getName(): string
    {
        return $this->config['name'];
    }

    /**
     * Get provider priority.
     */
    public function getPriority(): int
    {
        return $this->config['priority'];
    }

    /**
     * Check if provider is enabled.
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? true;
    }

    /**
     * Validate extracted book data.
     */
    public function validateBookData(array $data): bool
    {
        $validation = config('book_discovery.validation');
        
        // Title is required
        if (empty($data['name'])) {
            return false;
        }

        // Validate title length
        $titleLength = mb_strlen($data['name']);
        if ($titleLength < $validation['name']['min_length'] || 
            $titleLength > $validation['name']['max_length']) {
            return false;
        }

        // Validate year if present
        if (isset($data['year'])) {
            $year = (int) $data['year'];
            $maxYear = $validation['year']['max_value'] ?? (date('Y') + 1);
            if ($year < $validation['year']['min_value'] || $year > $maxYear) {
                unset($data['year']);
            }
        }

        // Validate page count if present
        if (isset($data['page_count'])) {
            $pageCount = (int) $data['page_count'];
            if ($pageCount < $validation['page_count']['min_value'] || 
                $pageCount > $validation['page_count']['max_value']) {
                unset($data['page_count']);
            }
        }

        return true;
    }
}
