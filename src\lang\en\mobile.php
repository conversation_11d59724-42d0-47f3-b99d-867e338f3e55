<?php

return [
    // General
    'app_name' => 'Okumobil',
    'welcome' => 'Welcome',
    'login' => 'Login',
    'home' => 'Home',
    'books' => 'My Books',
    'activities' => 'Activities',
    'friends' => 'My Friends',
    'profile' => 'My Profile',
    'me' => 'Me',
    'back' => 'Back',
    'save' => 'Save',
    'submit' => 'Submit',
    'search_button' => 'SEARCH',
    'view' => 'View',
    'books_alt' => 'Books',
    'badges_alt' => 'Badges',
    'rewards_alt' => 'Rewards',
    'points_alt' => 'Points',
    'required' => 'Required',
    'need_approval' => 'Approval Required',
    'or' => 'OR',
    'close' => 'Close',
    'books_localizable' => 'Books',
    'activities_localizable' => 'Activities',
    'points_localizable' => 'Points',
    'students_localizable' => 'Students',
    'pages_localizable' => 'Pages',


    // Greetings
    'good_morning' => 'Good morning',
    'good_afternoon' => 'Good afternoon',
    'good_evening' => 'Good evening',

    // Login
    'remember_me' => 'Remember me',
    'login_button' => 'LOGIN',
    'invalid_credentials' => 'Invalid username or password.',
    'logging_in' => 'Logging in...',
    'enter_username' => 'Enter user name...',
    'back_to_username' => '← Back to username',
    'your_password' => 'Your password',
    'login_again' => 'Session has expired. Please log in again.',
    'login_to_continue' => 'Please log in to continue.',

    


    // Avatar Selection
    'avatar_selection' => 'Avatar Selection',
    'avatar_subtitle' => 'Let\'s select an avatar for you',
    'select_avatar' => 'SELECT',
    'choose_later' => 'LATER',
    'avatar_selection_error' => 'Failed to select avatar. Please try again.',
    'selecting' => 'Selecting...',

    // Welcome Celebration
    'badge_unlocked' => 'Badge Unlocked',
    'congratulations' => 'Congratulations',
    'all_set' => 'You are all set to start reading!',
    'start' => 'START',
    'you_unlocked_badge' => 'You unlocked a badge!',
    'team_unlocked_badge' => 'Your team unlocked a badge!',
    'avatar_unlocked' => 'Avatar Unlocked!',
    'next' => 'NEXT',
    'yay' => 'YAY!',

    // Home Screen
    'badges' => 'Badges',
    'points' => 'Points',
    'start_reading_journey' => 'Start your reading journey by adding your first book!',
    'read_tab' => '📖 READ',
    'earn_points_tab' => '⭐ EARN POINTS',
    'keep_streak_alive' => 'Keep your reading streak alive!',
    'assigned_tasks' => 'My Tasks',
    'active_challenges' => 'Active Challenges',
    'complete_books_message' => 'Complete some books to unlock activities and earn points!',
    'completed' => 'Completed',
    'add_new_book_to_read' => 'Add a book to read',
    'pages_left' => 'pages left',
    'due_date' => 'Due Date',
    'due_on' => 'Due on: ',
    'days' => 'days',
    'weeks' => 'weeks',
    'months' => 'months',
    'almost_there' => 'Almost There!',


    // Books
    'my_books' => 'My Books',
    'add_new_book' => 'Add New Book',
    'no_completed_books' => 'No completed books yet',
    'complete_first_book' => 'Complete your first book to see it here and unlock activities!',
    'log_reading' => '📝 Log Reading',
    'complete_book' => '✅ Complete',
    'progress' => 'Progress',
    'completed_on' => 'Completed on',
    'book_completed_success' => 'Congratulations! You completed the book!',
    'book_removed_success' => 'Book removed from your list.',
    'book_already_in_list' => 'This book is already in your reading list!',
    'book_added_success' => 'Book added to your reading list!',
    'add_new' => 'ADD NEW',
    'type_book_name_to_search' => 'Type book name to search',
    'reading' => 'Reading',
    'completed_reading' => 'completed reading',

    // Add Book
    'how_to_find_barcode' => '📚 How to find book barcode?',
    'how_to_find_barcode_desc' => 'Find the barcode on the back cover and enter the numbers below, without any spaces',
    'scan_barcode' => 'Scan Barcode',
    'scan_with_camera' => 'Scan with your device camera',
    'camera_not_available' => 'Camera not available',
    'camera_not_available_desc' => 'Camera functionality is not available on this device. Please enter ISBN manually.',
    'enter_book_barcode' => 'Enter book barcode',
    'isbn' => 'ISBN',
    'pages' => 'Pages',
    'add_book' => '📚 Add Book',
    'book_found' => 'Book found in library!',
    'add_to_books' => '📚 Add to My Books',
    'search_again' => '🔄 Search Again',
    'ready_to_scan' => 'Ready to Scan',
    'point_camera_at_barcode' => 'Point your camera at the book\'s barcode',
    'start_scanning' => '📷 Start Scanning',
    'stop_scanning' => '❌ Stop Scanning',
    'position_barcode' => 'Position barcode within the frame',
    'book_created_success' => 'Book created and added to your reading list!',
    'failed_add_book' => 'Failed to add book. Please try again.',
    'searching_for_book' => 'Searching for book...',
    'type_isbn_manually' => 'Type ISBN manually',
    'unknown_author' => 'Unknown Author',
    'adding' => 'Adding...',
    'invalid_isbn' => 'Please enter a valid ISBN (10 or 13 digits).',
    'barcode_scanned_successfully' => 'Barcode scanned successfully! Searching for book...',
    'invalid_barcode_format' => 'Invalid barcode format. Please try scanning again or enter ISBN manually.',
    'initializing_camera' => 'Initializing camera...',
    'please_allow_camera_access' => 'Please allow camera access to scan the barcode.',

    // Book Discovery
    'book_found_external' => 'Book found! Please complete the information below.',
    'select_book_type' => 'Select Book Type',
    'enter_page_count' => 'Enter page count',
    'page_count_help' => 'If you know the page count, please enter it to help with reading progress tracking.',
    'creating_book' => 'Creating Book...',
    'book_not_found_try_again' => 'Book not found. Please check the barcode and try again.',
    'isbn_search_error' => 'ISBN search error: ',
    'an_error_occurred_try_again' => 'An error occurred while searching. Please try again.',
    'no_book_selected' => 'No book selected.',
    'book_already_in_list' => 'This book is already in your reading list!',
    'book_added_success' => 'Book added to your reading list!',

    // Reading Log
    'reading_log' => 'Reading Log',
    'pages_read' => 'Pages Read',
    'reading_history' => '📚 Reading History',
    'no_reading_logs' => 'No reading logs yet',
    'start_tracking_progress' => 'Start tracking your reading progress!',
    'log_added_success' => 'Reading log added successfully!',
    'failed_add_log' => 'Failed to add reading log. Please try again.',
    'log_deleted_success' => 'Reading log deleted.',
    'day_streak' => 'Day Streak',
    'save_log_button' => 'SAVE',
    'complete_the_book_button' => 'COMPLETE THE BOOK',
    'today' => 'TODAY',
    'yesterday' => 'YESTERDAY',
    'failed_to_complete_book' => 'Failed to complete book. Please try again.',
    'promise_required' => 'You must promise that the information is true and accurate.',
    'promise_accepted' => 'You must promise that the information is true and accurate.',
    'book_already_completed' => 'This book is already completed!',
    'book_already_completed_in_session' => 'This book is already completed in your current reading session!',
    'promise_info' => 'I promise this information is true and accurate.',
    'minutes' => 'Minutes',
    'saving' => 'Saving...',
    

    // Activities
    'earn_points' => 'Earn Points',
    'pending_approval' => 'Pending Approval',
    'completed_activities' => 'Completed Activities',
    'available_activities' => 'Available Activities',
    'all_activities_completed' => 'All activities completed!',
    'great_job_completed' => 'Great job! You\'ve completed all available activities for this book.',
    'activity_already_completed' => 'You have already completed this activity.',
    'activity_already_started' => 'You have already started this activity.',
    'unknown_activity_type' => 'Unknown activity type.',
    'activity_not_found' => 'Activity not found.',
    'submitted_on' => 'Submitted on',
    'writing_activity' => 'Writing',
    'rating_activity' => 'Rating',
    'upload_activity' => 'Art Upload',
    'thank_you_for_your_submission' => 'Thank you for your submission!',
    'your_submission_is_pending_teacher_approval' => 'Your submission is pending teacher approval.',
    'next_unlock' => 'Next unlock: ',
    'in_progress' => 'In Progress',
    'avatar_unlock_progress' => 'Avatar Unlock Progress',
    'complete_activities_to_unlock' => 'Complete <strong>:points more points</strong> worth of activities to unlock new avatars!',
    'rejected_activities' => 'Rejected Activities',
    'rejected' => 'Rejected',
    'please_revise_and_resubmit' => 'Please revise and resubmit.',
    'edit_and_resubmit' => 'Edit & Resubmit',
    'writing' => 'Writing',
    'rating' => 'Rating',
    'art_upload' => 'Art Upload', 


    // Writing Activity
    'writing_activity_title' => '✍️ Writing Activity',
    'your_writing' => 'Your Writing',
    'start_writing_placeholder' => 'Start writing your thoughts about the book here...',
    'submit_writing' => 'Submit Writing',
    'words' => 'words',
    'characters' => 'characters',
    'failed_submit_writing' => 'Failed to submit your writing. Please try again.',

    // Rating Activity
    'rating_activity_title' => 'Rating Activity',
    'how_rate_book' => 'How would you rate this book?',
    'rate_from_min_to_max_stars' => 'Rate from :min to :max stars',
    'tap_stars_to_rate' => 'Tap the stars to rate',
    'why_rating' => 'Tell us why you gave this rating (Optional)',
    'what_like_dislike' => 'What did you like or dislike about this book?',
    'update_rating' => 'Update Rating',
    'submit_rating' => 'Submit Rating',
    'select_rating_to_submit' => 'Please select a rating to submit',
    'failed_to_submit_rating_try_again' => 'Failed to submit your rating. Please try again.',
    'updating' => 'Updating...',
    'submitting' => 'Submitting...',    


    // Upload Activity
    'upload_activity_title' => '🎨 Art Upload',
    'upload_audio_file' => 'Upload an audio file',
    'upload_photo' => 'Upload a photo',
    'tap_to_select' => 'Tap to select from your device',
    'mp3_wav_m4a_aac_up_to_10mb' => 'MP3, WAV, M4A, AAC up to 10MB',
    'jpg_png_5mb' => 'JPG, PNG up to 5MB',
    'describe_artwork' => 'Describe Your Artwork (Optional)',
    'tell_about_artwork' => 'Tell us about your artwork...',
    'update_submission' => 'Update Submission',
    'submit_artwork' => 'Submit Artwork',
    'upload_file_to_submit' => 'Please upload a file to submit',
    'failed_submit_media' => 'Failed to submit your media. Please try again.',
    'uploading' => 'Uploading...',
    'invalid_audio_file_format' => 'Invalid audio file format. Please upload MP3, WAV, M4A, or AAC files only.',

    // PWA
    'add_to_home_screen' => 'Add to Home Screen',
    'install_app_better' => 'Install our app for a better experience!',
    'install' => 'Install',
    'later' => 'Later',
    'enable_notifications' => 'Enable Notifications',
    'get_notified_progress' => 'Get notified about your reading progress!',
    'get_notified_teacher' => 'Get notified about your students\' reading progress!',
    'allow' => 'Allow',

    // Friends page
    'all' => 'All',
    'my_class' => 'My Class',
    'my_teams' => 'My Teams',
    'name_asc' => 'Sort by Name',
    'books_desc' => 'Sort by Books Read',
    'badges_desc' => 'Sort by Badges',
    'points_desc' => 'Sort by Activity Points',
    'currently_reading' => 'Currently Reading',
    'not_currently_reading' => 'Not currently reading',
    'no_friends_found' => 'No Friends Found',
    'no_classmates_message' => 'You don\'t have any classmates yet. Join a class to see your classmates here.',
    'no_teammates_message' => 'You don\'t have any teammates yet. Join a team to see your teammates here.',
    'no_friends_message' => 'You don\'t have any friends yet. Join a class or team to connect with other students.',


    // Test Activities
    'test_activity_title' => 'Test Activity',
    'quiz' => 'Quiz',
    'vocabulary_test' => 'Vocabulary Test',
    'test_passed' => 'Test Passed!',
    'test_failed' => 'Test Failed',
    'score' => 'Score',
    'correct_answers' => 'Correct Answers',
    'minimum_grade_required' => 'Minimum grade required',
    'remaining_attempts' => 'Remaining attempts',
    'retake_test' => 'Retake Test',
    'back_to_activities' => 'Back to Activities',
    'progress' => 'Progress',
    'previous' => 'Previous',
    'next' => 'Next',
    'submit_test' => 'Submit Test',
    'test_instructions' => 'Test Instructions',
    'answer_all_questions' => 'Answer all questions before submitting',
    'you_can_navigate_between_questions' => 'You can navigate between questions using the number buttons',
    'minimum_score_required' => 'Minimum score required: :score',
    'you_have_attempts' => 'You have :attempts attempts to pass this test',
    'please_answer_all_questions' => 'Please answer all questions before submitting',
    'test_passed_successfully' => 'Congratulations! You passed the test!',
    'test_failed_try_again' => 'You didn\'t pass this time. You can try again if you have remaining attempts.',
    'test_retry_limit_exceeded' => 'You have exceeded the maximum number of attempts for this test',
    'insufficient_questions_for_quiz' => 'This book doesn\'t have enough questions to generate a quiz',
    'insufficient_words_for_vocabulary_test' => 'This book doesn\'t have enough words to generate a vocabulary test',
    'invalid_activity_type' => 'Invalid activity type',
    'required_activities_pending' => 'Required Activities Pending',
    'complete_required_activities_message' => 'You have mandatory activities that need to be completed before you can earn full rewards for this book.',
    'failed_to_generate_quiz' => 'Failed to generate quiz. Please try again later.',
    'test_retry_limit_exceeded' => 'You have exceeded the maximum number of attempts for this test.',
    'test_in_progress' => 'You have a test in progress. Please complete it first.',
    'failed_tests' => 'Failed Tests',
    'test_failed' => 'Test Failed',
    'definition_question' => 'What does :word mean?', 
    'synonym_question' => 'What is a synonym for :word?',
    'antonym_question' => 'What is an antonym for :word?',   

    // Validation Messages
    'field_required' => 'This field is required.',
    'min_words' => 'Please write at least :min words. Current word count: :count.',

    // Confirmation Messages

    // Profile Page
    'change_avatar' => 'Change Avatar',
    'profile_books' => 'Books',
    'page_points' => 'Page Points',
    'activity_points' => 'Activity Points',
    'last_30_days' => 'Last 30 Days',
    'earned_badges' => 'Earned Badges',
    'view_all' => 'View All',
    'no_badges_yet' => 'No badges yet',
    'complete_activities_to_earn_badges' => 'Complete activities to earn badges',

    // Profile motivational messages
    'message_unstoppable_streaker' => 'You\'re on fire! Keep turning pages and making progress!',
    'message_consistent_comebacker' => 'Your comeback is inspiring, every day you read adds to your momentum!',
    'message_weekend_warrior' => 'Every page counts, your unique rhythm still builds your story!',
    'message_restart_hero' => 'Starting again takes courage, your new chapter is already in motion!',
    'message_burnout_survivor' => 'Even the best stories pause, when You\'re ready, your next page awaits.',
    'message_explorer' => 'Exploring at your pace is still a journey, keep discovering one page at a time.',
    'message_recent_recruit' => 'You\'ve taken the first step, let the pages pull you forward!',
    'message_almost_streaker' => 'You\'re closer than you think, just one more day can build your streak story!',
    'message_lost_and_found' => 'Welcome back, your story didn\'t end, it just took a plot twist.',
    'message_power_starter' => 'You\'ve already proven you can, why not start again today?',
    'message_streak_default' => 'Every book is a new adventure waiting for you to explore. Discover the adventurer inside you!',

    // Avatar Selection
    'select_avatar' => 'Select Avatar',
    'available_avatars' => 'Available Avatars',
    'unlockable_avatars' => 'Unlockable Avatars',
    'insufficient_points_for_avatar' => 'You don\'t have enough activity points for this avatar',
    'avatar_selected_successfully' => 'Avatar selected successfully!',
    'failed_to_select_avatar' => 'Failed to select avatar. Please try again.',
    'no_avatars_available' => 'No avatars available at the moment',

    // My Badges
    'my_badges' => 'My Badges',
    'badge' => 'Badge',
    'from_book' => 'From book',
    'from_activity' => 'From activity',
    'how_to_earn_badges' => 'How to earn badges',
    'complete_reading_activities' => 'Complete reading activities',
    'finish_books' => 'Finish reading books',
    'maintain_reading_streaks' => 'Maintain reading streaks',
    'participate_in_challenges' => 'Participate in challenges',

    // My Rewards (All Types)
    'my_rewards' => 'My Rewards',
    'rewards' => 'Rewards',
    'earned_rewards' => 'Earned Rewards',
    'no_rewards_yet' => 'No rewards yet',
    'complete_activities_to_earn_rewards' => 'Complete activities to earn rewards',
    'how_to_earn_rewards' => 'How to earn rewards',
    'reward_types' => 'Reward Types',
    'gift' => 'Gift',
    'trophy' => 'Trophy',
    'card' => 'Card',
    'item' => 'Item',

    // Login Screen
    'your_password' => 'Your password',
    'back_to_username' => '← Back to username',

    // Congratulatory Messages
    'congratulations_first_place' => 'Amazing! You\'re #1 in your class!',
    'congratulations_top_reader' => 'Great Job! Top Reader!',
    'first_place_message' => 'You\'re the top reader in :class with :books books completed! Keep up the excellent work!',
    'top_reader_message' => 'You\'re ranked #:rank in :class with :books books completed! You\'re doing fantastic!',
    'books_needed_next_rank' => 'Read :books more book(s) to reach #:rank place!',

    // Book Active Status Messages
    'book_inactive_warning' => 'This book is currently inactive and cannot be used for reading logs or activities.',

    // Level System
    'level' => 'Level',
    'level_progress' => 'Level Progress',
    'next_level' => 'Next Level',
    'next_level_progress_text' => 'You are at :progress% towards the next level.',  
    'level_achieved' => 'Level Achieved!',
    'level_requirements' => 'Level Requirements',
    'books_and_points' => ':books books and :points points',
    'books_or_points' => ':books books or :points points',
    'max_level_reached' => 'You reached the maximum level!',
    'more_books' => 'more books',
    'more_points' => 'more points',

    // Teacher-specific translations
    'teacher_dashboard' => 'Teacher Dashboard',
    'teacher' => 'Teacher',
    'last_24_hours' => 'Last 24 Hours',
    'pending_activities' => 'Pending Activities',
    'activity_review' => 'Activity Review',
    'activity_content' => 'Activity Content',
    'student' => 'Student',
    'by' => 'by',
    'began_reading' => 'began reading',
    'read_pages' => 'read :pages pages',
    'started' => 'Started',
    'minutes' => 'minutes',
    'no_activities_last_24h' => 'No Activities in Last 24 Hours',
    'no_student_activities_description' => 'Your students haven\'t had any reading activities in the past 24 hours.',
    'no_pending_activities' => 'No pending activities to review',
    'review' => 'Review',
    'student_rating' => 'Student Rating',
    'student_uploaded_image' => 'Student uploaded image',
    'student_uploaded_audio' => 'Student uploaded audio recording',
    'audio_not_supported' => 'Your browser does not support the audio element.',
    'feedback_optional' => 'Feedback (Optional)',
    'add_feedback_placeholder' => 'Add feedback for the student...',
    'accept' => 'Accept',
    'reject' => 'Reject',
    'processing' => 'Processing',
    'processing_request' => 'Processing your request',
    'activity_approved_successfully' => 'Activity approved successfully!',
    'activity_rejected_successfully' => 'Activity rejected successfully!',
    'error_approving_activity' => 'Error approving activity. Please try again.',
    'error_rejecting_activity' => 'Error rejecting activity. Please try again.',
    'activity_already_reviewed' => 'This activity has already been reviewed.',
    'view_all' => 'View All',
    'students' => 'Students',
    'access_denied' => 'Access denied. You need student or teacher role to use the mobile app.',
    'unauthorized_activity' => 'Unauthorized access to this activity.',


    // Messages
    'messages' => 'Messages',
    'message' => 'Message',
    'message_detail' => 'Message Detail',
    'unread_messages' => 'Unread Messages',
    'no_messages' => 'No messages yet',
    'no_unread_messages' => 'No unread messages',
    'mark_as_read' => 'Mark as Read',
    'message_from' => 'From',
    'sent_on' => 'Sent on',
    'back_to_messages' => 'Back to Messages',
    'my_messages' => 'My Messages',
    'unread' => 'Unread',

    // Add Book from Class Bookshelf
    'from_class_bookshelf' => 'From Class Bookshelf',
    'select_from_class_bookshelf' => 'Select from class bookshelf',
    'loading_class_books' => 'Loading class books...',
    'no_class_books' => 'No Books Available',
    'no_class_books_message' => 'Your class bookshelf is empty. Ask your teacher to add books to the class bookshelf.',
    'no_class_assigned' => 'You are not assigned to any class.',
    'error_loading_class_books' => 'Error loading class books. Please try again.',
    'add_book_to_collection_confirm' => 'Do you want to add this book to your reading collection?',
    'cancel' => 'Cancel',
    'failed_to_add_book' => 'Failed to add book to your collection. Please try again.',

    // Daily Reading Limits
    'daily_pages_limit_exceeded' => 'Daily pages limit exceeded! You can only read :limit pages per day. You have already read :current pages today and have :remaining pages remaining. You tried to add :attempted pages.',
    'daily_minutes_limit_exceeded' => 'Daily minutes limit exceeded! You can only read for :limit minutes per day. You have already read for :current minutes today and have :remaining minutes remaining. You tried to add :attempted minutes.',
    'daily_limits_both_exceeded' => 'Daily reading limits exceeded! You can only read :pages_limit pages and :minutes_limit minutes per day. You have :pages_remaining pages and :minutes_remaining minutes remaining today.',

];
