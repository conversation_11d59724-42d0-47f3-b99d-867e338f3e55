
Git işlemleri 
https://github.com/settings/tokens create personal access token 

(sadece src klasör<PERSON> için)
sadmin@debian12:~/web/okumobil.com/private/app$ git init
sadmin@debian12:~/web/okumobil.com/private/app$ git remote add origin https://(token)@github.com/bilalakcay/okuokuoku.git
sadmin@debian12:~/web/okumobil.com/private/app$ git config core.sparsecheckout true
sadmin@debian12:~/web/okumobil.com/private/app$ echo src/ >> .git/info/sparse-checkout
sadmin@debian12:~/web/okumobil.com/private/app$ git pull origin main

Composer install 
sadmin@debian12:~/web/okumobil.com/private/app/src$ curl -sS https://getcomposer.org/installer -o composer-setup.php
sadmin@debian12:~/web/okumobil.com/private/app/src$ php composer-setup.php
sadmin@debian12:~/web/okumobil.com/private/app/src$ mv composer.phar /usr/local/bin/composer ( !!!! Permission denied)
sadmin@debian12:~/web/okumobil.com/private/app/src$ mv composer.phar composer
sadmin@debian12:~/web/okumobil.com/private/app/src$ chmod +x composer
sadmin@debian12:~/web/okumobil.com/private/app/src$ php composer install --optimize-autoloader --no-dev

nodejs yüklenmedi sudo
node_modules elle kopyalama...

Laravel Kılavuz
https://stackoverflow.com/a/59664416

db import
mysql -u xxxxxx -p xxxx --init-command="SET SESSION FOREIGN_KEY_CHECKS=0;" < moondb.sql

public_html/.htaccess
RewriteEngine On
RewriteCond %{REQUEST_URI} !^/app/src/public
RewriteRule ^(.*)$ /app/src/public/$1 [L]

chmod -R 775 app/src/storage
chmod -R 775 app/src/bootstrap/cache
chown -R sadmin:sadmin app/src


// APP_URL panel ve mobil durumuna göre değişmediği için dosya storage adresi bozuluyordu. MoonShineServiceProvider değişikliği yaptık.
// set config filesystem public disk url based on request scheme with host 
config(['filesystems.disks.public.url' => request()->getSchemeAndHttpHost().'/storage']);
        