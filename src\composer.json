{"$schema": "https://getcomposer.org/schema.json", "name": "moonshine/app", "type": "project", "description": "The skeleton application for the Laravel framework with MoonShine.", "keywords": ["laravel", "framework", "moonshine"], "license": "MIT", "require": {"php": "^8.2", "laravel/framework": "^12.0", "laravel/pulse": "^1.4", "laravel/tinker": "^2.10.1", "livewire/livewire": "^3.6", "moonshine/advanced": "^1.0", "moonshine/apexcharts": "^1.2", "moonshine/import-export": "^1.0", "moonshine/moonshine": "^3.8", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-permission": "^6.20", "sweet1s/moonshine-roles-permissions": "^3.0"}, "require-dev": {"buggregator/trap": "^1.13", "fakerphp/faker": "^1.23", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php vendormodified/_check_vendor_updated_files.php"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"npm run dev\" --names='server,queue,vite'"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}