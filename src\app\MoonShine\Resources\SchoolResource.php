<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;


use App\Models\School;
use App\Models\EnumSchoolType;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;


#[Icon('building-office')]

class SchoolResource extends BaseResource
{
    protected string $model = School::class;

    protected string $column = 'name';

    public function getTitle(): string
    {
        return __('admin.schools');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),

                    BelongsTo::make(
                        __('admin.school_type'),
                        'schoolType',
                        formatted: fn(EnumSchoolType $type) => $type->name,
                        resource: EnumSchoolTypeResource::class
                    )
                        ->placeholder(__('admin.select_school_type')),
                ]),

                Flex::make([
                    Switcher::make(__('admin.active'), 'active')
                        ->default(true),
                ]),
            ]),




        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            BelongsTo::make(
                __('admin.school_type'),
                'schoolType',
                formatted: fn(EnumSchoolType $type) => $type->name,
                resource: EnumSchoolTypeResource::class
            ),

            Switcher::make(__('admin.active'), 'active'),

            HasMany::make(
                __('admin.school_classes'),
                'schoolClasses',
                resource: SchoolClassResource::class
            ),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'school_type_id' => ['nullable', 'exists:enum_school_types,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
