<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\UserSchool;
use App\Models\UserClass;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Set default schools for users who have only one school
        $userSchoolCounts = UserSchool::selectRaw('user_id, COUNT(*) as school_count')
            ->groupBy('user_id')
            ->having('school_count', '=', 1)
            ->get();

        foreach ($userSchoolCounts as $userSchoolCount) {
            UserSchool::where('user_id', $userSchoolCount->user_id)
                ->update(['default' => true]);
        }

        // For users with multiple schools, set the first active one as default
        $usersWithMultipleSchools = UserSchool::selectRaw('user_id, COUNT(*) as school_count')
            ->groupBy('user_id')
            ->having('school_count', '>', 1)
            ->pluck('user_id');

        foreach ($usersWithMultipleSchools as $userId) {
            $firstActiveSchool = UserSchool::where('user_id', $userId)
                ->where('active', true)
                ->first();

            if ($firstActiveSchool) {
                $firstActiveSchool->update(['default' => true]);
            }
        }

        // Set default classes for users who have only one class
        $userClassCounts = UserClass::selectRaw('user_id, COUNT(*) as class_count')
            ->groupBy('user_id')
            ->having('class_count', '=', 1)
            ->get();

        foreach ($userClassCounts as $userClassCount) {
            UserClass::where('user_id', $userClassCount->user_id)
                ->update(['default' => true]);
        }

        // For users with multiple classes, set the first active one as default
        $usersWithMultipleClasses = UserClass::selectRaw('user_id, COUNT(*) as class_count')
            ->groupBy('user_id')
            ->having('class_count', '>', 1)
            ->pluck('user_id');

        foreach ($usersWithMultipleClasses as $userId) {
            $firstActiveClass = UserClass::where('user_id', $userId)
                ->where('active', true)
                ->first();

            if ($firstActiveClass) {
                $firstActiveClass->update(['default' => true]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset all default flags
        UserSchool::query()->update(['default' => false]);
        UserClass::query()->update(['default' => false]);
    }
};
