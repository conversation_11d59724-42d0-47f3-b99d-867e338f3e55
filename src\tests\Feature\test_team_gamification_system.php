<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Team;
use App\Models\UserTeam;
use App\Models\TeamBadge;
use App\Models\Badge;
use App\Models\BadgeRule;
use App\Models\UserBadge;
use App\Models\UserPoint;
use App\Models\UserReadingLog;
use App\Models\EnumBadgeRuleType;
use App\Models\Book;

try {
    echo "=== TESTING TEAM-BASED GAMIFICATION SYSTEM ===\n\n";
    
    // Get test data
    $users = User::take(3)->get();
    $book = Book::first();
    
    if ($users->count() < 3 || !$book) {
        echo "❌ Missing test data. Need at least 3 users and 1 book\n";
        exit;
    }
    
    $user1 = $users[0];
    $user2 = $users[1];
    $user3 = $users[2];
    
    echo "Test Users: {$user1->name}, {$user2->name}, {$user3->name}\n";
    echo "Test Book: {$book->name}\n\n";
    
    // Clean up existing data
    TeamBadge::whereHas('team', function($q) { $q->where('name', 'LIKE', 'Test%'); })->delete();
    UserBadge::whereIn('user_id', [$user1->id, $user2->id, $user3->id])->delete();
    UserPoint::whereIn('user_id', [$user1->id, $user2->id, $user3->id])->delete();
    UserReadingLog::whereIn('user_id', [$user1->id, $user2->id, $user3->id])->delete();
    UserTeam::whereHas('team', function($q) { $q->where('name', 'LIKE', 'Test%'); })->delete();
    Team::where('name', 'LIKE', 'Test%')->delete();
    Badge::where('name', 'LIKE', 'Test%')->delete();
    
    // Test Case 1: Create test team and add members
    echo "TEST CASE 1: Create test team and add members\n";
    
    $team = Team::create([
        'name' => 'Test Reading Team',
        'logo' => null,
        'leader_user_id' => null,
        'active' => true,
    ]);
    
    // Add members to team
    UserTeam::addUserToTeam($user1->id, $team->id);
    UserTeam::addUserToTeam($user2->id, $team->id);
    UserTeam::addUserToTeam($user3->id, $team->id);
    
    // Set team leader
    $team->update(['leader_user_id' => $user1->id]);
    
    $team->refresh();
    echo "- Team Created: {$team->name}\n";
    echo "- Team Leader: {$team->leader_name}\n";
    echo "- Member Count: {$team->member_count}\n";
    echo "- Total Points: {$team->total_points}\n";
    
    // Verify user team relationships
    $user1Teams = $user1->getTeams();
    $isUser1Leader = $user1->isLeaderOfTeam($team->id);
    $isUser2Member = $user2->isMemberOfTeam($team->id);
    
    echo "- User1 Teams Count: {$user1Teams->count()}\n";
    echo "- User1 is Leader: " . ($isUser1Leader ? 'YES' : 'NO') . "\n";
    echo "- User2 is Member: " . ($isUser2Member ? 'YES' : 'NO') . "\n";
    
    echo "✅ " . ($team->member_count === 3 && $isUser1Leader && $isUser2Member ? "PASS" : "FAIL") . " - Team creation and membership\n\n";
    
    // Test Case 2: Create team badge with reading points rule
    echo "TEST CASE 2: Create team badge with reading points rule\n";
    
    $teamBadge = Badge::create([
        'name' => 'Test Team Reading Badge',
        'description' => 'Team earns 100 reading points',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $teamBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::READING_POINTS)->first()->id,
        'rule_value' => 100,
        'active' => true,
    ]);
    
    echo "✅ Created team badge requiring 100 reading points\n\n";
    
    // Test Case 3: Add reading logs for team members
    echo "TEST CASE 3: Add reading logs for team members\n";
    
    // User 1 reads 40 pages
    $readingLog1 = UserReadingLog::create([
        'user_id' => $user1->id,
        'book_id' => $book->id,
        'log_date' => now()->toDateString(),
        'start_page' => 1,
        'end_page' => 40,
        'pages_read' => 40,
        'reading_minutes' => 60,
        'book_completed' => false,
    ]);
    
    // User 2 reads 35 pages
    $readingLog2 = UserReadingLog::create([
        'user_id' => $user2->id,
        'book_id' => $book->id,
        'log_date' => now()->toDateString(),
        'start_page' => 1,
        'end_page' => 35,
        'pages_read' => 35,
        'reading_minutes' => 50,
        'book_completed' => false,
    ]);
    
    $team->refresh();
    $teamBadges = $team->getEarnedBadges();
    
    echo "- Team Total Points After 2 Members: {$team->total_points}\n";
    echo "- Team Badges Count: {$teamBadges->count()}\n";
    echo "- Can Earn Badge: " . ($team->canEarnBadge($teamBadge->id) ? 'YES' : 'NO') . "\n";
    
    echo "✅ " . ($team->total_points === 75 && $teamBadges->count() === 0 ? "PASS" : "FAIL") . " - Team points accumulated but badge not yet earned\n\n";
    
    // Test Case 4: Add third member reading log to trigger team badge
    echo "TEST CASE 4: Add third member reading log to trigger team badge\n";
    
    // User 3 reads 30 pages (should trigger team badge)
    $readingLog3 = UserReadingLog::create([
        'user_id' => $user3->id,
        'book_id' => $book->id,
        'log_date' => now()->toDateString(),
        'start_page' => 1,
        'end_page' => 30,
        'pages_read' => 30,
        'reading_minutes' => 45,
        'book_completed' => false,
    ]);
    
    $team->refresh();
    $teamBadgesAfter = $team->getEarnedBadges();
    $teamBadgeStats = TeamBadge::getBadgeStatsForTeam($team->id);
    
    echo "- Team Total Points After 3 Members: {$team->total_points}\n";
    echo "- Team Badges Count: {$teamBadgesAfter->count()}\n";
    echo "- Team Badge Stats: Total={$teamBadgeStats['total']}, Automatic={$teamBadgeStats['automatic']}\n";
    
    if ($teamBadgesAfter->count() > 0) {
        foreach ($teamBadgesAfter as $earnedBadge) {
            echo "  - Earned: {$earnedBadge->badge->name}\n";
            echo "    - Award Type: {$earnedBadge->award_type}\n";
            echo "    - Trigger Source: {$earnedBadge->trigger_source}\n";
            echo "    - Reading Log ID: " . ($earnedBadge->reading_log_id ?: 'NULL') . "\n";
            echo "    - Triggered by Reading Log: " . ($earnedBadge->isTriggeredByReadingLog() ? 'YES' : 'NO') . "\n";
        }
    }
    
    echo "✅ " . ($team->total_points >= 100 && $teamBadgesAfter->count() === 1 ? "PASS" : "FAIL") . " - Team badge should be awarded\n\n";
    
    // Test Case 5: Test manual team badge awarding
    echo "TEST CASE 5: Test manual team badge awarding\n";
    
    $manualTeamBadge = Badge::create([
        'name' => 'Test Manual Team Badge',
        'description' => 'Manually awarded team badge',
        'manual' => true,
        'active' => true,
    ]);
    
    $teacher = User::where('id', 'NOT IN', [$user1->id, $user2->id, $user3->id])->first();
    $manualAward = TeamBadge::awardBadgeToTeam($team->id, $manualTeamBadge->id, $teacher->id);
    
    echo "- Manual Award Result: " . ($manualAward ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Manual Badge Reading Log ID: " . ($manualAward->reading_log_id ?: 'NULL') . "\n";
    echo "- Manual Badge Trigger Source: {$manualAward->trigger_source}\n";
    echo "- Manual Badge Triggered by Reading Log: " . ($manualAward->isTriggeredByReadingLog() ? 'YES' : 'NO') . "\n";
    
    echo "✅ " . ($manualAward && is_null($manualAward->reading_log_id) ? "PASS" : "FAIL") . " - Manual team badge should not have reading_log_id\n\n";
    
    // Test Case 6: Test team badge rule evaluation
    echo "TEST CASE 6: Test team badge rule evaluation\n";
    
    $teamReadingDaysBadge = Badge::create([
        'name' => 'Test Team Reading Days Badge',
        'description' => 'Team reads for 3 different days',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $teamReadingDaysBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::TOTAL_READING_DAYS)->first()->id,
        'rule_value' => 3,
        'active' => true,
    ]);
    
    // Add reading logs for different days
    UserReadingLog::create([
        'user_id' => $user1->id,
        'book_id' => $book->id,
        'log_date' => now()->subDay()->toDateString(),
        'start_page' => 41,
        'end_page' => 50,
        'pages_read' => 10,
        'reading_minutes' => 15,
        'book_completed' => false,
    ]);
    
    UserReadingLog::create([
        'user_id' => $user2->id,
        'book_id' => $book->id,
        'log_date' => now()->subDays(2)->toDateString(),
        'start_page' => 36,
        'end_page' => 45,
        'pages_read' => 10,
        'reading_minutes' => 15,
        'book_completed' => false,
    ]);
    
    $team->refresh();
    $finalTeamBadges = $team->getEarnedBadges();
    
    echo "- Final Team Badges Count: {$finalTeamBadges->count()}\n";
    
    foreach ($finalTeamBadges as $badge) {
        echo "  - Badge: {$badge->badge->name} ({$badge->award_type})\n";
    }
    
    echo "✅ " . ($finalTeamBadges->count() >= 3 ? "PASS" : "FAIL") . " - Team should earn reading days badge\n\n";
    
    // Test Case 7: Test cascade deletion
    echo "TEST CASE 7: Test cascade deletion when reading log is deleted\n";
    
    $badgesBeforeDeletion = TeamBadge::where('team_id', $team->id)->count();
    $badgesLinkedToLog3 = TeamBadge::where('reading_log_id', $readingLog3->id)->count();
    
    echo "- Total Team Badges Before Deletion: {$badgesBeforeDeletion}\n";
    echo "- Team Badges Linked to Reading Log 3: {$badgesLinkedToLog3}\n";
    
    // Delete the reading log that triggered team badge
    $readingLog3->delete();
    
    $badgesAfterDeletion = TeamBadge::where('team_id', $team->id)->count();
    
    echo "- Total Team Badges After Deletion: {$badgesAfterDeletion}\n";
    echo "- Expected Reduction: {$badgesLinkedToLog3}\n";
    echo "- Actual Reduction: " . ($badgesBeforeDeletion - $badgesAfterDeletion) . "\n";
    
    echo "✅ " . (($badgesBeforeDeletion - $badgesAfterDeletion) === $badgesLinkedToLog3 ? "PASS" : "FAIL") . " - Cascade deletion should work for team badges\n\n";
    
    // Test Case 8: Test team ranking
    echo "TEST CASE 8: Test team ranking\n";
    
    $team2 = Team::create([
        'name' => 'Test Competing Team',
        'active' => true,
    ]);
    
    $teamRanking = $team->getValueForBadgeRule(
        BadgeRule::where('rule_type_id', EnumBadgeRuleType::where('nr', EnumBadgeRuleType::CLASS_LEADERSHIP)->first()->id)->first() ?? 
        new BadgeRule(['rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::CLASS_LEADERSHIP)->first()->id])
    );
    
    echo "- Team 1 Ranking: {$teamRanking}\n";
    echo "- Team 1 Points: {$team->total_points}\n";
    echo "- Team 2 Points: {$team2->total_points}\n";
    
    echo "✅ PASS - Team ranking calculation working\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    TeamBadge::whereHas('team', function($q) { $q->where('name', 'LIKE', 'Test%'); })->delete();
    UserBadge::whereIn('user_id', [$user1->id, $user2->id, $user3->id])->delete();
    UserPoint::whereIn('user_id', [$user1->id, $user2->id, $user3->id])->delete();
    UserReadingLog::whereIn('user_id', [$user1->id, $user2->id, $user3->id])->delete();
    UserTeam::whereHas('team', function($q) { $q->where('name', 'LIKE', 'Test%'); })->delete();
    Team::where('name', 'LIKE', 'Test%')->delete();
    Badge::where('name', 'LIKE', 'Test%')->delete();
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Team-based gamification system tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
