<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\UserClass;
use App\Models\UserSchool;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserLocalScopeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'system_admin', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'school_admin', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'teacher', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'student', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'parent', 'guard_name' => 'moonshine']);
    }

    public function test_system_admin_can_access_all_users_with_local_scope()
    {
        $admin = User::factory()->create();
        $admin->assignRole('system_admin');
        
        $student = User::factory()->create();
        $student->assignRole('student');
        
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');

        // Authenticate as system admin
        $this->actingAs($admin, 'moonshine');

        // System admin should see all users using local scope
        $users = User::forCurrentUser()->get();
        $this->assertCount(3, $users);
        
        // Test individual scope methods
        $usersForSystemAdmin = User::forSystemAdmin()->get();
        $this->assertCount(3, $usersForSystemAdmin);
    }

    public function test_school_admin_can_access_users_in_their_schools()
    {
        $school1 = School::factory()->create();
        $school2 = School::factory()->create();
        
        $class1 = SchoolClass::factory()->create(['school_id' => $school1->id]);
        $class2 = SchoolClass::factory()->create(['school_id' => $school2->id]);
        
        $schoolAdmin = User::factory()->create();
        $schoolAdmin->assignRole('school_admin');
        
        $teacher1 = User::factory()->create();
        $teacher1->assignRole('teacher');
        
        $student1 = User::factory()->create();
        $student1->assignRole('student');
        
        $student2 = User::factory()->create();
        $student2->assignRole('student');

        // Assign school admin to school1
        $schoolAdminRole = Role::where('name', 'school_admin')->first();
        UserSchool::create([
            'user_id' => $schoolAdmin->id,
            'school_id' => $school1->id,
            'role_id' => $schoolAdminRole->id,
            'active' => true,
        ]);

        // Assign teacher1 to school1
        UserClass::create([
            'user_id' => $teacher1->id,
            'class_id' => $class1->id,
            'school_id' => $school1->id,
            'active' => true,
        ]);

        // Assign student1 to school1
        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class1->id,
            'school_id' => $school1->id,
            'active' => true,
        ]);

        // Assign student2 to school2 (different school)
        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class2->id,
            'school_id' => $school2->id,
            'active' => true,
        ]);

        // Authenticate as school admin
        $this->actingAs($schoolAdmin, 'moonshine');

        // School admin should see themselves, teacher1, and student1 (all in school1)
        $users = User::forCurrentUser()->get();
        $this->assertCount(3, $users);
        
        $userIds = $users->pluck('id')->toArray();
        $this->assertContains($schoolAdmin->id, $userIds);
        $this->assertContains($teacher1->id, $userIds);
        $this->assertContains($student1->id, $userIds);
        $this->assertNotContains($student2->id, $userIds);
    }

    public function test_teacher_can_access_students_in_their_classes()
    {
        $school = School::factory()->create();
        $class1 = SchoolClass::factory()->create(['school_id' => $school->id]);
        $class2 = SchoolClass::factory()->create(['school_id' => $school->id]);
        
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');
        
        $student1 = User::factory()->create();
        $student1->assignRole('student');
        
        $student2 = User::factory()->create();
        $student2->assignRole('student');
        
        $student3 = User::factory()->create();
        $student3->assignRole('student');

        // Assign teacher to class1
        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class1->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Assign student1 and student2 to class1 (teacher's class)
        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class1->id,
            'school_id' => $school->id,
            'active' => true,
        ]);
        
        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class1->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Assign student3 to class2 (different class)
        UserClass::create([
            'user_id' => $student3->id,
            'class_id' => $class2->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Authenticate as teacher
        $this->actingAs($teacher, 'moonshine');

        // Teacher should see themselves and students in their class (student1, student2)
        $users = User::forCurrentUser()->get();
        $this->assertCount(3, $users);
        
        $userIds = $users->pluck('id')->toArray();
        $this->assertContains($teacher->id, $userIds);
        $this->assertContains($student1->id, $userIds);
        $this->assertContains($student2->id, $userIds);
        $this->assertNotContains($student3->id, $userIds);
    }

    public function test_student_can_only_access_themselves()
    {
        $student1 = User::factory()->create();
        $student1->assignRole('student');
        
        $student2 = User::factory()->create();
        $student2->assignRole('student');

        // Authenticate as student1
        $this->actingAs($student1, 'moonshine');

        // Student should only see themselves
        $users = User::forCurrentUser()->get();
        $this->assertCount(1, $users);
        $this->assertEquals($student1->id, $users->first()->id);
    }

    public function test_parent_can_only_access_themselves()
    {
        $parent = User::factory()->create();
        $parent->assignRole('parent');
        
        $student = User::factory()->create();
        $student->assignRole('student');

        // Authenticate as parent
        $this->actingAs($parent, 'moonshine');

        // Parent should only see themselves (until parent-child relationships are implemented)
        $users = User::forCurrentUser()->get();
        $this->assertCount(1, $users);
        $this->assertEquals($parent->id, $users->first()->id);
    }

    public function test_unauthenticated_user_has_no_access()
    {
        $student = User::factory()->create();
        $student->assignRole('student');

        // No authentication
        $users = User::forCurrentUser()->get();
        $this->assertCount(0, $users);
    }

    public function test_user_without_role_has_no_access()
    {
        $user = User::factory()->create();
        // No role assigned

        // Authenticate as user without role
        $this->actingAs($user, 'moonshine');

        $users = User::forCurrentUser()->get();
        $this->assertCount(0, $users);
    }

    public function test_individual_scope_methods_work_correctly()
    {
        $school = School::factory()->create();
        $class = SchoolClass::factory()->create(['school_id' => $school->id]);
        
        $schoolAdmin = User::factory()->create();
        $schoolAdmin->assignRole('school_admin');
        
        $teacher = User::factory()->create();
        $teacher->assignRole('teacher');
        
        $student = User::factory()->create();
        $student->assignRole('student');

        // Set up relationships
        $schoolAdminRole = Role::where('name', 'school_admin')->first();
        UserSchool::create([
            'user_id' => $schoolAdmin->id,
            'school_id' => $school->id,
            'role_id' => $schoolAdminRole->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $student->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Test individual scope methods
        $schoolAdminUsers = User::forSchoolAdmin($schoolAdmin)->get();
        $this->assertCount(3, $schoolAdminUsers); // school admin + teacher + student

        $teacherUsers = User::forTeacher($teacher)->get();
        $this->assertCount(2, $teacherUsers); // teacher + student

        $studentUsers = User::forStudent($student)->get();
        $this->assertCount(1, $studentUsers); // only student

        $parentUsers = User::forParent($student)->get();
        $this->assertCount(1, $parentUsers); // only themselves
    }

    public function test_local_scopes_work_with_authentication_and_user_methods()
    {
        $school = School::factory()->create();
        $class = SchoolClass::factory()->create(['school_id' => $school->id]);
        
        $teacher = User::factory()->create([
            'username' => 'teacher',
            'password' => Hash::make('password123'),
        ]);
        $teacher->assignRole('teacher');
        
        $student1 = User::factory()->create();
        $student1->assignRole('student');
        
        $student2 = User::factory()->create();
        $student2->assignRole('student');

        // Set up class relationships
        UserClass::create([
            'user_id' => $teacher->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $student1->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $student2->id,
            'class_id' => $class->id,
            'school_id' => $school->id,
            'active' => true,
        ]);

        // Authenticate using Auth::attempt
        $this->assertTrue(Auth::guard('moonshine')->attempt([
            'username' => 'teacher',
            'password' => 'password123',
        ]));

        // Local scope should work after authentication
        $users = User::forCurrentUser()->get();
        $this->assertCount(3, $users); // teacher + 2 students

        // User social methods should still work (they don't use local scopes)
        $friends = $teacher->getFriends();
        $classmates = $teacher->getClassmates();
        $teammates = $teacher->getTeammates();
        
        $this->assertIsIterable($friends);
        $this->assertIsIterable($classmates);
        $this->assertIsIterable($teammates);
    }
}
