<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;
use MoonShine\Laravel\MoonShineRequest;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Support\Enums\ToastType;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use App\Models\{Activity, Book, User, UserActivity, UserActivityReview};
use MoonShine\Support\ListOf;
use MoonShine\UI\Components\ActionButton;
use MoonShine\UI\Components\Layout\{Box, Flex};
use MoonShine\UI\Fields\{Date, File, Image, Number, Select, Text, Textarea};

#[Icon('clipboard-document-check')]
class UserActivityResource extends BaseResource
{
    use WithRolePermissions;

    protected string $model = UserActivity::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'book', 'activity', 'activity.category', 'review'];

    public function getTitle(): string
    {
        return __('admin.user_activities');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: BookResource::class
            )
                ->unescape()
                ->sortable(),

            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            )
                ->sortable(),

            Date::make(__('admin.activity_date'), 'activity_date')
                ->sortable(),

            Select::make(__('admin.status'), 'status')
                ->options(UserActivity::getStatusOptions())
                ->sortable(),

            Number::make(__('admin.points'), 'activity.points')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class
                    )                   
                        ->valuesQuery(function (Builder $query) { return $query->forCurrentUser(); })
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.book'),
                        'book',
                        formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                        resource: BookResource::class
                    )
                        ->required()
                        ->searchable(),
                ]),

                Flex::make([
                    BelongsTo::make(
                        __('admin.activity'),
                        'activity',
                        formatted: fn(Activity $activity) => $activity->name . ' (' . $activity->points . ' pts)',
                        resource: ActivityResource::class
                    )
                        ->required()
                        ->searchable(),

                    Date::make(__('admin.activity_date'), 'activity_date')
                        ->required()
                        ->default(now()->format('Y-m-d')),
                ]),

                Textarea::make(__('admin.content'), 'content')
                    ->hint(__('admin.activity_content_hint')),

                Flex::make([
                    Number::make(__('admin.rating'), 'rating')
                        ->min(1)
                        ->max(10)
                        ->hint(__('admin.activity_rating_hint')),

                    File::make(__('admin.media_content'), 'media_url')
                        ->dir('user_activities')
                        ->removable()
                        ->hint(__('admin.activity_media_url_hint')),
                ]),

                Select::make(__('admin.status'), 'status')
                    ->options(UserActivity::getStatusOptions())
                    ->default(UserActivity::STATUS_PENDING),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        // find activity media type and decide for file or image field  
        $mediaField = $this->item->activity->media_type === Activity::MEDIA_TYPE_IMAGE ?  
            [ Image::make(__('admin.media_content'), 'media_url') ] : 
            [ File::make(__('admin.media_content'), 'media_url') ];
            
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name)   . ' - ' . $book->isbn,
                resource: BookResource::class
            ),

            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            ),

            Date::make(__('admin.activity_date'), 'activity_date'),
            Text::make(__('admin.content'), 'content'),
            Number::make(__('admin.rating'), 'rating'),
                
            ...$mediaField,
            Text::make(__('admin.status'), 'localized_status_name'),

            // Test Activity Results
            Text::make(__('admin.test_score'), 'test_score'),
            Text::make(__('admin.test_passed'), 'test_passed'),
            Text::make(__('admin.attempt_count'), 'attempt_count'),
            Number::make(__('admin.points'), 'activity.points'),
            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            ...UserActivity::validationRules(),
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'book.name', 'book.isbn', 'activity.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['activity_date' => 'desc', 'user.name' => 'asc'];
    }

    protected function detailButtons(): ListOf
    {
        $buttons = parent::detailButtons();

        // if status is pending
        if($this->item->status === UserActivity::STATUS_PENDING) {
            $buttons = $buttons->prepend(
                ActionButton::make(__('admin.reject'))
                    ->method('rejectReview') 
                    ->error()
                    ->withConfirm(__('admin.confirm_reject_review'))
                    ->icon('x-mark'),

                ActionButton::make(__('admin.approve'))
                    ->method('approveReview') 
                    ->success()
                    ->withConfirm(__('admin.confirm_approve_review'))
                    ->icon('check'),
            );
        }
        return $buttons;
    }
    
    public function approveOrRejectReview(MoonShineRequest $request, $action): MoonShineJsonResponse
    {    
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User) || (!$user->isTeacher() && !$user->isSystemAdmin())) {
            return MoonShineJsonResponse::make()->toast(__('admin.unauthorized_action'), ToastType::ERROR);
        }

        $id = $request->get('resourceItem');
        if (!$id) {
            return MoonShineJsonResponse::make()->toast(__('admin.invalid_request'), ToastType::ERROR);
        }
        $activity = UserActivity::where('id', $id)->first();
        if (!$activity) {
            return MoonShineJsonResponse::make()->toast(__('admin.activity_not_found'), ToastType::ERROR);
        }
        $review = UserActivityReview::where('user_activity_id', $activity->id)->first();
        if (!$review) {
            return MoonShineJsonResponse::make()->toast(__('admin.review_not_found'), ToastType::ERROR);
        }

        // Verify teacher has access to this review
        if(!$user->isSystemAdmin()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();
            $studentInTeacherClass = $review->userActivity->user->activeUserClasses()
                ->whereIn('class_id', $teacherClassIds)
                ->exists();

            if (!$studentInTeacherClass) {
                return MoonShineJsonResponse::make()->toast(__('admin.unauthorized_action'), ToastType::ERROR);
            }
        }
        if ($action === 'approve') {
            $review->approveReview($user->id);
            $toastMessage = __('admin.review_approved_successfully');
        } else {
            $review->rejectReview($user->id);
            $toastMessage = __('admin.review_rejected_successfully');
        }

        $resource = $user->isSystemAdmin()? app(UserActivityResource::class) : app(PanelUserActivityResource::class);
        $redirectUrl = $resource->getIndexPageUrl();
        return MoonShineJsonResponse::make()

            ->toast($toastMessage, ToastType::SUCCESS)
            ->redirect($redirectUrl);

    }

    public function approveReview(MoonShineRequest $request): MoonShineJsonResponse
    {   
        return $this->approveOrRejectReview($request, 'approve');
    }   

    public function rejectReview(MoonShineRequest $request): MoonShineJsonResponse
    {
        return $this->approveOrRejectReview($request, 'reject');
    }    
    
}
