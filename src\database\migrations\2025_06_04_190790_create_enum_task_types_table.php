<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enum_task_types', function (Blueprint $table) {
            $table->id();
            $table->integer('nr')->unique();
            $table->string('name');

            // Index for performance
            $table->index('nr');
        });

        // Insert the predefined task types
        DB::table('enum_task_types')->insert([
            ['nr' => 1, 'name' => 'Read Pages'],
            ['nr' => 2, 'name' => 'Read Books'],
            ['nr' => 3, 'name' => 'Read Minutes'],
            ['nr' => 4, 'name' => 'Read Days'],
            ['nr' => 5, 'name' => 'Read Streak'],
            ['nr' => 6, 'name' => 'Earn Reading Points'],
            ['nr' => 7, 'name' => 'Earn Activity Points'],
            ['nr' => 8, 'name' => 'Complete a Book Activity'],
            ['nr' => 9, 'name' => 'Complete a Book List'],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enum_task_types');
    }
};
