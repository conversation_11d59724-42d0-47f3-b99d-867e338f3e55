<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserBook;
use App\Models\User;
use App\Models\Book;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder;
use MoonShine\Laravel\QueryTags\QueryTag;
use MoonShine\UI\Fields\Preview;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;
use App\MoonShine\Resources\UserReadingLogResource;

#[Icon('queue-list')]
class PanelUserBookResource extends UserBookResource
{
    use WithRolePermissions;

    public function getTitle(): string
    {
        return __('admin.user_reading_logs');
    }

    public function queryTags(): array
    {
        // add query tags for ongoing and completed books
        return [
            QueryTag::make(
                __('admin.reading_status_in_progress'),
                static fn(Builder $query) => $query->whereNull('end_date')
            )
            ->default(),
            QueryTag::make(
                __('admin.reading_status_read'),
                static fn(Builder $query) => $query->whereNotNull('end_date')
            ),             
            QueryTag::make(
                __('admin.reading_status_completed'),
                static fn(Builder $query) => $query->completed()
            ),
        ];
    }


    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(
                __('admin.student'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            )
                ->link(link: fn() => '#')
                ->sortable(),

            Preview::make(__('admin.cover_image'), formatted:fn(UserBook $userBook) => asset('storage/' . $userBook['book']['cover_image']))
                ->image(),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            )
                ->link(link: fn() => '#')
                ->sortable(),

            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y')
                ->sortable(),

            Text::make(__('admin.progress_percentage'), 'progress_percentage_text'),
            Text::make(__('admin.total_pages_read'), 'total_pages_read'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: StudentResource::class
                    )
                        ->valuesQuery(function (Builder $query) { return $query->forCurrentUser(); })
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.book'),
                        'book',
                        formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                        resource: PanelBookResource::class
                    )
                        ->creatable()
                        ->required()
                        ->asyncSearch()
                        ->searchable(),
                ]),

                Flex::make([
                    Date::make(__('admin.start_date'), 'start_date')
                        ->required()
                        ->default(now()->format('Y-m-d'))
                        ->hint(__('admin.start_date_hint')),

                    Date::make(__('admin.end_date'), 'end_date')
                        ->hint(__('admin.end_date_hint')),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: StudentResource::class
            )
            ->link(link: fn(string $value, BelongsTo $ctx) => $this->getStudentLink($ctx) ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            )
            ->link(link: fn(string $value, BelongsTo $ctx) => $ctx->getResource()->getDetailPageUrl($ctx->getData()->getKey())),

            Date::make(__('admin.start_date'), 'start_date'),
            Text::make(__('admin.progress_percentage'), 'progress_percentage_text'),
            Text::make(__('admin.total_pages_read'), 'total_pages_read'),
            Text::make(__('admin.total_reading_time'), 'total_reading_time'),

            HasMany::make(__('admin.reading_history'), 'readingLogs', resource: UserReadingLogResource::class)
                ->async()
                ->searchable(false)
                ->fields([
                    Date::make(__('admin.log_date'), 'log_date')
                        ->format('d.m.Y'),
                    Number::make(__('admin.pages_read'), 'pages_read'),
                    Number::make(__('admin.reading_duration'), 'reading_duration'),
                    Switcher::make(__('admin.book_completed'), 'book_completed')
                        ->disabled(),
                ]),

            ...parent::getCommonDetailFields(),
        ];
    }

    private function getStudentLink(BelongsTo $ctx) {
                trap($ctx);
                return $ctx->getResource()->getDetailPageUrl($ctx->rawValue);
            }

    public function rules(mixed $item): array
    {
        $rules = [
            'user_id' => ['required', 'exists:users,id'],
            'book_id' => ['required', 'exists:books,id'],
            'start_date' => ['required', 'date', 'before_or_equal:today'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date', 'before_or_equal:today'],
            ...parent::getCommonRules($item),
        ];

        // Add custom validation for new sessions
        if (!$item || !$item->id) {
            $rules['user_id'][] = function ($attribute, $value, $fail) {
                $bookId = request('book_id');
                if ($bookId && !UserBook::canStartNewSession($value, $bookId)) {
                    $fail(__('admin.cannot_start_new_session_active_exists'));
                }
            };
        }

        return $rules;
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        // Only show users of the current user's default class
        return $builder->whereHas('user.activeUserClasses', function ($q) {
            $q->where('class_id', auth('moonshine')->user()->getDefaultClass()->class_id ?? 0);
        });
    }
}
