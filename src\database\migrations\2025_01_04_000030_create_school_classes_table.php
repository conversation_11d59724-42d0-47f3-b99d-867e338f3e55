<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('school_classes', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "A", "B", "C" for sections
            $table->foreignId('school_id')->constrained('schools')->onDelete('cascade');
            $table->foreignId('class_level_id')->constrained('enum_class_levels')->onDelete('cascade');
            $table->boolean('active')->default(true);

            // Add indexes and constraints
            $table->unique(['school_id', 'name', 'active']);
            $table->index(['school_id', 'class_level_id', 'active']);
            $table->index('class_level_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_classes');
    }
};
