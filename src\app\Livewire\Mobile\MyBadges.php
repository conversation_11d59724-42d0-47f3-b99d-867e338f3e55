<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserReward;
use App\Models\Reward;

class MyBadges extends Component
{
    public $user;
    public $badges = [];
    public $totalBadges = 0;

    public function mount()
    {
        $this->user = Auth::user();
        $this->loadBadges();
    }

    private function loadBadges()
    {
        // Get all badge-type rewards earned by this user
        $this->badges = $this->user->userRewards()
            ->byRewardType(Reward::TYPE_BADGE)
            ->with(['reward', 'readingLog.book', 'userActivity.activity'])
            ->orderBy('awarded_date', 'desc')
            ->get();

        $this->totalBadges = $this->badges->count();
    }

    public function goBack()
    {
        return redirect()->route('mobile.me');
    }

    public function render()
    {
        return view('livewire.mobile.my-badges');
    }
}
