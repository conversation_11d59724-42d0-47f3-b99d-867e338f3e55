<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\User;

class UserPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // System admin, school admin, and teacher can view users
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function view(User $user, User $item): bool
    {
        // System admin can view all users
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admin can view teacher and student roles within their assigned schools
        if ($user->isSchoolAdmin()) {
            if ($item->isTeacher() || $item->isStudent()) {
                $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();
                return $item->activeUserClasses()->whereIn('school_id', $userSchoolIds)->exists();
            }
        }

        // Teacher can view student roles within their assigned classes
        if ($user->isTeacher()) {
            if ($item->isStudent()) {
                $userClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();
                return $item->activeUserClasses()->whereIn('class_id', $userClassIds)->exists();
            }
        }

        return false;
    }

    public function create(User $user): bool
    {
        // System admin, school admin, and teacher can create users
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function update(User $user, User $item): bool
    {
        // System admin can update all users
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admin can update teacher and student roles within their assigned schools
        if ($user->isSchoolAdmin()) {
            if ($item->isTeacher() || $item->isStudent()) {
                $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();
                return $item->activeUserClasses()->whereIn('school_id', $userSchoolIds)->exists();
            }
        }

        // Teacher can update student roles within their assigned classes
        if ($user->isTeacher()) {
            if ($item->isStudent()) {
                $userClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();
                return $item->activeUserClasses()->whereIn('class_id', $userClassIds)->exists();
            }
        }

        return false;
    }

    public function delete(User $user, User $item): bool
    {
        return $this->update($user, $item);
    }

    public function restore(User $user, User $item): bool
    {
        return $this->update($user, $item);
    }

    public function forceDelete(User $user, User $item): bool
    {
        return $this->update($user, $item);
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }
}
