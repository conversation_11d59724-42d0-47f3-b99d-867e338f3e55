<div class="min-h-screen bg-purple-300 flex flex-col justify-center p-6">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-white mb-2"> {{ __('mobile.welcome') }}  {{ Auth::user()->name }},</h1>
        <p class="text-white text-lg">{{ __('mobile.avatar_subtitle') }}</p>
    </div>

    <!-- Avatar Grid -->
    <div class="bg-white rounded-3xl p-6 shadow-2xl max-w-sm mx-auto w-full">
        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <div class="grid grid-cols-3 gap-4 mb-6">
            @foreach($avatars as $avatar)
                <button
                    wire:click="selectAvatar({{ $avatar['id'] }})"
                    class="relative p-2 rounded-2xl transition-all duration-200 {{ $selectedAvatarId == $avatar['id'] ? 'bg-primary shadow-lg scale-105' : 'bg-gray-100 hover:bg-gray-200' }}"
                >
                    <div class="w-16 h-16 rounded-full overflow-hidden mx-auto {{ $selectedAvatarId == $avatar['id'] ? 'ring-4 ring-white' : '' }}">
                        <img
                            src="{{ asset('storage/' . $avatar['base_image']) }}"
                            alt="{{ $avatar['name'] }}"
                            class="w-full h-full object-cover"
                        >
                    </div>

                    @if($selectedAvatarId == $avatar['id'])
                        <div class="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    @endif
                </button>
            @endforeach
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
            <button
                wire:click="confirmSelection"
                wire:loading.attr="disabled"
                class="mobile-button {{ !$selectedAvatarId || $isLoading ? 'opacity-50 cursor-not-allowed' : '' }}"
                {{ !$selectedAvatarId ? 'disabled' : '' }}
            >
                <span wire:loading.remove wire:target="confirmSelection">{{ __('mobile.select_avatar') }}</span>
                <span wire:loading wire:target="confirmSelection" class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ __('mobile.selecting') }}
                </span>
            </button>

            <button
                wire:click="chooseLater"
                class="mobile-button-secondary"
                wire:loading.attr="disabled"
            >
                {{ __('mobile.choose_later') }}
            </button>
        </div>
    </div>
</div>
