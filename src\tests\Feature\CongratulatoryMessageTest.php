<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\UserClass;
use App\Models\UserBook;
use App\Models\Book;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

class CongratulatoryMessageTest extends TestCase
{
    use RefreshDatabase;

    private User $student1;
    private User $student2;
    private User $student3;
    private User $student4;
    private SchoolClass $class;
    private Role $studentRole;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create student role
        $this->studentRole = Role::factory()->create(['name' => 'student']);
        
        // Create school and class
        $school = School::factory()->create();
        $this->class = SchoolClass::factory()->create([
            'school_id' => $school->id,
            'name' => 'Test Class 5A'
        ]);
        
        // Create students
        $this->student1 = User::factory()->create(['name' => 'Student One']);
        $this->student2 = User::factory()->create(['name' => 'Student Two']);
        $this->student3 = User::factory()->create(['name' => 'Student Three']);
        $this->student4 = User::factory()->create(['name' => 'Student Four']);
        
        // Assign role to students
        $this->student1->assignRole($this->studentRole);
        $this->student2->assignRole($this->studentRole);
        $this->student3->assignRole($this->studentRole);
        $this->student4->assignRole($this->studentRole);
        
        // Assign students to class
        UserClass::create([
            'user_id' => $this->student1->id,
            'class_id' => $this->class->id,
            'school_id' => $school->id,
            'active' => true
        ]);
        UserClass::create([
            'user_id' => $this->student2->id,
            'class_id' => $this->class->id,
            'school_id' => $school->id,
            'active' => true
        ]);
        UserClass::create([
            'user_id' => $this->student3->id,
            'class_id' => $this->class->id,
            'school_id' => $school->id,
            'active' => true
        ]);
        UserClass::create([
            'user_id' => $this->student4->id,
            'class_id' => $this->class->id,
            'school_id' => $school->id,
            'active' => true
        ]);
    }

    /** @test */
    public function first_place_student_sees_congratulatory_message()
    {
        // Create completed books for students
        $this->createCompletedBooks($this->student1, 5); // 1st place
        $this->createCompletedBooks($this->student2, 3); // 2nd place
        $this->createCompletedBooks($this->student3, 2); // 3rd place
        $this->createCompletedBooks($this->student4, 1); // 4th place

        $this->actingAs($this->student1);

        Livewire::test('mobile.books')
            ->assertSet('classRankingData.current_rank', 1)
            ->assertSet('classRankingData.completed_books', 5)
            ->assertSet('classRankingData.is_first_place', true)
            ->assertSee('Amazing! You\'re #1!')
            ->assertSee('top reader in Test Class 5A with 5 books completed');
    }

    /** @test */
    public function second_place_student_sees_congratulatory_message_with_advancement_info()
    {
        // Create completed books for students
        $this->createCompletedBooks($this->student1, 5); // 1st place
        $this->createCompletedBooks($this->student2, 3); // 2nd place
        $this->createCompletedBooks($this->student3, 2); // 3rd place
        $this->createCompletedBooks($this->student4, 1); // 4th place

        $this->actingAs($this->student2);

        Livewire::test('mobile.books')
            ->assertSet('classRankingData.current_rank', 2)
            ->assertSet('classRankingData.completed_books', 3)
            ->assertSet('classRankingData.is_first_place', false)
            ->assertSet('classRankingData.books_needed_for_next_rank', 3) // Need 3 more to reach 6 (5+1)
            ->assertSee('Great Job! Top Reader!')
            ->assertSee('ranked #2 in Test Class 5A with 3 books completed')
            ->assertSee('Read 3 more book(s) to reach #1 place!');
    }

    /** @test */
    public function third_place_student_sees_congratulatory_message()
    {
        // Create completed books for students
        $this->createCompletedBooks($this->student1, 5); // 1st place
        $this->createCompletedBooks($this->student2, 3); // 2nd place
        $this->createCompletedBooks($this->student3, 2); // 3rd place
        $this->createCompletedBooks($this->student4, 1); // 4th place

        $this->actingAs($this->student3);

        Livewire::test('mobile.books')
            ->assertSet('classRankingData.current_rank', 3)
            ->assertSet('classRankingData.completed_books', 2)
            ->assertSet('classRankingData.is_first_place', false)
            ->assertSet('classRankingData.books_needed_for_next_rank', 2) // Need 2 more to reach 4 (3+1)
            ->assertSee('Great Job! Top Reader!')
            ->assertSee('ranked #3 in Test Class 5A with 2 books completed')
            ->assertSee('Read 2 more book(s) to reach #2 place!');
    }

    /** @test */
    public function fourth_place_student_does_not_see_congratulatory_message()
    {
        // Create completed books for students
        $this->createCompletedBooks($this->student1, 5); // 1st place
        $this->createCompletedBooks($this->student2, 3); // 2nd place
        $this->createCompletedBooks($this->student3, 2); // 3rd place
        $this->createCompletedBooks($this->student4, 1); // 4th place

        $this->actingAs($this->student4);

        Livewire::test('mobile.books')
            ->assertSet('classRankingData', null)
            ->assertDontSee('Great Job! Top Reader!')
            ->assertDontSee('Amazing! You\'re #1!');
    }

    /** @test */
    public function student_with_no_class_assignment_does_not_see_message()
    {
        // Create a student not assigned to any class
        $studentNoClass = User::factory()->create();
        $studentNoClass->assignRole($this->studentRole);

        $this->actingAs($studentNoClass);

        Livewire::test('mobile.books')
            ->assertSet('classRankingData', null);
    }

    /** @test */
    public function student_in_class_with_only_one_student_does_not_see_message()
    {
        // Create a new class with only one student
        $school = School::factory()->create();
        $singleStudentClass = SchoolClass::factory()->create([
            'school_id' => $school->id,
            'name' => 'Single Student Class'
        ]);
        
        $singleStudent = User::factory()->create();
        $singleStudent->assignRole($this->studentRole);
        
        UserClass::create([
            'user_id' => $singleStudent->id,
            'class_id' => $singleStudentClass->id,
            'school_id' => $school->id,
            'active' => true
        ]);

        $this->createCompletedBooks($singleStudent, 10);

        $this->actingAs($singleStudent);

        Livewire::test('mobile.books')
            ->assertSet('classRankingData', null);
    }

    /** @test */
    public function ranking_updates_after_book_completion()
    {
        // Initial setup: student2 is in 2nd place
        $this->createCompletedBooks($this->student1, 5); // 1st place
        $this->createCompletedBooks($this->student2, 3); // 2nd place
        $this->createCompletedBooks($this->student3, 2); // 3rd place

        $this->actingAs($this->student2);

        $component = Livewire::test('mobile.books')
            ->assertSet('classRankingData.current_rank', 2);

        // Complete more books to potentially change ranking
        $this->createCompletedBooks($this->student2, 3); // Now has 6 total, should be 1st

        // Trigger ranking refresh
        $component->call('loadBooks');

        $component->assertSet('classRankingData.current_rank', 1)
                 ->assertSet('classRankingData.is_first_place', true);
    }

    private function createCompletedBooks(User $user, int $count): void
    {
        for ($i = 0; $i < $count; $i++) {
            $book = Book::factory()->create([
                'name' => "Test Book {$i} for {$user->name}",
                'page_count' => 100
            ]);

            UserBook::create([
                'user_id' => $user->id,
                'book_id' => $book->id,
                'start_date' => now()->subDays($count - $i),
                'end_date' => now()->subDays($count - $i - 1), // Completed
            ]);
        }
    }
}
