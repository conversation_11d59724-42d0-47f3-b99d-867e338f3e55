<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('team_rewards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->onDelete('cascade');
            $table->foreignId('reward_id')->constrained('rewards')->onDelete('cascade');
            $table->timestamp('awarded_date')->useCurrent();
            $table->foreignId('awarded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('reading_log_id')->nullable()->constrained('user_reading_logs')->onDelete('cascade');
            $table->foreignId('user_activity_id')->nullable()->constrained('user_activities')->onDelete('cascade');

            // Indexes for performance
            $table->index(['team_id', 'reward_id']);
            $table->index(['team_id', 'awarded_date']);
            $table->index(['reward_id', 'awarded_date']);
            $table->index('awarded_by');
            $table->index('reading_log_id');
            $table->index('user_activity_id');
            $table->index('awarded_date');
            
            // Unique constraint to prevent duplicate team-reward awards
            $table->unique(['team_id', 'reward_id'], 'team_reward_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('team_rewards');
    }
};
