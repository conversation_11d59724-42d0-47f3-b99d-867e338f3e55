<?php

use App\Models\{User, Book, UserBook, UserReadingLog, Activity, UserActivity, Category};
use Illuminate\Foundation\Testing\RefreshDatabase;

echo "=== UserBook Completed Field Test ===\n\n";

// Test Case 1: Book completion without required activities
echo "TEST CASE 1: Book completion without required activities\n";

// Create test user and book
$user = User::where('username', 'student1')->first();
if (!$user) {
    echo "❌ Test user 'student1' not found\n";
    exit;
}

$book = Book::where('active', true)->first();
if (!$book) {
    echo "❌ No active books found\n";
    exit;
}

// Create UserBook session
$userBook = UserBook::create([
    'user_id' => $user->id,
    'book_id' => $book->id,
    'start_date' => now()->subDays(5),
]);

echo "- Created UserBook session (ID: {$userBook->id})\n";
echo "- Initial completed status: " . ($userBook->completed ? 'true' : 'false') . "\n";
echo "- Initial end_date: " . ($userBook->end_date ? $userBook->end_date : 'null') . "\n";

// Create reading log with book completion
$readingLog = UserReadingLog::create([
    'user_id' => $user->id,
    'book_id' => $book->id,
    'log_date' => now(),
    'pages_read' => $book->page_count ?? 100,
    'book_completed' => true,
]);

echo "- Created reading log with book_completed=true\n";

// Refresh UserBook to see changes
$userBook->refresh();
echo "- After reading log - completed status: " . ($userBook->completed ? 'true' : 'false') . "\n";
echo "- After reading log - end_date: " . ($userBook->end_date ? $userBook->end_date : 'null') . "\n";

if ($userBook->completed && $userBook->end_date) {
    echo "✅ PASS - Book marked as completed without required activities\n\n";
} else {
    echo "❌ FAIL - Book not properly marked as completed\n\n";
}

// Test Case 2: Book completion with required activities (incomplete)
echo "TEST CASE 2: Book completion with required activities (incomplete)\n";

// Find or create a required activity
$requiredActivity = Activity::where('required', true)->where('active', true)->first();
if (!$requiredActivity) {
    echo "❌ No required activities found\n";
} else {
    // Create another UserBook session
    $userBook2 = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now()->subDays(3),
    ]);

    echo "- Created UserBook session with required activities (ID: {$userBook2->id})\n";
    echo "- Required activity found: {$requiredActivity->name}\n";

    // Create reading log with book completion
    $readingLog2 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now(),
        'pages_read' => $book->page_count ?? 100,
        'book_completed' => true,
    ]);

    echo "- Created reading log with book_completed=true\n";

    // Refresh UserBook to see changes
    $userBook2->refresh();
    echo "- After reading log - completed status: " . ($userBook2->completed ? 'true' : 'false') . "\n";
    echo "- After reading log - end_date: " . ($userBook2->end_date ? $userBook2->end_date : 'null') . "\n";

    if (!$userBook2->completed && $userBook2->end_date) {
        echo "✅ PASS - Book has end_date but not completed due to required activities\n";
    } else {
        echo "❌ FAIL - Book completion logic incorrect with required activities\n";
    }

    // Test Case 3: Complete required activity and check retroactive completion
    echo "\nTEST CASE 3: Complete required activity and check retroactive completion\n";

    // Complete the required activity
    $userActivity = UserActivity::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'activity_id' => $requiredActivity->id,
        'activity_date' => now(),
        'content' => ['test' => 'content'],
        'status' => UserActivity::STATUS_COMPLETED,
    ]);

    echo "- Created completed UserActivity for required activity\n";

    // Refresh UserBook to see changes
    $userBook2->refresh();
    echo "- After activity completion - completed status: " . ($userBook2->completed ? 'true' : 'false') . "\n";

    if ($userBook2->completed) {
        echo "✅ PASS - Book marked as completed after required activity completion\n\n";
    } else {
        echo "❌ FAIL - Book not marked as completed after required activity completion\n\n";
    }
}

// Test Case 4: Test completed() scope
echo "TEST CASE 4: Test completed() scope\n";

$completedBooks = UserBook::where('user_id', $user->id)->completed()->count();
echo "- Completed books count using scope: {$completedBooks}\n";

$manualCount = UserBook::where('user_id', $user->id)->where('completed', true)->count();
echo "- Manual completed books count: {$manualCount}\n";

if ($completedBooks === $manualCount && $completedBooks > 0) {
    echo "✅ PASS - completed() scope works correctly\n\n";
} else {
    echo "❌ FAIL - completed() scope not working correctly\n\n";
}

// Test Case 5: Test isCompleted() method
echo "TEST CASE 5: Test isCompleted() method\n";

$testBook = UserBook::where('user_id', $user->id)->where('completed', true)->first();
if ($testBook) {
    $isCompletedMethod = $testBook->isCompleted();
    $completedField = $testBook->completed;
    
    echo "- isCompleted() method result: " . ($isCompletedMethod ? 'true' : 'false') . "\n";
    echo "- completed field value: " . ($completedField ? 'true' : 'false') . "\n";
    
    if ($isCompletedMethod === $completedField) {
        echo "✅ PASS - isCompleted() method matches completed field\n\n";
    } else {
        echo "❌ FAIL - isCompleted() method doesn't match completed field\n\n";
    }
} else {
    echo "❌ No completed books found for method test\n\n";
}

echo "=== Test Complete ===\n";
