<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAgreement extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'agreement_type',
        'version',
        'accepted_at',
        'ip_address',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'accepted_at' => 'datetime',
        ];
    }

    /**
     * Agreement type constants
     */
    const TYPE_PRIVACY_POLICY = 'privacy_policy';
    const TYPE_TERMS_OF_SERVICE = 'terms_of_service';
    const TYPE_COOKIE_POLICY = 'cookie_policy';

    /**
     * Current version constants
     */
    const CURRENT_PRIVACY_VERSION = '1.0';
    const CURRENT_TERMS_VERSION = '1.0';

    /**
     * Get the user who accepted the agreement.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if user has accepted the current version of privacy policy.
     */
    public static function hasAcceptedCurrentPrivacyPolicy(int $userId): bool
    {
        return self::where('user_id', $userId)
            ->where('agreement_type', self::TYPE_PRIVACY_POLICY)
            ->where('version', self::CURRENT_PRIVACY_VERSION)
            ->exists();
    }

    /**
     * Record user's acceptance of privacy policy.
     */
    public static function recordPrivacyPolicyAcceptance(int $userId, string $ipAddress = null): self
    {
        return self::create([
            'user_id' => $userId,
            'agreement_type' => self::TYPE_PRIVACY_POLICY,
            'version' => self::CURRENT_PRIVACY_VERSION,
            'accepted_at' => now(),
            'ip_address' => $ipAddress,
        ]);
    }

    /**
     * Get available agreement types.
     */
    public static function getAgreementTypes(): array
    {
        return [
            self::TYPE_PRIVACY_POLICY => __('admin.privacy_policy'),
            self::TYPE_TERMS_OF_SERVICE => __('admin.terms_of_service'),
            self::TYPE_COOKIE_POLICY => __('admin.cookie_policy'),
        ];
    }

    /**
     * Get the display name for the agreement type.
     */
    public function getAgreementTypeDisplayAttribute(): string
    {
        $types = self::getAgreementTypes();
        return $types[$this->agreement_type] ?? $this->agreement_type;
    }

    /**
     * Scope to filter by agreement type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('agreement_type', $type);
    }

    /**
     * Scope to filter by version.
     */
    public function scopeOfVersion($query, string $version)
    {
        return $query->where('version', $version);
    }

    /**
     * Scope to get recent agreements.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('accepted_at', '>=', now()->subDays($days));
    }
}
