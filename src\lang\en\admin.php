<?php

return [
	// System
	'system' => 'System',
	'users' => 'Users',
	'roles' => 'Roles',
	'dashboard' => 'Dashboard',
	'parent_dashboard' => 'Parent Dashboard',
	'school_admin_dashboard' => 'School Admin Dashboard',
	'system_admin_dashboard' => 'System Admin Dashboard',
	'system_overview' => 'System Overview',
	'unauthorized_action' => 'You are not authorized to perform this action',

	// Academic
	'school_types' => 'School Types',
	'school_type' => 'School Type',
	'schools' => 'Schools',
	'school_overview' => 'School Overview',
	'school_classes' => 'Classes',


	// Books
	'book_types' => 'Book Types',
	'book_type' => 'Book Type',
	'books' => 'Books',
	'books_localizable' => 'Books',
	'authors' => 'Authors',
	'categories' => 'Categories',
	'categories_localizable' => 'Categories',
	'publishers' => 'Publishers',
	'class_books' => 'Class Bookshelf',
	'export_reading_matrix' => 'Export Book Reading Report',
	'class_books_reading_report' => 'Class Library Book Reading Report',
	'no_default_class' => 'No default class assigned',
	'class_not_found' => 'Class not found',
	'no_books_in_class' => 'No books assigned to this class',
	'no_students_in_class' => 'No students in this class',
	'export_successful' => 'Export completed successfully',
	'export_failed' => 'Export failed',
	'page_points' => 'Page Points',
	'reading' => 'Reading',
	'user_reading_logs' => 'Reading Logs',
	'user_points' => 'User Points',
	'log_date' => 'Log Date',
	'start_page' => 'Start Page',
	'end_page' => 'End Page',
	'pages_read' => 'Pages Read',
	'reading_duration' => 'Reading Duration (min)',
	'book_completed' => 'Book Completed',
	'point_date' => 'Point Date',
	'point_type' => 'Point Type',
	'points' => 'Points',
	'point_type_page' => 'Page',
	'point_type_activity' => 'Activity',
	'point_type_task' => 'Task',
	'point_type_manual' => 'Manual',
	'start_page_hint' => 'Optional: Starting page number',
	'end_page_hint' => 'Optional: Ending page number',
	'add_to_class_books' => 'Add to Class Bookshelf',
	'confirm_add_to_class_books' => 'Are you sure you want to add this book to the class bookshelf?',
	'book_added_to_class_books_successfully' => 'Book added to class bookshelf successfully',
	'book_added_to_class_books_error' => 'Error adding book to class bookshelf',
	'book_not_found' => 'Book not found',
	'remove_from_class_books' => 'Remove from Class Bookshelf',
	'confirm_remove_from_class_books' => 'Are you sure you want to remove this book from the class bookshelf?',
	'book_removed_from_class_books_successfully' => 'Book removed from class bookshelf successfully',
	'book_removed_from_class_books_error' => 'Error removing book from class bookshelf',
	'book_not_in_class_books' => 'Book is not in class bookshelf',
	'questions_localizable' => 'Questions',
	'words_localizable' => 'Words',	
	

	// Tasks
	'task_management' => 'Task Management',
	'tasks' => 'Tasks',
	'task' => 'Task',
	'task_name' => 'Task Name',
	'task_types' => 'Task Types',
	'task_type' => 'Task Type',
	'task_cycles' => 'Task Cycles',
	'task_cycle' => 'Task Cycle',
	'task_value' => 'Task Value',
	'task_value_with_unit' => 'Task Value',
	'task_details' => 'Task Details',
	'task_configuration' => 'Task Configuration',
	'task_books' => 'Task Books',
	'task_categories' => 'Task Categories',
	'configuration_summary' => 'Configuration Summary',
	'task_type_hint' => 'Select the type of task to be performed',
	'task_cycle_hint' => 'Select how often this task should be completed',
	'task_value_hint' => 'Enter the target value for quantitative tasks',
	'task_activity_hint' => 'Select the specific activity for activity-based tasks',
	'task_books_hint' => 'Select books that must be completed for this task',
	'task_categories_hint' => 'Select categories that books must belong to',
	'task_description_hint' => 'Optional detailed description of what this task involves',
	'assign_to_class' => 'Assign to Class',
	'assign_to_team' => 'Assign to Team',
	'assign_to_students' => 'Assign to Students',
	'task_assigned_to_class_students' => 'Task assigned to :count students in the class',
	'task_assigned_to_team_students' => 'Task assigned to :count students in the team',
	'task_assigned_to_students' => 'Task assigned to :count students',
	'no_students_in_class' => 'No students found in the selected class',
	'no_students_in_team' => 'No students found in the selected team',
	'no_students_selected' => 'No students selected',
	'task_type_number_hint' => 'Unique number identifier for this task type',
	'task_type_name_hint' => 'Name of the task type',
	'task_cycle_number_hint' => 'Unique number identifier for this task cycle',
	'task_cycle_name_hint' => 'Name of the task cycle',
	'standalone_task' => 'Standalone Task',
	'standalone_tasks' => 'Standalone Tasks',
	'user_tasks' => 'User Tasks',
	'complete_date' => 'Complete Date',
	'due_date_in_past' => 'Due date cannot be in the past',
	'task_assignment' => 'Task Assignment',
	'completion_status' => 'Completion Status',
	'assignee' => 'Assignee',
	'days_since_assignment' => 'Days Since Assignment',
	'estimated_completion' => 'Estimated Completion',
	'motivational_message' => 'Motivational Message',
	'duration_days' => 'Duration (Days)',
	'current_value' => 'Current Value',
	'target_value' => 'Target Value',
	'days_remaining' => 'Days Remaining',
	'completion_time_days' => 'Completion Time (Days)',
	'individual_assignment_hint' => 'Select a user for individual assignment',
	'team_assignment_hint' => 'Select a team for team assignment',
	'individual_task_assignment_hint' => 'Select a user for individual task assignment',
	'team_task_assignment_hint' => 'Select a team for team task assignment',
	'enter_assignment_comment' => 'Enter assignment comment',
	'cannot_assign_to_both_user_and_team' => 'Cannot assign to both user and team',
	'must_assign_to_user_or_team' => 'Must assign to either user or team',
	'task_already_assigned_to_assignee' => 'Task :task is already assigned to :assignee',
	'users' => 'Users',
	'teams' => 'Teams',
	'active_tasks' => 'Active Tasks',
	'no_active_tasks' => 'No Active Tasks',


	// Challenge Management
	'challenges' => 'Challenges',
	'challenge' => 'Challenge',
	'challenge_name' => 'Challenge Name',
	'challenge_description' => 'Challenge Description',
	'challenge_image' => 'Challenge Image',
	'challenge_image_hint' => 'Upload a banner or flyer image for the challenge',
	'enter_challenge_name' => 'Enter challenge name',
	'enter_challenge_description' => 'Enter challenge description',
	'enter_prize_description' => 'Enter prize description',
	'prize' => 'Prize',
	'duration' => 'Duration (days)',
	'participants' => 'Participants',
	'participating_schools' => 'Participating Schools',
	'participating_classes' => 'Participating Classes',
	'participating_teams' => 'Participating Teams',
	'select_schools_for_challenge' => 'Select schools to participate in this challenge',
	'select_classes_for_challenge' => 'Select classes to participate in this challenge',
	'select_teams_for_challenge' => 'Select teams to participate in this challenge',
	'participants_count' => 'Participants',
	'completion_count' => 'Completed',
	'upcoming' => 'Upcoming',
	'completed' => 'Completed',
	'pending' => 'Pending',
	'individual' => 'Individual',
	'team' => 'Team',
	

	// Challenge Tasks
	'challenge_tasks' => 'Challenge Tasks',
	'challenge_task' => 'Challenge Task',
	'user_challenge_tasks' => 'User Challenge Tasks',
	'user_challenge_task' => 'User Challenge Task',
	'assignment_type' => 'Assignment Type',
	'days_since_assignment' => 'Days Since Assignment',
	'progress_percentage' => 'Progress %',
	'leave_empty_for_individual_assignment' => 'Leave empty for individual assignment',
	'assignment_date' => 'Assignment Date',
	'completion_date' => 'Completion Date',

	// Challenge Management Menu
	'challenge_management' => 'Challenge Management',

	// Communication
	'communication' => 'Communication',
	'messages' => 'Messages',
	'message' => 'Message',
	'message_recipients' => 'Message Recipients',
	'message_recipient' => 'Message Recipient',
	'title' => 'Title',
	'message_date' => 'Message Date',
	'message_preview' => 'Message Preview',
	'default' => 'Default Message',
	'default_message_hint' => 'Default messages are automatically sent to all new users',
	'total_recipients' => 'Total Recipients',
	'read_count' => 'Read',
	'unread_count' => 'Unread',
	'read' => 'Read',
	'unread' => 'Unread',
	'sent_date' => 'Sent Date',
	'read_date' => 'Read Date',
	'message_info' => 'Message Information',
	'recipients' => 'Recipients',
	'recipient_info' => 'Recipient Information',
	'enter_message_title' => 'Enter message title',
	'enter_message_content' => 'Enter message content',
	'select_message_recipients' => 'Select users to receive this message',
	'all_messages' => 'All Messages',
	'default_messages' => 'Default Messages',
	'custom_messages' => 'Custom Messages',
	'all' => 'All',

	// Challenge Actions
	'confirm_toggle_challenge_status' => 'Are you sure you want to change the challenge status?',
	'challenge_activated_successfully' => 'Challenge activated successfully and assignments created.',
	'challenge_deactivated_successfully' => 'Challenge deactivated successfully.',
	'challenge_not_found' => 'Challenge not found.',

	// Reward System
	'rewards' => 'Rewards',
	'reward' => 'Reward',
	'reward_name' => 'Reward Name',
	'reward_type' => 'Reward Type',
	'reward_type_display' => 'Type',
	'reward_description' => 'Description',
	'reward_image' => 'Image',
	'reward_tasks' => 'Reward Tasks',
	'user_rewards' => 'User Rewards',
	'team_rewards' => 'Team Rewards',
	'badges' => 'Badges',
	'badge' => 'Badge',
	'gifts' => 'Gifts',
	'gift' => 'Gift',
	'trophies' => 'Trophies',
	'trophy' => 'Trophy',
	'cards' => 'Cards',
	'card' => 'Card',
	'items' => 'Items',
	'item' => 'Item',
	'unknown' => 'Unknown',
	'enter_reward_name' => 'Enter reward name',
	'enter_reward_description' => 'Enter reward description',
	'reward_image_hint' => 'Upload an image for this reward (optional)',
	'select_tasks_for_reward' => 'Select tasks that will award this reward when completed',
	'associated_tasks' => 'Associated Tasks',
	'repeatable' => 'Repeatable',
	'repeatable_reward_hint' => 'Allow this reward to be earned multiple times by the same user',
	'tasks_count' => 'Tasks Count',
	'users_earned_count' => 'Users Earned',
	'teams_earned_count' => 'Teams Earned',
	'basic_info' => 'Basic Information',
	'awarded_date' => 'Awarded Date',
	'activity_trigger' => 'Activity Trigger',
	'leave_empty_for_automatic_award' => 'Leave empty for automatic system award',
	'optional_reward_for_task_completion' => 'Optional reward to be awarded when this task is completed',
	'awarding_type' => 'Awarding Type',
	'my_rewards' => 'My Rewards',
	'system_rewards' => 'System Rewards',
	'award_to_class' => 'Award to Class',
	'confirm_award_to_class' => 'Are you sure you want to award this reward to all students in your class?',
	'reward_awarded_to_class' => 'Reward awarded to class successfully',
	'award_to_team' => 'Award to Team',
	'confirm_award_to_team' => 'Are you sure you want to award this reward to the selected team?',
	'reward_awarded_to_team' => 'Reward awarded to team successfully',
	'award_to_students' => 'Award to Students',
	'confirm_award_to_students' => 'Are you sure you want to award this reward to the selected students?',
	'reward_awarded_to_students' => 'Reward awarded to students successfully',
	'reward_already_awarded' => 'Reward already awarded',
	'reward_not_found' => 'Reward not found',
	'reward_not_active' => 'Reward is not active',



	// Task Type Descriptions
	'task_type_read_pages_desc' => 'Complete reading a specific number of pages',
	'task_type_read_books_desc' => 'Complete reading a specific number of books',
	'task_type_read_minutes_desc' => 'Spend a specific amount of time reading',
	'task_type_read_days_desc' => 'Read for a specific number of days',
	'task_type_read_streak_desc' => 'Maintain a reading streak for consecutive days',
	'task_type_earn_reading_points_desc' => 'Earn a specific number of reading points',
	'task_type_earn_activity_points_desc' => 'Earn a specific number of activity points',
	'task_type_complete_book_activity_desc' => 'Complete a specific book activity',
	'task_type_complete_book_list_desc' => 'Complete reading all books in a specified list',

	// Task Cycle Descriptions
	'task_cycle_total_desc' => 'Cumulative progress over the entire period',
	'task_cycle_daily_desc' => 'Task must be completed each day',
	'task_cycle_weekly_desc' => 'Task must be completed each week',
	'task_cycle_monthly_desc' => 'Task must be completed each month',

	// Task Units and Types
	'pages' => 'pages',
	'pages_localizable' => 'pages',
	'minutes' => 'minutes',
	'minutes_localizable' => 'minutes',
	'days' => 'days',
	'days_localizable' => 'days',
	'activities' => 'activities',
	'activities_localizable' => 'activities',
	'points_localizable' => 'points',
	'total' => 'total',
	'per_day' => 'per day',
	'per_week' => 'per week',
	'per_month' => 'per month',
	'quantitative' => 'Quantitative',
	'qualitative' => 'Qualitative',
	'time_based' => 'Time-based',
	'cumulative' => 'Cumulative',
	'number' => 'Number',
	'unit' => 'Unit',
	'type' => 'Type',
	'description' => 'Description',
	'display_name' => 'Display Name',
	'summary' => 'Summary',
	'activity' => 'Activity',
	'pages_read_hint' => 'Number of pages read in this session',
	'reading_duration_hint' => 'Optional: Time spent reading in minutes',
	'activity_categories' => 'Activity Categories',
	'activities' => 'Activities',
	'user_activities' => 'User Activities',
	'user_activity_reviews' => 'Activity Reviews',
	'activity_category' => 'Activity Category',
	'activity_category_hint' => 'Category for the activity',
	'activity_types' => 'Activity Types',
	'activity_type' => 'Activity Type',
	'activity_type_rating' => 'Rating',
	'activity_type_media' => 'Media',
	'activity_type_game' => 'Game',
	'activity_type_quiz' => 'Quiz',
	'activity_type_vocabulary_test' => 'Vocabulary Test',
	'activity_type_hint' => 'Type of activity to create',
	'overdue' => 'Overdue',
	'class_activity_alert' => 'You can add activities that will be available to your class from the defined set of activities. You can also customize the activities for your class.',

	// Test Activity Fields
	'question_count' => 'Question Count',
	'question_count_hint' => 'Number of questions for quiz/vocabulary tests (1-10)',
	'choices_count' => 'Choices Count',
	'choices_count_hint' => 'Number of answer choices per question (1-6)',
	'min_grade' => 'Minimum Grade',
	'min_grade_hint' => 'Minimum passing grade out of 100',
	'required' => 'Required',
	'required_activity_hint' => 'Whether this activity is mandatory for book completion',
	'allowed_tries' => 'Allowed Tries',
	'allowed_tries_hint' => 'Number of retry attempts allowed',

	// Test Activity Validation
	'question_count_range_error' => 'Question count must be between 1 and 10',
	'choices_count_range_error' => 'Choices count must be between 1 and 6',
	'min_grade_range_error' => 'Minimum grade must be between 0 and 100',
	'allowed_tries_min_error' => 'Allowed tries must be at least 1',

	// Test Activity Results
	'test_score' => 'Test Score',
	'test_passed' => 'Test Passed',
	'attempt_count' => 'Attempt Count',
	'yes' => 'Yes',
	'no' => 'No',
	
	'media_type_image' => 'Image',
	'media_type_audio' => 'Audio',
	'media_type_hint' => 'Type of media content for uploaded activities',
	'min_word_count' => 'Min Word Count',
	'min_rating' => 'Min Rating',
	'max_rating' => 'Max Rating',
	'media_url' => 'Media URL',
	'need_approval' => 'Need Approval',
	'need_approval_hint' => 'Whether activity submissions require approval',
	'activity_date' => 'Activity Date',
	'content' => 'Content',
	'rating' => 'Rating',
	'status_pending' => 'Pending',
	'status_approved' => 'Approved',
	'status_rejected' => 'Rejected',
	'status_completed' => 'Completed',
	'review_date' => 'Review Date',
	'reviewer' => 'Reviewer',
	'feedback' => 'Feedback',
	'review_status_waiting' => 'Waiting',
	'review_status_approved' => 'Approved',
	'review_status_rejected' => 'Rejected',
	'activity_count' => 'Activity Count',
	'active_activity_count' => 'Active Activity Count',
	'student' => 'Student',
	'user_activity' => 'User Activity',
	'activity_content' => 'Activity Content',
	'activity_rating' => 'Activity Rating',
	'activity_media_url' => 'Activity Media URL',
	'activity_description_hint' => 'Describe what students need to do for this activity',
	'min_word_count_hint' => 'Minimum words required for writing activities',
	'min_rating_hint' => 'Minimum rating value for rating activities',
	'max_rating_hint' => 'Maximum rating value for rating activities',
	'media_url_hint' => 'URL for game or media content',
	'activity_content_hint' => 'Written content for writing activities',
	'activity_rating_hint' => 'Rating value (1-10)',
	'activity_media_url_hint' => 'URL for submitted media content',
	'review_feedback_hint' => 'Feedback for the student about their activity submission',
	'user_books' => 'User Books',
	'start_date' => 'Start Date',
	'end_date' => 'End Date',
	'reading_status' => 'Reading Status',
	'reading_status_in_progress' => 'In Progress',
	'reading_status_read' => 'Read',
	'reading_status_completed' => 'Completed',
	'reading_duration' => 'Reading Duration',
	'progress_percentage' => 'Progress',
	'total_pages_read' => 'Total Pages Read',
	'total_reading_time' => 'Total Reading Time (minutes)',
	'has_reading_logs' => 'Has Reading Logs',
	'start_date_hint' => 'Date when the student started reading this book',
	'end_date_hint' => 'Leave empty if still reading, set date when completed',
	'same_day' => 'Same day',
	'one_day' => '1 day',
	'days_count' => '{count} days',
	'session_info' => 'Session Info',
	'reading_history' => 'Reading History',
	'cannot_start_new_session_active_exists' => 'Cannot start a new reading session while an active session exists for this book. Please complete the current session first.',
	'user_avatars' => 'User Avatars',
	'current_avatar_image' => 'Current Avatar Image',
	'user_activity_points' => 'User Activity Points',
	'avatar_required_points' => 'Avatar Required Points',
	'required_points' => 'Required Points',
	'selected_at' => 'Selected At',
	'selection_age' => 'Selection Age',
	'selected_at_hint' => 'Date and time when the avatar was selected',
	'selected_today' => 'Selected today',
	'selected_yesterday' => 'Selected yesterday',
	'selected_days_ago' => 'Selected {days} days ago',
	'insufficient_activity_points_for_avatar' => 'User has insufficient activity points for this avatar. Required: {required}, Current: {current}',
	'manual' => 'Manual',
	'automatic' => 'Automatic',
	'rule_type' => 'Rule Type',
	'rule_value' => 'Rule Value',
	'rule_value_hint' => 'The minimum value required to satisfy this rule',
	'media_url_invalid' => 'Media URL is invalid',
	'media_url_required' => 'Media URL is required',
	'my_activities' => 'My Activities',
	'rating_required' => 'Rating is required',
	'book_activities' => 'Book Activities',

	'award_type' => 'Award Type',
	'awarder' => 'Awarded By',
	'awarded_at' => 'Awarded At',
	'awarded_by' => 'Awarded By',
	'award_age' => 'Award Age',
	'awarded_at_hint' => 'Date and time when the reward was awarded',
	'awarded_by_hint' => 'Leave empty for automatic awards',
	'awarded_today' => 'Awarded today',
	'awarded_yesterday' => 'Awarded yesterday',
	'awarded_days_ago' => 'Awarded {days} days ago',

	'system' => 'System',
	'points_rule' => 'points',
	'minutes_rule' => 'minutes',
	'days_rule' => 'days',
	'consecutive_days_rule' => 'consecutive days',
	'books_rule' => 'books',
	'rank_rule' => 'rank',
	'percentage_rule' => 'percentage',
	'rules' => 'rules',
	'users_rule' => 'users',
	'no_rules' => 'No rules defined',
	'unit' => 'Unit',

	'number' => 'Number',
	'rule_type_number_hint' => 'Unique number identifier for this rule type',
	'rule_type_name_hint' => 'Descriptive name for this rule type',
	'trigger_source' => 'Trigger Source',
	'reading_log' => 'Reading Log',
	'reading_log_trigger' => 'Reading Log Trigger',
	'manual_award' => 'Manual Award',
	'automatic_award' => 'Automatic Award',
	'no_rewards' => 'No rewards',
	'teams' => 'Teams',

	'user_teams' => 'User Teams',
	'team_members' => 'Team Members',
	'team' => 'Team',
	'leader' => 'Leader',
	'members' => 'members',
	'total_points' => 'Total Points',
	'logo' => 'Logo',
	'team_logo_hint' => 'Upload a logo for this team (optional)',
	'team_leader_hint' => 'Select a team leader from team members',
	'team_members_hint' => 'Add users to this team',
	'no_leader' => 'No Leader',
	'team_leader' => 'Team Leader',
	'team_member' => 'Team Member',
	'membership_role' => 'Membership Role',
	'team_status' => 'Team Status',
	'inactive' => 'Inactive',
	'leader_must_be_team_member' => 'Leader {user} must be a member of team {team}',
	'team_not_found' => 'Team not found',
	'user_already_in_team' => 'User {user} is already a member of team {team}',

	// Gamification
	'gamification' => 'Gamification',
	'avatars' => 'Avatars',
	'stories' => 'Stories',
	'story_rules' => 'Story Rules',
	'story_rule_details' => 'Story Rule Details',
	'story_chapters' => 'Story Chapters',
	'story_achievements' => 'Story Achievements',
	'story_books' => 'Story Books',
	
	// Assessment System
	'assessment_system' => 'Assessment System',
	'book_questions' => 'Book Questions',
	'book_words' => 'Book Vocabulary',

	// Reading Log System
	'reading_log_system' => 'Reading Log System',

	'story' => 'Story',
	'start_date' => 'Start Date',
	'end_date' => 'End Date',
	'is_active' => 'Active',
	'status' => 'Status',
	'duration' => 'Duration',
	'participating_schools' => 'Participating Schools',
	'participating_classes' => 'Participating Classes',

	// Task Fields
	'task_type' => 'Task Type',
	'page_start' => 'Start Page',
	'page_end' => 'End Page',
	'page_range' => 'Page Range',
	'days_remaining' => 'Days Remaining',

	// Assessment Fields
	'question_text' => 'Question Text',
	'correct_answer' => 'Correct Answer',
	'incorrect_answer' => 'Incorrect Answer',
	'question_image_url' => 'Question Image',
	'question_audio_url' => 'Question Audio',
	'question_video_url' => 'Question Video',
	'page_reference' => 'Page Reference',
	'word' => 'Word',
	'definition' => 'Definition',
	'synonym' => 'Synonym',
	'antonym' => 'Antonym',
	'word_display' => 'Word Display',
	'min_word_count' => 'Minimum Word Count',
	'activity_content' => 'Activity Content',
	'feedback' => 'Feedback',
	'media_type' => 'Media Type',
	'answer_options' => 'Answer Options',
	'media_content' => 'Media Content',

	// Reading Log Fields
	'start_page' => 'Start Page',
	'end_page' => 'End Page',
	'page_range' => 'Page Range',
	'pages_read' => 'Pages Read',

	'active' => 'Active',
	'inactive' => 'Inactive',
	'upcoming' => 'Upcoming',
	'completed' => 'Completed',

	// Point Sources
	'achievement' => 'Achievement',
	'task' => 'Task',
	'quest' => 'Quest',
	'reading' => 'Reading',

	// Task Types
	'reading_log' => 'Reading Log',
	'activity' => 'Activity',
	'question' => 'Question',

	// Task Status
	'pending' => 'Pending',
	'upcoming' => 'Upcoming',

	// Recurrence Patterns

	// Assignment Types
	'individual' => 'Individual',
	'team' => 'Team',

	// Activity Categories

	// Quiz Types
	'completion' => 'Book Completion Quiz',

	'difficulty_level' => 'Difficulty Level',

	// Common Actions
	'select_school' => 'Select School',
	'enter_name' => 'Enter Name',
	'enter_name_surname' => 'Enter Name and Surname',
	'enter_description' => 'Enter Description',
	'select_class' => 'Select Class',
	'select_book' => 'Select Book',
	'select_team' => 'Select Team',
	'select_book_type' => 'Select Book Type',
	'view' => 'View',

	// Additional common fields
	'enter_user_title' => 'Enter Title',
	'enter_email' => 'Enter Email',
	'select_user' => 'Select User',
	'main_information' => 'Main Information',
	'user' => 'User',
	'role' => 'Role',
	'email' => 'Email',
	'username' => 'Username',
	'enter_username' => 'Enter Username',
	'unique_username_hint' => 'Username must be unique',
	'check_username' => 'Check Username',
	'username_exists' => 'Username already exists',
	'username_available' => 'Username is available',
	'point' => 'Point',
	'date_range' => 'Date Range',


	// Tags
	
	// Common Fields
	'name' => 'Name',
	'name_surname' => 'Name & Surname',
	'email' => 'Email',
	'password' => 'Password',
	'password_repeat' => 'Repeat Password',
	'description' => 'Description',
	'active' => 'Active',
	'default' => 'Default',
	'available' => 'Available',
	'related_information' => 'Related Information',
	'default_school_hint' => 'Mark this as the user\'s default school. Only one school can be default per user.',
	'default_class_hint' => 'Mark this as the user\'s default class. Only one class can be default per user.',
	'cannot_deactivate_default_school' => 'Cannot deactivate the default school. Please set another school as default first.',
	'cannot_deactivate_default_class' => 'Cannot deactivate the default class. Please set another class as default first.',
	'cannot_delete_default_school' => 'Cannot delete the default school. Please set another school as default first.',
	'cannot_delete_default_class' => 'Cannot delete the default class. Please set another class as default first.',
	'default_class_not_found' => 'Default class not found',
	'invalid_request' => 'Invalid request',


	// Student Management
	'students' => 'Students',
	'student_management_subtitle' => 'Comprehensive student management and progress tracking',
	'student_profile' => 'Student Profile',
	'student_metrics' => 'Student Metrics',
	'student_school_assignment_hint' => 'Assign student to a school. Uses your default school if not specified.',
	'student_class_assignment_hint' => 'Assign student to a class within the selected school.',
	'total_students' => 'Total Students',
	'active_readers_today' => 'Active Readers Today',
	'books_completed_this_month' => 'Books Completed This Month',
	'students_by_reading_activity' => 'Students by Reading Activity',
	'active_today' => 'Active Today',
	'active_this_week' => 'Active This Week',
	'inactive' => 'Inactive',
	'no_school' => 'No School',
	'no_class' => 'No Class',
	'books_reading' => 'Books Currently Reading',
	'books_completed' => 'Books Completed',
	'reading_points' => 'Reading Points',
	'reading_minutes' => 'Reading Minutes',
	'rewards_earned' => 'Rewards Earned',
	'activity_points' => 'Activity Points',
	'reading_streak' => 'Reading Streak',
	'days' => 'days',
	'reading_details' => 'Reading Details',
	'activity_details' => 'Activity Details',
	'rewards_details' => 'Rewards Details',
	'tasks_completed' => 'Tasks Completed',
	'assigned_date' => 'Assigned Date',
	'book_name' => 'Book Name',
	'date' => 'Date',
	'reading_duration' => 'Reading Duration',
	'progress' => 'Progress',
	'activity_name' => 'Activity Name',
	'activity_date' => 'Activity Date',
	'status' => 'Status',
	'rating' => 'Rating',
	'points_earned' => 'Points Earned',
	'feedback' => 'Feedback',
	'reward_type' => 'Reward Type',
	'reward_name' => 'Reward Name',
	'awarded_date' => 'Awarded Date',
	'image' => 'Image',
	'pending_activities' => 'Pending Activities',
	'status_pending' => 'Pending',
	'status_approved' => 'Approved',
	'status_rejected' => 'Rejected',
	'status_completed' => 'Completed',
	'status_failed' => 'Failed',
	'status_unknown' => 'Unknown',
	'approve' => 'Approve',
	'reject' => 'Reject',
	'student_not_found' => 'Student not found',
	'review_approved_successfully' => 'Review approved successfully',
	'review_rejected_successfully' => 'Review rejected successfully',	
	'review_not_found' => 'Review not found',

	// Class Activities
	'class_activities' => 'Class Activities',
	'class_activity' => 'Class Activity',
	'test_settings' => 'Test Settings',
	'writing_settings' => 'Writing Settings',
	'general_settings' => 'General Settings',
	'question_count_hint' => 'Override the number of questions for quiz/vocabulary tests (leave empty to use activity default)',
	'min_grade_hint' => 'Override the minimum passing grade for tests (leave empty to use activity default)',
	'allowed_tries_hint' => 'Override the maximum retry attempts for test activities (leave empty to use activity default)',
	'min_word_count_hint' => 'Override the minimum word count for writing activities (leave empty to use activity default)',
	'activity_points_hint' => 'Override the points awarded for this activity (leave empty to use activity default)',
	'required_activity_hint' => 'Override whether this activity is required for book completion',
	'active_activity_hint' => 'Override whether this activity is enabled for this class',
	'basic_information' => 'Basic Information',
	'school_assignments' => 'School Assignments',
	'enter_name' => 'Enter name',
	'enter_username' => 'Enter username',
	'enter_email' => 'Enter email',
	'enter_user_title' => 'Enter user title',
	'confirm_approve_review' => 'Are you sure you want to approve this review?',
	'confirm_reject_review' => 'Are you sure you want to reject this review?',
	'activity_not_found' => 'Activity not found',
	'content_required_for_writing' => 'Content is required for writing activities',

	'type' => 'Type',
	'role' => 'Role',
	'user' => 'User',
	'school' => 'School',
	'class' => 'Class',
	'start_date' => 'Start Date',
	'end_date' => 'End Date',
	'status' => 'Status',
	'thumbnail' => 'Thumbnail',
	'guard_name' => 'Guard Name',
	
	// Book Fields
	'isbn' => 'ISBN',
	'book_isbn' => 'Book ISBN',
	'publisher' => 'Publisher',
	'page_count' => 'Page Count',
	'year_of_publish' => 'Publication Year',
	'author' => 'Author',
	'category' => 'Category',
	'book' => 'Book',
	

	// Gamification Fields
	'story' => 'Story',
	'cover_image' => 'Cover Image',
	'rule_type' => 'Rule Type',
	'no' => 'No',
	'assignment_info' => 'Assignment Information',
	'start_date' => 'Start Date',
	'end_date' => 'End Date',
	'status' => 'Status',
	'completed' => 'Completed',
	'duration_days' => 'Duration',
	'days' => 'days',
	'base_image' => 'Base Image',
	'happy_image' => 'Happy Image',
	'sad_image' => 'Sad Image',
	'sleepy_image' => 'Sleepy Image',
	'avatar' => 'Avatar',
	'image' => 'Image',
	'achievement' => 'Achievement',
	'rule' => 'Rule',
	
	// Counts
	'books_count' => 'Books Count',
	'student_count' => 'Student Count',
	'duration_days' => 'Duration (Days)',
	
	// Relationships
	'school_assignments' => 'User School/Role Assignments',
	'class_assignments' => 'User Class Assignments',
	'assignment_info' => 'Assignment Information',
	'summary' => 'Summary',
	
	// Info Sections
	'main_information' => 'Main Information',
	'assignments' => 'Assignments',
	
	// Placeholders
	'enter_name' => 'Enter name',
	'enter_email' => 'Enter email',
	'enter_description' => 'Enter description',
	'select_user' => 'Select user',
	'select_school' => 'Select school',
	'select_school_type' => 'Select school type',
	'select_class' => 'Select class',
	'select_publisher' => 'Select publisher',
	'select_book' => 'Select book',
	'select_class_level' => 'Select grade level',
	'enter_isbn' => 'Enter ISBN',
	'enter_page_count' => 'Enter page count',
	'enter_year' => 'Enter year',

	// Task Placeholders
	'enter_page_start' => 'Enter start page',
	'enter_page_end' => 'Enter end page',

	// Assessment Placeholders
	'enter_question_text' => 'Enter question text',
	'enter_correct_answer' => 'Enter correct answer',
	'enter_incorrect_answer' => 'Enter incorrect answer',
	'enter_image_url' => 'Enter image URL',
	'enter_audio_url' => 'Enter audio URL',
	'enter_video_url' => 'Enter video URL',
	'enter_word' => 'Enter word',
	'enter_definition' => 'Enter definition',
	'enter_synonym' => 'Enter synonym',
	'enter_antonym' => 'Enter antonym',
	'enter_page_reference' => 'Enter page reference',

	// Reading Log Placeholders

	// Reading Log Messages

	// Attributes
	'display_name' => 'Display Name',
	'author_names' => 'Author Names',
	'difficulty' => 'Difficulty',
	'total_pages' => 'Total Pages',
	'average_pages' => 'Average Pages',
	'books_list' => 'Books List',
	
	// Privacy Agreement
	'privacy_agreement_title' => 'Privacy Policy Agreement',
	'privacy_policy_title' => 'Privacy Policy',
	'privacy_agreement_consent' => 'I accept the privacy policy and terms of use',
	'accept_and_continue' => 'Accept and Continue',
	'privacy_consent_required' => 'You must accept the privacy policy to continue.',
	'privacy_agreement_accepted' => 'Privacy policy accepted successfully.',
	'privacy_agreement_error' => 'An error occurred while processing your consent. Please try again.',
	'authentication_required' => 'Authentication required.',
	'last_updated' => 'Last Updated',
	'version' => 'Version',

	// Privacy Policy Content
	'privacy_policy_intro' => 'This privacy policy explains how we collect, use, and protect your personal information when you use our reading tracker application.',
	'data_collection_title' => '1. Data Collection',
	'data_collection_content' => 'We collect information you provide directly to us, such as your name, email address, reading progress, and usage data to provide and improve our services.',
	'data_usage_title' => '2. Data Usage',
	'data_usage_content' => 'We use your information to provide educational services, track reading progress, generate reports, and improve our application functionality.',
	'data_sharing_title' => '3. Data Sharing',
	'data_sharing_content' => 'We do not sell or share your personal information with third parties except as necessary to provide our services or as required by law.',
	'data_security_title' => '4. Data Security',
	'data_security_content' => 'We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
	'user_rights_title' => '5. Your Rights',
	'user_rights_content' => 'You have the right to access, update, or delete your personal information. Contact us if you wish to exercise these rights.',
	'contact_info_title' => '6. Contact Information',
	'contact_info_content' => 'If you have questions about this privacy policy, please contact your system administrator or school administration.',

	// User Agreements
	'user_agreements' => 'User Agreements',
	'agreement_type' => 'Agreement Type',
	'accepted_at' => 'Accepted At',
	'ip_address' => 'IP Address',
	'privacy_policy' => 'Privacy Policy',
	'terms_of_service' => 'Terms of Service',
	'cookie_policy' => 'Cookie Policy',
	'enter_version' => 'Enter version',
	'enter_ip_address' => 'Enter IP address',

	// Actions
	'cancel' => 'Cancel',
	'cancel' => 'Cancel',
	
	// Messages
	
	// Validation
	
	// Role Levels
	'student' => 'Student',
	
	// School Types
	'school' => 'School',
	
	// Tag Types
	
	// Status Values
	'current' => 'Current',

	// Rule Types
	'time_based' => 'Time Based',
	
	// Time
	'minutes' => 'minutes',
	'days' => 'days',

	  // Motivational Messages
    'goal_messages' => [
        'achieved' => 'Congratulations! You have achieved this goal!',
        'almost_there' => 'You\'re almost there! Keep up the great work!',
        'great_progress' => 'Great progress! You\'re doing excellent!',
        'halfway_there' => 'You\'re halfway there! Keep going!',
        'good_start' => 'Good start! You\'re on the right track!',
        'just_started' => 'You\'ve just started. Take it one step at a time!',
    ],

    // Recommendations
    'goal_recommendations' => [
        'need_more_effort' => 'Consider dedicating more time to reading activities to catch up.',
        'almost_there' => 'You\'re so close! Just a little more effort to reach your goal.',
        'deadline_approaching' => 'You have upcoming deadlines. Focus on completing urgent tasks.',
        'keep_going' => 'You\'re making steady progress. Keep up the good work!',
    ],

    'student_assignment' => 'Student Assignment',
    'save_and_continue' => 'Save and Continue',
    'complete_step_1_first' => 'Please complete Step 1 first',
    'add_task' => 'Add Task',
    'continue_to_assignment' => 'Continue to Assignment',
    'complete_previous_steps_first' => 'Please complete previous steps first',
    'student_team_assignment' => 'Student & Team Assignment',
    'assignment_comment' => 'Assignment Comment',
    'optional_comment' => 'Optional comment for this assignment',
    'save_task' => 'Save Task',
    'task_deleted_successfully' => 'Task deleted successfully',
    'completed_assignments' => 'Completed Assignments',
    'assigned_students' => 'Assigned Students',
    'assigned_teams' => 'Assigned Teams',
    'completion_rate' => 'Completion Rate',
'completion_percentage' => 'Completion %',
    'total_tasks' => 'Total Tasks',
    'progress_overview' => 'Progress Overview',
    'tasks_completed' => 'Tasks Completed',
    'in_progress' => 'In Progress',
    'completed' => 'Completed',
    'individual_assignments' => 'Individual Assignments',
    'team_assignments' => 'Team Assignments',
	'class_task_assignments' => 'Class Assignments',
    'in_progress_assignments' => 'In Progress Assignments',
    'average_progress' => 'Average Progress',
    'member_count' => 'Member Count',
    'team_leader' => 'Team Leader',
    'no_leader' => 'No Leader',
    'achieve_date' => 'Achievement Date',
    'not_specified' => 'Not Specified',
    'days' => 'Days',
    'duration' => 'Duration',
    'please_complete_previous_steps_first' => 'Please complete the previous steps first',
	'assigned_by' => 'Assigned By',
	'assign_date' => 'Assign Date',
	'due_date' => 'Due Date',
	'active' => 'Active',
	'inactive' => 'Inactive',
	'missing_book_type' => 'Missing book type',
	'missing_page_count' => 'Missing page count',
	'book_inactive_warning' => 'This book is inactive and cannot be used for reading logs or activities',
	'cannot_create_reading_log_inactive_book' => 'Cannot create reading log for inactive book',
	'cannot_create_activity_inactive_book' => 'Cannot create activity for inactive book',
	'assignments_count' => 'Assignments Count',
	'completed_tasks' => 'Completed Tasks',

	// Book Discovery
	'search_import_book' => 'Search & Import Book',
	'isbn_hint' => 'Enter 10 or 13 digit ISBN to automatically discover book information',
	'publisher_auto_filled' => 'This field will be automatically filled when book is found',
	'enter_isbn_to_search' => 'Please enter an ISBN to search for.',
	'invalid_isbn_format' => 'Please enter a valid ISBN (10 or 13 digits).',
	'book_already_exists' => 'A book with this ISBN already exists in the database.',
	'book_not_found_sources' => 'Book not found in any of the configured sources. Please enter the information manually.',
	'book_exists_locally' => 'This book already exists in the local database.',
	'book_found_auto_populated' => 'Book found! Information has been populated automatically.',
	'book_search_error' => 'An error occurred while searching for the book. Please try again or enter the information manually.',

	// Level System
	'levels' => 'Levels',
	'level' => 'Level',
	'user_levels' => 'User Levels',
	'level_number' => 'Level Number',
	'level_name' => 'Level Name',
	'level_image' => 'Level Badge',
	'books_required' => 'Books Required',
	'page_points_required' => 'Page Points Required',
	'all_required' => 'All Required',
	'users_achieved' => 'Users Achieved',
	'achievement_date' => 'Achievement Date',
	'triggered_by_reading' => 'Triggered by Reading',
	'manual' => 'Manual',
	'level_number_hint' => 'Incremental level number starting from 1 (not editable)',
	'level_name_hint' => 'Display name for the level (e.g., "Beginner Reader")',
	'level_image_hint' => 'Optional level badge/icon image',
	'books_required_hint' => 'Minimum number of books required to reach this level',
	'page_points_required_hint' => 'Minimum page points required to reach this level',
	'all_required_hint' => 'On: Both book and point conditions must be met. Off: Either condition is sufficient',
	'reading_log_trigger_hint' => 'Reading log entry that triggered this level achievement (if any)',
	'can_only_delete_most_recent_reading_log' => 'Only the most recent reading log can be deleted',

	// Dashboard Metrics
	'dashboard_metrics' => 'Dashboard Metrics',
	'activities_pending_approval' => 'Activities Pending Approval',
	'students_read_last_24h' => 'Students Read (Last 24h)',
	'pages_read_last_24h' => 'Pages Read (Last 24h)',
	'activities_done_last_24h' => 'Activities Done (Last 24h)',
	'never_read' => 'Never Read',

	// Student Rankings
	'student_rankings' => 'Student Rankings',
	'most_books_read' => 'Most Books Read',
	'fewest_books_read' => 'Fewest Books Read',
	'highest_level_students' => 'Highest Level Students',
	'most_badges' => 'Most Badges',
	'fewest_badges' => 'Fewest Badges',
	'longest_reading_streak' => 'Longest Reading Streak',
	'longest_reading_gap' => 'Longest Reading Gap',
	'highest_activity_score' => 'Highest Activity Score',
	'lowest_activity_score' => 'Lowest Activity Score',
	'lowest_level_students' => 'Lowest Level Students',
	'student_name' => 'Student Name',
	'books_count' => 'Books',
	'level' => 'Level',
	'badges_count' => 'Badges',
	'streak_days' => 'Streak (Days)',
	'activity_points' => 'Activity Points',
	'days_since_reading' => 'Days Since Reading (Days)',
	'avatar' => 'Avatar',

	// Import 
	'import_template_hint' => '1- Please download the :template file and fill it. <br>2- Save the file as CSV (UTF-8) format. <br>3- Select the file to import.',
	'template' => 'template',

	// Notifications
	'send_push_notification' => 'Send Push Notification',
	'activity_approved_notification_title' => 'Activity Approved! 🎉',
	'activity_approved_notification_body' => 'Hello :student_name, your activity has been approved by your teacher.',
	'activity_rejected_notification_title' => 'Activity Needs Revision 📝',
	'activity_rejected_notification_body' => 'Hello :student_name, your teacher has provided feedback on your activity.',
	'activity_submitted_notification_title' => 'New Activity Submission 📚',
	'activity_submitted_notification_body' => 'Student :student_name has submitted an activity for review.',
	'todays_reading_reminder_notification_title' => 'Time to Read :student_name! 📖',
	'todays_reading_reminder_notification_body' => 'Don\'t forget to log your reading progress today!',
	'inactivity_reminder_notification_title' => 'We Miss You :student_name! 📖',
	'inactivity_reminder_notification_body' => 'Come back and continue your reading journey.',
	'admin_broadcast_notification_title' => 'Important Announcement 📢',
	'admin_broadcast_notification_body' => 'You have a new message from the administration.',
	
];
