<?php

namespace Database\Factories;

use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Team>
 */
class TeamFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Team::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->words(2, true) . ' Team',
            'logo' => null,
            'leader_user_id' => null,
            'active' => true,
            'created_by' => 1, // Default system user
        ];
    }

    /**
     * Indicate that the team has a leader.
     */
    public function withLeader(User $leader = null): static
    {
        return $this->state(function (array $attributes) use ($leader) {
            return [
                'leader_user_id' => $leader ? $leader->id : User::factory()->create()->id,
            ];
        });
    }

    /**
     * Indicate that the team is inactive.
     */
    public function inactive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => false,
            ];
        });
    }
}
