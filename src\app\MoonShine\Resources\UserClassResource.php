<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserClass;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\School;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('academic-cap')]
class UserClassResource extends BaseResource
{
    // TODO: Class list should be filtered by school
    // TODO: School list should be filtered by user's active schools

    protected string $model = UserClass::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'schoolClass', 'school'];

    public function getTitle(): string
    {
        return __('admin.class_assignments');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.school'),
                'school',
                formatted: fn(School $org) => $org->name,
                resource: SchoolResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name,
                resource: SchoolClassResource::class
            )
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),

            Switcher::make(__('admin.default'), 'default')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.user'),
                    'user',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class
                )
                    ->required(),

                BelongsTo::make(
                    __('admin.school'),
                    'school',
                    formatted: fn(School $org) => $org->name,
                    resource: SchoolResource::class
                )
                    ->required(),

                BelongsTo::make(
                    __('admin.class'),
                    'schoolClass',
                    formatted: fn(SchoolClass $class) => $class->name,
                    resource: SchoolClassResource::class
                )
                    ->required(),

                Switcher::make(__('admin.active'), 'active')
                    ->default(true),

                Switcher::make(__('admin.default'), 'default')
                    ->default(false)
                    ->hint(__('admin.default_class_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.school'),
                'school',
                formatted: fn(School $org) => $org->name,
                resource: SchoolResource::class
            ),

            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name,
                resource: SchoolClassResource::class
            ),

            Switcher::make(__('admin.active'), 'active'),
            Switcher::make(__('admin.default'), 'default'),

            Text::make(__('admin.summary'), 'summary'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'class_id' => ['required', 'exists:school_classes,id'],
            'school_id' => ['required', 'exists:schools,id'],
            'active' => ['boolean'],
            'default' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'schoolClass.name', 'school.name'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all class assignments
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // Admin can see assignments in their school
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereIn('school_id', $userSchoolIds);
        }

        // Teachers can see assignments in their classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereIn('class_id', $teacherClassIds);
        }

        // Students have no access to class assignment management
        return $builder->where('id', 0);
    }
}
