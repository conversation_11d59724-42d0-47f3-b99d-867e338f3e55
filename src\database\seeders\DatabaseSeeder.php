<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core System Seeders
            RoleSeeder::class,
            UserSeeder::class,
/*
            // Academic Structure Seeders
            EnumSchoolTypeSeeder::class,
            EnumClassLevelSeeder::class,
            SchoolSeeder::class,
            SchoolClassSeeder::class,
            UserSchoolSeeder::class,
            UserClassSeeder::class,
            TeamSeeder::class,
            UserTeamSeeder::class,

            // Book Management Seeders
            BookTypeSeeder::class,
            PagePointSeeder::class,
            AuthorSeeder::class,
            PublisherSeeder::class,
            CategorySeeder::class,
            BookSeeder::class,
            BookAuthorSeeder::class,
            BookCategorySeeder::class,
            ClassBookSeeder::class,

            // Activity System Seeders
            ActivityCategorySeeder::class,
            ActivitySeeder::class,
            UserActivitySeeder::class,
            UserActivityReviewSeeder::class,

            // Gamification Seeders
            AvatarSeeder::class,
            UserAvatarSeeder::class,
            EnumBadgeRuleTypeSeeder::class,
            BadgeSeeder::class,
            BadgeRuleSeeder::class,
            UserBadgeSeeder::class,
            TeamBadgeSeeder::class,

            // Task Management Seeders
            TaskSeeder::class,
            TaskBookSeeder::class,
            TaskBookCategorySeeder::class,

            // Reading Tracking Seeders
            UserReadingLogSeeder::class,
            UserPointSeeder::class,
            UserBookSeeder::class,
*/            
        ]);
    }
}
