<div class="min-h-screen bg-base-200">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-100">
        <div class="px-4 py-6">
            <h1 class="text-2xl font-bold text-gray-900">{{ __('mobile.friends') }}</h1>
        </div>
        <div class="px-4 pb-4">
            <select wire:model.live="sortBy" class="w-full px-3 py-1 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                @foreach($sortOptions as $value => $label)
                    <option value="{{ $value }}">{{ __('mobile.' . $value) ?? $label }}</option>
                @endforeach
            </select>
        </div>
    </div>
    
    <!-- Friends List -->
    <div class="px-4 py-4 space-y-4">
        <div class="flex bg-white rounded-2xl p-1 shadow-sm">
            <button
                wire:click="setActiveTab('all')"
                class="flex-1 py-1 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'all' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
            >
                {{ __('mobile.all') }}
            </button>
            <button
                wire:click="setActiveTab('class')"
                class="flex-1 py-1 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'class' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
            >
                {{ __('mobile.my_class') }}
            </button>
            <button
                wire:click="setActiveTab('teams')"
                class="flex-1 py-1 px-4 rounded-xl font-semibold transition-all {{ $activeTab === 'teams' ? 'bg-violet-400 text-white' : 'text-gray-600' }}"
            >
                {{ __('mobile.my_teams') }}
            </button>
        </div>

        @if($friends && $friends->count() > 0)
            @foreach($friends as $friend)
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-4">
                    <div class="flex items-start space-x-4">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            @if($friend->avatar)
                                <img src="{{ asset('storage/' . $friend->avatar) }}" 
                                     alt="{{ $friend->name }}" 
                                     class="w-16 h-16 rounded-full object-cover">
                            @else
                                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-400 to-blue-500 flex items-center justify-center">
                                    <span class="text-white font-bold text-lg">{{ substr($friend->name, 0, 1) }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Friend Info -->
                        <div class="flex-1 min-w-0">
                            <div class="mb-2">
                                <h3 class="font-semibold text-gray-900 text-lg">{{ $friend->name }}</h3>
                            </div>

                            <div class="mb-3">
                                <div class="flex flex-wrap gap-1">
                                    @if($friend->primary_class)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {{ $friend->primary_class }}
                                        </span>
                                    @endif
                                    @if($friend->shared_teams && $friend->shared_teams->count() > 0)
                                        @foreach($friend->shared_teams as $team)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $team->name }}
                                            </span>
                                        @endforeach
                                    @endif
                                </div>
                            </div>

                            <!-- Currently Reading -->
                            @if($friend->currently_reading && $friend->currently_reading->book)
                                <div class="mb-3">
                                    <div class="flex items-center space-x-2">
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-600">{{ __('mobile.currently_reading') }}</p>
                                            <span class="text-sm font-medium text-gray-900">{{ $friend->currently_reading->book->name }}</span>
                                        </div>
                                        <img src="{{ $friend->currently_reading->book->cover_image ? asset('storage/' . $friend->currently_reading->book->cover_image) : '/images/default-book-cover.png' }}" 
                                            alt="{!! $friend->currently_reading->book->name !!}" 
                                            class="w-8 h-10 rounded object-cover">
                                    </div>
                                </div>
                            @else
                                <div class="mb-3">
                                    <p class="text-sm text-gray-500 italic">{{ __('mobile.not_currently_reading') }}</p>
                                </div>
                            @endif

                            <!-- Statistics -->
                            <div class="flex items-center space-x-6">
                                <x-mobile-stat-box  icon="books-icon.png" alt="{{ __('mobile.books_alt') }}"  :value="$friend->total_books"  />
                                <x-mobile-stat-box  icon="badge-icon.png" alt="{{ __('mobile.badges_alt') }}"  :value="$friend->total_badges"  />
                                <x-mobile-stat-box  icon="star-icon.png" alt="{{ __('mobile.points_alt') }}"  :value="$friend->total_points"  />
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('mobile.no_friends_found') }}</h3>
                <p class="text-gray-600 max-w-sm mx-auto">
                    @if($activeTab === 'class')
                        {{ __('mobile.no_classmates_message') }}
                    @elseif($activeTab === 'teams')
                        {{ __('mobile.no_teammates_message') }}
                    @else
                        {{ __('mobile.no_friends_message') }}
                    @endif
                </p>
            </div>
        @endif
    </div>
</div>
