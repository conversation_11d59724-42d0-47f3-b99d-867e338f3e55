<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\UserBook;
use App\Models\User;
use App\Models\Book;

try {
    echo "=== TESTING SESSION-BASED TRACKING SYSTEM ===\n\n";
    
    // Get a test user and book
    $user = User::first();
    $book = Book::first();
    
    if (!$user || !$book) {
        echo "❌ No users or books found in database\n";
        exit;
    }
    
    echo "Test User: {$user->name}\n";
    echo "Test Book: {$book->name}\n\n";
    
    // Test Case 1: Check if user can start new session (should be true initially)
    echo "TEST CASE 1: Can start new session (no active sessions)\n";
    $canStart = UserBook::canStartNewSession($user->id, $book->id);
    echo "Can start new session: " . ($canStart ? "YES" : "NO") . "\n";
    echo "✅ " . ($canStart ? "PASS" : "FAIL") . " - Should be able to start new session\n\n";
    
    // Test Case 2: Create first session
    echo "TEST CASE 2: Create first reading session\n";
    $session1 = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now()->subDays(10)->toDateString(),
        'end_date' => null, // Active session
    ]);
    
    echo "Session 1 created:\n";
    echo "- Session Number: {$session1->getSessionNumber()}\n";
    echo "- Status: {$session1->localized_reading_status}\n";
    echo "- Session Info: {$session1->session_info}\n";
    echo "✅ PASS - First session created\n\n";
    
    // Test Case 3: Check if user can start another session (should be false now)
    echo "TEST CASE 3: Cannot start new session (active session exists)\n";
    $canStartAgain = UserBook::canStartNewSession($user->id, $book->id);
    echo "Can start new session: " . ($canStartAgain ? "YES" : "NO") . "\n";
    echo "✅ " . (!$canStartAgain ? "PASS" : "FAIL") . " - Should NOT be able to start new session\n\n";
    
    // Test Case 4: Get current active session
    echo "TEST CASE 4: Get current active session\n";
    $currentSession = UserBook::getCurrentSession($user->id, $book->id);
    if ($currentSession) {
        echo "Current session found:\n";
        echo "- ID: {$currentSession->id}\n";
        echo "- Session Number: {$currentSession->getSessionNumber()}\n";
        echo "- Start Date: {$currentSession->start_date->format('Y-m-d')}\n";
        echo "✅ PASS - Current session retrieved\n\n";
    } else {
        echo "❌ FAIL - No current session found\n\n";
    }
    
    // Test Case 5: Complete the first session
    echo "TEST CASE 5: Complete first session\n";
    $session1->markAsCompleted(now()->subDays(5)->toDateString());
    $session1->refresh();
    
    echo "Session 1 after completion:\n";
    echo "- Status: {$session1->localized_reading_status}\n";
    echo "- End Date: {$session1->end_date->format('Y-m-d')}\n";
    echo "- Duration: {$session1->reading_duration_text}\n";
    echo "✅ PASS - Session completed\n\n";
    
    // Test Case 6: Check if user can start new session after completion
    echo "TEST CASE 6: Can start new session after completion\n";
    $canStartAfterCompletion = UserBook::canStartNewSession($user->id, $book->id);
    echo "Can start new session: " . ($canStartAfterCompletion ? "YES" : "NO") . "\n";
    echo "✅ " . ($canStartAfterCompletion ? "PASS" : "FAIL") . " - Should be able to start new session after completion\n\n";
    
    // Test Case 7: Create second session (re-reading)
    echo "TEST CASE 7: Create second reading session (re-reading)\n";
    $session2 = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now()->subDays(3)->toDateString(),
        'end_date' => null, // Active session
    ]);
    
    echo "Session 2 created:\n";
    echo "- Session Number: {$session2->getSessionNumber()}\n";
    echo "- Status: {$session2->localized_reading_status}\n";
    echo "- Session Info: {$session2->session_info}\n";
    echo "✅ PASS - Second session created\n\n";
    
    // Test Case 8: Get all sessions for user-book combination
    echo "TEST CASE 8: Get all sessions for user-book combination\n";
    $allSessions = UserBook::getAllSessions($user->id, $book->id);
    echo "Total sessions: {$allSessions->count()}\n";
    
    foreach ($allSessions as $session) {
        echo "- Session {$session->getSessionNumber()}: {$session->localized_reading_status} ({$session->reading_duration_text})\n";
    }
    
    echo "✅ PASS - All sessions retrieved\n\n";
    
    // Test Case 9: Reading history display
    echo "TEST CASE 9: Reading history display\n";
    $readingHistory = $session2->reading_history;
    echo "Reading History: {$readingHistory}\n";
    echo "✅ PASS - Reading history generated\n\n";
    
    // Cleanup: Remove test sessions
    echo "CLEANUP: Removing test sessions\n";
    UserBook::where('user_id', $user->id)
        ->where('book_id', $book->id)
        ->delete();
    echo "✅ Test sessions cleaned up\n\n";
    
    echo "✅ Session-based tracking system tests completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
