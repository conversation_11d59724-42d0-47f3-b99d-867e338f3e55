<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\EnumTaskType;
use App\Models\EnumTaskCycle;
use Carbon\Carbon;

class TaskSystemUnitTest extends TestCase
{
    public function test_enum_task_type_constants_are_defined()
    {
        $this->assertEquals(1, EnumTaskType::READ_PAGES);
        $this->assertEquals(2, EnumTaskType::READ_BOOKS);
        $this->assertEquals(3, EnumTaskType::READ_MINUTES);
        $this->assertEquals(4, EnumTaskType::READ_DAYS);
        $this->assertEquals(5, EnumTaskType::READ_STREAK);
        $this->assertEquals(6, EnumTaskType::EARN_READING_POINTS);
        $this->assertEquals(7, EnumTaskType::EARN_ACTIVITY_POINTS);
        $this->assertEquals(8, EnumTaskType::COMPLETE_BOOK_ACTIVITY);
        $this->assertEquals(9, EnumTaskType::COMPLETE_BOOK_LIST);
    }

    public function test_enum_task_cycle_constants_are_defined()
    {
        $this->assertEquals(1, EnumTaskCycle::TOTAL);
        $this->assertEquals(2, EnumTaskCycle::DAILY);
        $this->assertEquals(3, EnumTaskCycle::WEEKLY);
        $this->assertEquals(4, EnumTaskCycle::MONTHLY);
    }

    public function test_task_type_classifications()
    {
        $quantitativeTypes = EnumTaskType::getQuantitativeTypes();
        $qualitativeTypes = EnumTaskType::getQualitativeTypes();
        $bookListTypes = EnumTaskType::getBookListTypes();
        $activityTypes = EnumTaskType::getActivityTypes();

        $this->assertCount(8, $quantitativeTypes);
        $this->assertCount(2, $qualitativeTypes);
        $this->assertCount(1, $bookListTypes);
        $this->assertCount(1, $activityTypes);

        $this->assertContains(EnumTaskType::READ_PAGES, $quantitativeTypes);
        $this->assertContains(EnumTaskType::COMPLETE_BOOK_LIST, $bookListTypes);
        $this->assertContains(EnumTaskType::COMPLETE_BOOK_ACTIVITY, $activityTypes);
    }

    public function test_task_cycle_classifications()
    {
        $timeBasedCycles = EnumTaskCycle::getTimeBasedCycles();
        $cumulativeCycles = EnumTaskCycle::getCumulativeCycles();

        $this->assertCount(3, $timeBasedCycles);
        $this->assertCount(1, $cumulativeCycles);

        $this->assertContains(EnumTaskCycle::DAILY, $timeBasedCycles);
        $this->assertContains(EnumTaskCycle::WEEKLY, $timeBasedCycles);
        $this->assertContains(EnumTaskCycle::MONTHLY, $timeBasedCycles);
        $this->assertContains(EnumTaskCycle::TOTAL, $cumulativeCycles);
    }

    public function test_task_cycle_period_calculations()
    {
        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');

        // Create mock EnumTaskCycle instances
        $daily = new EnumTaskCycle(['nr' => EnumTaskCycle::DAILY, 'name' => 'Daily']);
        $weekly = new EnumTaskCycle(['nr' => EnumTaskCycle::WEEKLY, 'name' => 'Weekly']);
        $monthly = new EnumTaskCycle(['nr' => EnumTaskCycle::MONTHLY, 'name' => 'Monthly']);
        $total = new EnumTaskCycle(['nr' => EnumTaskCycle::TOTAL, 'name' => 'Total']);

        $this->assertEquals(31, $daily->getPeriodsInRange($startDate, $endDate));
        $this->assertEquals(5, $weekly->getPeriodsInRange($startDate, $endDate));
        $this->assertEquals(1, $monthly->getPeriodsInRange($startDate, $endDate));
        $this->assertEquals(1, $total->getPeriodsInRange($startDate, $endDate));
    }

    public function test_task_cycle_expected_progress_calculation()
    {
        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');
        $currentDate = Carbon::parse('2024-01-16'); // Halfway through month

        // Create mock EnumTaskCycle instances
        $daily = new EnumTaskCycle(['nr' => EnumTaskCycle::DAILY, 'name' => 'Daily']);
        $total = new EnumTaskCycle(['nr' => EnumTaskCycle::TOTAL, 'name' => 'Total']);

        // For daily cycle, 16 days out of 31 days = ~51.6%
        $dailyProgress = $daily->getExpectedProgressPercentage($startDate, $endDate, $currentDate);
        $this->assertEqualsWithDelta(51.6, $dailyProgress, 0.1);

        // For total cycle, 16 days out of 31 days = ~51.6%
        $totalProgress = $total->getExpectedProgressPercentage($startDate, $endDate, $currentDate);
        $this->assertEqualsWithDelta(51.6, $totalProgress, 0.1);
    }

    public function test_task_type_helper_methods()
    {
        // Create mock EnumTaskType instances
        $readPages = new EnumTaskType(['nr' => EnumTaskType::READ_PAGES, 'name' => 'Read Pages']);
        $bookList = new EnumTaskType(['nr' => EnumTaskType::COMPLETE_BOOK_LIST, 'name' => 'Complete a Book List']);
        $activity = new EnumTaskType(['nr' => EnumTaskType::COMPLETE_BOOK_ACTIVITY, 'name' => 'Complete a Book Activity']);

        // Test quantitative/qualitative
        $this->assertTrue($readPages->isQuantitative());
        $this->assertFalse($readPages->isQualitative());
        
        // Test special requirements
        $this->assertTrue($bookList->requiresBookList());
        $this->assertFalse($readPages->requiresBookList());

        $this->assertTrue($activity->requiresActivity());
        $this->assertFalse($readPages->requiresActivity());

        // Test measurable/binary
        $this->assertTrue($readPages->isMeasurable());
        $this->assertFalse($readPages->isBinary());
    }

    public function test_task_cycle_helper_methods()
    {
        // Create mock EnumTaskCycle instances
        $daily = new EnumTaskCycle(['nr' => EnumTaskCycle::DAILY, 'name' => 'Daily']);
        $total = new EnumTaskCycle(['nr' => EnumTaskCycle::TOTAL, 'name' => 'Total']);

        $this->assertTrue($daily->isTimeBased());
        $this->assertFalse($daily->isCumulative());

        $this->assertFalse($total->isTimeBased());
        $this->assertTrue($total->isCumulative());
    }

    public function test_task_cycle_period_boundaries()
    {
        $startDate = Carbon::parse('2024-01-01');
        $endDate = Carbon::parse('2024-01-31');

        // Create mock EnumTaskCycle instance
        $daily = new EnumTaskCycle(['nr' => EnumTaskCycle::DAILY, 'name' => 'Daily']);

        // Test period start dates
        $period1Start = $daily->getPeriodStartDate($startDate, 1);
        $period5Start = $daily->getPeriodStartDate($startDate, 5);

        $this->assertEquals('2024-01-01', $period1Start->format('Y-m-d'));
        $this->assertEquals('2024-01-05', $period5Start->format('Y-m-d'));

        // Test period end dates
        $period1End = $daily->getPeriodEndDate($startDate, $endDate, 1);
        $this->assertEquals('2024-01-01 23:59:59', $period1End->format('Y-m-d H:i:s'));
    }

    public function test_task_type_units_and_descriptions()
    {
        // Create mock EnumTaskType instances
        $readPages = new EnumTaskType(['nr' => EnumTaskType::READ_PAGES, 'name' => 'Read Pages']);
        $readBooks = new EnumTaskType(['nr' => EnumTaskType::READ_BOOKS, 'name' => 'Read Books']);
        $readMinutes = new EnumTaskType(['nr' => EnumTaskType::READ_MINUTES, 'name' => 'Read Minutes']);

        // Test units (these will use translation keys, so we test the logic)
        $this->assertNotEmpty($readPages->getUnitAttribute());
        $this->assertNotEmpty($readBooks->getUnitAttribute());
        $this->assertNotEmpty($readMinutes->getUnitAttribute());

        // Test descriptions (these will use translation keys, so we test the logic)
        $this->assertNotEmpty($readPages->getDescriptionAttribute());
        $this->assertNotEmpty($readBooks->getDescriptionAttribute());
        $this->assertNotEmpty($readMinutes->getDescriptionAttribute());
    }

    public function test_task_cycle_units_and_descriptions()
    {
        // Create mock EnumTaskCycle instances
        $daily = new EnumTaskCycle(['nr' => EnumTaskCycle::DAILY, 'name' => 'Daily']);
        $weekly = new EnumTaskCycle(['nr' => EnumTaskCycle::WEEKLY, 'name' => 'Weekly']);
        $total = new EnumTaskCycle(['nr' => EnumTaskCycle::TOTAL, 'name' => 'Total']);

        // Test units (these will use translation keys, so we test the logic)
        $this->assertNotEmpty($daily->getUnitAttribute());
        $this->assertNotEmpty($weekly->getUnitAttribute());
        $this->assertNotEmpty($total->getUnitAttribute());

        // Test descriptions (these will use translation keys, so we test the logic)
        $this->assertNotEmpty($daily->getDescriptionAttribute());
        $this->assertNotEmpty($weekly->getDescriptionAttribute());
        $this->assertNotEmpty($total->getDescriptionAttribute());
    }
}
