<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\UserActivity;
use App\Models\UserActivityReview;
use App\Models\Activity;
use App\Services\StatisticsService;

class TeacherActivityReview extends Component
{
    public $user;
    public $userActivity;
    public $student;
    public $book;
    public $activity;
    public $isLoading = false;
    public $feedback = '';
    
    public function mount($userActivity)
    {
        $this->user = Auth::user();
        
        // Ensure user is a teacher
        if (!$this->user->isTeacher()) {
            return redirect()->route('mobile.home');
        }
        
        // Load the user activity with relationships
        $this->userActivity = UserActivity::with(['user', 'book', 'activity'])
            ->findOrFail($userActivity);
        
        // Ensure this activity belongs to a student in teacher's classes
        $teacherClassIds = StatisticsService::getTeacherClassIds($this->user);
        $studentInTeacherClass = $this->userActivity->user
            ->activeUserClasses()
            ->whereIn('class_id', $teacherClassIds)
            ->exists();
            
        if (!$studentInTeacherClass) {
            abort(403, __('mobile.unauthorized_activity'));
        }
        
        // Ensure activity is pending review
        if ($this->userActivity->status !== UserActivity::STATUS_PENDING) {
            session()->flash('info', __('mobile.activity_already_reviewed'));
            return redirect()->route('mobile.teacher.home');
        }
        
        $this->student = $this->userActivity->user;
        $this->book = $this->userActivity->book;
        $this->activity = $this->userActivity->activity;
    }
    
    public function acceptActivity()
    {
        $this->reviewActivity(UserActivityReview::STATUS_APPROVED, __('mobile.activity_approved_successfully'), __('mobile.error_approving_activity'));
    }
    
    public function rejectActivity()
    {
        $this->reviewActivity(UserActivityReview::STATUS_REJECTED, __('mobile.activity_rejected_successfully'), __('mobile.error_rejecting_activity'));
    }

    public function reviewActivity($status, $successMessage, $errorMessage)
    {
        $this->isLoading = true;

        try {
            // Create review record if it doesn't exist
            $review = UserActivityReview::firstOrCreate(
                ['user_activity_id' => $this->userActivity->id],
                [
                    'review_date' => now()->toDateString(),
                    'reviewed_by' => $this->user->id,
                    'status' => UserActivityReview::STATUS_WAITING,
                    'feedback' => $this->feedback,
                    'created_by' => $this->user->id,
                ]
            );

            // Update review status
            $review->update([
                'status' => $status,
                'reviewed_by' => $this->user->id,
                'feedback' => $this->feedback,
                'review_date' => now()->toDateString(),
            ]);
            
            session()->flash('success', $successMessage);
            return redirect()->route('mobile.teacher.home');

        } catch (\Exception $e) {
            session()->flash('error', $errorMessage);
            $this->isLoading = false;
        }
    }

    /**
     * Get the activity content for display.
     */
    public function getActivityContentProperty()
    {
        switch ($this->activity->activity_type) {
            case Activity::ACTIVITY_TYPE_WRITING:
                return $this->userActivity->content;
                
            case Activity::ACTIVITY_TYPE_RATING:
                return $this->userActivity->rating;
                
            case Activity::ACTIVITY_TYPE_MEDIA:
                return $this->userActivity->media_url;
                
            default:
                return null;
        }
    }
    
    /**
     * Check if activity has media content.
     */
    public function getHasMediaProperty()
    {
        return $this->activity->activity_type === Activity::ACTIVITY_TYPE_MEDIA 
            && !empty($this->userActivity->media_url);
    }
    
    /**
     * Check if activity is a rating activity.
     */
    public function getIsRatingProperty()
    {
        return $this->activity->activity_type === Activity::ACTIVITY_TYPE_RATING;
    }
    
    /**
     * Check if activity is a writing activity.
     */
    public function getIsWritingProperty()
    {
        return $this->activity->activity_type === Activity::ACTIVITY_TYPE_WRITING;
    }
    
    /**
     * Get media type for display.
     */
    public function getMediaTypeProperty()
    {
        if (!$this->hasMedia) {
            return null;
        }
        
        return $this->activity->media_type === Activity::MEDIA_TYPE_AUDIO ? 'audio' : 'image';
    }    
    
    public function render()
    {
        return view('livewire.mobile.teacher-activity-review');
    }
}
