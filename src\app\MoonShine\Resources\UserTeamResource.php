<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserTeam;
use App\Models\User;
use App\Models\Team;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('user-group')]
class UserTeamResource extends BaseResource
{
    protected string $model = UserTeam::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'team'];

    public function getTitle(): string
    {
        return __('admin.user_teams');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.team'),
                'team',
                formatted: fn(Team $team) => $team->name,
                resource: TeamResource::class
            )
                ->sortable(),

            Text::make(__('admin.membership_role'), 'membership_role')
                ->sortable(),

            Text::make(__('admin.team_status'), 'team.active')
                ->badge(fn($value) => $value ? 'success' : 'secondary')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class
                    )
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.team'),
                        'team',
                        formatted: fn(Team $team) => $team->name . ' (' . ($team->active ? __('admin.active') : __('admin.inactive')) . ')',
                        resource: TeamResource::class
                    )
                        ->required()
                        ->searchable(),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.team'),
                'team',
                formatted: fn(Team $team) => $team->name,
                resource: TeamResource::class
            ),

            Text::make(__('admin.membership_role'), 'membership_role'),
            Text::make(__('admin.team_status'), 'team.active'),
            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'user_id' => ['required', 'exists:users,id'],
            'team_id' => ['required', 'exists:teams,id'],
            ...parent::getCommonRules($item),
        ];

        // Add custom validation to prevent duplicate team memberships
        $rules['team_id'][] = function ($attribute, $value, $fail) {
            $userId = request('user_id');
            if ($userId && $value) {
                $exists = UserTeam::where('user_id', $userId)
                    ->where('team_id', $value)
                    ->exists();
                
                if ($exists) {
                    $user = User::find($userId);
                    $team = Team::find($value);
                    $fail(__('admin.user_already_in_team', [
                        'user' => $user->name,
                        'team' => $team->name
                    ]));
                }
            }
        };

        return $rules;
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'team.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['team.name' => 'asc', 'user.name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all team memberships
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see team memberships for users in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            });
        }

        // Teachers can see team memberships for users in their assigned classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            });
        }

        // Students can see their own team memberships
        if ($user->isStudent()) {
            return $builder->where('user_id', $user->id);
        }

        // Default: no access
        return $builder->where('id', 0);
    }
}
