<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Book;
use App\Models\BookWord;
use App\MoonShine\Resources\PanelBookResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use MoonShine\UI\Fields\{Number, Switcher, Text, Textarea};

/**
 * @extends BaseResource<BookWord>
 */
#[Icon('book-open')]
class PanelBookWordResource extends BookWordResource
{
    use WithRolePermissions;
    protected bool $editInModal = true;
    protected bool $detailInModal = true;
    
    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.books'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            )
                ->sortable(),

            Text::make(__('admin.word'), 'word')
                ->sortable(),

            Text::make(__('admin.definition'), 'definition'),

            Text::make(__('admin.synonym'), 'synonym')
                ->badge('blue'),

            Text::make(__('admin.antonym'), 'antonym')
                ->badge('orange'),

            Switcher::make(__('admin.is_active'), 'is_active')
                ->sortable(),
        ];
    }

     protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.books'),
                    'book',
                    formatted: fn(Book $book) => html_entity_decode($book->name),
                    resource: PanelBookResource::class
                )
                    ->required()
                    ->placeholder(__('admin.select_book')),

                Flex::make([
                    Text::make(__('admin.word'), 'word')
                        ->required()
                        ->placeholder(__('admin.enter_word')),

                    Number::make(__('admin.page_reference'), 'page_reference')
                        ->min(1)
                        ->placeholder(__('admin.enter_page_reference')),
                ]),

                Textarea::make(__('admin.definition'), 'definition')
                    ->placeholder(__('admin.enter_definition')),

                Flex::make([
                    Text::make(__('admin.synonym'), 'synonym')
                        ->placeholder(__('admin.enter_synonym')),

                    Text::make(__('admin.antonym'), 'antonym')
                        ->placeholder(__('admin.enter_antonym')),
                ]),

                Switcher::make(__('admin.is_active'), 'is_active')
                    ->default(true),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.word'), 'word'),
            BelongsTo::make(
                __('admin.books'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            ),
            Text::make(__('admin.definition'), 'definition'),
            Text::make(__('admin.synonym'), 'synonym')
                ->badge('blue'),
            Text::make(__('admin.antonym'), 'antonym')
                ->badge('orange'),
            Number::make(__('admin.page_reference'), 'page_reference')
                ->badge('gray'),
            Switcher::make(__('admin.is_active'), 'is_active')
                ->disabled(),
        ];
    }
 
    protected function filters(): iterable
    {
        return [
        ];
    }
}
