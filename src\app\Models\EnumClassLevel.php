<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EnumClassLevel extends BaseModel
{
    use HasFactory;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get school classes that have this level.
     */
    public function schoolClasses(): HasMany
    {
        return $this->hasMany(SchoolClass::class, 'class_level_id');
    }
}
