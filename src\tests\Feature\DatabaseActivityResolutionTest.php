<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Activity;
use App\Models\ClassActivity;
use App\Models\SchoolClass;
use App\Models\UserClass;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DatabaseActivityResolutionTest extends TestCase
{
    use RefreshDatabase;

    public function test_activity_resolution_without_class_returns_original_values()
    {
        // Create a user without a class
        $user = User::factory()->create();
        
        // Create an activity
        $activity = Activity::factory()->create([
            'required' => true,
            'points' => 100,
            'question_count' => 10,
            'allowed_tries' => 3,
            'need_approval' => true,
        ]);

        // Get resolved activity for user without class
        $resolvedActivity = Activity::resolvedForUser($user)->find($activity->id);

        // Should return original activity values
        $this->assertEquals(true, $resolvedActivity->required);
        $this->assertEquals(100, $resolvedActivity->points);
        $this->assertEquals(10, $resolvedActivity->question_count);
        $this->assertEquals(3, $resolvedActivity->allowed_tries);
        $this->assertEquals(true, $resolvedActivity->need_approval);
    }

    public function test_activity_resolution_with_class_overrides_returns_class_specific_values()
    {
        // Create a school class
        $schoolClass = SchoolClass::factory()->create();
        
        // Create a user and assign to class
        $user = User::factory()->create();
        UserClass::create([
            'user_id' => $user->id,
            'class_id' => $schoolClass->id,
            'school_id' => $schoolClass->school_id,
            'active' => true,
            'default' => true,
        ]);
        
        // Create an activity
        $activity = Activity::factory()->create([
            'required' => true,
            'points' => 100,
            'question_count' => 10,
            'allowed_tries' => 3,
            'need_approval' => true,
        ]);

        // Create class-specific overrides
        ClassActivity::create([
            'class_id' => $schoolClass->id,
            'activity_id' => $activity->id,
            'required' => false,
            'points' => 200,
            'question_count' => 5,
            'allowed_tries' => 5,
            'need_approval' => false,
        ]);

        // Get resolved activity for user with class
        $resolvedActivity = Activity::resolvedForUser($user)->find($activity->id);

        // Should return class-specific values
        $this->assertEquals(false, $resolvedActivity->required);
        $this->assertEquals(200, $resolvedActivity->points);
        $this->assertEquals(5, $resolvedActivity->question_count);
        $this->assertEquals(5, $resolvedActivity->allowed_tries);
        $this->assertEquals(false, $resolvedActivity->need_approval);
    }

    public function test_activity_resolution_with_partial_class_overrides()
    {
        // Create a school class
        $schoolClass = SchoolClass::factory()->create();
        
        // Create a user and assign to class
        $user = User::factory()->create();
        UserClass::create([
            'user_id' => $user->id,
            'class_id' => $schoolClass->id,
            'school_id' => $schoolClass->school_id,
            'active' => true,
            'default' => true,
        ]);
        
        // Create an activity
        $activity = Activity::factory()->create([
            'required' => true,
            'points' => 100,
            'question_count' => 10,
            'allowed_tries' => 3,
            'need_approval' => true,
        ]);

        // Create partial class-specific overrides (only some fields)
        ClassActivity::create([
            'class_id' => $schoolClass->id,
            'activity_id' => $activity->id,
            'points' => 150, // Override only points
            'required' => false, // Override only required
            // Other fields remain null, should use activity defaults
        ]);

        // Get resolved activity for user with class
        $resolvedActivity = Activity::resolvedForUser($user)->find($activity->id);

        // Should return mix of class and activity values
        $this->assertEquals(false, $resolvedActivity->required); // From class
        $this->assertEquals(150, $resolvedActivity->points); // From class
        $this->assertEquals(10, $resolvedActivity->question_count); // From activity
        $this->assertEquals(3, $resolvedActivity->allowed_tries); // From activity
        $this->assertEquals(true, $resolvedActivity->need_approval); // From activity
    }

    public function test_activity_resolution_with_multiple_activities()
    {
        // Create a school class
        $schoolClass = SchoolClass::factory()->create();
        
        // Create a user and assign to class
        $user = User::factory()->create();
        UserClass::create([
            'user_id' => $user->id,
            'class_id' => $schoolClass->id,
            'school_id' => $schoolClass->school_id,
            'active' => true,
            'default' => true,
        ]);
        
        // Create multiple activities
        $activity1 = Activity::factory()->create(['points' => 100]);
        $activity2 = Activity::factory()->create(['points' => 200]);
        
        // Create class overrides for only one activity
        ClassActivity::create([
            'class_id' => $schoolClass->id,
            'activity_id' => $activity1->id,
            'points' => 300,
        ]);

        // Get all resolved activities
        $resolvedActivities = Activity::resolvedForUser($user)->get();

        $resolved1 = $resolvedActivities->where('id', $activity1->id)->first();
        $resolved2 = $resolvedActivities->where('id', $activity2->id)->first();

        // Activity 1 should have class override
        $this->assertEquals(300, $resolved1->points);
        
        // Activity 2 should have original value
        $this->assertEquals(200, $resolved2->points);
    }
}
