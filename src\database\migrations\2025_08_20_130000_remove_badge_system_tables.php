<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This migration removes all badge-related tables as part of the transition
     * to the new comprehensive reward system.
     */
    public function up(): void
    {
        // Remove badge-related tables in correct order (respecting foreign key constraints)
        
        // 1. Remove pivot/junction tables first
        Schema::dropIfExists('badge_rules');
        
        // 2. Remove award tracking tables
        Schema::dropIfExists('user_badges');
        Schema::dropIfExists('team_badges');
        
        // 3. Remove core badge tables
        Schema::dropIfExists('badges');
        Schema::dropIfExists('enum_badge_rule_types');
    }

    /**
     * Reverse the migrations.
     * 
     * Note: This reverse migration recreates the basic table structure
     * but does not restore data. This is intentional as the badge system
     * is being permanently replaced by the reward system.
     */
    public function down(): void
    {
        // Recreate enum_badge_rule_types table
        Schema::create('enum_badge_rule_types', function (Blueprint $table) {
            $table->id();
            $table->integer('nr')->unique();
            $table->string('name');
            $table->index('nr');
        });

        // Recreate badges table
        Schema::create('badges', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->boolean('manual')->default(false);
            $table->boolean('active')->default(true);
            $table->index('name');
            $table->index('manual');
            $table->index('active');
            $table->index(['active', 'manual']);
        });

        // Recreate badge_rules table
        Schema::create('badge_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rule_type_id')->constrained('enum_badge_rule_types')->onDelete('cascade');
            $table->integer('rule_value');
            $table->foreignId('category_id')->nullable()->constrained('categories')->onDelete('cascade');
            $table->foreignId('badge_id')->constrained('badges')->onDelete('cascade');
            $table->boolean('active')->default(true);
            $table->index('rule_type_id');
            $table->index('badge_id');
            $table->index('category_id');
            $table->index('active');
            $table->index(['badge_id', 'active']);
            $table->index(['rule_type_id', 'active']);
        });

        // Recreate user_badges table
        Schema::create('user_badges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('badge_id')->constrained('badges')->onDelete('cascade');
            $table->timestamp('awarded_at')->useCurrent();
            $table->foreignId('awarded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('reading_log_id')->nullable()->constrained('user_reading_logs')->onDelete('cascade');
            $table->unique(['user_id', 'badge_id']);
            $table->index('user_id');
            $table->index('badge_id');
            $table->index('awarded_at');
            $table->index('awarded_by');
            $table->index('reading_log_id');
            $table->index(['user_id', 'awarded_at']);
        });

        // Recreate team_badges table
        Schema::create('team_badges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->onDelete('cascade');
            $table->foreignId('badge_id')->constrained('badges')->onDelete('cascade');
            $table->timestamp('awarded_at')->useCurrent();
            $table->foreignId('awarded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('reading_log_id')->nullable()->constrained('user_reading_logs')->onDelete('cascade');
            $table->unique(['team_id', 'badge_id']);
            $table->index('team_id');
            $table->index('badge_id');
            $table->index('awarded_at');
            $table->index('awarded_by');
            $table->index('reading_log_id');
            $table->index(['team_id', 'awarded_at']);
        });
    }
};
