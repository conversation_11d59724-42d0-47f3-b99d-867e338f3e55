<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Book;
use App\Models\ClassBook;
use App\Models\UserBook;
use App\Models\SchoolClass;
use App\Models\School;
use App\Models\UserClass;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

class ClassBooksMatrixExportTest extends TestCase
{
    use RefreshDatabase;

    protected $teacher;
    protected $student1;
    protected $student2;
    protected $school;
    protected $schoolClass;
    protected $book1;
    protected $book2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'teacher', 'guard_name' => 'moonshine']);
        Role::create(['name' => 'student', 'guard_name' => 'moonshine']);

        // Create school and class
        $this->school = School::factory()->create(['name' => 'Test School']);
        $this->schoolClass = SchoolClass::factory()->create([
            'name' => 'Test Class',
            'school_id' => $this->school->id
        ]);

        // Create teacher
        $this->teacher = User::factory()->create(['name' => 'Test Teacher']);
        $this->teacher->assignRole('teacher');

        // Create teacher's class assignment
        UserClass::create([
            'user_id' => $this->teacher->id,
            'class_id' => $this->schoolClass->id,
            'school_id' => $this->school->id,
            'active' => true,
            'default' => true,
        ]);

        // Create students
        $this->student1 = User::factory()->create(['name' => 'Student One']);
        $this->student1->assignRole('student');
        UserClass::create([
            'user_id' => $this->student1->id,
            'class_id' => $this->schoolClass->id,
            'school_id' => $this->school->id,
            'active' => true,
            'default' => true,
        ]);

        $this->student2 = User::factory()->create(['name' => 'Student Two']);
        $this->student2->assignRole('student');
        UserClass::create([
            'user_id' => $this->student2->id,
            'class_id' => $this->schoolClass->id,
            'school_id' => $this->school->id,
            'active' => true,
            'default' => true,
        ]);

        // Create books
        $this->book1 = Book::factory()->create(['name' => 'Test Book 1']);
        $this->book2 = Book::factory()->create(['name' => 'Test Book 2']);

        // Add books to class
        ClassBook::create([
            'class_id' => $this->schoolClass->id,
            'book_id' => $this->book1->id,
        ]);
        ClassBook::create([
            'class_id' => $this->schoolClass->id,
            'book_id' => $this->book2->id,
        ]);

        // Create reading records - student1 completed book1, student2 completed book2
        UserBook::create([
            'user_id' => $this->student1->id,
            'book_id' => $this->book1->id,
            'start_date' => now()->subDays(10),
            'end_date' => now()->subDays(5), // Completed
        ]);

        UserBook::create([
            'user_id' => $this->student2->id,
            'book_id' => $this->book2->id,
            'start_date' => now()->subDays(8),
            'end_date' => now()->subDays(3), // Completed
        ]);

        // Create in-progress reading record
        UserBook::create([
            'user_id' => $this->student1->id,
            'book_id' => $this->book2->id,
            'start_date' => now()->subDays(5),
            'end_date' => null, // In progress
        ]);
    }

    /** @test */
    public function teacher_can_export_class_books_matrix()
    {
        $this->actingAs($this->teacher, 'moonshine');

        $resource = new \App\MoonShine\Resources\PanelClassBookResource();
        $request = new \MoonShine\Laravel\MoonShineRequest();

        $response = $resource->exportClassBooksMatrix($request);

        $this->assertInstanceOf(\MoonShine\Laravel\Http\Responses\MoonShineJsonResponse::class, $response);
        
        // Check that the response contains success toast
        $responseData = $response->getData();
        $this->assertEquals('success', $responseData['toast']['type']);
        $this->assertStringContainsString('Export completed successfully', $responseData['toast']['message']);
    }

    /** @test */
    public function unauthorized_user_cannot_export_matrix()
    {
        $unauthorizedUser = User::factory()->create();
        $this->actingAs($unauthorizedUser, 'moonshine');

        $resource = new \App\MoonShine\Resources\PanelClassBookResource();
        $request = new \MoonShine\Laravel\MoonShineRequest();

        $response = $resource->exportClassBooksMatrix($request);

        $responseData = $response->getData();
        $this->assertEquals('error', $responseData['toast']['type']);
        $this->assertStringContainsString('unauthorized', strtolower($responseData['toast']['message']));
    }

    /** @test */
    public function export_fails_when_teacher_has_no_default_class()
    {
        // Remove teacher's default class
        UserClass::where('user_id', $this->teacher->id)->update(['default' => false]);

        $this->actingAs($this->teacher, 'moonshine');

        $resource = new \App\MoonShine\Resources\PanelClassBookResource();
        $request = new \MoonShine\Laravel\MoonShineRequest();

        $response = $resource->exportClassBooksMatrix($request);

        $responseData = $response->getData();
        $this->assertEquals('error', $responseData['toast']['type']);
    }

    protected function tearDown(): void
    {
        // Clean up any created files
        Storage::disk('public')->deleteDirectory('exports');
        parent::tearDown();
    }
}
