SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;


CREATE TABLE `activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL,
  `activity_type` int(11) NOT NULL DEFAULT 1 COMMENT '1-Writing, 2-Rating, 3-Media, 4-Physical, 5-Game',
  `media_type` int(11) DEFAULT NULL COMMENT '1-image, 2-audio',
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `min_word_count` int(11) DEFAULT NULL,
  `min_rating` int(11) DEFAULT NULL,
  `max_rating` int(11) DEFAULT NULL,
  `media_url` varchar(255) DEFAULT NULL COMMENT 'Playable game URL or media content',
  `points` int(11) NOT NULL DEFAULT 0,
  `need_approval` tinyint(1) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `activities` (`id`, `category_id`, `activity_type`, `media_type`, `name`, `description`, `min_word_count`, `min_rating`, `max_rating`, `media_url`, `points`, `need_approval`, `active`) VALUES
(1, 1, 1, NULL, 'Kitap Yorumu Yaz', 'Bu kitap hakkındaki düşüncelerin neler? Bize anlatabilir misin?', 10, NULL, NULL, NULL, 10, 0, 1),
(2, 2, 2, NULL, 'Kitaba puan ver', NULL, NULL, 1, 10, NULL, 3, 0, 1),
(3, 3, 3, 1, 'Sen olsaydın bu kitap için nasıl bir kapak tasarlardın? Çiz, fotoğrafını çek gönder', NULL, NULL, NULL, NULL, NULL, 10, 1, 1);

CREATE TABLE `activity_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `activity_categories` (`id`, `name`, `active`) VALUES
(1, 'Fikir ve Yorumlar', 1),
(2, 'Puanlamalar', 1),
(3, 'Sanatsal Çalışmalar', 1),
(4, 'Fiziki Aktiviteler', 1),
(5, 'Kelime Oyunları', 1);

CREATE TABLE `activity_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `log_name` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `subject_type` varchar(255) DEFAULT NULL,
  `event` varchar(255) DEFAULT NULL,
  `subject_id` bigint(20) UNSIGNED DEFAULT NULL,
  `causer_type` varchar(255) DEFAULT NULL,
  `causer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`properties`)),
  `batch_uuid` char(36) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `authors` (`id`, `created_by`, `name`) VALUES
(1, NULL, 'Mustafa Orakçı'),
(2, NULL, 'Mustafa Ecevit');

CREATE TABLE `avatars` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `base_image` varchar(255) NOT NULL,
  `happy_image` varchar(255) NOT NULL,
  `sad_image` varchar(255) NOT NULL,
  `sleepy_image` varchar(255) NOT NULL,
  `required_points` int(11) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `avatars` (`id`, `name`, `description`, `base_image`, `happy_image`, `sad_image`, `sleepy_image`, `required_points`, `active`) VALUES
(1, 'Pembe Robot', 'Pembe Robot', 'azBpgaQpXwGEMWZdC7nyxEeX9DuoW1hD5DhJKcLf.png', 'zvEtCV4OhP71wzstyeQxXemxOyAyAba4vZsisIVg.png', 'g8o4fnIQioQ49FY48VxZnu00vs2t6rFP9WORqEW5.png', 'Qzr6gINt2sIvdIlT5MVeWFVC4zC6dJ1bv42a4jDi.png', 0, 1),
(2, 'Erkek çocuk', 'Erkek çocuk', 'G0BgZ6x8lW0z8YDHFnhiJIbBzMwlzmYzzwJrAr7l.png', '2VJRNTNrKJvH3Y0enaAgVPHF1HCVYy1JKCROKewD.png', 'IRodA38uh3xqlz9huGJuKMTU8xrD6jHOzE2WB5bZ.png', 'N6FD80wCs2wbSVcO8fpftcByuVcXawJHlEL33Tas.png', 0, 1),
(3, 'Aslancık', 'Aslancık', '93spVyVBI3AxJdY2Z5uVPtweN7uIzRLPQ3NfiSLg.png', '1Q5DMyOp7vg2DwyF46nydGTEU49TAPMLZKk5ZwXE.png', '5GZ7o5xLmdFMtl9kLsANYHXkwYDYLBniZJkP0Di7.png', 'ddr67gpBgA5ADfgpTYVaRQTumX8dvgBuzpSuCVXg.png', 20, 1);

CREATE TABLE `books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `isbn` varchar(255) NOT NULL,
  `publisher_id` bigint(20) UNSIGNED NOT NULL,
  `book_type_id` bigint(20) UNSIGNED NOT NULL,
  `page_count` int(11) NOT NULL,
  `year_of_publish` year(4) NOT NULL,
  `cover_image` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `books` (`id`, `created_by`, `name`, `isbn`, `publisher_id`, `book_type_id`, `page_count`, `year_of_publish`, `cover_image`) VALUES
(1, NULL, 'Dünyadaki Son Robot - Haşmet 2.0', '9786255978189', 1, 3, 64, '2025', 'axtBxIa4OTrxoraX2rxPEggRfNGBVHrKOx7gwDjc.jpg'),
(2, NULL, 'Kikurcan ve Hayalet Tayfa 3 - Sisler Ormanı', '9786257841412', 2, 2, 88, '2021', 'vCy8slp0SFGTOIkcPQ4dB8EXd4UNaaJOZ90VRT69.jpg'),
(3, NULL, 'Hacı Bektaşi Veli&#039;yle Bir Gün', '9786050820966', 1, 2, 48, '2015', 'oiDqpNqVJXlzBKDRBBML38yoKZSBcpKGLdUfLfO8.jpg');

CREATE TABLE `book_authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `author_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `book_authors` (`id`, `book_id`, `author_id`) VALUES
(1, 1, 1),
(2, 2, 2),
(3, 3, 1);

CREATE TABLE `book_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `book_categories` (`id`, `book_id`, `category_id`) VALUES
(1, 1, 2),
(2, 2, 2),
(3, 3, 1);

CREATE TABLE `book_questions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `question_text` text NOT NULL COMMENT 'The question content',
  `question_image_url` varchar(255) DEFAULT NULL COMMENT 'Optional image URL',
  `question_audio_url` varchar(255) DEFAULT NULL COMMENT 'Optional audio URL',
  `question_video_url` varchar(255) DEFAULT NULL COMMENT 'Optional video URL',
  `correct_answer` varchar(255) NOT NULL COMMENT 'The correct answer text',
  `incorrect_answer_1` varchar(255) DEFAULT NULL COMMENT 'First incorrect option',
  `incorrect_answer_2` varchar(255) DEFAULT NULL COMMENT 'Second incorrect option',
  `incorrect_answer_3` varchar(255) DEFAULT NULL COMMENT 'Third incorrect option',
  `incorrect_answer_4` varchar(255) DEFAULT NULL COMMENT 'Fourth incorrect option',
  `incorrect_answer_5` varchar(255) DEFAULT NULL COMMENT 'Fifth incorrect option',
  `page_start` int(11) DEFAULT NULL COMMENT 'Starting page reference',
  `page_end` int(11) DEFAULT NULL COMMENT 'Ending page reference',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether question is available for use'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `thumbnail` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `book_types` (`id`, `name`, `description`, `thumbnail`) VALUES
(1, 'Çok resimli az yazılı', 'Çok resimli az yazılı', '3r1wZxOsn8evJz2fg1PA0g8pYmLvbZj6j4cIao9x.png'),
(2, 'Resim ve yazı dengeli', 'Resim ve yazı dengeli', 'PCOg4iexj4iEXQ9aO1Qc0whz9a8yYvywg7loojnr.png'),
(3, 'Çok yazılı az resimli', 'Çok yazılı az resimli', '6efgIr4dWkhyekOn3KVjMhnmB1A2TM9XYk157Lri.png'),
(4, 'Tamamen yazılı büyük puntolu', 'Tamamen yazılı büyük puntolu', 'ktVb9L9g3J66lzrdTdZx8lyrFxhuArEP635TX7wH.png'),
(5, 'Tamamen yazılı küçük puntolu', 'Tamamen yazılı küçük puntolu', 'g9qOtekn8aUW7CSUPM8s9Xnryp8vBNXrwcvfHcSB.png');

CREATE TABLE `book_words` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `word` varchar(255) NOT NULL COMMENT 'The vocabulary word',
  `definition` text DEFAULT NULL COMMENT 'Word definition',
  `synonym` varchar(255) DEFAULT NULL COMMENT 'Synonym of the word',
  `antonym` varchar(255) DEFAULT NULL COMMENT 'Antonym of the word',
  `page_reference` int(11) DEFAULT NULL COMMENT 'Page where word appears',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `categories` (`id`, `name`) VALUES
(1, 'Hikaye'),
(2, 'Roman');

CREATE TABLE `challenges` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL COMMENT 'Challenge banner/flyer image path',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `prize` text DEFAULT NULL COMMENT 'Challenge reward description',
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `challenges` (`id`, `created_by`, `name`, `description`, `image`, `start_date`, `end_date`, `prize`, `active`) VALUES
(2, NULL, 'Yaz yarışması', '', NULL, '2025-08-08', '2025-09-07', '', 1),
(3, 5, 'test', 'test açıklama', 'challenges/TknNr7pioqkX2zkzbJnGSCEv21UnTWbeC4762B0L.jpg', '2025-08-28', '2025-09-10', 'ödül', 1),
(4, 5, 'test yarışma', 'aaaa', 'challenges/gnvlvtOnanl7V8v4k8Vj5Xbi9QNEqfq4Tt7e6Bmu.jpg', '2025-08-29', '2025-09-11', 'aaaa', 0),
(5, 5, 'test2', 'aaaa', 'challenges/2m3sfRBTOiuTh3ve2OBLOjYN3eg41mqLv7OJ822x.png', '2025-08-29', '2025-09-11', 'ğaei', 0);

CREATE TABLE `challenge_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `school_class_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `challenge_classes` (`id`, `challenge_id`, `school_class_id`) VALUES
(2, 2, 1);

CREATE TABLE `challenge_schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `challenge_schools` (`id`, `challenge_id`, `school_id`) VALUES
(2, 2, 1);

CREATE TABLE `challenge_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `reward_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `challenge_tasks` (`id`, `created_by`, `challenge_id`, `task_id`, `start_date`, `end_date`, `reward_id`) VALUES
(3, NULL, 2, 2, '2025-08-08', '2025-09-07', NULL);

CREATE TABLE `challenge_teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `challenge_teams` (`id`, `challenge_id`, `team_id`) VALUES
(2, 2, 1);

CREATE TABLE `class_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_class_levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `enum_class_levels` (`id`, `name`) VALUES
(1, '1.Sınıf'),
(2, '2.Sınıf'),
(3, '3.Sınıf'),
(4, '4.Sınıf'),
(5, '5.Sınıf');

CREATE TABLE `enum_school_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `enum_school_types` (`id`, `name`) VALUES
(1, 'İlkokul'),
(2, 'Ortaokul');

CREATE TABLE `enum_task_cycles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `enum_task_cycles` (`id`, `nr`, `name`) VALUES
(1, 1, 'Total'),
(2, 2, 'Daily'),
(3, 3, 'Weekly'),
(4, 4, 'Monthly');

CREATE TABLE `enum_task_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `enum_task_types` (`id`, `nr`, `name`) VALUES
(1, 1, 'Sayfa Oku'),
(2, 2, 'Kitap Oku'),
(3, 3, 'Dakika Oku'),
(4, 4, 'Gün Oku'),
(5, 5, 'Gün Arka Arkaya Oku'),
(6, 6, 'Okuma Puanı Kazan'),
(7, 7, 'Aktivite Puanı Kazan'),
(8, 8, 'Bir Kitap Aktivitesi Tamamla'),
(9, 9, 'Kitap Listesini Tamamla'),
(10, 10, 'Evet/Hayır Görevi');

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `goals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `motto` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `goals` (`id`, `created_by`, `name`, `description`, `motto`, `active`) VALUES
(1, NULL, '100 sayfa kitap oku', NULL, NULL, 1),
(2, NULL, 'Aylık 3 adet hikaye oku ve kitaba yorum yaz', NULL, '3 hikaye okuyan kazanır', 1),
(8, 1, 'Aylık 3 adet hikaye oku ve kitaba yorum yaz', NULL, '3 hikaye okuyan kazanır', 1),
(9, NULL, 'Yeni hedef', NULL, NULL, 1);

CREATE TABLE `goal_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `goal_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `goal_tasks` (`id`, `created_by`, `goal_id`, `task_id`, `start_date`, `end_date`) VALUES
(1, NULL, 1, 1, '2025-08-08', '2025-09-07'),
(2, NULL, 2, 2, '2025-08-08', '2025-09-07'),
(3, NULL, 2, 3, '2025-08-08', '2025-09-07'),
(4, NULL, 9, 12, '2025-09-02', '2025-10-02');

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2020_10_04_115514_create_moonshine_roles_table', 1),
(5, '2020_10_05_173148_create_moonshine_tables', 1),
(6, '2024_12_19_000001_create_user_agreements_table', 1),
(8, '2025_01_04_000000_create_enum_class_levels_table', 1),
(9, '2025_01_04_000010_create_enum_school_types_table', 1),
(10, '2025_01_04_000020_create_schools_table', 1),
(11, '2025_01_04_000030_create_school_classes_table', 1),
(12, '2025_01_04_120000_create_user_schools_table', 1),
(13, '2025_01_04_120001_create_user_classes_table', 1),
(15, '2025_06_04_090749_create_publishers_table', 1),
(16, '2025_06_04_090750_create_authors_table', 1),
(17, '2025_06_04_090751_create_book_types_table', 1),
(18, '2025_06_04_090751_create_books_table', 1),
(19, '2025_06_04_090751_create_page_points_table', 1),
(20, '2025_06_04_090752_create_book_authors_table', 1),
(21, '2025_06_04_090752_create_categories_table', 1),
(22, '2025_06_04_090753_create_book_categories_table', 1),
(23, '2025_06_04_090754_create_class_books_table', 1),
(24, '2025_06_04_090755_create_user_reading_logs_table', 1),
(25, '2025_06_04_090756_create_user_points_table', 1),
(26, '2025_06_04_090757_create_activity_categories_table', 1),
(27, '2025_06_04_090758_create_activities_table', 1),
(28, '2025_06_04_090759_create_user_activities_table', 1),
(29, '2025_06_04_090760_create_user_activity_reviews_table', 1),
(30, '2025_06_04_090762_create_user_books_table', 1),
(31, '2025_06_04_091008_create_book_questions_table', 1),
(32, '2025_06_04_091009_create_book_words_table', 1),
(33, '2025_06_04_091804_create_avatars_table', 1),
(34, '2025_06_04_190764_create_user_avatars_table', 1),
(35, '2025_06_04_190765_create_badges_table', 1),
(36, '2025_06_04_190766_create_enum_badge_rule_types_table', 1),
(37, '2025_06_04_190767_create_badge_rules_table', 1),
(38, '2025_06_04_190768_create_user_badges_table', 1),
(39, '2025_06_04_190770_create_teams_table', 1),
(40, '2025_06_04_190771_create_user_teams_table', 1),
(41, '2025_06_04_190772_create_team_badges_table', 1),
(42, '2025_06_04_190790_create_enum_task_types_table', 1),
(43, '2025_06_04_190791_create_enum_task_cycles_table', 1),
(44, '2025_06_04_190792_create_tasks_table', 1),
(45, '2025_06_04_190793_create_task_books_table', 1),
(46, '2025_06_04_190794_create_task_book_categories_table', 1),
(47, '2025_07_29_124912_create_activity_log_table', 1),
(48, '2025_07_29_124913_add_event_column_to_activity_log_table', 1),
(49, '2025_07_29_124914_add_batch_uuid_column_to_activity_log_table', 1),
(50, 'add_role_priority_to_roles_table', 1),
(51, 'create_or_supplement_users_table', 1),
(53, '2025_03_17_073436_create_notifications_table', 2),
(54, '2025_01_01_000010_create_permission_tables', 3),
(55, '2025_06_04_090800_create_goals_table', 4),
(56, '2025_06_04_090801_create_goal_tasks_table', 4),
(57, '2025_06_04_090802_create_user_goals_table', 5),
(58, '2025_06_04_090803_create_user_goal_tasks_table', 5),
(59, '2025_06_04_090804_modify_existing_tables_for_goals', 5),
(60, '2025_06_04_190800_create_goals_table', 6),
(61, '2025_06_04_190801_create_goal_tasks_table', 6),
(62, '2025_06_04_190802_create_user_goals_table', 6),
(63, '2025_06_04_190803_create_user_goal_tasks_table', 6),
(64, '2025_08_08_120000_create_challenges_table', 6),
(65, '2025_08_08_120001_create_challenge_tasks_table', 6),
(66, '2025_08_08_120002_create_challenge_classes_table', 6),
(67, '2025_08_08_120003_create_challenge_schools_table', 6),
(68, '2025_08_08_120004_create_challenge_teams_table', 6),
(69, '2025_08_08_120005_create_user_challenge_tasks_table', 6),
(70, '2025_08_08_120006_add_challenge_task_id_to_user_books_table', 6),
(71, '2025_08_08_120007_add_challenge_task_id_to_user_reading_logs_table', 6),
(72, '2025_08_08_120008_add_challenge_task_id_to_user_activities_table', 6),
(73, '2025_08_19_163138_add_media_type_to_activities_table', 7),
(74, '2025_08_20_120000_create_rewards_table', 8),
(75, '2025_08_20_120001_create_reward_tasks_table', 8),
(76, '2025_08_20_120002_create_user_rewards_table', 8),
(77, '2025_08_20_120003_create_team_rewards_table', 8),
(78, '2025_08_20_120004_add_reward_id_to_challenge_tasks_table', 8),
(79, '2025_08_20_130000_remove_badge_system_tables', 8),
(80, '2025_08_23_140000_add_default_field_to_user_schools_table', 9),
(81, '2025_08_23_140001_add_default_field_to_user_classes_table', 9),
(82, '2025_08_23_140002_set_initial_default_schools_and_classes', 9),
(83, '2025_08_25_000000_add_permission_fields_to_tables', 10);

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(2, 'App\\Models\\User', 4),
(3, 'App\\Models\\User', 5),
(3, 'App\\Models\\User', 6),
(3, 'App\\Models\\User', 7),
(3, 'App\\Models\\User', 8),
(4, 'App\\Models\\User', 9),
(4, 'App\\Models\\User', 10),
(4, 'App\\Models\\User', 11),
(4, 'App\\Models\\User', 12),
(4, 'App\\Models\\User', 13),
(4, 'App\\Models\\User', 14),
(4, 'App\\Models\\User', 15),
(4, 'App\\Models\\User', 16),
(4, 'App\\Models\\User', 17),
(4, 'App\\Models\\User', 18),
(4, 'App\\Models\\User', 19),
(4, 'App\\Models\\User', 20),
(4, 'App\\Models\\User', 21),
(4, 'App\\Models\\User', 22),
(4, 'App\\Models\\User', 23),
(4, 'App\\Models\\User', 24),
(4, 'App\\Models\\User', 25),
(4, 'App\\Models\\User', 26),
(4, 'App\\Models\\User', 27),
(4, 'App\\Models\\User', 28),
(4, 'App\\Models\\User', 30);

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `page_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_type_id` bigint(20) UNSIGNED NOT NULL,
  `class_level_id` bigint(20) UNSIGNED NOT NULL,
  `point` decimal(8,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `page_points` (`id`, `book_type_id`, `class_level_id`, `point`) VALUES
(1, 1, 1, 1.00),
(2, 1, 2, 0.75),
(3, 1, 3, 0.50),
(4, 1, 4, 0.25),
(5, 1, 5, 0.10),
(6, 2, 1, 2.00),
(7, 2, 2, 1.75),
(8, 2, 3, 1.50),
(9, 2, 4, 1.00),
(10, 2, 5, 0.75),
(11, 3, 1, 3.00),
(12, 3, 2, 2.50),
(13, 3, 3, 2.00),
(14, 3, 4, 1.50),
(15, 3, 5, 1.00),
(16, 4, 1, 3.00),
(17, 4, 2, 2.50),
(18, 4, 3, 2.00),
(19, 4, 4, 1.50),
(20, 4, 5, 1.25),
(21, 5, 1, 4.00),
(22, 5, 2, 3.50),
(23, 5, 3, 3.00),
(24, 5, 4, 2.50),
(25, 5, 5, 2.00);

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'RoleResource.viewAny', 'moonshine', '2025-08-08 05:37:18', '2025-08-08 05:37:18'),
(2, 'RoleResource.view', 'moonshine', '2025-08-08 05:37:18', '2025-08-08 05:37:18'),
(3, 'RoleResource.create', 'moonshine', '2025-08-08 05:37:18', '2025-08-08 05:37:18'),
(4, 'RoleResource.update', 'moonshine', '2025-08-08 05:37:18', '2025-08-08 05:37:18'),
(5, 'RoleResource.delete', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(6, 'RoleResource.massDelete', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(7, 'RoleResource.restore', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(8, 'RoleResource.forceDelete', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(9, 'PermissionResource.viewAny', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(10, 'PermissionResource.view', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(11, 'PermissionResource.create', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(12, 'PermissionResource.update', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(13, 'PermissionResource.delete', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(14, 'PermissionResource.massDelete', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(15, 'PermissionResource.restore', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(16, 'PermissionResource.forceDelete', 'moonshine', '2025-08-08 05:37:19', '2025-08-08 05:37:19'),
(17, 'EnumSchoolTypeResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(18, 'EnumClassLevelResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(19, 'SchoolResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(20, 'SchoolClassResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(21, 'UserResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(22, 'UserSchoolResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(23, 'UserClassResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(24, 'TeamResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(25, 'UserTeamResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(26, 'BookTypeResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(27, 'AuthorResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(28, 'PublisherResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(29, 'CategoryResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(30, 'BookResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(31, 'ClassBookResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(32, 'BookQuestionResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(33, 'BookWordResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(34, 'UserBookResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(35, 'UserReadingLogResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(36, 'UserPointResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(37, 'ActivityCategoryResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(38, 'ActivityResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(39, 'UserActivityResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(40, 'UserActivityReviewResource.viewAny', 'moonshine', '2025-08-24 12:26:45', '2025-08-24 12:26:45'),
(41, 'AvatarResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(42, 'UserAvatarResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(43, 'RewardResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(44, 'RewardTaskResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(45, 'UserRewardResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(46, 'TeamRewardResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(47, 'EnumTaskTypeResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(48, 'EnumTaskCycleResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(49, 'TaskResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(50, 'GoalResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(51, 'GoalTaskResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(52, 'UserGoalResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(53, 'UserGoalTaskResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(54, 'ChallengeResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(55, 'ChallengeTaskResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(56, 'UserChallengeTaskResource.viewAny', 'moonshine', '2025-08-24 12:26:46', '2025-08-24 12:26:46'),
(57, 'StudentResource.view', 'moonshine', '2025-08-31 10:35:00', '2025-08-31 10:35:00'),
(58, 'StudentResource.viewAny', 'moonshine', '2025-08-31 10:35:14', '2025-08-31 10:35:14'),
(59, 'StudentResource.create', 'moonshine', '2025-08-31 17:11:12', '2025-08-31 17:11:12'),
(60, 'StudentResource.update', 'moonshine', '2025-08-31 17:11:12', '2025-08-31 17:11:12'),
(61, 'StudentResource.delete', 'moonshine', '2025-08-31 17:11:12', '2025-08-31 17:11:12'),
(62, 'StudentResource.massDelete', 'moonshine', '2025-08-31 17:11:12', '2025-08-31 17:11:12'),
(63, 'StudentResource.restore', 'moonshine', '2025-08-31 17:11:12', '2025-08-31 17:11:12'),
(64, 'StudentResource.forceDelete', 'moonshine', '2025-08-31 17:11:12', '2025-08-31 17:11:12');

CREATE TABLE `publishers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `publishers` (`id`, `created_by`, `name`) VALUES
(1, NULL, 'Timaş Çocuk'),
(2, NULL, 'Çamlıca Çocuk Yayınları');

CREATE TABLE `rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reward_type` int(11) NOT NULL DEFAULT 1 COMMENT '1-Badge, 2-Trophy, 3-Card, 4-Item',
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `rewards` (`id`, `created_by`, `reward_type`, `name`, `description`, `image`, `active`) VALUES
(1, NULL, 1, 'İlk Kitabı Bitirdin', 'İlk Kitabı Okumayı Bitirdin', 'sBEyq5YbQdcTwGTlcg96BXjJx1PsbTTMKcXsGnSQ.png', 1),
(2, NULL, 1, '3 Kitap Bitirdin', NULL, 'H3csIYqeZJSn1NUaiOkljLp4UFkxqwcaIf2RQd41.png', 1);

CREATE TABLE `reward_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `reward_tasks` (`id`, `created_by`, `reward_id`, `task_id`) VALUES
(1, NULL, 1, 4),
(2, NULL, 2, 5);

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `role_priority` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`role_priority`)),
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `roles` (`id`, `name`, `guard_name`, `role_priority`, `description`, `created_at`, `updated_at`) VALUES
(1, 'system_admin', 'moonshine', NULL, 'Sistem Yöneticisi', '2025-08-08 05:12:52', '2025-08-08 05:37:10'),
(2, 'school_admin', 'moonshine', NULL, 'Okul Yöneticisi', '2025-08-08 05:12:52', '2025-08-08 05:37:25'),
(3, 'teacher', 'moonshine', NULL, 'Öğretmen', '2025-08-08 05:12:52', '2025-08-08 05:37:34'),
(4, 'student', 'moonshine', NULL, 'Öğrenci', '2025-08-08 05:12:52', '2025-08-08 05:37:43'),
(5, 'parent', 'moonshine', NULL, 'Ebeveyn', '2025-08-10 15:57:15', '2025-08-10 16:05:02'),
(6, 'region_user', 'moonshine', NULL, 'Bölge Kullanıcısı', '2025-08-22 16:37:57', '2025-08-22 16:37:57');

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(57, 3),
(58, 3),
(59, 3),
(60, 3),
(61, 3),
(62, 3),
(63, 3),
(64, 3);

CREATE TABLE `schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `school_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `schools` (`id`, `name`, `school_type_id`, `active`) VALUES
(1, 'Örnek İlkokulu', 1, 1),
(2, 'Örnek Ortaokulu', 2, 1);

CREATE TABLE `school_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `class_level_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `school_classes` (`id`, `created_by`, `name`, `school_id`, `class_level_id`, `active`) VALUES
(1, NULL, '3-A', 1, 3, 1),
(2, NULL, '5-A', 2, 5, 1);

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('nkZF4F0W74xDGD9NuUFxfIBitdVPRe7mdIevFd8i', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0', 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiSzBvWGZ1eVV6Vm5VYnJUckZoa1ZGZUZvMjlxcVAzOHdla290Y29DayI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjY2OiJodHRwOi8vbW9vbi5zaXRlL2FkbWluL3Jlc291cmNlL3VzZXItZ29hbC10YXNrLXJlc291cmNlL2luZGV4LXBhZ2UiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjU2OiJsb2dpbl9tb29uc2hpbmVfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MjM6InBhc3N3b3JkX2hhc2hfbW9vbnNoaW5lIjtzOjYwOiIkMnkkMTIkM0xKRzBDQS52UWkwZWFnelNjSG9RdWxaMnNLSzdaUGY0eUJlczlCbGphVjNIOXRJQXJRRHUiO30=', 1756847101),
('xrerbCOe1gNG5GPzJiiPLQUtJVqStO3ZlgAQsPb9', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0', 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiVWY2cHE1WUpJNGFNc3RhVGxkdjVMRkc3N1RWWll0NUZCa2s4dmlnRiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjU2OiJodHRwOi8vbW9vbi5zaXRlL2FkbWluL3Jlc291cmNlL2dvYWwtcmVzb3VyY2UvaW5kZXgtcGFnZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTY6ImxvZ2luX21vb25zaGluZV81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoyMzoicGFzc3dvcmRfaGFzaF9tb29uc2hpbmUiO3M6NjA6IiQyeSQxMiQzTEpHMENBLnZRaTBlYWd6U2NIb1F1bFoyc0tLN1pQZjR5QmVzOUJsamFWM0g5dElBclFEdSI7fQ==', 1756880187);

CREATE TABLE `tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `task_type_id` bigint(20) UNSIGNED NOT NULL,
  `task_cycle_id` bigint(20) UNSIGNED NOT NULL,
  `task_value` int(11) DEFAULT NULL,
  `activity_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `tasks` (`id`, `created_by`, `name`, `task_type_id`, `task_cycle_id`, `task_value`, `activity_id`, `active`) VALUES
(1, NULL, '100 sayfa kitap oku', 1, 1, 100, NULL, 1),
(2, NULL, 'Aylık 3 Adet Hikaye Oku', 2, 4, 5, NULL, 1),
(3, NULL, 'Haşmet kitabına yorum yaz', 8, 1, 1, 1, 1),
(4, NULL, 'Rozet-1 Kitap Bitir', 2, 1, 1, NULL, 1),
(5, NULL, 'Rozet-3 Kitap Bitir', 2, 1, 3, NULL, 1),
(11, NULL, '5 Kitap Oku', 2, 1, 5, NULL, 1),
(12, NULL, 'test görev', 1, 1, 5, NULL, 1),
(13, NULL, 'eeeee', 1, 1, 4, NULL, 1),
(14, NULL, 'aaaaaaaaa', 2, 1, 5, NULL, 1),
(15, NULL, 'ieaiaeei', 1, 1, 6, NULL, 1),
(16, NULL, 'uieaueiauia', 1, 1, 33, NULL, 1);

CREATE TABLE `task_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `task_books` (`id`, `created_by`, `task_id`, `book_id`) VALUES
(1, NULL, 3, 1);

CREATE TABLE `task_book_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `task_book_categories` (`id`, `created_by`, `task_id`, `category_id`) VALUES
(1, NULL, 2, 1);

CREATE TABLE `teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `leader_user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `teams` (`id`, `created_by`, `name`, `logo`, `leader_user_id`, `active`) VALUES
(1, NULL, 'Aslanlar', NULL, 9, 1);

CREATE TABLE `team_rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `awarded_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `awarded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reading_log_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `team_rewards` (`id`, `created_by`, `team_id`, `reward_id`, `awarded_date`, `awarded_by`, `reading_log_id`, `user_activity_id`) VALUES
(5, NULL, 1, 1, '2025-09-01 07:42:04', NULL, 33, NULL),
(6, NULL, 1, 2, '2025-09-01 07:42:04', NULL, 33, NULL);

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `users` (`id`, `username`, `name`, `title`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`, `avatar`) VALUES
(1, 'admin', 'system_admin', 'System Admin', '<EMAIL>', '2025-08-08 05:16:57', '$2y$12$3LJG0CA.vQi0eagzScHoQulZ2sKK7ZPf4yBes9BljaV3H9tIArQDu', NULL, '2025-08-08 05:15:33', '2025-08-08 05:16:57', NULL),
(4, 'schooladmin', 'Ahmet Yılmaz', 'School Administrator', '<EMAIL>', '2025-08-08 05:16:57', '$2y$12$vkPUcmpjQ3bfho8hl.EUseSRRVFBmT0Vq2IGEk6OV6QpoTBzJ.TDC', NULL, '2025-08-08 05:16:57', '2025-08-08 05:16:57', NULL),
(5, 'teacher1', 'Fatma Demir', 'Turkish Language Teacher', '<EMAIL>', '2025-08-08 05:16:57', '$2y$12$9VEiT6m0zVOoXfJIakACmu3JjSXOeYMVHS4nnIcsn8i/ju.u4x9g2', 'Ikq51IDvbGlejBWYTHupetjfsMZBOw1SGtBI74xYhVFDof7cPoK5v5bcleir', '2025-08-08 05:16:57', '2025-08-08 05:16:57', NULL),
(6, 'teacher2', 'Mehmet Kaya', 'Literature Teacher', '<EMAIL>', '2025-08-08 05:16:58', '$2y$12$MhNGdqsgBN6p1Y3x2Wayb.nuBjhqTr.rAq/6H4sToTn3rYIAP/SGq', NULL, '2025-08-08 05:16:58', '2025-08-08 05:16:58', NULL),
(7, 'teacher3', 'Ayşe Özkan', 'Primary School Teacher', '<EMAIL>', '2025-08-08 05:16:58', '$2y$12$ErZJ5//hwa7..JHQojMfh.2w40bN.7lkhjKWhk/j7rr4Rqtn6ByEi', NULL, '2025-08-08 05:16:58', '2025-08-08 05:16:58', NULL),
(8, 'teacher4', 'Ali Çelik', 'Reading Specialist', '<EMAIL>', '2025-08-08 05:16:58', '$2y$12$KzCf3oAHEtGFZl8FfovjA.trgQgSYewEAunBlRTR4ZrHGvpec.VJW', NULL, '2025-08-08 05:16:58', '2025-08-08 05:16:58', NULL),
(9, 'student1', 'Beyza Akçay', 'Student', '<EMAIL>', '2025-08-08 05:16:58', '$2y$12$G.2MiazFpLudCHFtbQDUKenAFE3nynGpVkz4JJCqdswRLO1my4nwu', 'PPPOevoa8undk4opMYIIt5FOSWS4ce27KsWHUFPAYsJ2TyQNGz6iZ2M4SoA2', '2025-08-08 05:16:58', '2025-08-19 09:56:34', NULL),
(10, 'student2', 'Emre Yıldız', 'Student', '<EMAIL>', '2025-08-08 05:16:59', '$2y$12$6SyzuG6S0aQfO049s08aDe3jcyGag8W4PO4yBdH04gb3GmgGvIrRO', 't4630TgIIWK2HVe993kAcW7pFjZQRU1fekdqU9e1b3GKWDaWp4LOnMp8zvgu', '2025-08-08 05:16:59', '2025-08-08 05:16:59', NULL),
(11, 'student3', 'Selin Koç', 'Student', '<EMAIL>', '2025-08-08 05:16:59', '$2y$12$F8CHAisb/lVhDDX1FqYsKOCiTXg7Mm4Dc8TwHfLmiJo3EgeJ2F1qq', NULL, '2025-08-08 05:16:59', '2025-08-08 05:16:59', NULL),
(12, 'student4', 'Burak Şahin', 'Student', '<EMAIL>', '2025-08-08 05:16:59', '$2y$12$gqwB0OTK5BcbuPUPXNPzUes/qnVxD6SPHrEkT7wTZ1o0YBXdC/IFK', 'Uyczfo3RyskNRO4LNm39e84OJl2BlPq6uETWjzLtMyWxp92y7DNX17Bgrlu5', '2025-08-08 05:16:59', '2025-08-08 05:16:59', NULL),
(13, 'student5', 'Elif Arslan', 'Student', '<EMAIL>', '2025-08-08 05:16:59', '$2y$12$Uzqr4ELhy8EUryFv1I.9i.ictpc5vOscoUiRpCG8Tl1LoLBqslq72', NULL, '2025-08-08 05:16:59', '2025-08-08 05:16:59', NULL),
(14, 'student6', 'Cem Doğan', 'Student', '<EMAIL>', '2025-08-08 05:17:00', '$2y$12$sGxncfF4/.bczXkI3DySlOX0G52TxGP0YDSFSrlwvTKaP08VgVoEW', NULL, '2025-08-08 05:17:00', '2025-08-08 05:17:00', NULL),
(15, 'student7', 'Nisa Güler', 'Student', '<EMAIL>', '2025-08-08 05:17:00', '$2y$12$n291L7/q1SbDOl.O9NWsb.4BS63vDxCdmCztOat6mT71E7zQ9PZgy', NULL, '2025-08-08 05:17:00', '2025-08-08 05:17:00', NULL),
(16, 'student8', 'Kaan Özdemir', 'Student', '<EMAIL>', '2025-08-08 05:17:00', '$2y$12$HIDdKoPcFAi3QthNd2F5WONMPapHD7jgYfzTwfz2H9.3Da1D0jT12', NULL, '2025-08-08 05:17:00', '2025-08-08 05:17:00', NULL),
(17, 'student9', 'Dila Aydın', 'Student', '<EMAIL>', '2025-08-08 05:17:00', '$2y$12$B4yyFMVgpGt1HXIQzT.dB.oZkShAy2cnNtdzPH95x4BFtW3FSGw7C', NULL, '2025-08-08 05:17:00', '2025-08-08 05:17:00', NULL),
(18, 'student10', 'Arda Polat', 'Student', '<EMAIL>', '2025-08-08 05:17:01', '$2y$12$ekmNkDWXY.MBmBPUn56u3el00KG6wAmVxldHK1psfQJX2BCqqfUY2', NULL, '2025-08-08 05:17:01', '2025-08-08 05:17:01', NULL),
(19, 'student11', 'İrem Çakır', 'Student', '<EMAIL>', '2025-08-08 05:17:01', '$2y$12$dondv.7nSZGswpBpK2OCwuNBe1.rxuPVe.coMtO6zjpbeadrqxwzi', NULL, '2025-08-08 05:17:01', '2025-08-08 05:17:01', NULL),
(20, 'student12', 'Emir Tunç', 'Student', '<EMAIL>', '2025-08-08 05:17:01', '$2y$12$CLZ2bMeFVGdrAuRn3bfHaeZkVyfaZnhBzxVtAwB2R/nPmHdqNQeN.', NULL, '2025-08-08 05:17:01', '2025-08-08 05:17:01', NULL),
(21, 'student13', 'Lara Öztürk', 'Student', '<EMAIL>', '2025-08-08 05:17:01', '$2y$12$Od3rZrU7hT3p1pd6ujdsVeSMATmi9Bmeq2rwrTr71px.95M3EzZAq', NULL, '2025-08-08 05:17:01', '2025-08-08 05:17:01', NULL),
(22, 'student14', 'Berat Kılıç', 'Student', '<EMAIL>', '2025-08-08 05:17:02', '$2y$12$xobcS2Kx72AlENLnHN55D.VbEvCAmBI1kIN7RhVZcFamYNz3TkYDC', NULL, '2025-08-08 05:17:02', '2025-08-08 05:17:02', NULL),
(23, 'student15', 'Ela Yaman', 'Student', '<EMAIL>', '2025-08-08 05:17:02', '$2y$12$.MC6hccpSH4iI9EKirXP0eqQgqqg3eaJgvUSY2wBNJjteQjYytVMm', NULL, '2025-08-08 05:17:02', '2025-08-08 05:17:02', NULL),
(24, 'student16', 'Deniz Erdoğan', 'Student', '<EMAIL>', '2025-08-08 05:17:02', '$2y$12$27swCSIjSrYv6jaR9xmkAu9XjchafZ/NqH988tTkdPgZn2oTlMs6W', NULL, '2025-08-08 05:17:02', '2025-08-08 05:17:02', NULL),
(25, 'student17', 'Mira Başak', 'Student', '<EMAIL>', '2025-08-08 05:17:02', '$2y$12$7646tsKCZTNmmdsPy/f1wuJqsN0cUmtZogvC7PA0gETmuNFiJ4LFu', NULL, '2025-08-08 05:17:02', '2025-08-08 05:17:02', NULL),
(26, 'student18', 'Yusuf Karaca', 'Student', '<EMAIL>', '2025-08-08 05:17:03', '$2y$12$tiRtfFdGCoHkLlRIDB7OtuL5muY5.WRzdAvA6paRgVzevUe4yTawy', NULL, '2025-08-08 05:17:03', '2025-08-08 05:17:03', NULL),
(27, 'student19', 'Defne Çetin', 'Student', '<EMAIL>', '2025-08-08 05:17:03', '$2y$12$jXu4TlZsRgr.e84WHI22Pefo55wIJo/ZDXgGiZmkeM8r9d5eohQBK', NULL, '2025-08-08 05:17:03', '2025-08-08 05:17:03', NULL),
(28, 'student20', 'Eren Güneş', 'Student', '<EMAIL>', '2025-08-08 05:17:03', '$2y$12$2nr9XWAkkho73luCtagTHuHbf0406yKhnRz88V1dhwdlGa06ZUqse', NULL, '2025-08-08 05:17:03', '2025-08-08 05:17:03', NULL),
(30, 'student55', 'Ali Veli', NULL, '<EMAIL>', NULL, '$2y$12$.M0F3EbFepO3F33oSbbAB.UWOAz0bZx0qXXNzrzPaTaJLtvZcMRZ2', NULL, '2025-08-31 04:29:18', '2025-08-31 04:29:18', NULL);

CREATE TABLE `user_activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `activity_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `content` text DEFAULT NULL COMMENT 'Written content for writing activities',
  `rating` int(11) DEFAULT NULL COMMENT 'Rating value for rating activities',
  `media_url` varchar(255) DEFAULT NULL COMMENT 'User-submitted media content',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0-pending, 1-approved, 2-rejected, 3-completed (no approval needed)',
  `goal_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_activities` (`id`, `created_by`, `user_id`, `book_id`, `activity_id`, `activity_date`, `content`, `rating`, `media_url`, `status`, `goal_task_id`, `challenge_task_id`) VALUES
(1, NULL, 9, 2, 1, '2025-08-19 17:31:51', 'sence de bu kitap çok amatörce yazılmış değil mi arkadaşım?', NULL, NULL, 3, NULL, NULL),
(2, NULL, 9, 2, 2, '2025-08-18 21:00:00', 'nice', 7, NULL, 3, NULL, NULL),
(3, NULL, 9, 2, 3, '2025-08-19 16:28:55', '', NULL, 'user-activities/wfffID3d3eL3NqiFZAgUye8ZpgcPfYAR6CEECSqh.jpg', 0, NULL, NULL),
(4, NULL, 10, 1, 1, '2025-09-01 07:43:21', 'Çok güzel bir kitaptı. Arkadaşlarıma tavsiye edeceğim, onlar da okusunlar', NULL, NULL, 3, NULL, NULL),
(5, NULL, 10, 1, 2, '2025-09-01 07:43:54', 'bilmem', 8, NULL, 3, NULL, NULL),
(6, NULL, 10, 1, 3, '2025-09-01 07:44:35', 'çok uğraştım', NULL, 'user-activities/HFwB1in3Zm88kY27tGNCsoZA5xqI7uTnsN8OzPOy.png', 1, NULL, NULL);

CREATE TABLE `user_activity_reviews` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED NOT NULL,
  `review_date` date NOT NULL,
  `reviewed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0-waiting, 1-approved, 2-rejected',
  `feedback` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_activity_reviews` (`id`, `created_by`, `user_activity_id`, `review_date`, `reviewed_by`, `status`, `feedback`) VALUES
(1, NULL, 3, '2025-08-19', 1, 0, NULL),
(2, NULL, 6, '2025-09-01', 1, 1, NULL);

CREATE TABLE `user_agreements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `agreement_type` varchar(255) NOT NULL DEFAULT 'privacy_policy' COMMENT 'Type of agreement (privacy_policy, terms_of_service, etc.)',
  `version` varchar(255) NOT NULL DEFAULT '1.0' COMMENT 'Version of the agreement accepted',
  `accepted_at` timestamp NOT NULL COMMENT 'When the user accepted the agreement',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the user when accepting'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_avatars` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `avatar_id` bigint(20) UNSIGNED NOT NULL,
  `selected_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_avatars` (`id`, `created_by`, `user_id`, `avatar_id`, `selected_at`) VALUES
(1, NULL, 9, 1, '2025-08-18 16:29:13'),
(2, NULL, 5, 2, '2025-08-24 05:41:12'),
(3, NULL, 10, 2, '2025-09-01 07:40:46');

CREATE TABLE `user_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `goal_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_books` (`id`, `created_by`, `user_id`, `book_id`, `start_date`, `end_date`, `goal_task_id`, `challenge_task_id`) VALUES
(1, NULL, 9, 1, '2025-08-19', '2025-08-19', NULL, NULL),
(2, NULL, 9, 2, '2025-08-19', '2025-08-19', NULL, NULL),
(4, NULL, 9, 3, '2025-08-21', NULL, NULL, NULL),
(5, NULL, 10, 1, '2025-09-01', '2025-09-01', NULL, NULL);

CREATE TABLE `user_challenge_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `assigned_by` bigint(20) UNSIGNED NOT NULL,
  `assign_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `start_date` timestamp NULL DEFAULT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `complete_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_challenge_tasks` (`id`, `created_by`, `challenge_task_id`, `team_id`, `user_id`, `assigned_by`, `assign_date`, `start_date`, `completed`, `complete_date`) VALUES
(1, NULL, 3, 1, 9, 1, '2025-08-09 08:39:01', NULL, 0, NULL),
(2, NULL, 3, NULL, 9, 1, '2025-08-09 08:39:01', NULL, 0, NULL),
(3, NULL, 3, 1, 10, 1, '2025-08-09 08:39:01', NULL, 0, NULL),
(4, NULL, 3, 1, 11, 1, '2025-08-09 08:39:01', NULL, 0, NULL);

CREATE TABLE `user_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `default` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_classes` (`id`, `created_by`, `user_id`, `class_id`, `school_id`, `active`, `default`) VALUES
(1, NULL, 9, 1, 1, 1, 1),
(2, NULL, 5, 1, 1, 1, 1),
(3, NULL, 30, 1, 1, 1, 1);

CREATE TABLE `user_goals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `goal_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `assigned_by` bigint(20) UNSIGNED NOT NULL,
  `assign_date` timestamp NULL DEFAULT NULL,
  `comment` text DEFAULT NULL,
  `achieved` tinyint(1) NOT NULL DEFAULT 0,
  `achieve_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_goals` (`id`, `created_by`, `goal_id`, `team_id`, `user_id`, `assigned_by`, `assign_date`, `comment`, `achieved`, `achieve_date`) VALUES
(1, NULL, 1, NULL, 9, 1, '2025-08-07 21:00:00', NULL, 0, NULL),
(3, NULL, 2, NULL, 9, 1, '2025-08-07 21:00:00', NULL, 0, NULL),
(4, NULL, 2, 1, NULL, 1, '2025-08-07 21:00:00', NULL, 0, NULL),
(5, NULL, 9, 1, NULL, 1, '2025-09-01 21:00:00', NULL, 0, NULL);

CREATE TABLE `user_goal_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_goal_id` bigint(20) UNSIGNED NOT NULL,
  `goal_task_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` timestamp NULL DEFAULT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `complete_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_goal_tasks` (`id`, `created_by`, `user_goal_id`, `goal_task_id`, `team_id`, `user_id`, `start_date`, `completed`, `complete_date`) VALUES
(1, NULL, 1, 1, NULL, 9, NULL, 0, NULL),
(2, NULL, 3, 2, NULL, 9, NULL, 0, NULL),
(3, NULL, 3, 3, NULL, 9, NULL, 0, NULL),
(4, NULL, 4, 2, 1, 9, NULL, 0, NULL),
(5, NULL, 4, 2, 1, 10, NULL, 0, NULL),
(6, NULL, 4, 2, 1, 11, NULL, 0, NULL),
(7, NULL, 4, 3, 1, 9, NULL, 0, NULL),
(8, NULL, 4, 3, 1, 10, NULL, 0, NULL),
(9, NULL, 4, 3, 1, 11, NULL, 0, NULL),
(10, NULL, 5, 4, 1, 9, NULL, 0, NULL),
(11, NULL, 5, 4, 1, 10, NULL, 0, NULL),
(12, NULL, 5, 4, 1, 11, NULL, 0, NULL);

CREATE TABLE `user_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `point_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED DEFAULT NULL,
  `source_id` bigint(20) DEFAULT NULL,
  `point_type` int(11) NOT NULL COMMENT '1-Page, 2-Activity, 3-Task, 4-Manual',
  `points` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_points` (`id`, `created_by`, `point_date`, `user_id`, `book_id`, `source_id`, `point_type`, `points`) VALUES
(1, NULL, '2025-08-19 08:59:26', 9, 1, 2, 1, 40),
(2, NULL, '2025-08-19 08:59:35', 9, 1, 3, 1, 40),
(3, NULL, '2025-08-19 09:17:20', 9, 1, 4, 1, 40),
(4, NULL, '2025-08-19 09:49:08', 9, 1, 5, 1, 20),
(5, NULL, '2025-08-19 09:49:18', 9, 1, 6, 1, 28),
(6, NULL, '2025-08-19 09:53:39', 9, 1, 7, 1, 28),
(7, NULL, '2025-08-19 10:46:53', 9, 2, 8, 1, 15),
(10, NULL, '2025-08-19 11:06:05', 9, 2, 11, 1, 15),
(11, NULL, '2025-08-19 11:25:26', 9, 2, 12, 1, 15),
(16, NULL, '2025-08-19 11:34:33', 9, 2, 17, 1, 15),
(17, NULL, '2025-08-19 11:36:07', 9, 2, 18, 1, 72),
(19, NULL, '2025-08-19 14:31:51', 9, 2, 1, 2, 10),
(20, NULL, '2025-08-19 14:35:57', 9, 2, 2, 2, 3),
(34, NULL, '2025-09-01 07:43:21', 10, 1, 4, 2, 10),
(35, NULL, '2025-09-01 07:43:54', 10, 1, 5, 2, 3),
(36, NULL, '2025-09-01 07:45:03', 10, 1, 6, 2, 10);

CREATE TABLE `user_reading_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `log_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `start_page` int(11) DEFAULT NULL COMMENT 'Optional start page information',
  `end_page` int(11) DEFAULT NULL COMMENT 'Optional end page information',
  `pages_read` int(11) NOT NULL,
  `reading_duration` int(11) DEFAULT NULL COMMENT 'Time spent reading in minutes',
  `book_completed` tinyint(1) NOT NULL DEFAULT 0,
  `goal_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_reading_logs` (`id`, `created_by`, `user_id`, `book_id`, `log_date`, `start_page`, `end_page`, `pages_read`, `reading_duration`, `book_completed`, `goal_task_id`, `challenge_task_id`) VALUES
(3, NULL, 9, 1, '2025-08-18 21:00:00', NULL, NULL, 20, 11, 0, NULL, NULL),
(4, NULL, 9, 1, '2025-08-18 21:00:00', NULL, NULL, 20, NULL, 0, NULL, NULL),
(5, NULL, 9, 1, '2025-08-18 21:00:00', NULL, NULL, 10, 5, 0, NULL, NULL),
(7, NULL, 9, 1, '2025-08-18 21:00:00', NULL, NULL, 14, 10, 1, NULL, NULL),
(8, NULL, 9, 2, '2025-08-18 21:00:00', NULL, NULL, 10, NULL, 0, NULL, NULL),
(11, NULL, 9, 2, '2025-08-18 21:00:00', NULL, NULL, 10, NULL, 0, NULL, NULL),
(12, NULL, 9, 2, '2025-08-18 21:00:00', NULL, NULL, 10, 15, 0, NULL, NULL),
(17, NULL, 9, 2, '2025-08-18 21:00:00', NULL, NULL, 10, 20, 0, NULL, NULL),
(18, NULL, 9, 2, '2025-08-18 21:00:00', NULL, NULL, 48, 50, 1, NULL, NULL),
(33, NULL, 10, 1, '2025-08-31 21:00:00', NULL, NULL, 25, NULL, 0, NULL, NULL),
(34, NULL, 10, 1, '2025-08-31 21:00:00', NULL, NULL, 39, NULL, 1, NULL, NULL);

CREATE TABLE `user_rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `awarded_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `awarded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reading_log_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_rewards` (`id`, `created_by`, `user_id`, `reward_id`, `awarded_date`, `awarded_by`, `reading_log_id`, `user_activity_id`) VALUES
(5, NULL, 9, 1, '2025-08-31 09:51:51', 1, NULL, NULL),
(6, NULL, 10, 1, '2025-09-01 07:42:19', NULL, 34, NULL);

CREATE TABLE `user_schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `default` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_schools` (`id`, `created_by`, `user_id`, `school_id`, `role_id`, `active`, `default`) VALUES
(2, NULL, 9, 1, 4, 1, 1),
(3, NULL, 5, 1, 3, 1, 1),
(4, NULL, 30, 1, 4, 1, 1);

CREATE TABLE `user_teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user_teams` (`id`, `created_by`, `team_id`, `user_id`) VALUES
(1, NULL, 1, 9),
(2, NULL, 1, 10),
(3, NULL, 1, 11);


ALTER TABLE `activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activities_category_id_active_index` (`category_id`,`active`),
  ADD KEY `activities_activity_type_index` (`activity_type`),
  ADD KEY `activities_need_approval_index` (`need_approval`),
  ADD KEY `activities_active_index` (`active`),
  ADD KEY `activities_points_index` (`points`);

ALTER TABLE `activity_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activity_categories_active_index` (`active`),
  ADD KEY `activity_categories_name_index` (`name`);

ALTER TABLE `activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subject` (`subject_type`,`subject_id`),
  ADD KEY `causer` (`causer_type`,`causer_id`),
  ADD KEY `activity_log_log_name_index` (`log_name`);

ALTER TABLE `authors`
  ADD PRIMARY KEY (`id`),
  ADD KEY `authors_name_index` (`name`),
  ADD KEY `authors_created_by_index` (`created_by`);

ALTER TABLE `avatars`
  ADD PRIMARY KEY (`id`),
  ADD KEY `avatars_name_index` (`name`);

ALTER TABLE `books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `books_isbn_unique` (`isbn`),
  ADD KEY `books_name_index` (`name`),
  ADD KEY `books_publisher_id_index` (`publisher_id`),
  ADD KEY `books_book_type_id_index` (`book_type_id`),
  ADD KEY `books_year_of_publish_index` (`year_of_publish`),
  ADD KEY `books_created_by_index` (`created_by`);

ALTER TABLE `book_authors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_authors_book_id_author_id_unique` (`book_id`,`author_id`),
  ADD KEY `book_authors_author_id_index` (`author_id`);

ALTER TABLE `book_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_categories_book_id_category_id_unique` (`book_id`,`category_id`),
  ADD KEY `book_categories_category_id_index` (`category_id`);

ALTER TABLE `book_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `book_questions_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_questions_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_questions_page_start_page_end_index` (`page_start`,`page_end`),
  ADD KEY `book_questions_difficulty_level_index` (`difficulty_level`),
  ADD KEY `book_questions_created_by_index` (`created_by`);

ALTER TABLE `book_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_types_name_unique` (`name`),
  ADD KEY `book_types_name_index` (`name`);

ALTER TABLE `book_words`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_book_word` (`book_id`,`word`),
  ADD KEY `book_words_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_words_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_words_word_book_id_index` (`word`,`book_id`),
  ADD KEY `book_words_page_reference_index` (`page_reference`),
  ADD KEY `book_words_difficulty_level_index` (`difficulty_level`),
  ADD KEY `book_words_created_by_index` (`created_by`);

ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `categories_name_unique` (`name`),
  ADD KEY `categories_name_index` (`name`);

ALTER TABLE `challenges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `challenges_active_index` (`active`),
  ADD KEY `challenges_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `challenges_active_start_date_end_date_index` (`active`,`start_date`,`end_date`),
  ADD KEY `challenges_created_by_index` (`created_by`);

ALTER TABLE `challenge_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_classes_challenge_id_school_class_id_unique` (`challenge_id`,`school_class_id`),
  ADD KEY `challenge_classes_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_classes_school_class_id_index` (`school_class_id`);

ALTER TABLE `challenge_schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_schools_challenge_id_school_id_unique` (`challenge_id`,`school_id`),
  ADD KEY `challenge_schools_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_schools_school_id_index` (`school_id`);

ALTER TABLE `challenge_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_tasks_challenge_id_task_id_start_date_end_date_unique` (`challenge_id`,`task_id`,`start_date`,`end_date`),
  ADD KEY `challenge_tasks_task_id_foreign` (`task_id`),
  ADD KEY `challenge_tasks_challenge_id_task_id_index` (`challenge_id`,`task_id`),
  ADD KEY `challenge_tasks_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `challenge_tasks_challenge_id_start_date_end_date_index` (`challenge_id`,`start_date`,`end_date`),
  ADD KEY `challenge_tasks_reward_id_foreign` (`reward_id`),
  ADD KEY `challenge_tasks_challenge_id_reward_id_index` (`challenge_id`,`reward_id`),
  ADD KEY `challenge_tasks_created_by_index` (`created_by`);

ALTER TABLE `challenge_teams`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_teams_challenge_id_team_id_unique` (`challenge_id`,`team_id`),
  ADD KEY `challenge_teams_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_teams_team_id_index` (`team_id`);

ALTER TABLE `class_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `class_books_unique` (`class_id`,`book_id`),
  ADD KEY `class_books_class_id_index` (`class_id`),
  ADD KEY `class_books_book_id_index` (`book_id`),
  ADD KEY `class_books_created_by_index` (`created_by`);

ALTER TABLE `enum_class_levels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_class_levels_name_unique` (`name`),
  ADD KEY `enum_class_levels_name_index` (`name`);

ALTER TABLE `enum_school_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_school_types_name_unique` (`name`),
  ADD KEY `enum_school_types_name_index` (`name`);

ALTER TABLE `enum_task_cycles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_task_cycles_nr_unique` (`nr`),
  ADD KEY `enum_task_cycles_nr_index` (`nr`);

ALTER TABLE `enum_task_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_task_types_nr_unique` (`nr`),
  ADD KEY `enum_task_types_nr_index` (`nr`);

ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

ALTER TABLE `goals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `goals_name_index` (`name`),
  ADD KEY `goals_active_index` (`active`),
  ADD KEY `goals_active_name_index` (`active`,`name`),
  ADD KEY `goals_created_by_index` (`created_by`);

ALTER TABLE `goal_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `goal_tasks_goal_id_task_id_start_date_end_date_unique` (`goal_id`,`task_id`,`start_date`,`end_date`),
  ADD KEY `goal_tasks_task_id_foreign` (`task_id`),
  ADD KEY `goal_tasks_goal_id_task_id_index` (`goal_id`,`task_id`),
  ADD KEY `goal_tasks_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `goal_tasks_goal_id_start_date_end_date_index` (`goal_id`,`start_date`,`end_date`),
  ADD KEY `goal_tasks_created_by_index` (`created_by`);

ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

ALTER TABLE `page_points`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `page_points_book_type_id_class_level_id_unique` (`book_type_id`,`class_level_id`),
  ADD KEY `page_points_class_level_id_foreign` (`class_level_id`);

ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

ALTER TABLE `publishers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `publishers_name_index` (`name`),
  ADD KEY `publishers_created_by_index` (`created_by`);

ALTER TABLE `rewards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rewards_reward_type_index` (`reward_type`),
  ADD KEY `rewards_name_index` (`name`),
  ADD KEY `rewards_active_index` (`active`),
  ADD KEY `rewards_reward_type_active_index` (`reward_type`,`active`),
  ADD KEY `rewards_created_by_index` (`created_by`);

ALTER TABLE `reward_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reward_tasks_reward_id_task_id_unique` (`reward_id`,`task_id`),
  ADD KEY `reward_tasks_reward_id_task_id_index` (`reward_id`,`task_id`),
  ADD KEY `reward_tasks_reward_id_index` (`reward_id`),
  ADD KEY `reward_tasks_task_id_index` (`task_id`),
  ADD KEY `reward_tasks_created_by_index` (`created_by`);

ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

ALTER TABLE `schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `schools_name_school_type_id_unique` (`name`,`school_type_id`),
  ADD KEY `schools_school_type_id_active_index` (`school_type_id`,`active`),
  ADD KEY `schools_school_type_id_index` (`school_type_id`);

ALTER TABLE `school_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `school_classes_school_id_name_active_unique` (`school_id`,`name`,`active`),
  ADD KEY `school_classes_school_id_class_level_id_active_index` (`school_id`,`class_level_id`,`active`),
  ADD KEY `school_classes_class_level_id_index` (`class_level_id`),
  ADD KEY `school_classes_created_by_index` (`created_by`);

ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

ALTER TABLE `tasks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tasks_task_type_id_index` (`task_type_id`),
  ADD KEY `tasks_task_cycle_id_index` (`task_cycle_id`),
  ADD KEY `tasks_activity_id_index` (`activity_id`),
  ADD KEY `tasks_active_index` (`active`),
  ADD KEY `tasks_active_task_type_id_index` (`active`,`task_type_id`),
  ADD KEY `tasks_created_by_index` (`created_by`);

ALTER TABLE `task_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_books_task_id_book_id_unique` (`task_id`,`book_id`),
  ADD KEY `task_books_task_id_index` (`task_id`),
  ADD KEY `task_books_book_id_index` (`book_id`),
  ADD KEY `task_books_created_by_index` (`created_by`);

ALTER TABLE `task_book_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_book_categories_task_id_category_id_unique` (`task_id`,`category_id`),
  ADD KEY `task_book_categories_task_id_index` (`task_id`),
  ADD KEY `task_book_categories_category_id_index` (`category_id`),
  ADD KEY `task_book_categories_created_by_index` (`created_by`);

ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teams_name_index` (`name`),
  ADD KEY `teams_leader_user_id_index` (`leader_user_id`),
  ADD KEY `teams_active_index` (`active`),
  ADD KEY `teams_active_name_index` (`active`,`name`),
  ADD KEY `teams_created_by_index` (`created_by`);

ALTER TABLE `team_rewards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_reward_unique` (`team_id`,`reward_id`),
  ADD KEY `team_rewards_team_id_reward_id_index` (`team_id`,`reward_id`),
  ADD KEY `team_rewards_team_id_awarded_date_index` (`team_id`,`awarded_date`),
  ADD KEY `team_rewards_reward_id_awarded_date_index` (`reward_id`,`awarded_date`),
  ADD KEY `team_rewards_awarded_by_index` (`awarded_by`),
  ADD KEY `team_rewards_reading_log_id_index` (`reading_log_id`),
  ADD KEY `team_rewards_user_activity_id_index` (`user_activity_id`),
  ADD KEY `team_rewards_awarded_date_index` (`awarded_date`),
  ADD KEY `team_rewards_created_by_index` (`created_by`);

ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_username_unique` (`username`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

ALTER TABLE `user_activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_activities_user_id_activity_date_index` (`user_id`,`activity_date`),
  ADD KEY `user_activities_book_id_activity_date_index` (`book_id`,`activity_date`),
  ADD KEY `user_activities_activity_id_status_index` (`activity_id`,`status`),
  ADD KEY `user_activities_status_index` (`status`),
  ADD KEY `user_activities_activity_date_index` (`activity_date`),
  ADD KEY `user_activities_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_activities_goal_task_id_status_index` (`goal_task_id`,`status`),
  ADD KEY `user_activities_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_activities_challenge_task_id_status_index` (`challenge_task_id`,`status`),
  ADD KEY `user_activities_created_by_index` (`created_by`);

ALTER TABLE `user_activity_reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_activity_reviews_user_activity_id_status_index` (`user_activity_id`,`status`),
  ADD KEY `user_activity_reviews_reviewed_by_review_date_index` (`reviewed_by`,`review_date`),
  ADD KEY `user_activity_reviews_status_index` (`status`),
  ADD KEY `user_activity_reviews_review_date_index` (`review_date`),
  ADD KEY `user_activity_reviews_created_by_index` (`created_by`);

ALTER TABLE `user_agreements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_agreements_user_id_agreement_type_index` (`user_id`,`agreement_type`),
  ADD KEY `user_agreements_agreement_type_version_index` (`agreement_type`,`version`),
  ADD KEY `user_agreements_accepted_at_index` (`accepted_at`);

ALTER TABLE `user_avatars`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_avatars_user_id_unique` (`user_id`),
  ADD KEY `user_avatars_avatar_id_index` (`avatar_id`),
  ADD KEY `user_avatars_selected_at_index` (`selected_at`),
  ADD KEY `user_avatars_created_by_index` (`created_by`);

ALTER TABLE `user_books`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_books_user_id_index` (`user_id`),
  ADD KEY `user_books_book_id_index` (`book_id`),
  ADD KEY `user_books_start_date_index` (`start_date`),
  ADD KEY `user_books_end_date_index` (`end_date`),
  ADD KEY `user_books_user_id_book_id_index` (`user_id`,`book_id`),
  ADD KEY `user_book_session_index` (`user_id`,`book_id`,`start_date`),
  ADD KEY `user_books_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_books_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_books_created_by_index` (`created_by`);

ALTER TABLE `user_challenge_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_challenge_task_unique` (`challenge_task_id`,`user_id`,`team_id`),
  ADD KEY `user_challenge_tasks_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_challenge_tasks_user_id_completed_index` (`user_id`,`completed`),
  ADD KEY `user_challenge_tasks_team_id_completed_index` (`team_id`,`completed`),
  ADD KEY `user_challenge_tasks_challenge_task_id_completed_index` (`challenge_task_id`,`completed`),
  ADD KEY `user_challenge_tasks_completed_at_index` (`complete_date`) USING BTREE,
  ADD KEY `user_challenge_tasks_assigned_by_assigned_at_index` (`assigned_by`,`assign_date`) USING BTREE,
  ADD KEY `user_challenge_tasks_created_by_index` (`created_by`);

ALTER TABLE `user_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_classes_unique` (`user_id`,`class_id`,`school_id`),
  ADD KEY `user_classes_class_id_active_index` (`class_id`,`active`),
  ADD KEY `user_classes_school_id_active_index` (`school_id`,`active`),
  ADD KEY `user_classes_user_id_active_index` (`user_id`,`active`),
  ADD KEY `user_classes_active_index` (`active`),
  ADD KEY `user_classes_user_id_default_index` (`user_id`,`default`),
  ADD KEY `user_classes_user_id_active_default_index` (`user_id`,`active`,`default`),
  ADD KEY `user_classes_created_by_index` (`created_by`);

ALTER TABLE `user_goals`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_goal` (`goal_id`,`user_id`),
  ADD UNIQUE KEY `unique_team_goal` (`goal_id`,`team_id`),
  ADD KEY `user_goals_goal_id_user_id_index` (`goal_id`,`user_id`),
  ADD KEY `user_goals_goal_id_team_id_index` (`goal_id`,`team_id`),
  ADD KEY `user_goals_user_id_achieved_index` (`user_id`,`achieved`),
  ADD KEY `user_goals_team_id_achieved_index` (`team_id`,`achieved`),
  ADD KEY `user_goals_assigned_by_index` (`assigned_by`),
  ADD KEY `user_goals_assigned_at_index` (`assign_date`),
  ADD KEY `user_goals_achieved_at_index` (`achieve_date`) USING BTREE,
  ADD KEY `user_goals_created_by_index` (`created_by`);

ALTER TABLE `user_goal_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_goal_task` (`user_goal_id`,`goal_task_id`,`user_id`),
  ADD KEY `user_goal_tasks_user_goal_id_goal_task_id_index` (`user_goal_id`,`goal_task_id`),
  ADD KEY `user_goal_tasks_user_id_completed_index` (`user_id`,`completed`),
  ADD KEY `user_goal_tasks_team_id_completed_index` (`team_id`,`completed`),
  ADD KEY `user_goal_tasks_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_goal_tasks_completed_at_index` (`complete_date`) USING BTREE,
  ADD KEY `user_goal_tasks_created_by_index` (`created_by`);

ALTER TABLE `user_points`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_points_user_id_point_date_index` (`user_id`,`point_date`),
  ADD KEY `user_points_book_id_point_date_index` (`book_id`,`point_date`),
  ADD KEY `user_points_user_id_point_type_index` (`user_id`,`point_type`),
  ADD KEY `user_points_point_type_index` (`point_type`),
  ADD KEY `user_points_point_date_index` (`point_date`),
  ADD KEY `user_points_point_type_source_id_index` (`point_type`,`source_id`),
  ADD KEY `user_points_created_by_index` (`created_by`);

ALTER TABLE `user_reading_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_reading_logs_user_id_log_date_index` (`user_id`,`log_date`),
  ADD KEY `user_reading_logs_book_id_log_date_index` (`book_id`,`log_date`),
  ADD KEY `user_reading_logs_user_id_book_id_index` (`user_id`,`book_id`),
  ADD KEY `user_reading_logs_log_date_index` (`log_date`),
  ADD KEY `user_reading_logs_book_completed_index` (`book_completed`),
  ADD KEY `user_reading_logs_goal_task_id_user_id_index` (`goal_task_id`,`user_id`),
  ADD KEY `user_reading_logs_goal_task_id_log_date_index` (`goal_task_id`,`log_date`),
  ADD KEY `user_reading_logs_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_reading_logs_challenge_task_id_log_date_index` (`challenge_task_id`,`log_date`),
  ADD KEY `user_reading_logs_created_by_index` (`created_by`);

ALTER TABLE `user_rewards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_reward_unique` (`user_id`,`reward_id`),
  ADD KEY `user_rewards_user_id_reward_id_index` (`user_id`,`reward_id`),
  ADD KEY `user_rewards_user_id_awarded_date_index` (`user_id`,`awarded_date`),
  ADD KEY `user_rewards_reward_id_awarded_date_index` (`reward_id`,`awarded_date`),
  ADD KEY `user_rewards_awarded_by_index` (`awarded_by`),
  ADD KEY `user_rewards_reading_log_id_index` (`reading_log_id`),
  ADD KEY `user_rewards_user_activity_id_index` (`user_activity_id`),
  ADD KEY `user_rewards_awarded_date_index` (`awarded_date`),
  ADD KEY `user_rewards_created_by_index` (`created_by`);

ALTER TABLE `user_schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_schools_unique` (`user_id`,`school_id`,`role_id`),
  ADD KEY `user_schools_role_id_foreign` (`role_id`),
  ADD KEY `user_schools_school_id_role_id_index` (`school_id`,`role_id`),
  ADD KEY `user_schools_user_id_active_index` (`user_id`,`active`),
  ADD KEY `user_schools_active_index` (`active`),
  ADD KEY `user_schools_user_id_default_index` (`user_id`,`default`),
  ADD KEY `user_schools_user_id_active_default_index` (`user_id`,`active`,`default`),
  ADD KEY `user_schools_created_by_index` (`created_by`);

ALTER TABLE `user_teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_teams_team_id_index` (`team_id`),
  ADD KEY `user_teams_user_id_index` (`user_id`),
  ADD KEY `user_teams_team_id_user_id_index` (`team_id`,`user_id`),
  ADD KEY `user_teams_created_by_index` (`created_by`);


ALTER TABLE `activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `activity_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `activity_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `avatars`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `book_authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `book_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `book_questions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `book_words`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `challenges`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `challenge_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `challenge_schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `challenge_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `challenge_teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `class_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_class_levels`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `enum_school_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `enum_task_cycles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

ALTER TABLE `enum_task_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `goals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

ALTER TABLE `goal_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=84;

ALTER TABLE `page_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=65;

ALTER TABLE `publishers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `reward_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

ALTER TABLE `schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `school_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

ALTER TABLE `task_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

ALTER TABLE `task_book_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

ALTER TABLE `teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

ALTER TABLE `team_rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

ALTER TABLE `user_activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

ALTER TABLE `user_activity_reviews`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

ALTER TABLE `user_agreements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_avatars`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `user_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `user_challenge_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

ALTER TABLE `user_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `user_goals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `user_goal_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

ALTER TABLE `user_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

ALTER TABLE `user_reading_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

ALTER TABLE `user_rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

ALTER TABLE `user_schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

ALTER TABLE `user_teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;


ALTER TABLE `activities`
  ADD CONSTRAINT `activities_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `activity_categories` (`id`) ON DELETE CASCADE;

ALTER TABLE `authors`
  ADD CONSTRAINT `authors_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `books`
  ADD CONSTRAINT `books_book_type_id_foreign` FOREIGN KEY (`book_type_id`) REFERENCES `book_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `books_publisher_id_foreign` FOREIGN KEY (`publisher_id`) REFERENCES `publishers` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_authors`
  ADD CONSTRAINT `book_authors_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_authors_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_categories`
  ADD CONSTRAINT `book_categories_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_categories_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_questions`
  ADD CONSTRAINT `book_questions_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_questions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `book_words`
  ADD CONSTRAINT `book_words_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_words_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `challenges`
  ADD CONSTRAINT `challenges_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `challenge_classes`
  ADD CONSTRAINT `challenge_classes_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_classes_school_class_id_foreign` FOREIGN KEY (`school_class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_schools`
  ADD CONSTRAINT `challenge_schools_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_schools_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_tasks`
  ADD CONSTRAINT `challenge_tasks_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `challenge_tasks_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `challenge_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_teams`
  ADD CONSTRAINT `challenge_teams_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_teams_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE;

ALTER TABLE `class_books`
  ADD CONSTRAINT `class_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `class_books_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `class_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `goals`
  ADD CONSTRAINT `goals_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `goal_tasks`
  ADD CONSTRAINT `goal_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `goal_tasks_goal_id_foreign` FOREIGN KEY (`goal_id`) REFERENCES `goals` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `goal_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

ALTER TABLE `page_points`
  ADD CONSTRAINT `page_points_book_type_id_foreign` FOREIGN KEY (`book_type_id`) REFERENCES `book_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `page_points_class_level_id_foreign` FOREIGN KEY (`class_level_id`) REFERENCES `enum_class_levels` (`id`) ON DELETE CASCADE;

ALTER TABLE `publishers`
  ADD CONSTRAINT `publishers_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `rewards`
  ADD CONSTRAINT `rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `reward_tasks`
  ADD CONSTRAINT `reward_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `reward_tasks_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reward_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

ALTER TABLE `schools`
  ADD CONSTRAINT `schools_school_type_id_foreign` FOREIGN KEY (`school_type_id`) REFERENCES `enum_school_types` (`id`) ON DELETE SET NULL;

ALTER TABLE `school_classes`
  ADD CONSTRAINT `school_classes_class_level_id_foreign` FOREIGN KEY (`class_level_id`) REFERENCES `enum_class_levels` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `school_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `school_classes_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE;

ALTER TABLE `tasks`
  ADD CONSTRAINT `tasks_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `tasks_task_cycle_id_foreign` FOREIGN KEY (`task_cycle_id`) REFERENCES `enum_task_cycles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `tasks_task_type_id_foreign` FOREIGN KEY (`task_type_id`) REFERENCES `enum_task_types` (`id`) ON DELETE CASCADE;

ALTER TABLE `task_books`
  ADD CONSTRAINT `task_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `task_books_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `task_book_categories`
  ADD CONSTRAINT `task_book_categories_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_book_categories_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `task_book_categories_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `teams`
  ADD CONSTRAINT `teams_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `teams_leader_user_id_foreign` FOREIGN KEY (`leader_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `team_rewards`
  ADD CONSTRAINT `team_rewards_awarded_by_foreign` FOREIGN KEY (`awarded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `team_rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `team_rewards_reading_log_id_foreign` FOREIGN KEY (`reading_log_id`) REFERENCES `user_reading_logs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_activities`
  ADD CONSTRAINT `user_activities_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_activities_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_activities_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activities_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activities_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activities_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_activity_reviews`
  ADD CONSTRAINT `user_activity_reviews_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activity_reviews_reviewed_by_foreign` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activity_reviews_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_agreements`
  ADD CONSTRAINT `user_agreements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_avatars`
  ADD CONSTRAINT `user_avatars_avatar_id_foreign` FOREIGN KEY (`avatar_id`) REFERENCES `avatars` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_avatars_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_avatars_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_books`
  ADD CONSTRAINT `user_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_books_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_books_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_books_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_challenge_tasks`
  ADD CONSTRAINT `user_challenge_tasks_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_challenge_tasks_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_challenge_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_challenge_tasks_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_challenge_tasks_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_classes`
  ADD CONSTRAINT `user_classes_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_classes_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_goals`
  ADD CONSTRAINT `user_goals_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goals_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_goals_goal_id_foreign` FOREIGN KEY (`goal_id`) REFERENCES `goals` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goals_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goals_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_goal_tasks`
  ADD CONSTRAINT `user_goal_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_goal_tasks_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goal_tasks_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goal_tasks_user_goal_id_foreign` FOREIGN KEY (`user_goal_id`) REFERENCES `user_goals` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_goal_tasks_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_points`
  ADD CONSTRAINT `user_points_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_points_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_points_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_reading_logs`
  ADD CONSTRAINT `user_reading_logs_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_reading_logs_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_reading_logs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_reading_logs_goal_task_id_foreign` FOREIGN KEY (`goal_task_id`) REFERENCES `goal_tasks` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_reading_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_rewards`
  ADD CONSTRAINT `user_rewards_awarded_by_foreign` FOREIGN KEY (`awarded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_rewards_reading_log_id_foreign` FOREIGN KEY (`reading_log_id`) REFERENCES `user_reading_logs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_schools`
  ADD CONSTRAINT `user_schools_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_schools_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_schools_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_schools_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_teams`
  ADD CONSTRAINT `user_teams_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_teams_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_teams_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
