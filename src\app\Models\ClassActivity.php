<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClassActivity extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'class_id',
        'activity_id',
        'question_count',
        'min_grade',
        'allowed_tries',
        'min_word_count',
        'points',
        'required',
        'need_approval',
        'active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'question_count' => 'integer',
            'min_grade' => 'integer',
            'allowed_tries' => 'integer',
            'min_word_count' => 'integer',
            'points' => 'integer',
            'required' => 'boolean',
            'need_approval' => 'boolean',
            'active' => 'boolean',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure unique constraint is respected
        static::creating(function ($classActivity) {
            $existing = static::where('class_id', $classActivity->class_id)
                ->where('activity_id', $classActivity->activity_id)
                ->first();
            
            if ($existing) {
                throw new \Exception('ClassActivity already exists for this class and activity combination.');
            }
        });
    }

    /**
     * Get the class this activity belongs to.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the activity definition.
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class);
    }

    /**
     * Scope to filter by class.
     */
    public function scopeByClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to filter by activity.
     */
    public function scopeByActivity($query, $activityId)
    {
        return $query->where('activity_id', $activityId);
    }

    /**
     * Scope to get active class activities.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Get the resolved value for a field, using ClassActivity value if set, otherwise Activity value.
     */
    public function getResolvedValue(string $field)
    {
        // If ClassActivity has a non-null value, use it
        if ($this->$field !== null) {
            return $this->$field;
        }

        // Otherwise, use the Activity's value
        return $this->activity->$field ?? null;
    }

    /**
     * Get all resolved activity settings for this class activity.
     */
    public function getResolvedSettings(): array
    {
        return [
            'question_count' => $this->getResolvedValue('question_count'),
            'min_grade' => $this->getResolvedValue('min_grade'),
            'allowed_tries' => $this->getResolvedValue('allowed_tries'),
            'min_word_count' => $this->getResolvedValue('min_word_count'),
            'points' => $this->getResolvedValue('points'),
            'required' => $this->getResolvedValue('required'),
            'need_approval' => $this->getResolvedValue('need_approval'),
            'active' => $this->getResolvedValue('active'),
        ];
    }

    /**
     * Create ClassActivity records for all existing activities when a new class is created.
     */
    public static function createForNewClass(int $classId): void
    {
        $activities = Activity::where('active', true)->get();
        
        $classActivities = [];
        foreach ($activities as $activity) {
            $classActivities[] = [
                'class_id' => $classId,
                'activity_id' => $activity->id,
                'question_count' => $activity->question_count,
                'min_grade' => $activity->min_grade,
                'allowed_tries' => $activity->allowed_tries,
                'min_word_count' => $activity->min_word_count,
                'points' => $activity->points,
                'required' => $activity->required,
                'need_approval' => $activity->need_approval,
                'active' => $activity->active,
            ];
        }

        if (!empty($classActivities)) {
            static::insert($classActivities);
        }
    }

    /**
     * Create ClassActivity records for all existing classes when a new activity is created.
     */
    public static function createForNewActivity(int $activityId): void
    {
        $activity = Activity::find($activityId);
        if (!$activity) {
            return;
        }

        $classes = SchoolClass::where('active', true)->get();
        
        $classActivities = [];
        foreach ($classes as $class) {
            $classActivities[] = [
                'class_id' => $class->id,
                'activity_id' => $activityId,
                'question_count' => $activity->question_count,
                'min_grade' => $activity->min_grade,
                'allowed_tries' => $activity->allowed_tries,
                'min_word_count' => $activity->min_word_count,
                'points' => $activity->points,
                'required' => $activity->required,
                'need_approval' => $activity->need_approval,
                'active' => $activity->active,
            ];
        }

        if (!empty($classActivities)) {
            static::insert($classActivities);
        }
    }

    /**
     * Get ClassActivity for a specific class and activity combination.
     */
    public static function getForClassAndActivity(int $classId, int $activityId): ?self
    {
        return static::where('class_id', $classId)
            ->where('activity_id', $activityId)
            ->first();
    }


}
