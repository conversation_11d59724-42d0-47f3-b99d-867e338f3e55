<?php

namespace App\Services;

use App\Models\Book;
use App\Models\Activity;
use App\Models\BookQuestion;
use App\Models\BookWord;
use Illuminate\Support\Collection;

class QuizGenerationService
{
    /**
     * Generate quiz questions for a book based on activity configuration.
     */
    public function generateQuiz(Book $book, Activity $activity): array
    {
        if (!$activity->isQuiz()) {
            throw new \InvalidArgumentException('Activity must be a quiz type');
        }

        // Check if book has enough questions
        $availableQuestions = BookQuestion::where('book_id', $book->id)
            ->where('is_active', true)
            ->get();

        if ($availableQuestions->count() < 10) {
            throw new \Exception('Book must have at least 10 questions to generate a quiz');
        }

        // Randomly select questions based on activity configuration
        $questionCount = $activity->question_count ?? 4;
        $selectedQuestions = $availableQuestions->random($questionCount);

        $quiz = [];
        foreach ($selectedQuestions as $question) {
            $quiz[] = $this->formatQuizQuestion($question, $activity);
        }

        return [
            'questions' => $quiz,
            'total_questions' => count($quiz),
            'activity_id' => $activity->id,
            'book_id' => $book->id,
            'quiz_type' => 'quiz'
        ];
    }

    /**
     * Generate vocabulary test questions for a book based on activity configuration.
     */
    public function generateVocabularyTest(Book $book, Activity $activity): array
    {
        if (!$activity->isVocabularyTest()) {
            throw new \InvalidArgumentException('Activity must be a vocabulary test type');
        }

        // Check if book has enough words
        $availableWords = BookWord::where('book_id', $book->id)
            ->where('is_active', true)
            ->get();

        if ($availableWords->count() < 10) {
            throw new \Exception('Book must have at least 10 words to generate a vocabulary test');
        }

        // Randomly select words based on activity configuration
        $questionCount = $activity->question_count ?? 4;
        $selectedWords = $availableWords->random($questionCount);

        $quiz = [];
        foreach ($selectedWords as $word) {
            $quiz[] = $this->formatVocabularyQuestion($word, $activity, $availableWords);
        }

        return [
            'questions' => $quiz,
            'total_questions' => count($quiz),
            'activity_id' => $activity->id,
            'book_id' => $book->id,
            'quiz_type' => 'vocabulary'
        ];
    }

    /**
     * Format a quiz question with answer choices.
     */
    private function formatQuizQuestion(BookQuestion $question, Activity $activity): array
    {
        $choicesCount = $activity->choices_count ?? 4;
        
        // Get all possible answers
        $allAnswers = collect([
            $question->correct_answer,
            $question->incorrect_answer_1,
            $question->incorrect_answer_2,
            $question->incorrect_answer_3,
            $question->incorrect_answer_4,
            $question->incorrect_answer_5,
        ])->filter()->values();

        // If we don't have enough answers, we need to generate some or use what we have
        $choices = $allAnswers->take($choicesCount)->shuffle()->values();
        
        // Find the correct answer index after shuffling
        $correctAnswerIndex = $choices->search($question->correct_answer);

        return [
            'id' => $question->id,
            'question' => $question->question_text,
            'question_image_url' => $question->question_image_url,
            'question_audio_url' => $question->question_audio_url,
            'question_video_url' => $question->question_video_url,
            'choices' => $choices->toArray(),
            'correct_answer_index' => $correctAnswerIndex,
            'correct_answer' => $question->correct_answer,
            'page_start' => $question->page_start,
            'page_end' => $question->page_end,
        ];
    }

    /**
     * Format a vocabulary question with answer choices.
     */
    private function formatVocabularyQuestion(BookWord $word, Activity $activity, Collection $allWords): array
    {
        $choicesCount = $activity->choices_count ?? 4;
        
        // Generate question based on available word data
        $questionType = $this->determineVocabularyQuestionType($word);
        
        switch ($questionType) {
            case 'definition':
                return $this->createDefinitionQuestion($word, $activity, $allWords, $choicesCount);
            case 'synonym':
                return $this->createSynonymQuestion($word, $activity, $allWords, $choicesCount);
            case 'antonym':
                return $this->createAntonymQuestion($word, $activity, $allWords, $choicesCount);
            default:
                return $this->createDefinitionQuestion($word, $activity, $allWords, $choicesCount);
        }
    }

    /**
     * Determine the type of vocabulary question to create.
     */
    private function determineVocabularyQuestionType(BookWord $word): string
    {
        $availableTypes = [];
        
        if ($word->definition) {
            $availableTypes[] = 'definition';
        }
        if ($word->synonym) {
            $availableTypes[] = 'synonym';
        }
        if ($word->antonym) {
            $availableTypes[] = 'antonym';
        }
        
        return $availableTypes ? $availableTypes[array_rand($availableTypes)] : 'definition';
    }

    /**
     * Create a definition-based vocabulary question.
     */
    private function createDefinitionQuestion(BookWord $word, Activity $activity, Collection $allWords, int $choicesCount): array
    {
        $correctAnswer = $word->definition ?: $word->word;
        
        // Get other definitions as wrong answers
        $wrongAnswers = $allWords->where('id', '!=', $word->id)
            ->where('definition', '!=', null)
            ->where('definition', '!=', '')
            ->pluck('definition')
            ->take($choicesCount - 1);
        
        $choices = collect([$correctAnswer])->merge($wrongAnswers)->shuffle()->values();
        $correctAnswerIndex = $choices->search($correctAnswer);

        return [
            'id' => $word->id,
            'question' => __('mobile.definition_question', ['word' => $word->word]),
            // "What does '{$word->word}' mean?",
            'choices' => $choices->toArray(),
            'correct_answer_index' => $correctAnswerIndex,
            'correct_answer' => $correctAnswer,
            'word' => $word->word,
            'question_type' => 'definition',
        ];
    }

    /**
     * Create a synonym-based vocabulary question.
     */
    private function createSynonymQuestion(BookWord $word, Activity $activity, Collection $allWords, int $choicesCount): array
    {
        $correctAnswer = $word->synonym;
        
        // Get other synonyms as wrong answers
        $wrongAnswers = $allWords->where('id', '!=', $word->id)
            ->where('synonym', '!=', null)
            ->where('synonym', '!=', '')
            ->pluck('synonym')
            ->take($choicesCount - 1);
        
        $choices = collect([$correctAnswer])->merge($wrongAnswers)->shuffle()->values();
        $correctAnswerIndex = $choices->search($correctAnswer);

        return [
            'id' => $word->id,
            'question' => __('mobile.synonym_question', ['word' => $word->word]),
            'choices' => $choices->toArray(),
            'correct_answer_index' => $correctAnswerIndex,
            'correct_answer' => $correctAnswer,
            'word' => $word->word,
            'question_type' => 'synonym',
        ];
    }

    /**
     * Create an antonym-based vocabulary question.
     */
    private function createAntonymQuestion(BookWord $word, Activity $activity, Collection $allWords, int $choicesCount): array
    {
        $correctAnswer = $word->antonym;
        
        // Get other antonyms as wrong answers
        $wrongAnswers = $allWords->where('id', '!=', $word->id)
            ->where('antonym', '!=', null)
            ->where('antonym', '!=', '')
            ->pluck('antonym')
            ->take($choicesCount - 1);
        
        $choices = collect([$correctAnswer])->merge($wrongAnswers)->shuffle()->values();
        $correctAnswerIndex = $choices->search($correctAnswer);

        return [
            'id' => $word->id,
            'question' => __('mobile.antonym_question', ['word' => $word->word]),
            'choices' => $choices->toArray(),
            'correct_answer_index' => $correctAnswerIndex,
            'correct_answer' => $correctAnswer,
            'word' => $word->word,
            'question_type' => 'antonym',
        ];
    }

    /**
     * Calculate quiz score based on user answers.
     */
    public function calculateScore(array $quizData, array $userAnswers): array
    {
        $totalQuestions = count($quizData['questions']);
        $correctAnswers = 0;

        $results = [];
        foreach ($quizData['questions'] as $index => $question) {
            $userAnswer = $userAnswers[$index] ?? null;
            $isCorrect = $userAnswer === $question['correct_answer_index'];
            
            if ($isCorrect) {
                $correctAnswers++;
            }

            $results[] = [
                'question_id' => $question['id'],
                'user_answer' => $userAnswer,
                'correct_answer' => $question['correct_answer_index'],
                'is_correct' => $isCorrect,
            ];
        }

        $scorePercentage = $totalQuestions > 0 ? round(($correctAnswers / $totalQuestions) * 100, 2) : 0;

        return [
            'total_questions' => $totalQuestions,
            'correct_answers' => $correctAnswers,
            'score_percentage' => $scorePercentage,
            'results' => $results,
        ];
    }

    /**
     * Check if a book has enough content for quiz generation.
     */
    public function bookHasEnoughQuestionsForQuiz(Book $book): bool
    {
        return BookQuestion::where('book_id', $book->id)
            ->where('is_active', true)
            ->count() >= 10;
    }

    /**
     * Check if a book has enough content for vocabulary test generation.
     */
    public function bookHasEnoughWordsForVocabularyTest(Book $book): bool
    {
        return BookWord::where('book_id', $book->id)
            ->where('is_active', true)
            ->count() >= 10;
    }
}
