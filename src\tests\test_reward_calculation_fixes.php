<?php

/**
 * Test script for critical reward calculation system fixes
 * 
 * This script tests:
 * 1. Repeatable rewards being awarded only once per cycle period
 * 2. Day count calculations counting unique calendar days correctly
 * 
 * Run with: php tests/test_reward_calculation_fixes.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\{User, Reward, Task, RewardTask, UserReward, UserReadingLog, Book, EnumTaskType, EnumTaskCycle};
use App\Services\RewardCalculationService;

class RewardCalculationFixesTest
{
    private $rewardService;
    private $testUserId;
    private $testBookId;
    
    public function __construct()
    {
        $this->rewardService = new RewardCalculationService();
        echo "🧪 REWARD CALCULATION FIXES TEST SUITE\n";
        echo "=====================================\n\n";
    }
    
    public function runAllTests()
    {
        try {
            $this->setupTestData();
            
            echo "📋 Running Test Suite:\n";
            echo "1. Testing repeatable reward cycle boundaries\n";
            echo "2. Testing day count calculation accuracy\n";
            echo "3. Testing non-repeatable rewards still work\n\n";
            
            $this->testRepeatableRewardCycleBoundaries();
            $this->testDayCountCalculationAccuracy();
            $this->testNonRepeatableRewardsStillWork();
            
            echo "\n✅ ALL TESTS COMPLETED SUCCESSFULLY!\n";
            echo "🎉 Critical reward calculation fixes are working correctly.\n\n";
            
        } catch (Exception $e) {
            echo "\n❌ TEST FAILED: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        } finally {
            $this->cleanupTestData();
        }
    }
    
    private function setupTestData()
    {
        echo "🔧 Setting up test data...\n";
        
        // Create test user
        $this->testUserId = User::insertGetId([
            'name' => 'Test User Reward Fixes',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        // Create test book
        $this->testBookId = Book::insertGetId([
            'title' => 'Test Book for Reward Fixes',
            'author' => 'Test Author',
            'total_pages' => 100,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        echo "✓ Test data created (User ID: {$this->testUserId}, Book ID: {$this->testBookId})\n\n";
    }
    
    private function testRepeatableRewardCycleBoundaries()
    {
        echo "🔄 TEST 1: Repeatable Reward Cycle Boundaries\n";
        echo "============================================\n";
        
        // Create daily repeatable reward
        $dailyRewardId = $this->createTestReward('Daily Reading Reward', true);
        $dailyTaskId = $this->createTestTask(EnumTaskType::READ_pages, EnumTaskCycle::DAILY, 10);
        $this->linkRewardToTask($dailyRewardId, $dailyTaskId);
        
        // Create reading logs for today (multiple entries same day)
        $today = Carbon::today();
        $this->createReadingLog($today, 5); // 5 pages
        $this->createReadingLog($today, 6); // 6 pages (total 11 pages today)
        
        echo "📖 Created reading logs: 5 + 6 = 11 pages today (exceeds 10 page requirement)\n";
        
        // First reward check - should award
        $rewards1 = $this->rewardService->checkAndAwardUserRewards($this->testUserId);
        $dailyRewardCount1 = $this->countUserRewards($dailyRewardId);
        
        echo "🏆 First check: " . count($rewards1) . " rewards awarded\n";
        echo "📊 Daily reward count: {$dailyRewardCount1}\n";
        
        // Add more reading same day
        $this->createReadingLog($today, 10); // 10 more pages (total 21 pages today)
        
        // Second reward check - should NOT award again (same day)
        $rewards2 = $this->rewardService->checkAndAwardUserRewards($this->testUserId);
        $dailyRewardCount2 = $this->countUserRewards($dailyRewardId);
        
        echo "🏆 Second check (same day): " . count($rewards2) . " rewards awarded\n";
        echo "📊 Daily reward count: {$dailyRewardCount2}\n";
        
        // Test assertions
        if ($dailyRewardCount1 !== 1) {
            throw new Exception("Expected 1 daily reward after first check, got {$dailyRewardCount1}");
        }
        
        if ($dailyRewardCount2 !== 1) {
            throw new Exception("Expected 1 daily reward after second check (same day), got {$dailyRewardCount2}");
        }
        
        if (count($rewards2) > 0) {
            throw new Exception("Expected 0 rewards on second check (same day), got " . count($rewards2));
        }
        
        // Test next day - should award again
        $tomorrow = Carbon::tomorrow();
        $this->createReadingLog($tomorrow, 15); // 15 pages tomorrow
        
        $rewards3 = $this->rewardService->checkAndAwardUserRewards($this->testUserId);
        $dailyRewardCount3 = $this->countUserRewards($dailyRewardId);
        
        echo "🏆 Third check (next day): " . count($rewards3) . " rewards awarded\n";
        echo "📊 Daily reward count: {$dailyRewardCount3}\n";
        
        if ($dailyRewardCount3 !== 2) {
            throw new Exception("Expected 2 daily rewards after next day, got {$dailyRewardCount3}");
        }
        
        echo "✅ PASS: Daily repeatable reward awarded only once per day\n\n";
    }
    
    private function testDayCountCalculationAccuracy()
    {
        echo "📅 TEST 2: Day Count Calculation Accuracy\n";
        echo "========================================\n";
        
        // Create READ_DAYS reward (5 unique days)
        $daysRewardId = $this->createTestReward('5 Days Reading Reward', false);
        $daysTaskId = $this->createTestTask(EnumTaskType::read_days, EnumTaskCycle::TOTAL, 5);
        $this->linkRewardToTask($daysRewardId, $daysTaskId);
        
        // Create reading logs on same days (multiple per day)
        $dates = [
            Carbon::today()->subDays(4),
            Carbon::today()->subDays(4), // Same day again
            Carbon::today()->subDays(3),
            Carbon::today()->subDays(3), // Same day again
            Carbon::today()->subDays(2),
            Carbon::today()->subDays(1),
            Carbon::today(),
            Carbon::today(), // Same day again
        ];
        
        foreach ($dates as $date) {
            $this->createReadingLog($date, 5);
        }
        
        echo "📖 Created " . count($dates) . " reading log entries across 5 unique days\n";
        
        // Check rewards - should award based on unique days (5), not total entries (8)
        $rewards = $this->rewardService->checkAndAwardUserRewards($this->testUserId);
        $daysRewardCount = $this->countUserRewards($daysRewardId);
        
        echo "🏆 Rewards awarded: " . count($rewards) . "\n";
        echo "📊 Days reward count: {$daysRewardCount}\n";
        
        if ($daysRewardCount !== 1) {
            throw new Exception("Expected 1 days reward (5 unique days), got {$daysRewardCount}");
        }
        
        echo "✅ PASS: Day count calculation counts unique calendar days correctly\n\n";
    }
    
    private function testNonRepeatableRewardsStillWork()
    {
        echo "🎯 TEST 3: Non-Repeatable Rewards Still Work\n";
        echo "===========================================\n";
        
        // Create non-repeatable reward
        $oneTimeRewardId = $this->createTestReward('One Time Achievement', false);
        $oneTimeTaskId = $this->createTestTask(EnumTaskType::read_pages, EnumTaskCycle::TOTAL, 50);
        $this->linkRewardToTask($oneTimeRewardId, $oneTimeTaskId);
        
        // Add enough reading to trigger reward
        $this->createReadingLog(Carbon::today(), 50);
        
        echo "📖 Created reading log: 50 pages (meets requirement)\n";
        
        // First check - should award
        $rewards1 = $this->rewardService->checkAndAwardUserRewards($this->testUserId);
        $oneTimeCount1 = $this->countUserRewards($oneTimeRewardId);
        
        echo "🏆 First check: " . count($rewards1) . " rewards awarded\n";
        echo "📊 One-time reward count: {$oneTimeCount1}\n";
        
        // Second check - should NOT award again
        $rewards2 = $this->rewardService->checkAndAwardUserRewards($this->testUserId);
        $oneTimeCount2 = $this->countUserRewards($oneTimeRewardId);
        
        echo "🏆 Second check: " . count($rewards2) . " rewards awarded\n";
        echo "📊 One-time reward count: {$oneTimeCount2}\n";
        
        if ($oneTimeCount1 !== 1) {
            throw new Exception("Expected 1 one-time reward after first check, got {$oneTimeCount1}");
        }
        
        if ($oneTimeCount2 !== 1) {
            throw new Exception("Expected 1 one-time reward after second check, got {$oneTimeCount2}");
        }
        
        echo "✅ PASS: Non-repeatable rewards work correctly\n\n";
    }
    
    // Helper methods
    private function createTestReward(string $name, bool $repeatable): int
    {
        return Reward::insertGetId([
            'name' => $name,
            'description' => 'Test reward for fixes',
            'reward_type' => 1, // Badge
            'repeatable' => $repeatable,
            'active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
    
    private function createTestTask(int $taskType, int $taskCycle, int $taskValue): int
    {
        return Task::insertGetId([
            'task_type_id' => $taskType,
            'task_cycle_id' => $taskCycle,
            'task_value' => $taskValue,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
    
    private function linkRewardToTask(int $rewardId, int $taskId): void
    {
        RewardTask::insert([
            'reward_id' => $rewardId,
            'task_id' => $taskId,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
    
    private function createReadingLog(Carbon $date, int $pages): void
    {
        UserReadingLog::insert([
            'user_id' => $this->testUserId,
            'book_id' => $this->testBookId,
            'log_date' => $date->format('Y-m-d'),
            'pages_read' => $pages,
            'reading_duration' => $pages * 2, // 2 minutes per page
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
    
    private function countUserRewards(int $rewardId): int
    {
        return UserReward::where('user_id', $this->testUserId)
            ->where('reward_id', $rewardId)
            ->count();
    }
    
    private function cleanupTestData()
    {
        echo "🧹 Cleaning up test data...\n";
        
        if ($this->testUserId) {
            UserReward::where('user_id', $this->testUserId)->delete();
            UserReadingLog::where('user_id', $this->testUserId)->delete();
            User::where('id', $this->testUserId)->delete();
        }
        
        if ($this->testBookId) {
            Book::where('id', $this->testBookId)->delete();
        }
        
        // Clean up test rewards and tasks
        $testRewards = Reward::where('name', 'like', '%Test%')->get();
        foreach ($testRewards as $reward) {
            RewardTask::where('reward_id', $reward->id)->delete();
            $reward->delete();
        }
        
        Task::where('created_at', '>', Carbon::now()->subMinutes(5))->delete();
        
        echo "✓ Test data cleaned up\n";
    }
}

// Run the tests
$test = new RewardCalculationFixesTest();
$test->runAllTests();
