<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\BookType;
use App\Models\Publisher;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

class BookTypeSelectionRedirectTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected BookType $bookType;
    protected Publisher $publisher;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'username' => 'testuser',
            'password' => bcrypt('password'),
        ]);

        // Create test book type
        $this->bookType = BookType::create([
            'name' => 'Test Book Type',
            'description' => 'Test book type description',
            'thumbnail' => 'test-thumbnail.jpg',
            'created_by' => $this->user->id,
        ]);

        // Create test publisher
        $this->publisher = Publisher::create([
            'name' => 'Test Publisher',
            'created_by' => $this->user->id,
        ]);
    }

    /** @test */
    public function redirect_event_is_dispatched_after_successful_book_creation()
    {
        $this->actingAs($this->user);

        $bookData = [
            'name' => 'Redirect Test Book',
            'isbn' => '9783333333333',
            'author' => ['Redirect Author'],
            'publisher' => 'Redirect Publisher',
            'year' => 2023,
            'page_count' => 180,
            'source' => 'Test Source',
        ];

        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->set('selectedBookTypeId', $this->bookType->id)
            ->call('createBook');

        // Check that redirect event was dispatched
        $component->assertDispatched('redirect-after-delay');

        // Check that success message is set
        $component->assertSet('successMessage', __('mobile.book_created_success'));
    }

    /** @test */
    public function component_handles_validation_errors_gracefully()
    {
        $this->actingAs($this->user);

        $bookData = [
            'name' => 'Test Book',
            'isbn' => '9781111111111',
            'author' => ['Test Author'],
            'publisher' => 'Test Publisher',
            'year' => 2023,
            'page_count' => 100,
            'source' => 'Test Source',
        ];

        // Test without selecting book type (should fail validation)
        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->call('createBook');

        $component->assertHasErrors(['selectedBookTypeId']);
        $component->assertSet('isLoading', false);
    }

    /** @test */
    public function component_loads_with_correct_initial_state()
    {
        $this->actingAs($this->user);

        $bookData = [
            'name' => 'Initial State Test Book',
            'isbn' => '9784444444444',
            'author' => ['Initial Author'],
            'publisher' => 'Initial Publisher',
            'year' => 2023,
            'page_count' => 200,
            'source' => 'Test Source',
        ];

        $component = Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ]);

        // Check initial state
        $component->assertSet('isLoading', false)
                  ->assertSet('selectedBookTypeId', null)
                  ->assertSet('manualPageCount', null)
                  ->assertSet('errorMessage', '')
                  ->assertSet('successMessage', '');

        // Check that book types are loaded
        $this->assertNotEmpty($component->get('bookTypes'));
        
        // Check that book data is set correctly
        $this->assertEquals($bookData, $component->get('bookData'));
    }
}
