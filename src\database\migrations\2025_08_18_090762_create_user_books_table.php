<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_books', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->foreignId('challenge_task_id')->nullable()->constrained('challenge_tasks')->onDelete('set null');
            

            // Add indexes for performance
            $table->index('user_id');
            $table->index('book_id');
            $table->index('start_date');
            $table->index('end_date');
            $table->index(['user_id', 'book_id']);
            $table->index(['user_id', 'book_id', 'start_date'], 'user_book_session_index');
            $table->index(['challenge_task_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_books');
    }
};
