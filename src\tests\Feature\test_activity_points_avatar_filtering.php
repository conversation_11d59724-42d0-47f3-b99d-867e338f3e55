<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Avatar;
use App\Models\UserAvatar;
use App\Models\UserPoint;

try {
    echo "=== TESTING ACTIVITY POINTS AVATAR FILTERING ===\n\n";
    
    // Get test user and avatars
    $user = User::first();
    $lowPointAvatar = Avatar::where('required_points', '<=', 50)->first();
    $highPointAvatar = Avatar::where('required_points', '>', 100)->first();
    
    if (!$user || !$lowPointAvatar || !$highPointAvatar) {
        echo "❌ Missing test data. Need user and avatars with different point requirements\n";
        exit;
    }
    
    echo "Test User: {$user->name}\n";
    echo "Low Point Avatar: {$lowPointAvatar->name} ({$lowPointAvatar->required_points} pts)\n";
    echo "High Point Avatar: {$highPointAvatar->name} ({$highPointAvatar->required_points} pts)\n\n";
    
    // Clean up existing data
    UserAvatar::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    
    // Test Case 1: Check initial state (no points)
    echo "TEST CASE 1: Initial state (no points)\n";
    $totalPoints = $user->total_points;
    $activityPoints = $user->activity_points;
    $canSelectLow = $user->canSelectAvatar($lowPointAvatar->id);
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    
    echo "- Total Points: {$totalPoints}\n";
    echo "- Activity Points: {$activityPoints}\n";
    echo "- Can Select Low Point Avatar: " . ($canSelectLow ? 'YES' : 'NO') . "\n";
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($totalPoints === 0 && $activityPoints === 0 && !$canSelectLow && !$canSelectHigh ? "PASS" : "FAIL") . "\n\n";
    
    // Test Case 2: Add reading points (should NOT unlock avatars)
    echo "TEST CASE 2: Add reading points (should NOT unlock avatars)\n";
    UserPoint::create([
        'point_date' => now(),
        'user_id' => $user->id,
        'book_id' => null,
        'source_id' => 1,
        'point_type' => UserPoint::POINT_TYPE_PAGE, // Reading points (value 1)
        'points' => 100,
    ]);
    
    $user->refresh();
    $totalPoints = $user->total_points;
    $activityPoints = $user->activity_points;
    $canSelectLow = $user->canSelectAvatar($lowPointAvatar->id);
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    
    echo "- Total Points: {$totalPoints}\n";
    echo "- Activity Points: {$activityPoints}\n";
    echo "- Can Select Low Point Avatar: " . ($canSelectLow ? 'YES' : 'NO') . "\n";
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($totalPoints === 100 && $activityPoints === 0 && !$canSelectLow && !$canSelectHigh ? "PASS" : "FAIL") . " - Reading points should not unlock avatars\n\n";
    
    // Test Case 3: Add activity points (should unlock avatars)
    echo "TEST CASE 3: Add activity points (should unlock avatars)\n";
    UserPoint::create([
        'point_date' => now(),
        'user_id' => $user->id,
        'book_id' => null,
        'source_id' => 1,
        'point_type' => UserPoint::POINT_TYPE_ACTIVITY, // Activity points (value 2)
        'points' => 75,
    ]);
    
    $user->refresh();
    $totalPoints = $user->total_points;
    $activityPoints = $user->activity_points;
    $canSelectLow = $user->canSelectAvatar($lowPointAvatar->id);
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    
    echo "- Total Points: {$totalPoints}\n";
    echo "- Activity Points: {$activityPoints}\n";
    echo "- Can Select Low Point Avatar: " . ($canSelectLow ? 'YES' : 'NO') . "\n";
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($totalPoints === 175 && $activityPoints === 75 && $canSelectLow && !$canSelectHigh ? "PASS" : "FAIL") . " - Activity points should unlock low point avatar only\n\n";
    
    // Test Case 4: Test avatar selection with activity points
    echo "TEST CASE 4: Test avatar selection with activity points\n";
    $selectionResult = $user->selectAvatar($lowPointAvatar->id);
    $currentAvatar = $user->getCurrentAvatar();
    
    echo "- Selection Result: " . ($selectionResult ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Current Avatar: " . ($currentAvatar ? $currentAvatar->name : 'None') . "\n";
    echo "✅ " . ($selectionResult && $currentAvatar && $currentAvatar->id === $lowPointAvatar->id ? "PASS" : "FAIL") . " - Should be able to select avatar with sufficient activity points\n\n";
    
    // Test Case 5: Add more activity points to unlock high point avatar
    echo "TEST CASE 5: Add more activity points to unlock high point avatar\n";
    UserPoint::create([
        'point_date' => now(),
        'user_id' => $user->id,
        'book_id' => null,
        'source_id' => 2,
        'point_type' => UserPoint::POINT_TYPE_ACTIVITY, // Activity points (value 2)
        'points' => 50,
    ]);
    
    $user->refresh();
    $totalPoints = $user->total_points;
    $activityPoints = $user->activity_points;
    $canSelectHigh = $user->canSelectAvatar($highPointAvatar->id);
    
    echo "- Total Points: {$totalPoints}\n";
    echo "- Activity Points: {$activityPoints}\n";
    echo "- Can Select High Point Avatar: " . ($canSelectHigh ? 'YES' : 'NO') . "\n";
    echo "✅ " . ($totalPoints === 225 && $activityPoints === 125 && $canSelectHigh ? "PASS" : "FAIL") . " - Should be able to select high point avatar with sufficient activity points\n\n";
    
    // Test Case 6: Test available and locked avatars based on activity points
    echo "TEST CASE 6: Test available and locked avatars based on activity points\n";
    $availableAvatars = $user->getAvailableAvatars();
    $lockedAvatars = $user->getLockedAvatars();
    
    echo "- Available Avatars: {$availableAvatars->count()}\n";
    echo "- Locked Avatars: {$lockedAvatars->count()}\n";
    
    foreach ($availableAvatars as $avatar) {
        echo "  - Available: {$avatar->name} ({$avatar->required_points} pts) - Activity Points: {$activityPoints}\n";
    }
    
    foreach ($lockedAvatars as $avatar) {
        echo "  - Locked: {$avatar->name} ({$avatar->required_points} pts) - Activity Points: {$activityPoints}\n";
    }
    
    echo "✅ PASS - Avatar lists generated based on activity points only\n\n";
    
    // Test Case 7: Add manual points (should NOT affect avatar selection)
    echo "TEST CASE 7: Add manual points (should NOT affect avatar selection)\n";
    UserPoint::create([
        'point_date' => now(),
        'user_id' => $user->id,
        'book_id' => null,
        'source_id' => null,
        'point_type' => UserPoint::POINT_TYPE_MANUAL, // Manual points (value 4)
        'points' => 1000,
    ]);
    
    $user->refresh();
    $totalPoints = $user->total_points;
    $activityPoints = $user->activity_points;
    $availableAvatarsAfter = $user->getAvailableAvatars();
    
    echo "- Total Points: {$totalPoints}\n";
    echo "- Activity Points: {$activityPoints}\n";
    echo "- Available Avatars Count: {$availableAvatarsAfter->count()}\n";
    echo "✅ " . ($totalPoints === 1225 && $activityPoints === 125 && 
                 $availableAvatarsAfter->count() === $availableAvatars->count() ? "PASS" : "FAIL") . 
                 " - Manual points should not affect avatar availability\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    UserAvatar::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Activity points avatar filtering tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
