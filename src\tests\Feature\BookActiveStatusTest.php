<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Book;
use App\Models\User;
use App\Models\UserReadingLog;
use App\Models\UserActivity;
use App\Models\Activity;
use App\Models\Publisher;
use App\Models\BookType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class BookActiveStatusTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $publisher;
    protected $bookType;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->publisher = Publisher::factory()->create();
        $this->bookType = BookType::factory()->create();
    }

    /** @test */
    public function book_is_active_by_default_when_all_required_fields_are_present()
    {
        $book = Book::create([
            'name' => 'Test Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $this->assertTrue($book->active);
        $this->assertTrue($book->isActive());
        $this->assertFalse($book->isInactive());
    }

    /** @test */
    public function book_is_inactive_when_book_type_id_is_null()
    {
        $book = Book::create([
            'name' => 'Test Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => null, // Missing book type
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $this->assertFalse($book->active);
        $this->assertFalse($book->isActive());
        $this->assertTrue($book->isInactive());
    }

    /** @test */
    public function book_is_inactive_when_page_count_is_null()
    {
        $book = Book::create([
            'name' => 'Test Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => null, // Missing page count
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $this->assertFalse($book->active);
        $this->assertFalse($book->isActive());
        $this->assertTrue($book->isInactive());
    }

    /** @test */
    public function book_is_inactive_when_both_book_type_id_and_page_count_are_null()
    {
        $book = Book::create([
            'name' => 'Test Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => null, // Missing book type
            'page_count' => null, // Missing page count
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $this->assertFalse($book->active);
        $this->assertFalse($book->isActive());
        $this->assertTrue($book->isInactive());
    }

    /** @test */
    public function active_scope_filters_only_active_books()
    {
        // Create active book
        $activeBook = Book::create([
            'name' => 'Active Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        // Create inactive book
        $inactiveBook = Book::create([
            'name' => 'Inactive Book',
            'isbn' => '1234567890124',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => null, // This makes it inactive
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $activeBooks = Book::active()->get();
        $inactiveBooks = Book::inactive()->get();

        $this->assertCount(1, $activeBooks);
        $this->assertCount(1, $inactiveBooks);
        $this->assertEquals($activeBook->id, $activeBooks->first()->id);
        $this->assertEquals($inactiveBook->id, $inactiveBooks->first()->id);
    }

    /** @test */
    public function cannot_create_reading_log_for_inactive_book()
    {
        $inactiveBook = Book::create([
            'name' => 'Inactive Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => null, // This makes it inactive
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $this->assertFalse($inactiveBook->canCreateReadingLogs());

        // Test validation rules
        $rules = UserReadingLog::validationRules();
        $validator = validator([
            'user_id' => $this->user->id,
            'book_id' => $inactiveBook->id,
            'log_date' => now()->format('Y-m-d'),
            'pages_read' => 10,
        ], $rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('book_id', $validator->errors()->toArray());
    }

    /** @test */
    public function cannot_create_activity_for_inactive_book()
    {
        $inactiveBook = Book::create([
            'name' => 'Inactive Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => null, // This makes it inactive
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $activity = Activity::factory()->create();

        $this->assertFalse($inactiveBook->canCreateActivities());

        // Test validation rules
        $rules = UserActivity::validationRules();
        $validator = validator([
            'user_id' => $this->user->id,
            'book_id' => $inactiveBook->id,
            'activity_id' => $activity->id,
            'activity_date' => now()->format('Y-m-d'),
            'status' => 0,
        ], $rules);

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('book_id', $validator->errors()->toArray());
    }

    /** @test */
    public function can_create_reading_log_for_active_book()
    {
        $activeBook = Book::create([
            'name' => 'Active Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $this->assertTrue($activeBook->canCreateReadingLogs());

        // Test validation rules
        $rules = UserReadingLog::validationRules();
        $validator = validator([
            'user_id' => $this->user->id,
            'book_id' => $activeBook->id,
            'log_date' => now()->format('Y-m-d'),
            'pages_read' => 10,
        ], $rules);

        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function can_create_activity_for_active_book()
    {
        $activeBook = Book::create([
            'name' => 'Active Book',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $activity = Activity::factory()->create();

        $this->assertTrue($activeBook->canCreateActivities());

        // Test validation rules
        $rules = UserActivity::validationRules();
        $validator = validator([
            'user_id' => $this->user->id,
            'book_id' => $activeBook->id,
            'activity_id' => $activity->id,
            'activity_date' => now()->format('Y-m-d'),
            'status' => 0,
        ], $rules);

        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function inactive_reason_shows_missing_fields()
    {
        $bookMissingType = Book::create([
            'name' => 'Book Missing Type',
            'isbn' => '1234567890123',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => null,
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $bookMissingPages = Book::create([
            'name' => 'Book Missing Pages',
            'isbn' => '1234567890124',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => null,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $bookMissingBoth = Book::create([
            'name' => 'Book Missing Both',
            'isbn' => '1234567890125',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => null,
            'page_count' => null,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $this->assertStringContainsString('Missing book type', $bookMissingType->inactive_reason);
        $this->assertStringContainsString('Missing page count', $bookMissingPages->inactive_reason);
        $this->assertStringContainsString('Missing book type', $bookMissingBoth->inactive_reason);
        $this->assertStringContainsString('Missing page count', $bookMissingBoth->inactive_reason);
    }
}
