<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\MoonShine\Pages\PrivacyAgreementPage;
use App\MoonShine\Pages\SendNotificationPage;
use App\Http\Controllers\MobileController;

// Privacy Agreement Routes
Route::middleware(['web'])->group(function () {
    Route::get('/admin/privacy-agreement', [PrivacyAgreementPage::class, 'handle'])
        ->name('moonshine.privacy-agreement');

    Route::post('/admin/process-privacy-consent', [PrivacyAgreementPage::class, 'processPrivacyConsent'])
        ->name('moonshine.process-privacy-consent');
});

// FCM Notification Routes
Route::middleware(['moonshine'])->group(function () {
    Route::post('/admin-send-notification', [SendNotificationPage::class, 'sendNotification'])
        ->name('moonshine.send-notification.post');
});




$host = request()->getSchemeAndHttpHost();

if($host == env('APP_MOBILE_URL')) {
    Route::middleware(['web'])->name('mobile.')->group(function () {

        // Public routes (no authentication)
        Route::get('/', [MobileController::class, 'splash'])->name('splash');
        Route::get('/login', [MobileController::class, 'showLogin'])->name('login');
        Route::post('/login', [MobileController::class, 'login']);
        Route::get('/previous-usernames', [MobileController::class, 'getPreviousUsernames'])->name('previous-usernames');

        // Authenticated routes
        Route::middleware(['App\Http\Middleware\MobileAuthentication'])->group(function () {
            Route::get('/home', [MobileController::class, 'home'])->name('home');
            Route::get('/avatar-selection', [MobileController::class, 'avatarSelection'])->name('avatar-selection');
            Route::get('/welcome', [MobileController::class, 'welcome'])->name('welcome');
            Route::post('/logout', [MobileController::class, 'logout'])->name('logout');

            // Book management routes (will be handled by Livewire components)
            Route::get('/books', function () { return view('mobile.books'); })->name('books');
            Route::get('/books/add', function () { return view('mobile.books.add'); })->name('books.add');
            Route::get('/books/{book}/log', function ($book) { return view('mobile.books.log', compact('book')); })->name('books.log');
            Route::get('/books/{book}/activities', function ($book) { return view('mobile.books.activities', compact('book')); })->name('books.activities');

            // Activity routes (will be handled by Livewire components)
            Route::get('/books/{book}/activities/{activity}/writing', function ($book, $activity) {
                $mode = request('mode', 'create');
                return view('mobile.activities.writing', compact('book', 'activity', 'mode'));
            })->name('activities.writing');
            Route::get('/books/{book}/activities/{activity}/rating', function ($book, $activity) {
                $mode = request('mode', 'create');
                return view('mobile.activities.rating', compact('book', 'activity', 'mode'));
            })->name('activities.rating');
            Route::get('/books/{book}/activities/{activity}/upload', function ($book, $activity) {
                $mode = request('mode', 'create');
                return view('mobile.activities.upload', compact('book', 'activity', 'mode'));
            })->name('activities.upload');
            Route::get('/books/{book}/activities/{activity}/test', function ($book, $activity) {
                $mode = request('mode', 'create');
                return view('mobile.activities.test', compact('book', 'activity', 'mode'));
            })->name('activities.test');
            Route::get('/activities/pending', function () { return view('mobile.activities.pending'); })->name('activities.pending');

            // Badge unlocked screen
            Route::get('/badge-unlocked', function () { return view('mobile.badge-unlocked'); })->name('badge-unlocked');

            // Friends and profile routes
            Route::get('/friends', function () { return view('mobile.friends'); })->name('friends');
            Route::get('/me', function () { return view('mobile.me'); })->name('me');
            Route::get('/choose-avatar', function () { return view('mobile.choose-avatar'); })->name('choose-avatar');
            Route::get('/my-rewards', function () { return view('mobile.my-rewards'); })->name('my-rewards');

            // Messages routes
            Route::get('/messages', function () { return view('mobile.messages'); })->name('messages');
            Route::get('/messages/{messageRecipient}', function ($messageRecipient) {
                return view('mobile.message-detail', compact('messageRecipient'));
            })->name('message-detail');

            // Teacher-specific routes
            Route::prefix('teacher')->name('teacher.')->group(function () {
                Route::get('/home', [MobileController::class, 'teacherHome'])->name('home');
                Route::get('/last-24-hours', function () { return view('mobile.teacher.last-24-hours'); })->name('last-24-hours');
                Route::get('/activity-review/{userActivity}', function ($userActivity) {
                    return view('mobile.teacher.activity-review', compact('userActivity'));
                })->name('activity-review');
            });

            Route::post('/api/fcm/register-token', [MobileController::class, 'registerFCMToken'] )->name('api.fcm.register-token');
        });
    });
}
