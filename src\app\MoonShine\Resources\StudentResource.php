<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Role;
use App\Models\User;
use App\Models\UserClass;
use App\Models\UserSchool;
use App\MoonShine\Pages\Student\StudentIndexPage;
use App\MoonShine\Pages\Student\StudentFormPage;
use App\MoonShine\Pages\Student\StudentDetailPage;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\QueryTags\QueryTag;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Support\AlpineJs;
use MoonShine\Support\Enums\JsEvent;
use MoonShine\Support\Enums\SortDirection;
use MoonShine\UI\Components\CardsBuilder;
use MoonShine\UI\Components\Metrics\Wrapped\ValueMetric;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\Password;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;
use MoonShine\Laravel\MoonShineRequest;
use MoonShine\Support\Enums\ToastType;

#[Icon('academic-cap')]
class StudentResource extends BaseResource
{
    use WithRolePermissions; 

    protected string $model = User::class;

    protected string $column = 'name';

    protected string $sortColumn = 'name';
    protected SortDirection $sortDirection = SortDirection::ASC;

    protected array $with = ['userAvatar.avatar', 'activeUserSchools.school', 'activeUserClasses.schoolClass'];

    public function getTitle(): string
    {
        return __('admin.students');
    }

    public function getSubTitle(): string
    {
        return __('admin.student_management_subtitle');
    }

    /**
     * Override pages to use custom implementations.
     */
    public function pages(): array
    {
        return [
            StudentIndexPage::class,
            StudentFormPage::class,
            StudentDetailPage::class,
        ];
    }

    /**
     * Query tags for school filtering if user has multiple schools.
     */
    public function queryTags(): array
    {
        $user = auth('moonshine')->user();
        
        if (!$user || !($user instanceof User)) {
            return [];
        }

        $activeSchools = $user->activeUserSchools()->with('school')->get();
        
        if ($activeSchools->count() <= 1) {
            return [];
        }

        $tags = [];
        foreach ($activeSchools as $userSchool) {
            $tags[] = QueryTag::make(
                $userSchool->school->name,
                static fn(Builder $query) => $query->whereHas('activeUserSchools', function ($q) use ($userSchool) {
                    $q->where('school_id', $userSchool->school_id);
                })
            );
        }

        return $tags;
    }

    /**
     * Metrics for student overview.
     */
    public function metrics(): array
    {
        return [
            
            ValueMetric::make(__('admin.total_students'))
                ->value(fn() => $this->getQuery()->count())
                ->columnSpan(4),

            ValueMetric::make(__('admin.active_readers_today'))
                ->value(fn() => $this->getQuery()->withCount(['readingLogs' => function ($q) {
                    $q->whereDate('log_date', today());
                }])->get()->sum('reading_logs_count'))
                ->columnSpan(4),

            
            ValueMetric::make(__('admin.books_completed_this_month'))
                ->value(fn() => $this->getQuery()->withCount(['readingLogs' => function ($q) {
                    $q->where('book_completed', true)
                      ->whereMonth('log_date', now()->month)
                      ->whereYear('log_date', now()->year);
                }])->get()->sum('reading_logs_count'))
                ->columnSpan(4),
                
        ];
    }

    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        if (!$builder instanceof EloquentBuilder) {
            return $builder;
        }
        return $builder->forCurrentUser();
    }

    public function rules(mixed $item): array
    {
        return [
            'username' => ['required', 'string', 'max:255', 'unique:users,username,' . ($item?->id ?? 'NULL')],
            'name' => ['required', 'string', 'max:255'],
//            'email' => ['required', 'email', 'max:255', 'unique:users,email,' . ($item?->id ?? 'NULL')],
            'title' => ['nullable', 'string', 'max:255'],
            
            // Password must contain at least 6 characters, at least one letter, and at least one number
            'password' => !$item             
                            ? ['required', 'string', Password::min(6)->letters()->numbers() ]  
                            : ['nullable', 'string', Password::min(6)->letters()->numbers() ],
            'password_confirmation' => !$item ? ['required', 'same:password'] : ['nullable', 'same:password'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name', 'username', 'email'];
    }

     public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();

        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }

    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), []) 
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
            ->title('name')
            ->subtitle('username')
            ->url(fn($user) => $this->getDetailPageUrl($user->getKey()))
            ->thumbnail(fn($user) => asset('storage/' . $user->getAvatarDisplayImage()));
//            ->buttons([
//                        $this->getDetailButton(),
//                        $this->getEditButton(isAsync: $this->isAsync()),
//            ]);
//            ->buttons($this->getIndexButtons());
    }
    
    protected function beforeCreating(mixed $item): Model
    {
        // trim and lowercase username, assign auto-generated email address
        $username = request('username');
        $username = strtolower(trim($username));
        $item->email = $username . '@' . config('app.domain');
        $item->username = $username;

        return $item;
    }

    protected function afterCreated(mixed $item): mixed
    {
        if ($item instanceof User) {
            // Assign student role
            $studentRole = Role::where('name', 'student')->first();
            if ($studentRole && method_exists($item, 'assignRole')) {
                $item->assignRole($studentRole);
            }

            // Create school assignment if provided
            $user = auth('moonshine')->user();

            if ($user instanceof User) {
                $defaultSchool = $user->getDefaultSchool();

                if ($defaultSchool) {
                    UserSchool::create([
                        'user_id' => $item->id,
                        'school_id' => $defaultSchool->school_id,
                        'role_id' => $studentRole?->id,
                        'active' => true,
                        'default' => true,
                        'created_by' => $user->id,
                    ]);
                }

                // Create class assignment if provided
                $defaultClass = $user->getDefaultClass();

                if ($defaultClass) {
                    UserClass::create([
                        'user_id' => $item->id,
                        'class_id' => $defaultClass->class_id,
                        'school_id' => $defaultClass->school_id,
                        'active' => true,
                        'default' => true,
                        'created_by' => $user->id,
                    ]);
                }
            }
        }

        return $item;
    }
    
    public function checkUsername(MoonShineRequest $request): MoonShineJsonResponse
    {
        $username = $request->input('username');
        if (empty($username)) {
            return MoonShineJsonResponse::make()->toast(__('admin.enter_username'), ToastType::ERROR);
        }
        // trim and lowercase username
        $username = strtolower(trim($username));

        $user = User::where('username', $username)->first();
        if ($user) {
            return MoonShineJsonResponse::make()->toast(__('admin.username_exists'), ToastType::ERROR);
        }
        return MoonShineJsonResponse::make()->toast(__('admin.username_available'), ToastType::SUCCESS);
    }

}
