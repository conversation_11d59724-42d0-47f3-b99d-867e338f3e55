<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\AuthActivityLogService;
use Illuminate\Console\Command;
use Spatie\Activitylog\Models\Activity;

/**
 * Console command to view authentication activity logs.
 * 
 * Usage examples:
 * php artisan auth:logs --user=1
 * php artisan auth:logs --stats
 * php artisan auth:logs --failed --limit=20
 */
class ViewAuthLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:logs 
                            {--user= : Show logs for specific user ID}
                            {--stats : Show authentication statistics}
                            {--failed : Show only failed attempts}
                            {--limit=20 : Number of records to show}
                            {--days=7 : Number of days to look back}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'View authentication activity logs';

    /**
     * The auth activity log service.
     */
    protected AuthActivityLogService $authLogService;

    /**
     * Create a new command instance.
     */
    public function __construct(AuthActivityLogService $authLogService)
    {
        parent::__construct();
        $this->authLogService = $authLogService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $userId = $this->option('user');
        $showStats = $this->option('stats');
        $showFailed = $this->option('failed');
        $limit = (int) $this->option('limit');
        $days = (int) $this->option('days');

        if ($showStats) {
            $this->showStatistics($days);
            return 0;
        }

        if ($userId) {
            $this->showUserLogs($userId, $limit, $showFailed);
            return 0;
        }

        if ($showFailed) {
            $this->showFailedAttempts($limit);
            return 0;
        }

        $this->showRecentLogs($limit);
        return 0;
    }

    /**
     * Show authentication statistics.
     */
    private function showStatistics(int $days): void
    {
        $this->info("Authentication Statistics (Last {$days} days)");
        $this->line('');

        $stats = $this->authLogService->getSystemAuthStats($days);

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Logins', $stats['total_logins']],
                ['Total Failures', $stats['total_failures']],
                ['Total Registrations', $stats['total_registrations']],
                ['Success Rate', $stats['success_rate'] . '%'],
                ['Unique Users', $stats['unique_users']],
                ['Unique IPs', $stats['unique_ips']],
                ['Daily Average Logins', $stats['daily_average_logins']],
            ]
        );
    }

    /**
     * Show logs for a specific user.
     */
    private function showUserLogs(string $userId, int $limit, bool $failedOnly): void
    {
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found.");
            return;
        }

        $this->info("Authentication logs for user: {$user->username} (ID: {$user->id})");
        $this->line('');

        if ($failedOnly) {
            $activities = $this->authLogService->getFailedLoginAttempts($user, $limit);
            $this->info("Failed login attempts:");
        } else {
            $activities = $this->authLogService->getUserAuthActivities($user, $limit);
            $this->info("All authentication activities:");
        }

        $this->displayActivities($activities);

        // Show user stats
        $this->line('');
        $stats = $this->authLogService->getUserLoginStats($user, 30);
        $this->info("User Statistics (Last 30 days):");
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Logins', $stats['total_logins']],
                ['Total Failures', $stats['total_failures']],
                ['Success Rate', $stats['success_rate'] . '%'],
                ['Unique IPs', $stats['unique_ips']],
                ['Last Login', $stats['last_login'] ? $stats['last_login']->format('Y-m-d H:i:s') : 'Never'],
            ]
        );
    }

    /**
     * Show failed authentication attempts.
     */
    private function showFailedAttempts(int $limit): void
    {
        $this->info("Recent Failed Authentication Attempts");
        $this->line('');

        $activities = Activity::where('log_name', 'auth')
            ->where('event', 'failed')
            ->latest()
            ->limit($limit)
            ->get();

        $this->displayActivities($activities);
    }

    /**
     * Show recent authentication logs.
     */
    private function showRecentLogs(int $limit): void
    {
        $this->info("Recent Authentication Activities");
        $this->line('');

        $activities = Activity::where('log_name', 'auth')
            ->latest()
            ->limit($limit)
            ->get();

        $this->displayActivities($activities);
    }

    /**
     * Display activities in a table format.
     */
    private function displayActivities($activities): void
    {
        if ($activities->isEmpty()) {
            $this->warn('No activities found.');
            return;
        }

        $tableData = [];
        foreach ($activities as $activity) {
            $properties = $activity->properties ?? collect();
            
            $tableData[] = [
                $activity->created_at ? $activity->created_at->format('Y-m-d H:i:s') : 'N/A',
                $activity->event,
                $activity->subject ? $activity->subject->username ?? $activity->subject->email : 'N/A',
                $properties->get('ip_address', 'N/A'),
                $properties->get('guard', 'N/A'),
                $activity->description,
            ];
        }

        $this->table(
            ['Timestamp', 'Event', 'User', 'IP Address', 'Guard', 'Description'],
            $tableData
        );
    }
}
