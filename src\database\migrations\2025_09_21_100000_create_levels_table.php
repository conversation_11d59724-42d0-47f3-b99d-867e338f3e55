<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('levels', function (Blueprint $table) {
            $table->id();
            $table->integer('nr')->unique()->comment('Level number, incremental starting from 1');
            $table->string('name')->comment('Level name/title');
            $table->string('image')->nullable()->comment('Optional level badge/icon image path');
            $table->integer('books_count')->default(0)->comment('Minimum books required for this level');
            $table->integer('page_points')->default(0)->comment('Minimum page points required for this level');
            $table->boolean('all_required')->default(true)->comment('true = both books_count AND page_points must be met, false = either condition can trigger level up');

            // Add indexes for performance
            $table->index('nr');
            $table->index(['books_count', 'page_points']);
            $table->index('all_required');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('levels');
    }
};
