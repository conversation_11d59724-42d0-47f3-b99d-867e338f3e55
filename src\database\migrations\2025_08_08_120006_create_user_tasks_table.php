<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_tasks', function (Blueprint $table) {
            $table->id();
            
            // Task type to distinguish between challenge, and standalone tasks
            // 0 = standalone task, 1 = challenge task 
            $table->integer('task_type')->default(0)->comment('0-Standalone, 1-Challenge');
            
            // Foreign keys - nullable to support different task types
            $table->foreignId('challenge_task_id')->nullable()->constrained('challenge_tasks')->onDelete('cascade');
            $table->foreignId('task_id')->nullable()->constrained('tasks')->onDelete('cascade'); // For standalone tasks
            
            // Common fields from both source tables
            $table->foreignId('team_id')->nullable()->constrained('teams')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('cascade');
            
            // Date fields
            $table->timestamp('assign_date')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->boolean('completed')->default(false);
            $table->timestamp('complete_date')->nullable();
            
            // Audit field
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');

            // Indexes for performance
            $table->index(['task_type', 'user_id', 'completed']);
            $table->index(['challenge_task_id', 'user_id']);
            $table->index(['task_id', 'user_id']); // For standalone tasks
            $table->index(['user_id', 'completed']);
            $table->index(['team_id', 'completed']);
            $table->index(['assigned_by', 'assign_date']);
            $table->index(['complete_date']);
            $table->index(['task_type', 'completed']);
            
            // Unique constraints to prevent duplicate assignments
            $table->unique(['challenge_task_id', 'user_id', 'team_id'], 'unique_user_challenge_task');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_tasks');
    }
};
