<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\UserAgreement;
use App\Models\User;

class UserAgreementPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // Only system admin can manage user agreements
        return $user->isSystemAdmin();
    }

    public function view(User $user, UserAgreement $item): bool
    {
        // System admin can view all, users can view their own agreements
        return $user->isSystemAdmin() || $item->user_id === $user->id;
    }

    public function create(User $user): bool
    {
        // Only system admin can create user agreements (users can only accept)
        return $user->isSystemAdmin();
    }

    public function update(User $user, UserAgreement $item): bool
    {
        // Only system admin can update user agreements
        return $user->isSystemAdmin();
    }

    public function delete(User $user, UserAgreement $item): bool
    {
        // Only system admin can delete user agreements
        return $user->isSystemAdmin();
    }

    public function restore(User $user, UserAgreement $item): bool
    {
        // Only system admin can restore user agreements
        return $user->isSystemAdmin();
    }

    public function forceDelete(User $user, UserAgreement $item): bool
    {
        // Only system admin can force delete user agreements
        return $user->isSystemAdmin();
    }

    public function massDelete(User $user): bool
    {
        // Only system admin can mass delete user agreements
        return $user->isSystemAdmin();
    }
}
