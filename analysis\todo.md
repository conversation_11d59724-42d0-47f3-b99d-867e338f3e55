# Yönetim Paneli

# Mobil

## Öğrenci Ekranları

* [ ] Açılış Ekranı (Splash Screen)
  Uygulama başlarken uygulama logosunun gösterildiği bir açılış ekranı çıkmalı. Bu sürede gerekli uygulama kaynakları arkada yüklenebilir.
* [ ] Giriş Ekranı
  Splash screen kaybolduktan sonra, ilk defa giriş yapılıyorsa kullanıcı adı (editable dropdown) ve şifre (password input) soran bir ekran çıkmalı.
  Password kutucuğunun sağında "show password" için göz simgesi bulunmalı.
  Remember me özelliği varsayılan olarak aktif gelmeli.
  Kullanıcı bir kez giriş yaptığında kendisi çıkmadan session sonlandırılmamalı.
  G<PERSON>ş yapmış kullanıcıların kullanıcı adları güvenli yerel bir depolama alanında tutulmalı.
  En az bir kez giriş ya<PERSON>, kola<PERSON> giri<PERSON>, giri<PERSON> yapmı<PERSON> kullanıcı adları  dropdown listesine eklenmeli.
* [ ] Avatar Seçimi (ilk defa girenler için)
  Eğer bir avatar tanımlı değilse (user->avatar is null or no row in users_avatars) bir avatar seçim ekranı çıkmalı.
  avatars tablosundaki required_points=0 olan resimler listelenmeli ve bir tanesi seçildiğinde user->avatar alanı ilgili resmin url bilgisiyle güncellenmeli.
  user_avatars tablosuna bir kayıt eklenmeli.
  Avatar seçim ekranında "Daha Sonra" seçeneği de olmalı.
* [ ] Tebrik Ekranı (hazırsın * avatar tanımlandıktan sonra)
  İlk defa giriş yapanlar için arkasında fireworks efekti olan kullanıcının adının yazdığı bir tebrik ekranı çıkmalı.
  Başla düğmesiyle ana ekran açılmalı.
* [ ] Ana Ekran (İlk defa girenler için)
* [ ] Ana Ekran (Tüm özellikleriyle)
