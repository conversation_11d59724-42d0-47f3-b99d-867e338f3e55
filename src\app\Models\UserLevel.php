<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Traits\BypassesPermissionScopes;

class UserLevel extends BaseModel
{
    use BypassesPermissionScopes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'level_id',
        'level_date',
        'reading_log_id',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'level_date' => 'datetime',
        ];
    }

    /**
     * Get the user who achieved this level.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the level that was achieved.
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(Level::class);
    }

    /**
     * Get the reading log that triggered this level achievement.
     */
    public function readingLog(): BelongsTo
    {
        return $this->belongsTo(UserReadingLog::class, 'reading_log_id');
    }

    /**
     * Scope to get user levels ordered by level date (most recent first).
     */
    public function scopeRecent($query)
    {
        return $query->orderBy('level_date', 'desc');
    }

    /**
     * Scope to get user levels ordered by level number.
     */
    public function scopeByLevelOrder($query)
    {
        return $query->join('levels', 'user_levels.level_id', '=', 'levels.id')
            ->orderBy('levels.nr', 'asc')
            ->select('user_levels.*');
    }

    /**
     * Scope to filter by user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by level.
     */
    public function scopeForLevel($query, $levelId)
    {
        return $query->where('level_id', $levelId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('level_date', [$startDate, $endDate]);
    }

    /**
     * Get the display name for the user level achievement.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' achieved ' . $this->level->display_name . ' on ' . $this->level_date->format('M d, Y');
    }

    /**
     * Get the summary for the user level achievement.
     */
    public function getSummaryAttribute(): string
    {
        $summary = $this->level->display_name . ' achieved on ' . $this->level_date->format('M d, Y');

        if ($this->readingLog) {
            $summary .= ' (triggered by reading ' . $this->readingLog->book->name . ')';
        }

        return $summary;
    }

    /**
     * Apply role-based filtering based on current user's access to users.
     */
    public function scopeForCurrentUser($query)
    {
        return $query->whereHas('user', function ($userQuery) {
            $userQuery->forCurrentUser();
        });
    }
}
