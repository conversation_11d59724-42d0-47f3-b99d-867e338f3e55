<?php

namespace App\Services\BookDiscovery\Providers;

use App\Services\BookDiscovery\AbstractBookDiscoveryProvider;

class FidanKitapProvider extends AbstractBookDiscoveryProvider
{
    /**
     * Fidan Kitap specific parsing logic.
     */
    protected function parseBookData(string $html): ?array
    {
        // First try the Fidan Kitap specific structure
        $bookData = $this->parseFidanKitapStructure($html);

        // If that fails, try the parent's generic parsing
        if (!$bookData) {
            $bookData = parent::parseBookData($html);
        }

        if (!$bookData) {
            return null;
        }

        // Fidan Kitap specific post-processing
        $bookData = $this->postProcessFidanKitapData($bookData);

        return $bookData;
    }

    /**
     * Parse Fidan Kitap's specific HTML structure.
     */
    protected function parseFidanKitapStructure(string $html): ?array
    {
        $dom = new \DOMDocument();
        @$dom->loadHTML('<?xml encoding="UTF-8">' . $html);
        $xpath = new \DOMXPath($dom);

        $bookData = [];

        // Look for the main product view container
        $productContainer = $xpath->query('//div[contains(@class, "prd_view_item")]');
        
        if ($productContainer->length === 0) {
            return null;
        }

        // Extract book title from h1.contentHeader.prdHeader
        $titleNodes = $xpath->query('//h1[contains(@class, "contentHeader") and contains(@class, "prdHeader")]');
        if ($titleNodes->length > 0) {
            $titleText = trim($titleNodes->item(0)->textContent);
            if (!empty($titleText)) {
                $bookData['name'] = html_entity_decode($titleText);
            }
        }

        // Extract authors from writer links
        $authorNodes = $xpath->query('//div[contains(@class, "writers")]//a[contains(@class, "writer")]/span');
        $authors = [];
        foreach ($authorNodes as $authorNode) {
            $authorName = trim($authorNode->textContent);
            if (!empty($authorName)) {
                $authors[] = html_entity_decode($authorName);
            }
        }
        if (!empty($authors)) {
            $bookData['author'] = $authors;
        }

        // Extract publisher from publisher link
        $publisherNodes = $xpath->query('//a[contains(@class, "publisher")]/span');
        if ($publisherNodes->length > 0) {
            $publisherText = trim($publisherNodes->item(0)->textContent);
            if (!empty($publisherText)) {
                // split on ' - ' text and take the first part
                $parts = explode(' - ', $publisherText);
                $publisherText = $parts[0];
                $bookData['publisher'] = html_entity_decode($publisherText);
            }
        }

        // Extract fields from prd_fields section
        $this->extractFieldsData($xpath, $bookData);

        // Extract cover image
        $this->extractCoverImage($xpath, $bookData);

        // Add source information
        $bookData['source'] = 'Fidan Kitap';

        return !empty($bookData) ? $bookData : null;
    }

    /**
     * Extract data from the prd_fields section.
     */
    protected function extractFieldsData(\DOMXPath $xpath, array &$bookData): void
    {
        // Field mappings: label text => data key
        $fieldMappings = [
            'Stok Kodu:' => 'isbn',
            'Sayfa Sayısı:' => 'page_count',
            'Basım Tarihi:' => 'year',
            'Kategori:' => 'category'
        ];

        foreach ($fieldMappings as $label => $dataKey) {
            $fieldNodes = $xpath->query("//div[contains(@class, 'prd_fields_item')]//div[contains(@class, 'prd_fields_label') and contains(text(), '{$label}')]/following-sibling::div[contains(@class, 'prd_fields_text')]");
            
            if ($fieldNodes->length > 0) {
                $fieldText = trim($fieldNodes->item(0)->textContent);
                
                if (!empty($fieldText)) {
                    switch ($dataKey) {
                        case 'isbn':
                            // Clean ISBN - remove any non-numeric characters except X
                            $cleanIsbn = preg_replace('/[^0-9X]/i', '', strtoupper($fieldText));
                            if (!empty($cleanIsbn)) {
                                $bookData['isbn'] = $cleanIsbn;
                            }
                            break;
                            
                        case 'page_count':
                            // Extract numeric page count
                            if (preg_match('/(\d+)/', $fieldText, $matches)) {
                                $bookData['page_count'] = (int) $matches[1];
                            }
                            break;
                            
                        case 'year':
                            // Extract year from date
                            if (preg_match('/(\d{4})/', $fieldText, $matches)) {
                                $bookData['year'] = (int) $matches[1];
                            }
                            break;
                            
                        case 'category':
                            // Extract categories from links
                            $categoryNodes = $xpath->query("//div[contains(@class, 'prd_fields_item')]//div[contains(@class, 'prd_fields_label') and contains(text(), '{$label}')]/following-sibling::div[contains(@class, 'prd_fields_text')]//a");
                            $categories = [];
                            foreach ($categoryNodes as $categoryNode) {
                                $categoryName = trim($categoryNode->textContent);
                                if (!empty($categoryName)) {
                                    $categories[] = html_entity_decode($categoryName);
                                }
                            }
                            if (!empty($categories)) {
                                $bookData['category'] = $categories;
                            }
                            break;
                    }
                }
            }
        }
    }

    /**
     * Extract cover image from the page.
     */
    protected function extractCoverImage(\DOMXPath $xpath, array &$bookData): void
    {
        // Look for product images
        $imageSelectors = [
            '//div[contains(@class, "prd_view_item")]//img[contains(@class, "prd_image")]',
            '//div[contains(@class, "product-image")]//img',
            '//img[contains(@alt, "kitap") or contains(@alt, "book")]'
        ];

        foreach ($imageSelectors as $selector) {
            $imageNodes = $xpath->query($selector);
            if ($imageNodes->length > 0) {
                $imageNode = $imageNodes->item(0);
                $imageSrc = $imageNode->getAttribute('src') ?: $imageNode->getAttribute('data-src');
                
                if (!empty($imageSrc)) {
                    // Convert relative URLs to absolute
                    if (strpos($imageSrc, 'http') !== 0) {
                        $imageSrc = 'https://www.fidankitap.com' . $imageSrc;
                    }
                    $bookData['cover_image'] = $imageSrc;
                    break;
                }
            }
        }
    }

    /**
     * Post-process Fidan Kitap specific data.
     */
    protected function postProcessFidanKitapData(array $bookData): array
    {
        // Clean and validate book title
        if (isset($bookData['name'])) {
            $bookData['name'] = $this->cleanText($bookData['name']);
        }

        // Clean author names
        if (isset($bookData['author'])) {
            if (is_array($bookData['author'])) {
                $bookData['author'] = array_map([$this, 'cleanText'], $bookData['author']);
                $bookData['author'] = array_filter($bookData['author']); // Remove empty values
            } else {
                $bookData['author'] = [$this->cleanText($bookData['author'])];
            }
        }

        // Clean publisher name
        if (isset($bookData['publisher'])) {
            $bookData['publisher'] = $this->cleanText($bookData['publisher']);
        }

        // Clean categories
        if (isset($bookData['category'])) {
            if (is_array($bookData['category'])) {
                $bookData['category'] = array_map([$this, 'cleanText'], $bookData['category']);
                $bookData['category'] = array_filter($bookData['category']); // Remove empty values
            }
        }

        // Add source information
        $bookData['source'] = 'Fidan Kitap';

        return $bookData;
    }

    /**
     * Clean text content.
     */
    protected function cleanText(string $text): string
    {
        // Remove extra whitespace and decode HTML entities
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * Check if the book was not found.
     */
    protected function isBookNotFound(string $html): bool
    {
        // Check for "kayıt bulunamadı" text (case insensitive)
        if (stripos($html, 'kayıt bulunamadı') !== false) {
            return true;
        }

        // Check for other not found indicators
        $notFoundPatterns = [
            'sonuç bulunamadı',
            'ürün bulunamadı',
            'no results found',
            'aradığınız ürün bulunamadı'
        ];

        foreach ($notFoundPatterns as $pattern) {
            if (stripos($html, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}
