<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Book Discovery Service
        $this->app->singleton(\App\Services\BookDiscovery\BookDiscoveryService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
    }
}


