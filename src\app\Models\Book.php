<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\ClassBook;
use App\Models\User;

class Book extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'isbn',
        'publisher_id',
        'book_type_id',
        'page_count',
        'year_of_publish',
        'cover_image',
        'active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'page_count' => 'integer',
            'year_of_publish' => 'integer',
            'active' => 'boolean',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically set active status based on required fields
        static::saving(function ($book) {

            // remove display fields in teacher panel book form before saving (author_names and category_names)            
            unset($book->author_names);
            unset($book->category_names);

            // If book_type_id OR page_count is null, set active to false
            if (is_null($book->book_type_id) || is_null($book->page_count)) {
                $book->active = false;
            }
        });
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the publisher of this book.
     */
    public function publisher(): BelongsTo
    {
        return $this->belongsTo(Publisher::class);
    }

    /**
     * Get the type of this book.
     */
    public function bookType(): BelongsTo
    {
        return $this->belongsTo(BookType::class);
    }


    /**
     * Get authors of this book.
     */
    public function authors(): BelongsToMany
    {
        return $this->belongsToMany(Author::class, 'book_authors');
    }

    /**
     * Get categories of this book.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'book_categories');
    }

    /**
     * Get school classes that have this book in their bookshelf.
     */
    public function schoolClasses(): BelongsToMany
    {
        return $this->belongsToMany(SchoolClass::class, 'class_books', 'book_id', 'class_id');
    }

    /**
     * Get reading logs for this book.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(UserReadingLog::class);
    }

    /**
     * Get points earned from this book.
     */
    public function points(): HasMany
    {
        return $this->hasMany(UserPoint::class);
    }

    /**
     * Get activities submitted for this book.
     */
    public function userActivities(): HasMany
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * Get user book assignments for this book.
     */
    public function userBooks(): HasMany
    {
        return $this->hasMany(UserBook::class);
    }

    /**
     * Get users currently reading this book.
     */
    public function currentReaders(): HasMany
    {
        return $this->hasMany(UserBook::class)->inProgress();
    }

    /**
     * Get users who have completed this book.
     */
    public function completedReaders(): HasMany
    {
        return $this->hasMany(UserBook::class)->completed();
    }

    /**
     * Scope to search books by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('isbn', 'like', '%' . $search . '%');
    }

    /**
     * Scope to filter by publisher.
     */
    public function scopeByPublisher($query, $publisherId)
    {
        return $query->where('publisher_id', $publisherId);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeByBookType($query, $bookTypeId)
    {
        return $query->where('book_type_id', $bookTypeId);
    }

    /**
     * Scope to filter by year.
     */
    public function scopeByYear($query, int $year)
    {
        return $query->where('year_of_publish', $year);
    }

    /**
     * Scope to filter by year range.
     */
    public function scopeByYearRange($query, int $startYear, int $endYear)
    {
        return $query->whereBetween('year_of_publish', [$startYear, $endYear]);
    }

    /**
     * Scope to filter by page count range.
     */
    public function scopeByPageRange($query, int $minPages, int $maxPages)
    {
        return $query->whereBetween('page_count', [$minPages, $maxPages]);
    }

    /**
     * Scope to filter by author.
     */
    public function scopeByAuthor($query, $authorId)
    {
        return $query->whereHas('authors', function ($q) use ($authorId) {
            $q->where('authors.id', $authorId);
        });
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->whereHas('categories', function ($q) use ($categoryId) {
            $q->where('categories.id', $categoryId);
        });
    }

    /**
     * Scope to get recent books.
     */
    public function scopeRecent($query, int $years = 5)
    {
        $startYear = now()->year - $years;
        return $query->where('year_of_publish', '>=', $startYear);
    }

    /**
     * Scope to get only active books.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Get class book assignments for this book.
     */
    public function classBooks(): HasMany
    {
        return $this->hasMany(ClassBook::class);
    }

    /**
     * Add this book to the default class assigned to the given user (teacher).
     *
     * @param int $userId The ID of the user (teacher) whose default class should get this book
     * @return int ID of the created ClassBook record
     */
    public function addClassBook(int $userId): int
    {
        $result = 0;
        $user = User::find($userId);
        if (!$user) {
            return $result;
        }
        $defaultClass = $user->getDefaultClass();
        if (!$defaultClass) {
            return $result;
        }
        $classId = $defaultClass->class_id;
        $existingClassBook = ClassBook::where('class_id', $classId)
            ->where('book_id', $this->id)
            ->first();

        if (!$existingClassBook) {
            // Create new class book assignment
            $classBook = ClassBook::create([
                'class_id' => $classId,
                'book_id' => $this->id,
                'created_by' => $userId,
            ]);

            $result = $classBook->id;
        }

        return $result;
    }
    
    public function removeClassBook(int $userId): int
    {
        $result = 0;
        $user = User::find($userId);
        if (!$user) {
            return $result;
        }
        $defaultClass = $user->getDefaultClass();   
        if (!$defaultClass) {
            return $result;
        }
        $classId = $defaultClass->class_id;
        $existingClassBook = ClassBook::where('class_id', $classId)
            ->where('book_id', $this->id)
            ->first();  
        if ($existingClassBook) {
            $existingClassBook->delete();
            $result = 1;
        }
        return $result;
    }   

    /**
     * Scope to get only inactive books.
     */
    public function scopeInactive($query)
    {
        return $query->where('active', false);
    }

    /**
     * Get the primary author (first author).
     */
    public function getPrimaryAuthorAttribute(): ?Author
    {
        return $this->authors()->first();
    }

    /**
     * Get all author names as a string.
     */
    public function getAuthorNamesAttribute(): string
    {
        return $this->authors->pluck('name')->join(', ');
    }

    /**
     * Get all category names as a string.
     */
    public function getCategoryNamesAttribute(): string
    {
        return $this->categories->pluck('name')->join(', ');
    }

    /**
     * Check if the book is active.
     */
    public function isActive(): bool
    {
        return $this->active === true;
    }

    /**
     * Check if the book is inactive.
     */
    public function isInactive(): bool
    {
        return $this->active === false;
    }

    /**
     * Get the active status display.
     */
    public function getActiveStatusDisplayAttribute(): string
    {
        return $this->active ? __('admin.active') : __('admin.inactive');
    }

    /**
     * Check if book can be used for reading logs.
     */
    public function canCreateReadingLogs(): bool
    {
        return $this->isActive();
    }

    /**
     * Check if book can be used for activities.
     */
    public function canCreateActivities(): bool
    {
        return $this->isActive();
    }

    /**
     * Get the reason why book is inactive (for user feedback).
     */
    public function getInactiveReasonAttribute(): ?string
    {
        if ($this->isActive()) {
            return null;
        }

        $reasons = [];
        if (is_null($this->book_type_id)) {
            $reasons[] = __('admin.missing_book_type');
        }
        if (is_null($this->page_count)) {
            $reasons[] = __('admin.missing_page_count');
        }

        return implode(', ', $reasons);
    }

    /**
     * Get formatted publication info.
     */
    public function getPublicationInfoAttribute(): string
    {
        return $this->publisher->name . ', ' . $this->year_of_publish;
    }

    /**
     * Get book age in years.
     */
    public function getAgeAttribute(): int
    {
        return now()->year - $this->year_of_publish;
    }

    /**
     * Check if book is recent (published in last 5 years).
     */
    public function isRecent(): bool
    {
        return $this->age <= 5;
    }

    /**
     * Get reading difficulty based on page count.
     */
    public function getDifficultyAttribute(): string
    {
        if ($this->page_count <= 50) {
            return 'Easy';
        } elseif ($this->page_count <= 150) {
            return 'Medium';
        } elseif ($this->page_count <= 300) {
            return 'Hard';
        } else {
            return 'Very Hard';
        }
    }

    /**
     * Get estimated reading time in hours.
     */
    public function getEstimatedReadingTimeAttribute(): float
    {
        // Assuming average reading speed of 250 words per minute
        // and approximately 250 words per page
        $wordsPerPage = 250;
        $wordsPerMinute = 250;

        $totalWords = $this->page_count * $wordsPerPage;
        $minutes = $totalWords / $wordsPerMinute;

        return round($minutes / 60, 1); // Convert to hours
    }

     /**
     * Get the questions for this book.
     */
    public function questions(): HasMany
    {
        return $this->hasMany(BookQuestion::class);
    }

    /**
     * Get the active questions for this book.
     */
    public function activeQuestions(): HasMany
    {
        return $this->hasMany(BookQuestion::class)->where('is_active', true);
    }

    /**
     * Get the vocabulary words for this book.
     */
    public function words(): HasMany
    {
        return $this->hasMany(BookWord::class);
    }

    /**
     * Get the active vocabulary words for this book.
     */
    public function activeWords(): HasMany
    {
        return $this->hasMany(BookWord::class)->where('is_active', true);
    }

    // if book has active questions and/or active words return questions and words count
    public function getQuestionsAndWordsCountAttribute(): ?string
    {
        $counts = [
            'questions' => $this->activeQuestions()->count(),
            'words' => $this->activeWords()->count(),
        ];

        // Filter out the zero counts
        $response = array_filter([
            $counts['questions'] > 0 ? $counts['questions'] . ' ' . __('admin.questions_localizable') : null,
            $counts['words'] > 0 ? $counts['words'] . ' ' . __('admin.words_localizable') : null,
        ]);

        // Return the combined result, or an empty string if no counts were found
        return $response ? implode(', ', $response) : null;        
    }
    
}
