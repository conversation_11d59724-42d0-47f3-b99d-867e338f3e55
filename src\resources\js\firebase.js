// Firebase configuration for Okumobil PWA
// Firebase SDK 12.4.0 - Modular API

console.log('Firebase.js module loading...');

import { initializeApp } from 'https://www.gstatic.com/firebasejs/12.4.0/firebase-app.js';
import { getMessaging, getToken, onMessage } from 'https://www.gstatic.com/firebasejs/12.4.0/firebase-messaging.js';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAR1qUdJdJFZuWUH2pqx0HTjcmCszuLMBc",
  authDomain: "okumobil-2025.firebaseapp.com",
  projectId: "okumobil-2025",
  storageBucket: "okumobil-2025.firebasestorage.app",
  messagingSenderId: "572159316141",
  appId: "1:572159316141:web:be0818d9518b853ef6b624"
};

// VAPID key for push notifications
const vapidKey = "BEsnuzrESgkHiaqNXjNpthBg2t7I8m1kxXqEqgNdaUi716bLfEpKW4zTfJnny6OJ6dCBnjMexgsnyNDsbaE5DnQ";

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = getMessaging(app);

// Export for use in other modules
window.firebaseApp = app;
window.firebaseMessaging = messaging;
window.firebaseConfig = firebaseConfig;
window.vapidKey = vapidKey;

// FCM Token Management Constants
const FCM_TOKEN_CACHE_KEY = 'fcm_token_cache';
const FCM_TOKEN_EXPIRY_HOURS = 24; // Refresh token every 24 hours
const FCM_REGISTRATION_COOLDOWN_MINUTES = 5; // Prevent duplicate registrations within 5 minutes

console.log('Firebase app initialized and exported to window');

// FCM Token Caching Utilities
function getCachedTokenData() {
  try {
    const cached = localStorage.getItem(FCM_TOKEN_CACHE_KEY);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.warn('Failed to parse cached FCM token data:', error);
    localStorage.removeItem(FCM_TOKEN_CACHE_KEY);
    return null;
  }
}

function setCachedTokenData(token, timestamp = Date.now()) {
  try {
    const tokenData = {
      token: token,
      timestamp: timestamp,
      lastRegistered: null
    };
    localStorage.setItem(FCM_TOKEN_CACHE_KEY, JSON.stringify(tokenData));
    return tokenData;
  } catch (error) {
    console.warn('Failed to cache FCM token data:', error);
    return null;
  }
}

function isTokenExpired(tokenData) {
  if (!tokenData || !tokenData.timestamp) return true;

  const expiryTime = tokenData.timestamp + (FCM_TOKEN_EXPIRY_HOURS * 60 * 60 * 1000);
  return Date.now() > expiryTime;
}

function shouldRegisterToken(tokenData, currentToken) {
  if (!tokenData) return true;

  // Token has changed
  if (tokenData.token !== currentToken) return true;

  // Never registered before
  if (!tokenData.lastRegistered) return true;

  // Registration cooldown period
  const cooldownTime = tokenData.lastRegistered + (FCM_REGISTRATION_COOLDOWN_MINUTES * 60 * 1000);
  if (Date.now() < cooldownTime) {
    console.log('FCM token registration in cooldown period, skipping...');
    return false;
  }

  // Force refresh every 24 hours
  const forceRefreshTime = tokenData.lastRegistered + (FCM_TOKEN_EXPIRY_HOURS * 60 * 60 * 1000);
  return Date.now() > forceRefreshTime;
}

function updateTokenRegistrationTime(token) {
  try {
    const cached = getCachedTokenData();
    if (cached && cached.token === token) {
      cached.lastRegistered = Date.now();
      localStorage.setItem(FCM_TOKEN_CACHE_KEY, JSON.stringify(cached));
    }
  } catch (error) {
    console.warn('Failed to update token registration time:', error);
  }
}

// FCM functions - Optimized with token caching
window.initializeFCM = async function() {
  try {
    console.log('FCM initialization started...');

    // Check if service worker and push notifications are supported
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.log('Push notifications not supported');
      return;
    }

    // Request notification permission
    const permission = await Notification.requestPermission();
    if (permission !== 'granted') {
      console.log('Notification permission denied');
      return;
    }

    console.log('Notification permission granted');

    // ALWAYS register service worker first - this is critical for push notifications
    let registration;
    try {
      console.log('Registering service worker...');
      registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });
      console.log('Service worker registered successfully:', registration);

      // Wait for service worker to be ready with timeout
      console.log('Waiting for service worker to be ready...');
      const serviceWorkerReady = Promise.race([
        navigator.serviceWorker.ready,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Service worker ready timeout')), 10000)
        )
      ]);

      await serviceWorkerReady;
      console.log('Service worker is ready');

    } catch (error) {
      console.error('Service worker registration failed:', error);
      throw error;
    }

    // Check cached token AFTER service worker is ready
    const cachedTokenData = getCachedTokenData();
    console.log('Cached token data:', cachedTokenData ? 'Found' : 'Not found');

    // If we have a valid cached token and don't need to refresh, skip Firebase API call
    if (cachedTokenData && !isTokenExpired(cachedTokenData)) {
      console.log('Using cached FCM token, skipping Firebase API call');

      // Still check if we need to register with server
      if (shouldRegisterToken(cachedTokenData, cachedTokenData.token)) {
        console.log('Cached token needs server registration');
        await registerFCMToken(cachedTokenData.token);
      } else {
        console.log('Cached token already registered, skipping server call');
      }

      // Set up foreground message handling
      setupForegroundMessageHandling();
      return;
    }

    console.log('Cached token expired or missing, retrieving new token from Firebase...');

    // Get FCM token from Firebase (service worker already registered above)
    console.log('Attempting to get FCM token from Firebase...');
    console.log('VAPID key:', vapidKey ? 'Present' : 'Missing');
    console.log('Service worker registration:', registration ? 'Present' : 'Missing');

    const currentToken = await getToken(messaging, {
      vapidKey: vapidKey,
      serviceWorkerRegistration: registration
    });

    if (currentToken) {
      console.log('FCM Token received from Firebase:', currentToken.substring(0, 20) + '...');

      // Cache the new token
      setCachedTokenData(currentToken);
      console.log('FCM token cached successfully');

      // Check if we need to register with server
      const cachedData = getCachedTokenData();
      if (shouldRegisterToken(cachedData, currentToken)) {
        console.log('New token needs server registration');
        await registerFCMToken(currentToken);
      } else {
        console.log('Token already registered with server, skipping registration');
      }

      // Set up foreground message handling
      setupForegroundMessageHandling();
    } else {
      console.log('No FCM token available from Firebase');
    }

  } catch (error) {
    console.error('FCM initialization failed:', error);
  }
};

// Set up foreground message handling (extracted to avoid duplication)
function setupForegroundMessageHandling() {
  onMessage(messaging, (payload) => {
    console.log('Foreground message received:', payload);

    // Show browser notification for foreground messages
    if (payload.notification) {
      showLocalNotification(
        payload.notification.title,
        {
          body: payload.notification.body,
          icon: payload.notification.image || '/images/icon-192x192.png',
          badge: '/images/icon-72x72.png',
          tag: payload.data?.type || 'general',
          data: payload.data,
          requireInteraction: true,
          actions: [
            {
              action: 'open',
              title: 'Aç',
              icon: '/images/icon-72x72.png'
            },
            {
              action: 'close',
              title: 'Kapat'
            }
          ]
        }
      );
    }
  });
}

// Register FCM token with server - Optimized with registration tracking
async function registerFCMToken(token) {
  try {
    console.log('Attempting to register FCM token...');

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    console.log('CSRF token found:', csrfToken ? 'Yes' : 'No');

    const response = await fetch('/api/fcm/register-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': csrfToken
      },
      body: JSON.stringify({ token })
    });

    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);

    if (response.ok) {
      const result = await response.json();
      console.log('FCM token registered successfully:', result);

      // Update registration timestamp in cache
      updateTokenRegistrationTime(token);
      console.log('Token registration timestamp updated');
    } else {
      const errorText = await response.text();
      console.error('Failed to register FCM token. Status:', response.status, 'Error:', errorText);
      throw new Error(`Registration failed: ${response.status} - ${errorText}`);
    }
  } catch (error) {
    console.error('Error registering FCM token:', error);
  }
}

// Show local notification
function showLocalNotification(title, options) {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification(title, options);

    notification.onclick = function(event) {
      event.preventDefault();

      // Handle deep linking
      const deepLinkUrl = options.data?.deep_link_url || '/mobile';

      // Focus or open window
      if (window.focus) {
        window.focus();
      }

      // Navigate to deep link URL
      if (deepLinkUrl && deepLinkUrl !== window.location.pathname) {
        window.location.href = deepLinkUrl;
      }

      notification.close();
    };

    // Auto close after 5 seconds
    setTimeout(() => {
      notification.close();
    }, 5000);
  }
}