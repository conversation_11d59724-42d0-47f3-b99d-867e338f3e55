<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use App\Models\{Reward, Task, User};
use MoonShine\UI\Components\Layout\{Box, Flex};
use MoonShine\UI\Fields\{Date, Number, Switcher, Text};

#[Icon('clipboard-document-check')]
class PanelUserTaskResource extends UserTaskResource
{
    use WithRolePermissions;

    protected bool $detailInModal = true;
    
    protected array $with = [
        'user', 
    ];

    public function getTitle(): string
    {
        return __('admin.tasks');
    }

    public function queryTags(): array
    {   
        return [];        
    }

    protected function indexFields(): iterable
    {
        return [     
            Text::make(__('admin.assignee'), 'assignee_display'),
            Date::make(__('admin.due_date'), 'due_date')
                ->format('d.m.Y'),
            BelongsTo::make(__('admin.reward'),'reward',
                formatted: fn(?Reward $reward) => $reward ? $reward->name : null,
                resource: PanelRewardResource::class
            ),

            Text::make(__('admin.progress'), 'progress_display'),

            Number::make(__('admin.progress_percentage'), 'progress_percentage')
                ->sortable(),

            Text::make(__('admin.status'), 'status_display'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make(__('admin.task_assignment'), [
                    BelongsTo::make(
                        __('admin.task'),
                        'task',
                        formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                        resource: PanelTaskResource::class
                    )
                        ->nullable()
                        ->searchable(),

                BelongsTo::make(
                    __('admin.user'),
                    'user',
                    formatted: fn(User $user) => $user->name,
                    resource: StudentResource::class
                )
                    ->valuesQuery(function (Builder $query) { return $query->forCurrentUser(); })
                    ->required()
                    ->nullable()
                    ->searchable(),

                Flex::make([
                    Date::make(__('admin.assign_date'), 'assign_date')
                        ->format('d.m.Y H:i'),

                    Date::make(__('admin.start_date'), 'start_date')
                        ->format('d.m.Y H:i'),
                ]),
            ]),

            Box::make(__('admin.completion_status'), [
                Flex::make([
                    Switcher::make(__('admin.completed'), 'completed'),

                    Date::make(__('admin.complete_date'), 'complete_date')
                        ->format('d.m.Y H:i')
                        ->showWhen('completed', true),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                resource: TaskResource::class
            ),
            Text::make(__('admin.assignee'), 'assignee_display'),
            Text::make(__('admin.progress'), 'progress_display'),
            Number::make(__('admin.progress_percentage'), 'progress_percentage'),
            Text::make(__('admin.status'), 'status_display'),

            Date::make(__('admin.assign_date'), 'assign_date')->format('d.m.Y H:i'),
            Date::make(__('admin.start_date'), 'start_date')->format('d.m.Y H:i'),
            Switcher::make(__('admin.completed'), 'completed'),
            Date::make(__('admin.complete_date'), 'complete_date')->format('d.m.Y H:i'),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'user_id' => ['required', 'exists:users,id'],
            'assigned_by' => ['nullable', 'exists:users,id'],
            'assign_date' => ['nullable', 'date'],
            'start_date' => ['nullable', 'date'],
            'completed' => ['boolean'],
            'complete_date' => ['nullable', 'date'],
            ...parent::getCommonRules($item),
        ];
        return $rules;
    }

    protected function search(): array
    {
        return [
            'user.name', 
        ];
    }

    protected function getDefaultSort(): array
    {
        return ['assign_date' => 'desc'];
    }

    // redirect to task detail page after save
    public function getRedirectAfterSave(): string
    {
        $resource = app(PanelTaskResource::class);
        return $resource->getDetailPageUrl($this->item->task_id);
    }

}
