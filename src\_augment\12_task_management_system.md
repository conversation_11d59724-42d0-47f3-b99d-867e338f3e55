# Task Management System

## Overview

The Task Management System provides a comprehensive foundation for creating various types of reading activities and objectives. It supports flexible task definitions with different cycles and tracking mechanisms, designed to serve as the building blocks for future goals and challenges features.

## Database Structure

### Core Tables

#### `enum_task_types`
- **Purpose**: Defines the types of tasks that can be created
- **Fields**: `id`, `nr` (unique number), `name`
- **Predefined Types**:
  1. Read Pages - Complete reading a specific number of pages
  2. Read Books - Complete reading a specific number of books
  3. Read Minutes - Spend a specific amount of time reading
  4. Read Days - Read for a specific number of days
  5. Read Streak - Maintain a reading streak for consecutive days
  6. Earn Reading Points - Earn a specific number of reading points
  7. Earn Activity Points - Earn a specific number of activity points
  8. Complete a Book Activity - Complete a specific book activity
  9. Complete a Book List - Complete reading all books in a specified list
  10. Yes/No Task - Binary task that is either completed or not

#### `enum_task_cycles`
- **Purpose**: Defines how often tasks should be completed
- **Fields**: `id`, `nr` (unique number), `name`
- **Predefined Cycles**:
  1. Total - Cumulative progress over the entire period
  2. Daily - Task must be completed each day
  3. Weekly - Task must be completed each week
  4. Monthly - Task must be completed each month

#### `tasks`
- **Purpose**: Core task definitions with configuration
- **Fields**: `id`, `name`, `task_type_id`, `task_cycle_id`, `task_value`, `activity_id`, `active`
- **Key Features**:
  - Links to task type and cycle
  - Optional task_value for quantitative tasks
  - Optional activity_id for activity-specific tasks
  - Active flag for enabling/disabling tasks

#### `task_books`
- **Purpose**: Many-to-many relationship between tasks and books
- **Fields**: `id`, `task_id`, `book_id`
- **Usage**: Used for "Complete a Book List" tasks

#### `task_book_categories`
- **Purpose**: Many-to-many relationship between tasks and book categories
- **Fields**: `id`, `task_id`, `category_id`
- **Usage**: Used for category-based task filtering

## Model Architecture

### EnumTaskType Model
- **Constants**: Defines constants for each task type (READ_PAGES, READ_BOOKS, etc.)
- **Helper Methods**:
  - `isQuantitative()` - Check if task requires task_value
  - `isQualitative()` - Check if task is Yes/No or book list completion
  - `requiresBookList()` - Check if task needs book selection
  - `requiresBookCategories()` - Check if task needs category selection
  - `requiresActivity()` - Check if task is linked to a specific activity
- **Attributes**: `unit`, `description`, `display_name`, `summary`

### EnumTaskCycle Model
- **Constants**: Defines constants for each cycle (TOTAL, DAILY, WEEKLY, MONTHLY)
- **Calculation Methods**:
  - `getPeriodsInRange()` - Calculate number of periods in date range
  - `getCurrentPeriod()` - Get current period number
  - `getExpectedProgressPercentage()` - Calculate expected progress
  - `getPeriodStartDate()` / `getPeriodEndDate()` - Get period boundaries
- **Helper Methods**:
  - `isTimeBased()` - Check if cycle is time-based
  - `isCumulative()` - Check if cycle is cumulative
- **Attributes**: `unit`, `description`, `display_name`, `summary`

### Task Model
- **Relationships**:
  - `taskType()` - BelongsTo EnumTaskType
  - `taskCycle()` - BelongsTo EnumTaskCycle
  - `activity()` - BelongsTo Activity (nullable)
  - `books()` - BelongsToMany Book
  - `categories()` - BelongsToMany Category
- **Helper Methods**:
  - `isQuantitative()` / `isQualitative()` - Delegate to task type
  - `requiresBookList()` / `requiresBookCategories()` / `requiresActivity()` - Delegate to task type
  - `isMeasurable()` / `isBinary()` - Check measurement type
- **Attributes**: `task_value_with_unit`, `configuration_summary`, `display_name`, `summary`

### TaskBook & TaskBookCategory Models
- **Purpose**: Pivot models for managing book and category relationships
- **Static Methods**:
  - `addBookToTask()` / `addCategoryToTask()` - Add relationships
  - `removeBookFromTask()` / `removeCategoryFromTask()` - Remove relationships
  - `getBooksForTask()` / `getCategoriesForTask()` - Get related items
  - `isBookInTask()` / `isCategoryInTask()` - Check relationships

## Task Types Classification

### Quantitative Tasks (Types 1-8)
- **Characteristics**: Require numeric target values (`task_value`)
- **Progress**: Measured against target value
- **Examples**: Read 50 pages, Read 3 books, Read 30 minutes

### Qualitative Tasks (Types 9-10)
- **Characteristics**: Binary completion (achieved/not achieved)
- **Progress**: Either 0% or 100%
- **Examples**: Complete book list, Yes/No task

### Special Task Types

#### Type 8: Complete a Book Activity
- **Special Feature**: Links to specific activity via `activity_id`
- **Usage**: Task completion tied to specific book activity completion

#### Type 9: Complete a Book List
- **Special Feature**: Uses `task_books` table instead of `task_value`
- **Usage**: Task completed when all specified books are read

## MoonShine Admin Interface

### TaskResource
- **Features**:
  - Conditional fields based on task type
  - Book and category selection for appropriate task types
  - Activity selection for activity-based tasks
  - Progress analytics and completion tracking
- **Form Sections**:
  - Task Details (name, active status)
  - Task Configuration (type, cycle, value, activity)
  - Task Books (for book list tasks)
  - Task Categories (for category-based filtering)

### EnumTaskTypeResource & EnumTaskCycleResource
- **Purpose**: Manage task types and cycles
- **Features**: View descriptions, units, and type classifications

## Integration Points

### Existing Systems
- **Activities**: Type 8 tasks link to specific activities
- **Books**: Book list tasks and category filtering
- **Categories**: Category-based task filtering
- **User Reading Logs**: Future integration for progress calculation
- **User Points**: Future integration for point-based tasks

### Future Extensions
- **Goals System**: Tasks will serve as goal templates
- **Challenges System**: Tasks will define challenge requirements
- **Progress Tracking**: User task assignments and progress calculation
- **Notifications**: Task completion and progress notifications

## Translation Support

### English Translations
- Task types, cycles, and management interface terminology
- Descriptions for each task type and cycle
- Form hints and validation messages

### Turkish Translations
- Complete Turkish translation set
- Culturally appropriate terminology
- Consistent with existing application translations

## Business Logic

### Task Value Handling
- **Quantitative Tasks**: Use `task_value` field for numeric targets
- **Book List Tasks**: Use `task_books` table, ignore `task_value`
- **Yes/No Tasks**: Binary completion, no numeric value

### Activity Integration
- **Type 8 Only**: Links to specific activities via `activity_id`
- **Other Types**: Not linked to specific activities
- **Future**: Integration with existing activity tracking system

### Category-Based Tasks
- **Purpose**: Tasks requiring books from specific categories
- **Implementation**: Use `task_book_categories` table
- **Progress**: Calculate based on books completed within specified categories

## Technical Implementation

### Database Constraints
- **Unique Constraints**: Prevent duplicate task-book and task-category relationships
- **Foreign Key Constraints**: Maintain referential integrity
- **Indexes**: Optimized for performance on common queries

### Model Validation
- **Task Rules**: Name required, valid type and cycle IDs
- **Conditional Validation**: Task value required for quantitative tasks
- **Relationship Validation**: Valid book and category IDs

### Performance Considerations
- **Eager Loading**: Relationships loaded efficiently
- **Indexes**: Strategic indexing on foreign keys and active status
- **Query Optimization**: Scoped queries for common filtering

## Future Development

### Phase 1: User Task Assignments
- Create user_tasks table for assigning tasks to users
- Implement progress tracking and completion detection
- Add task scheduling and deadline management

### Phase 2: Goals Integration
- Use tasks as goal templates
- Implement goal creation from task definitions
- Add goal progress calculation based on task completion

### Phase 3: Challenges Integration
- Use tasks as challenge requirements
- Implement challenge creation with multiple task requirements
- Add challenge progress tracking and leaderboards

### Phase 4: Advanced Features
- Task dependencies and prerequisites
- Dynamic task generation based on user behavior
- Personalized task recommendations
- Social features (task sharing, team challenges)

## Testing Strategy

### Unit Tests
- Model methods and relationships
- Task type and cycle calculations
- Validation rules and constraints

### Integration Tests
- MoonShine resource functionality
- Database migrations and seeding
- Translation completeness

### Feature Tests
- Task creation and management workflows
- Book and category relationship management
- Progress calculation accuracy

This task management system provides a solid foundation for building sophisticated goal and challenge systems while maintaining flexibility and extensibility for future enhancements.
