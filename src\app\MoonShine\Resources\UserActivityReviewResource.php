<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Activity;
use App\Models\UserActivityReview;
use App\Models\UserActivity;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Image;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Select;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\UI\Fields\File;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

#[Icon('clipboard-document-list')]
class UserActivityReviewResource extends BaseResource
{
    use WithRolePermissions;

    protected string $model = UserActivityReview::class;

    protected string $column = 'display_name';

    protected array $with = ['userActivity.user', 'userActivity.book', 'userActivity.activity', 'reviewer'];

    public function getTitle(): string
    {
        return __('admin.user_activity_reviews');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.student'), 'userActivity.user.name')
                ->sortable(),

            Text::make(__('admin.book'), 'userActivity.book.name')
                ->unescape()
                ->sortable(),

            Text::make(__('admin.activity'), 'userActivity.activity.name')
                ->sortable(),

            Date::make(__('admin.review_date'), 'review_date')
                ->sortable(),

            BelongsTo::make(
                __('admin.reviewer'),
                'reviewer',
                formatted: fn(?User $reviewer) => $reviewer?->name ?? 'Unassigned',
                resource: UserResource::class
            )
                ->sortable(),

            Select::make(__('admin.status'), 'status')
                ->options(UserActivityReview::getStatusOptions())
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.user_activity'),
                    'userActivity',
                    formatted: fn(UserActivity $userActivity) => html_entity_decode($userActivity->display_name),
                    resource: UserActivityResource::class
                )
                    ->required()
                    ->searchable(),

                Date::make(__('admin.review_date'), 'review_date')
                    ->required()
                    ->default(now()->format('Y-m-d')),                

                Flex::make([
                    BelongsTo::make(
                        __('admin.reviewer'),
                        'reviewer',
                        formatted: fn(?User $reviewer) => $reviewer?->name ?? 'Unassigned',
                        resource: UserResource::class
                    )
                        ->searchable(),

                    Select::make(__('admin.status'), 'status')
                        ->required()
                        ->options(UserActivityReview::getStatusOptions())
                        ->default(UserActivityReview::STATUS_WAITING),
                ]),

                Textarea::make(__('admin.feedback'), 'feedback')
                    ->hint(__('admin.review_feedback_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        // find activity media type and decide for file or image field  
        $mediaField = $this->item->userActivity->activity->media_type === Activity::MEDIA_TYPE_IMAGE ?  
            [ Image::make(__('admin.media_content'), 'userActivity.media_url') ] : 
            [ File::make(__('admin.media_content'), 'userActivity.media_url') ];
            
        return [
            BelongsTo::make(
                __('admin.user_activity'),
                'userActivity',
                formatted: fn(UserActivity $userActivity) => html_entity_decode($userActivity->display_name),
                resource: UserActivityResource::class
            ),

            Text::make(__('admin.student'), 'userActivity.user.name'),
            Text::make(__('admin.book'), 'userActivity.book.name')->unescape(),
            Text::make(__('admin.activity'), 'userActivity.activity.name'),
            Date::make(__('admin.activity_date'), 'userActivity.activity_date'),
            Textarea::make(__('admin.activity_content'), 'userActivity.content')->unescape(),
            Text::make(__('admin.activity_rating'), 'userActivity.rating'),
            ...$mediaField,

            Date::make(__('admin.review_date'), 'review_date'),
            BelongsTo::make(
                __('admin.reviewer'),
                'reviewer',
                formatted: fn(?User $reviewer) => $reviewer?->name ?? 'Unassigned',
                resource: UserResource::class
            ),
            Text::make(__('admin.status'), 'localized_status_name'),
            Textarea::make(__('admin.feedback'), 'feedback'),
            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_activity_id' => ['required', 'exists:user_activities,id'],
            'review_date' => ['required', 'date'],
            'reviewed_by' => ['nullable', 'exists:users,id'],
            'status' => ['required', 'integer', 'in:0,1,2'],
            'feedback' => ['nullable', 'string'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return [
            'userActivity.user.name', 
            'userActivity.user.email', 
            'userActivity.book.name', 
            'userActivity.activity.name',
            'reviewer.name',
            'feedback'
        ];
    }

    protected function getDefaultSort(): array
    {
        return ['review_date' => 'desc', 'status' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all activity reviews
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see reviews for activities in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('userActivity', function ($q) use ($userSchoolIds) {
                $q->whereHas('user.activeUserClasses', function ($subQ) use ($userSchoolIds) {
                    $subQ->whereIn('school_id', $userSchoolIds);
                });
            });
        }

        // Teachers can see reviews for activities from students in their assigned classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('userActivity', function ($q) use ($teacherClassIds) {
                $q->whereHas('user.activeUserClasses', function ($subQ) use ($teacherClassIds) {
                    $subQ->whereIn('class_id', $teacherClassIds);
                });
            });
        }

        // Students have no access to review management
        return $builder->where('id', 0);
    }
}
