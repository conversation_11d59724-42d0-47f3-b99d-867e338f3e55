<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_reading_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->timestamp('log_date');
            $table->integer('start_page')->nullable()->comment('Optional start page information');
            $table->integer('end_page')->nullable()->comment('Optional end page information');
            $table->integer('pages_read');
            $table->integer('reading_duration')->nullable()->comment('Time spent reading in minutes');
            $table->boolean('book_completed')->default(false);
            $table->foreignId('challenge_task_id')->nullable()->constrained('challenge_tasks')->onDelete('set null');

            // Add indexes for performance
            $table->index(['user_id', 'log_date']);
            $table->index(['book_id', 'log_date']);
            $table->index(['user_id', 'book_id']);
            $table->index('log_date');
            $table->index('book_completed');
            $table->index(['challenge_task_id', 'user_id']);
            $table->index(['challenge_task_id', 'log_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_reading_logs');
    }
};
