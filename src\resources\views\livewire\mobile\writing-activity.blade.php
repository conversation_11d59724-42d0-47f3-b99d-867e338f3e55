<div class="min-h-screen bg-base-200">
     <x-mobile-page-header route="{{ route('mobile.books.activities', $book->id) }}" header="{{ __('mobile.writing_activity_title') }}" />

    <div class="p-4">
        <!-- Activity Info -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
            <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center">
                    <span class="text-3xl">✍️</span>
                </div>

                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-1">{{ $activity->name }}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ $activity->description }}</p>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="mobile-badge">{{ $activity->points }} {{ __('mobile.points') }}</span>
                        <span class="text-gray-500">📖 {!! $book->name !!}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <!-- Writing Form -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <form wire:submit="submitActivity" class="space-y-4">
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('mobile.your_writing') }}
                    </label>
                    <textarea
                        id="content"
                        wire:model.live="content"
                        class="mobile-input @error('content') border-red-500 @enderror"
                        placeholder="{{ __('mobile.start_writing_placeholder') }}"
                        rows="12"
                        maxlength="2000"
                        @if($mode === 'view') disabled @endif
                    ></textarea>
                    @error('content')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror

                    <!-- Writing Stats -->
                    <div class="flex justify-between text-sm mt-2">
                        <span class="text-gray-600">
                            {{ $wordCount }} {{ __('mobile.words') }}
                            @if($activity->min_word_count)
                                <span class="text-gray-400">/ {{ $activity->min_word_count }} {{ __('mobile.required') }}</span>
                            @endif
                        </span>                        
                    </div>

                    <!-- Word Count Progress -->
                    @if($activity->min_word_count)
                        <div class="mobile-progress-bar mt-2">
                            <div class="mobile-progress-fill {{ $wordCount >= $activity->min_word_count ? 'bg-green-500' : 'bg-blue-500' }}"
                                 style="width: {{ min(100, ($wordCount / $activity->min_word_count) * 100) }}%"></div>
                        </div>
                    @endif
                </div>

                <!-- Action Buttons -->
                @if($mode !== 'view')
                <div class="flex space-x-3">
                    <button
                        type="submit"
                        class="flex-1 mobile-button {{ $isLoading ? 'opacity-50 cursor-not-allowed' : '' }}"
                        wire:loading.attr="disabled"                        
                    >
                        <span wire:loading.remove>📝 {{ __('mobile.submit_writing') }}</span>
                        <span wire:loading class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ __('mobile.submitting') }}
                        </span>
                    </button>
                </div>
                @endif
            </form>
        </div>
    </div>
</div>
