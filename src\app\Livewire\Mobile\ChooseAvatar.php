<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Avatar;
use App\Models\UserAvatar;

class ChooseAvatar extends Component
{
    public $user;
    public $availableAvatars = [];
    public $lockedAvatars = [];
    public $currentAvatar = null;
    public $userActivityPoints = 0;

    public function mount()
    {
        $this->user = Auth::user();
        $this->userActivityPoints = $this->user->getActivityPoints();
        $this->currentAvatar = $this->user->getCurrentAvatar();
        $this->loadAvatars();
    }

    private function loadAvatars()
    {
        // Get available avatars (user can select)
        $this->availableAvatars = $this->user->getAvailableAvatars();
        
        // Get locked avatars (user cannot select yet)
        $this->lockedAvatars = $this->user->getLockedAvatars();
    }

    public function selectAvatar($avatarId)
    {
        if (!$this->user->canSelectAvatar($avatarId)) {
            session()->flash('error', __('mobile.insufficient_points_for_avatar'));
            return;
        }

        if ($this->user->selectAvatar($avatarId)) {
            // Refresh current avatar
            $this->currentAvatar = $this->user->getCurrentAvatar();
            
            session()->flash('success', __('mobile.avatar_selected_successfully'));
            
            // Redirect back to profile after a short delay
            $this->dispatch('avatar-selected');
        } else {
            session()->flash('error', __('mobile.failed_to_select_avatar'));
        }
    }

    public function goBack()
    {
        return redirect()->route('mobile.me');
    }

    public function render()
    {
        return view('livewire.mobile.choose-avatar');
    }
}
