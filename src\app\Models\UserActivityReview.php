<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;
use App\Models\Traits\BypassesPermissionScopes;
use App\Services\FCMService;

class UserActivityReview extends BaseModel
{
    use BypassesPermissionScopes;

    /**
     * Review status constants.
     */
    const STATUS_WAITING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REJECTED = 2;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_activity_id',
        'review_date',
        'reviewed_by',
        'status',
        'feedback',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'review_date' => 'date',
            'status' => 'integer',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Handle approval/rejection when review status changes
        static::updated(function ($review) {
            if ($review->isDirty('status')) {
                $oldStatus = $review->getOriginal('status');
                $newStatus = $review->status;

                // If status changed to approved, approve the user activity
                if ($newStatus === self::STATUS_APPROVED && $oldStatus !== self::STATUS_APPROVED) {
                    $review->userActivity->approve();

                    // Send FCM notification to student
                    try {
                        $fcmService = app(FCMService::class);
                        $student = $review->userActivity->user;
                        $fcmService->sendActivityApprovalNotification($student, $review->userActivity);
                    } catch (\Exception $e) {
                        Log::error('Failed to send FCM approval notification: ' . $e->getMessage());
                    }
                }

                // If status changed to rejected, reject the user activity
                if ($newStatus === self::STATUS_REJECTED && $oldStatus !== self::STATUS_REJECTED) {
                    $review->userActivity->reject();

                    // Send FCM notification to student with feedback
                    try {
                        $fcmService = app(FCMService::class);
                        $student = $review->userActivity->user;
                        $fcmService->sendActivityRejectionNotification($student, $review->userActivity, $review->feedback ?? '');
                    } catch (\Exception $e) {
                        Log::error('Failed to send FCM rejection notification: ' . $e->getMessage());
                    }
                }
            }
        });
    }

    /**
     * Get the user activity being reviewed.
     */
    public function userActivity(): BelongsTo
    {
        return $this->belongsTo(UserActivity::class);
    }

    /**
     * Get the user who reviewed this activity.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by reviewer.
     */
    public function scopeByReviewer($query, $reviewerId)
    {
        return $query->where('reviewed_by', $reviewerId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('review_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get waiting reviews.
     */
    public function scopeWaiting($query)
    {
        return $query->where('status', self::STATUS_WAITING);
    }

    /**
     * Scope to get approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope to get rejected reviews.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    /**
     * Get the status name.
     */
    public function getStatusNameAttribute(): string
    {
        return match($this->status) {
            self::STATUS_WAITING => 'Waiting',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            default => 'Unknown',
        };
    }

    /**
     * Get the localized status name.
     */
    public function getLocalizedStatusNameAttribute(): string
    {
        return match($this->status) {
            self::STATUS_WAITING => __('admin.review_status_waiting'),
            self::STATUS_APPROVED => __('admin.review_status_approved'),
            self::STATUS_REJECTED => __('admin.review_status_rejected'),
            default => __('admin.review_status_unknown'),
        };
    }

    /**
     * Get the display name for the review.
     */
    public function getDisplayNameAttribute(): string
    {
        $reviewerName = $this->reviewer ? $this->reviewer->name : 'Unassigned';
        return $this->userActivity->display_name . ' - Review by ' . $reviewerName;
    }

    /**
     * Get summary information for the review.
     */
    public function getSummaryAttribute(): string
    {
        $reviewerName = $this->reviewer ? $this->reviewer->name : 'Unassigned';
        return sprintf(
            '%s reviewed on %s - %s',
            $reviewerName,
            $this->review_date->format('M d, Y'),
            $this->localized_status_name
        );
    }

    /**
     * Check if this review is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_WAITING;
    }

    /**
     * Check if this review is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if this review is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Get all review status options for forms.
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_WAITING => __('admin.review_status_waiting'),
            self::STATUS_APPROVED => __('admin.review_status_approved'),
            self::STATUS_REJECTED => __('admin.review_status_rejected'),
        ];
    }

    /**
     * Approve this review and the associated user activity.
     */
    public function approveReview($reviewerId = null, $feedback = null)
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
            'reviewed_by' => $reviewerId ?: auth('moonshine')->id(),
            'feedback' => $feedback,
            'review_date' => now()->toDateString(),
        ]);
    }

    /**
     * Reject this review and the associated user activity.
     */
    public function rejectReview($reviewerId = null, $feedback = null)
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'reviewed_by' => $reviewerId ?: auth('moonshine')->id(),
            'feedback' => $feedback,
            'review_date' => now()->toDateString(),
        ]);
    }
}
