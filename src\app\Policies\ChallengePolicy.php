<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Challenge;
use App\Models\User;

class ChallengePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // System admin, school admin, and teacher can view challenges
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function view(User $user, Challenge $item): bool
    {
        //TODO: School admin or teacher can see only challenges they are part of (their schools or classes)
        
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function create(User $user): bool
    {
        // System admin, school admin, and teacher can create challenges
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function update(User $user, Challenge $item): bool
    {
        return $this->view($user, $item);
    }

    public function delete(User $user, Challenge $item): bool
    {
        return $this->view($user, $item);
    }

    public function restore(User $user, Challenge $item): bool
    {
        return $this->view($user, $item);
    }

    public function forceDelete(User $user, Challenge $item): bool
    {
        return $this->view($user, $item);
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }
}
