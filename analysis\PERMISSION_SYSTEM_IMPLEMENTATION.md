# Role-Based Permission System Implementation

## Overview

This document describes the comprehensive role-based permission system implemented for the Laravel application. The system provides data isolation and access control based on user roles and their assigned schools/classes.

## Role Hierarchy

1. **System Admin (`system_admin`)** - Full access to all data
2. **School Admin (`school_admin`)** - Access to data within assigned schools
3. **Teacher (`teacher`)** - Access to data within assigned classes
4. **Student (`student`)** - Access to own data only

## Implementation Components

### 1. Database Migration

**File:** `database/migrations/2025_08_25_000000_add_permission_fields_to_tables.php`

- Adds `created_by` fields to all relevant tables for ownership tracking
- Creates foreign key relationships to users table
- Adds indexes for performance

### 2. Global Scopes

**Location:** `app/Models/Scopes/`

#### BasePermissionScope
- Abstract base class for all permission scopes
- Handles authentication checks and system admin bypass
- Provides helper methods for school/class filtering

#### UserDataScope
- Applied to: UserBook, UserReadingLog, UserActivity, UserPoint, UserAvatar
- Filters data based on user relationships (school/class assignments)

#### OwnershipScope
- Applied to: Task, Goal, Challenge, BookQuestion, BookWord
- Filters based on `created_by` field with role-based visibility

#### SchoolScope
- Applied to: SchoolClass, ClassBook
- Filters based on school assignments

#### TeamScope
- Applied to: Team, UserTeam, TeamReward
- Filters based on team membership and school/class relationships

#### GoalChallengeScope
- Applied to: UserGoal, UserGoalTask, UserChallengeTask
- Filters goal/challenge data based on user relationships

### 3. Model Updates

All relevant models have been updated with:
- `#[ScopedBy([ScopeClass::class])]` attributes
- `created_by` field in fillable arrays
- `creator()` relationship methods
- `BypassesPermissionScopes` trait for system operations

### 4. Policy Updates

All model policies have been updated to implement the permission matrix:
- Role-based access checks
- School/class relationship validation
- Ownership verification for created content

### 5. Automatic Operations

**Trait:** `app/Models/Traits/BypassesPermissionScopes.php`

Provides methods to bypass permission scopes for:
- Automatic reward creation
- System-triggered operations
- Administrative functions

**Usage:**
```php
// Create without scopes (for system operations)
UserReward::createWithoutScopes($attributes);

// Query without scopes
UserReward::queryWithoutScopes()->where(...)->get();
```

## Permission Matrix

### System Admin
- Full CRUD access to ALL models

### School Admin
- SchoolClass: CRUD only for their assigned schools
- User: CRUD for teacher/student roles within their schools
- UserSchool, UserClass: CRUD within their schools/classes
- Team, UserTeam: CRUD for students in their schools
- ClassBook: CRUD within their schools/classes
- BookQuestion, BookWord: CRUD for own records
- UserBook, UserReadingLog, UserPoint: CRUD for students in their schools
- UserActivity: CRUD within their schools/classes
- UserAvatar: CRUD for students in their schools
- UserReward: Manual CRUD for students in their schools
- TeamReward: Manual CRUD for teams in their schools
- Task: CRUD for own created challenges/goals
- Goal, Challenge: CRUD for students in their schools

### Teacher
- Same permissions as school_admin but limited to assigned classes only

### Student
- UserBook, UserReadingLog, UserPoint, UserAvatar: CRUD only for themselves
- UserActivity: Create and update only for themselves
- UserGoalTask, UserChallengeTask: Update only for assigned tasks

### Read-only Access (All Users)
- EnumSchoolType, EnumClassLevel, BookType, ActivityCategory
- Activity, Avatar, Reward, RewardTask, EnumTaskType, EnumTaskCycle
- School: Via UserSchool table relationships

### System-only Models
- Role, Permission: CRUD restricted to system_admin only
- UserAgreement: Management by system_admin only
- Author, Publisher, Book: CRUD by system_admin, automatic creation via backend
- UserActivityReview, UserReward (automatic), TeamReward (automatic): System events

## Testing

**File:** `tests/Feature/PermissionSystemTest.php`

Comprehensive tests covering:
- System admin full access
- Student data isolation
- School admin scope filtering
- Automatic operation bypass
- Policy enforcement

## Usage Guidelines

### For Developers

1. **Adding New Models:**
   - Apply appropriate scope using `#[ScopedBy([ScopeClass::class])]`
   - Add `created_by` field if ownership tracking needed
   - Create corresponding policy
   - Add `BypassesPermissionScopes` trait if system operations needed

2. **System Operations:**
   - Use `withoutGlobalScopes()` or trait methods for automatic operations
   - Always use scoped methods for user-initiated operations

3. **Queries:**
   - Normal queries automatically apply permission filtering
   - Use `withoutGlobalScopes()` only for system operations
   - Test with different user roles to ensure proper filtering

### For Administrators

1. **User Management:**
   - Assign users to appropriate roles
   - Set up school/class relationships via UserSchool/UserClass tables
   - Verify permission boundaries through testing

2. **Monitoring:**
   - Monitor automatic operations to ensure they continue working
   - Verify data isolation between schools/classes
   - Check that system operations bypass scopes correctly

## Security Considerations

1. **Data Isolation:** Users can only access data within their permitted scope
2. **Automatic Operations:** System operations bypass scopes to maintain functionality
3. **Role Verification:** All operations verify user roles and relationships
4. **Audit Trail:** `created_by` fields provide ownership tracking
5. **Policy Enforcement:** Laravel policies provide additional access control layer

## Maintenance

1. **Adding New Roles:** Update scopes and policies to handle new roles
2. **Schema Changes:** Update migration if new permission fields needed
3. **Performance:** Monitor query performance with scopes applied
4. **Testing:** Run permission tests after any changes to ensure system integrity
