# Amazon TR Book Discovery Provider Implementation

## Overview

This document describes the implementation of the Amazon TR book discovery provider for the kitapokuma application. The provider integrates with amazon.com.tr to search for books by ISBN and extract book metadata.

## Implementation Details

### Files Created/Modified

1. **New Provider Class**: `src/app/Services/BookDiscovery/Providers/AmazonTRProvider.php`
2. **Configuration**: Added `amazontr` section to `src/config/book_discovery.php`
3. **Service Registration**: Modified `src/app/Services/BookDiscovery/BookDiscoveryService.php`

### Provider Architecture

The `AmazonTRProvider` extends `AbstractBookDiscoveryProvider` and implements the following key methods:

#### Core Methods

- **`buildSearchUrl(string $isbn)`**: Constructs search URL using pattern `https://www.amazon.com.tr/s?k={ISBN}&i=stripbooks`
- **`isBookNotFound(string $html)`**: Detects no results by checking for "sonuç bulunamadı" vs "Sonuçlar" text
- **`parseBookData(string $html)`**: Two-stage parsing process (search results → detail page)

#### Search Process Flow

1. **Search Results Page**: Extract detail URL from first `div[data-cy="title-recipe"]` element
2. **Detail Page Fetch**: Retrieve book detail page using extracted URL
3. **Data Extraction**: Parse book metadata from detail page HTML

### Data Extraction Mapping

| Field | HTML Selector | Database Field | Type | Processing |
|-------|---------------|----------------|------|------------|
| Book Title | `span#productTitle` | `name` | string | HTML entity decode, trim |
| Cover Image | `img#landingImage[src]` | `cover_image` | string (URL) | Direct URL extraction |
| ISBN | `span.a-list-item span.a-text-bold:contains("ISBN-13") + span` | `isbn` | string | Remove hyphens and spaces |
| Authors | `span.author a` | `author` | array | Multiple authors, unique values |
| Publisher | `span.a-list-item span.a-text-bold:contains("Yayıncı") + span` | `publisher` | string | HTML entity decode |
| Page Count | `div#rpi-attribute-book_details-fiona_pages .rpi-attribute-value span` | `page_count` | integer | Extract number, remove " sayfa" |
| Publication Year | `span.a-list-item span.a-text-bold:contains("Yayınlanma Tarihi") + span` | `year_of_publish` | integer | Turkish date parsing |

### Turkish Date Parsing

The provider includes specialized Turkish date parsing functionality:

#### Turkish Month Names Mapping
```php
'Ocak' => 1, 'Şubat' => 2, 'Mart' => 3, 'Nisan' => 4,
'Mayıs' => 5, 'Haziran' => 6, 'Temmuz' => 7, 'Ağustos' => 8,
'Eylül' => 9, 'Ekim' => 10, 'Kasım' => 11, 'Aralık' => 12
```

#### Date Format Support
- **Primary**: 4-digit year extraction (`/(\d{4})/`)
- **Turkish Format**: "28 Ocak 2025" pattern parsing
- **Validation**: Year range 1000 to current year + 1

### Configuration

```php
'amazontr' => [
    'name' => 'Amazon TR',
    'priority' => 4,
    'enabled' => true,
    'search_url' => 'https://www.amazon.com.tr/s?k={isbn}&i=stripbooks',
    'timeout' => 15,
    'not_found_indicators' => [
        'text_patterns' => ['sonuç bulunamadı', 'Sonuç bulunamadı'],
        'empty_selectors' => ['div[data-cy="title-recipe"]']
    ],
    // ... parsing rules
]
```

### Error Handling

- **Network Failures**: Graceful handling with retry mechanism from parent class
- **HTML Parsing Errors**: DOM creation with error suppression
- **Missing Fields**: Optional field handling with null checks
- **Invalid Data**: Data validation and cleanup
- **Anti-Scraping**: Proper user agents and rate limiting

### Data Processing

#### Post-Processing Steps

1. **Text Cleaning**: HTML entity decoding and whitespace normalization
2. **Author Array**: Multiple authors extracted as unique array
3. **ISBN Cleaning**: Remove all hyphens and spaces from ISBN
4. **Page Count**: Extract numeric value, remove " sayfa" suffix
5. **Date Parsing**: Turkish date format to year extraction
6. **Source Attribution**: Adds `source: 'Amazon TR'` to result

#### Validation

- Title is required (minimum 2 characters)
- Year must be between 1000 and current year + 1
- Page count must be positive integer
- Authors cleaned and filtered for empty values
- ISBN cleaned of formatting characters

### Integration

The provider is automatically registered in `BookDiscoveryService` and will be used in the discovery priority order:

1. Database lookup
2. Local repository
3. External providers (by priority: D&R → Fidan Kitap → Kitabinabak → Amazon TR)

### Usage Example

```php
$service = new BookDiscoveryService();
$bookData = $service->searchByIsbn('9786257947824');

if ($bookData) {
    echo "Found: " . $bookData['name'];
    echo "Authors: <AUTHORS>
    echo "Publisher: " . $bookData['publisher'];
    echo "Year: " . $bookData['year_of_publish'];
    echo "Pages: " . $bookData['page_count'];
}
```

### Amazon-Specific Considerations

#### Anti-Scraping Measures
- Uses rotating user agents
- Respects rate limiting delays
- Proper HTTP headers for legitimate browsing simulation

#### Search URL Structure
- `k` parameter: ISBN search term
- `i=stripbooks` parameter: Filters results to books only
- Direct product page redirects handled automatically

#### HTML Structure Specifics
- `data-cy="title-recipe"` for search result identification
- `#productTitle` for book title extraction
- `#landingImage` for cover image
- Structured data attributes for metadata

### Testing Considerations

- **Valid ISBN**: Test with `9786257947824` (should return results)
- **Invalid ISBN**: Test with non-existent ISBN (should return "sonuç bulunamadı")
- **Missing Fields**: Handle cases where optional fields are absent
- **Turkish Characters**: Ensure proper UTF-8 handling
- **Date Formats**: Test various Turkish date format variations

### Performance Considerations

- **Caching**: Inherits caching mechanism from parent class
- **Rate Limiting**: Respects configured delays between requests
- **Timeout Handling**: 15-second timeout for HTTP requests
- **Memory Efficient**: DOM objects properly cleaned up
- **Two-Stage Parsing**: Minimizes unnecessary detail page fetches

### Logging

The provider logs important events:
- Search attempts and results
- HTTP errors and retries
- Parsing failures and warnings
- Data extraction success/failure
- Turkish date parsing attempts

### Constraints Followed

- ✅ No modifications to `AbstractBookDiscoveryProvider`
- ✅ No breaking changes to existing providers
- ✅ Database field compatibility verified
- ✅ Consistent code style with existing providers
- ✅ Backward compatibility maintained
- ✅ Turkish language support implemented
- ✅ Amazon-specific parsing patterns implemented

### Future Enhancements

Potential improvements for future versions:
- Category extraction from Amazon breadcrumbs
- Book description/summary extraction
- Customer rating and review count
- Enhanced anti-scraping countermeasures
- Support for Amazon's API if available

## Conclusion

The Amazon TR provider successfully integrates with the existing book discovery architecture, providing an additional high-quality source for book metadata while maintaining system reliability and performance standards. The implementation includes specialized Turkish language support and handles Amazon's specific HTML structure and anti-scraping measures.
