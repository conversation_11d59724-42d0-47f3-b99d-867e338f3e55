<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schools', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('school_type_id')->nullable()->constrained('enum_school_types')->onDelete('set null');
            $table->boolean('active')->default(true);

            // Add indexes and constraints
            $table->index(['school_type_id', 'active']);
            $table->unique(['name', 'school_type_id']);
            $table->index('school_type_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schools');
    }
};
