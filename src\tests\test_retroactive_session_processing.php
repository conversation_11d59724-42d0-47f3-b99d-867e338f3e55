<?php

/**
 * Test Retroactive Session Processing Fix
 * 
 * This script tests the fix for retroactive processing when activities are completed
 * AFTER a book session is already completed (end_date is set).
 * 
 * The issue was that getCurrentSession() only returns active sessions (end_date IS NULL),
 * but retroactive processing needs to work on completed sessions too.
 */

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Book;
use App\Models\UserBook;
use App\Models\UserReadingLog;
use App\Models\UserPoint;
use App\Models\Activity;
use App\Models\UserActivity;

echo "=== RETROACTIVE SESSION PROCESSING TEST ===\n\n";

try {
    // Find a test user and book
    $user = User::where('email', 'like', '%test%')->first();
    if (!$user) {
        $user = User::first();
    }
    
    if (!$user) {
        echo "❌ No users found in database\n";
        exit(1);
    }
    
    $book = Book::where('active', true)->first();
    if (!$book) {
        echo "❌ No active books found in database\n";
        exit(1);
    }
    
    echo "📚 Test Setup:\n";
    echo "- User: {$user->name} (ID: {$user->id})\n";
    echo "- Book: {$book->name} (ID: {$book->id})\n\n";
    
    // Clean up any existing data for this test
    echo "🧹 Cleaning up existing test data...\n";
    UserPoint::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserActivity::where('user_id', $user->id)->delete();
    UserReadingLog::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    UserBook::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // Find a required activity for testing
    $requiredActivity = Activity::where('active', true)->where('required', true)->first();
    if (!$requiredActivity) {
        echo "⚠️  No required activities found - creating a mock scenario\n";
        echo "   This test simulates the retroactive processing logic\n\n";
    } else {
        echo "🎯 Found required activity: {$requiredActivity->name} (ID: {$requiredActivity->id})\n\n";
    }
    
    // STEP 1: Create a reading session with multiple logs
    echo "📖 STEP 1: Creating reading session with multiple logs...\n";
    
    $userBook = UserBook::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'start_date' => now()->subDays(5),
        'end_date' => null, // Active session initially
    ]);
    echo "✅ Created UserBook session (ID: {$userBook->id})\n";
    
    // Create multiple reading logs
    $readingLogs = [];
    
    // Log 1: 10 pages, not completed
    $log1 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->subDays(4),
        'start_page' => 1,
        'end_page' => 10,
        'pages_read' => 10,
        'reading_duration' => 30,
        'book_completed' => false,
    ]);
    $readingLogs[] = $log1;
    echo "✅ Created Log 1: 10 pages, not completed (ID: {$log1->id})\n";
    
    // Log 2: 15 pages, not completed
    $log2 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->subDays(3),
        'start_page' => 11,
        'end_page' => 25,
        'pages_read' => 15,
        'reading_duration' => 45,
        'book_completed' => false,
    ]);
    $readingLogs[] = $log2;
    echo "✅ Created Log 2: 15 pages, not completed (ID: {$log2->id})\n";
    
    // Log 3: 20 pages, COMPLETED - This completes the session
    $log3 = UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->subDays(2),
        'start_page' => 26,
        'end_page' => 45,
        'pages_read' => 20,
        'reading_duration' => 60,
        'book_completed' => true,
    ]);
    $readingLogs[] = $log3;
    echo "✅ Created Log 3: 20 pages, COMPLETED (ID: {$log3->id})\n";
    
    // STEP 2: Complete the session (simulate book completion)
    echo "\n📅 STEP 2: Completing the session (end_date set)...\n";
    $userBook->update(['end_date' => now()->subDays(2)]);
    echo "✅ Session completed with end_date: " . $userBook->fresh()->end_date . "\n";
    
    // Verify no active session exists now
    $activeSession = UserBook::getCurrentSession($user->id, $book->id);
    if ($activeSession) {
        echo "❌ ERROR: Active session still exists when it should be completed\n";
    } else {
        echo "✅ Confirmed: No active session exists (session is completed)\n";
    }
    
    // STEP 3: Check initial points (should be none if required activities exist)
    echo "\n💰 STEP 3: Checking initial points...\n";
    $initialPoints = UserPoint::where('user_id', $user->id)
        ->where('book_id', $book->id)
        ->where('point_type', UserPoint::POINT_TYPE_PAGE)
        ->get();
    
    echo "Initial points records: " . $initialPoints->count() . "\n";
    if ($initialPoints->count() > 0) {
        echo "⚠️  Points were awarded initially (no required activities or different logic)\n";
    } else {
        echo "✅ No points awarded initially (expected if required activities exist)\n";
    }
    
    // STEP 4: Test the new findSessionForLog method
    echo "\n🔍 STEP 4: Testing findSessionForLog method (with date comparison fix)...\n";
    foreach ($readingLogs as $index => $log) {
        $foundSession = $log->findSessionForLog();
        if ($foundSession && $foundSession->id === $userBook->id) {
            echo "✅ Log " . ($index + 1) . " correctly found its session (ID: {$foundSession->id})\n";
            echo "   Log date: {$log->log_date} | Session: {$foundSession->start_date} to " . ($foundSession->end_date ?? 'ongoing') . "\n";
        } else {
            echo "❌ Log " . ($index + 1) . " failed to find its session\n";
            if ($foundSession) {
                echo "   Found wrong session (ID: {$foundSession->id})\n";
            } else {
                echo "   No session found at all\n";
            }
            echo "   Log date: {$log->log_date}\n";
            echo "   Expected session: {$userBook->start_date} to " . ($userBook->end_date ?? 'ongoing') . "\n";
        }
    }
    
    // STEP 5: Simulate retroactive processing (activity completed after session ended)
    echo "\n🎯 STEP 5: Simulating retroactive processing...\n";
    echo "Scenario: Required activity completed AFTER book session was completed\n";
    
    // Clear any existing points to simulate the withholding scenario
    UserPoint::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // Call the retroactive processing method
    echo "Calling awardWithheldRewardsForBook()...\n";
    UserReadingLog::awardWithheldRewardsForBook($user->id, $book->id);
    
    // STEP 6: Check results
    echo "\n📊 STEP 6: Checking results after retroactive processing...\n";
    $finalPoints = UserPoint::where('user_id', $user->id)
        ->where('book_id', $book->id)
        ->where('point_type', UserPoint::POINT_TYPE_PAGE)
        ->get();
    
    echo "Final points records: " . $finalPoints->count() . "\n";
    
    foreach ($readingLogs as $index => $log) {
        $logPoints = $finalPoints->where('source_id', $log->id)->first();
        if ($logPoints) {
            echo "✅ Log " . ($index + 1) . " (ID: {$log->id}): {$logPoints->points} points awarded\n";
        } else {
            echo "❌ Log " . ($index + 1) . " (ID: {$log->id}): NO points awarded\n";
        }
    }
    
    $totalPoints = $finalPoints->sum('points');
    echo "\n📈 Total Points Awarded: {$totalPoints}\n";
    
    // STEP 7: Verify the fix worked
    echo "\n🎉 STEP 7: Verification Results...\n";
    
    if ($finalPoints->count() === count($readingLogs)) {
        echo "✅ SUCCESS: All " . count($readingLogs) . " reading logs received points via retroactive processing\n";
        echo "✅ FIX CONFIRMED: Session-based retroactive processing works for completed sessions\n";
    } else {
        echo "❌ FAILURE: Expected " . count($readingLogs) . " point records, got " . $finalPoints->count() . "\n";
        echo "❌ The fix may not be working correctly\n";
    }
    
    // Test the awardPointsForSession method directly
    echo "\n🧪 STEP 8: Testing awardPointsForSession method directly...\n";
    
    // Clear points again
    UserPoint::where('user_id', $user->id)->where('book_id', $book->id)->delete();
    
    // Get the completed session
    $completedSession = UserBook::where('user_id', $user->id)
        ->where('book_id', $book->id)
        ->where('completed', true)
        ->first();
    
    if ($completedSession) {
        echo "Found completed session (ID: {$completedSession->id})\n";
        
        // Call awardPointsForSession directly
        $log1->awardPointsForSession($completedSession);
        
        $directPoints = UserPoint::where('user_id', $user->id)
            ->where('book_id', $book->id)
            ->where('point_type', UserPoint::POINT_TYPE_PAGE)
            ->get();
        
        echo "Points created by awardPointsForSession: " . $directPoints->count() . "\n";
        
        if ($directPoints->count() === count($readingLogs)) {
            echo "✅ SUCCESS: Direct method call worked correctly\n";
        } else {
            echo "❌ FAILURE: Direct method call didn't work as expected\n";
        }
    } else {
        echo "❌ ERROR: Could not find completed session for direct testing\n";
    }
    
    echo "\n🎉 Test completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
