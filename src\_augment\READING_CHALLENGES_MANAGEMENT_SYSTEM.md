# Reading Challenges Management System

## Overview

A comprehensive reading challenges management system that builds upon the existing task management infrastructure, allowing teachers to organize reading competitions across multiple schools, classes, or teams with automatic progress tracking through reading logs and activities.

## Database Schema

### Core Tables

#### `challenges`
- `id` - Primary key
- `name` - Challenge name
- `description` - Challenge description (nullable)
- `image` - Challenge banner/flyer image path (nullable)
- `start_date` - Challenge start date
- `end_date` - Challenge end date
- `prize` - Challenge reward description (nullable)
- `active` - Active status (boolean, default: true)

#### `challenge_tasks`
- `id` - Primary key
- `challenge_id` - Foreign key to challenges
- `task_id` - Foreign key to tasks
- `start_date` - Task start date within challenge
- `end_date` - Task end date within challenge
- **Unique constraint:** `challenge_id`, `task_id`, `start_date`, `end_date`

#### `challenge_classes`
- `id` - Primary key
- `challenge_id` - Foreign key to challenges
- `school_class_id` - Foreign key to school_classes
- **Unique constraint:** `challenge_id`, `school_class_id`

#### `challenge_schools`
- `id` - Primary key
- `challenge_id` - Foreign key to challenges
- `school_id` - Foreign key to schools
- **Unique constraint:** `challenge_id`, `school_id`

#### `challenge_teams`
- `id` - Primary key
- `challenge_id` - Foreign key to challenges
- `team_id` - Foreign key to teams
- **Unique constraint:** `challenge_id`, `team_id`

#### `user_challenge_tasks`
- `id` - Primary key
- `challenge_task_id` - Foreign key to challenge_tasks
- `team_id` - Foreign key to teams (nullable)
- `user_id` - Foreign key to users
- `assigned_by` - Foreign key to users (who assigned)
- `assign_date` - Assignment timestamp
- `completed` - Completion status (boolean, default: false)
- `complete_date` - Completion timestamp (nullable)
- **Unique constraint:** `challenge_task_id`, `user_id`, `team_id`

### Enhanced Existing Tables

Added `challenge_task_id` foreign key (nullable, 'set null' on delete) to:
- `user_books` - Links book reading to challenge tasks
- `user_reading_logs` - Links reading logs to challenge tasks
- `user_activities` - Links activities to challenge tasks

## Key Models and Features

### Challenge Model (`App\Models\Challenge`)

**Relationships:**
- `challengeTasks()` - HasMany ChallengeTask
- `userChallengeTasks()` - HasManyThrough UserChallengeTask
- `tasks()` - BelongsToMany Task (through challenge_tasks)
- `schools()` - BelongsToMany School
- `classes()` - BelongsToMany SchoolClass
- `teams()` - BelongsToMany Team

**Scopes:**
- `active()` - Active challenges
- `current()` - Currently running challenges
- `upcoming()` - Future challenges
- `past()` - Completed challenges

**Key Methods:**
- `isCurrentlyActive()` - Check if challenge is within date range
- `assignToSchools()` - Assign challenge to schools (assigns to all classes)
- `assignToClasses()` - Assign challenge to classes (assigns to all students)
- `assignToTeams()` - Assign challenge to teams (assigns to all members)

**Computed Attributes:**
- `status_display` - Current status (Active, Upcoming, Completed, Inactive)
- `date_range_display` - Formatted date range
- `duration` - Duration in days
- `tasks_count` - Number of challenge tasks
- `participants_count` - Number of participating users
- `completion_rate` - Overall completion percentage
- `summary` - Summary information

### ChallengeTask Model (`App\Models\ChallengeTask`)

**Relationships:**
- `challenge()` - BelongsTo Challenge
- `task()` - BelongsTo Task
- `userChallengeTasks()` - HasMany UserChallengeTask

**Scopes:**
- `active()` - Currently active challenge tasks
- `upcoming()` - Future challenge tasks
- `past()` - Completed challenge tasks

**Computed Attributes:**
- `status_display` - Current status
- `date_range_display` - Formatted date range
- `duration` - Duration in days
- `participants_count` - Number of participants
- `completion_count` - Number of completed tasks
- `completion_rate` - Completion percentage

### UserChallengeTask Model (`App\Models\UserChallengeTask`)

**Relationships:**
- `challengeTask()` - BelongsTo ChallengeTask
- `team()` - BelongsTo Team (nullable)
- `user()` - BelongsTo User
- `assignedBy()` - BelongsTo User
- `challenge()` - BelongsTo Challenge (through challengeTask)
- `task()` - BelongsTo Task (through challengeTask)

**Scopes:**
- `completed()` - Completed tasks
- `pending()` - Pending tasks
- `individual()` - Individual assignments
- `team()` - Team assignments
- `byUser()` - Filter by user
- `byTeam()` - Filter by team

**Key Methods:**
- `isIndividual()` / `isTeam()` - Check assignment type
- `isOverdue()` - Check if task is overdue
- `markAsCompleted()` / `markAsPending()` - Update completion status

**Computed Attributes:**
- `assignment_type_display` - Individual or Team
- `status_display` - Completed or Pending
- `days_since_assignment` - Days since assignment
- `days_to_complete` - Days remaining (if not completed)
- `progress_percentage` - Progress based on related activities

## MoonShine Admin Resources

### ChallengeResource
- **Main challenge management** similar to GoalResource
- **Image upload** for challenge banners/flyers
- **Date range validation** (end_date must be after start_date)
- **Multi-select fields** for participating schools, classes, and teams
- **RelationRepeater** for challenge tasks with date ranges
- **Role-based filtering** for participant selection

### ChallengeTaskResource
- **Link challenges to tasks** with specific date ranges
- **Date validation** ensures task dates are within challenge date range
- **Progress tracking** shows participants and completion rates
- **Task type and cycle information** from linked tasks

### UserChallengeTaskResource
- **Individual student progress tracking** similar to UserGoalTaskResource
- **Assignment type display** (Individual vs Team)
- **Progress monitoring** with completion status and dates
- **Role-based access control** for viewing assignments
- **Overdue task identification** and progress percentage calculation

## Automatic Assignment Logic

### Challenge Creation Flow
1. **Create Challenge** - Define basic challenge information
2. **Add Challenge Tasks** - Link tasks with specific date ranges using RelationRepeater
3. **Select Participants** - Choose schools, classes, and/or teams
4. **Automatic Processing** - System creates UserChallengeTask records for all participants

### Assignment Processing
- **School Assignment** → All classes in school → All students in classes
- **Class Assignment** → All students in selected classes
- **Team Assignment** → All members of selected teams
- **Individual Tracking** - Each student gets individual UserChallengeTask records
- **Team Context** - Team assignments include team_id for group tracking

## Progress Tracking Integration

### Automatic Linking
- **Reading Activities** - user_reading_logs linked via challenge_task_id
- **Book Completion** - user_books linked via challenge_task_id
- **Activity Completion** - user_activities linked via challenge_task_id

### Progress Calculation
- **Task-based Progress** - Calculated from linked reading logs, books, and activities
- **Challenge Progress** - Aggregated from all challenge tasks
- **Team Progress** - Combined progress of all team members
- **Real-time Updates** - Progress updated as students complete activities

## Role-Based Access Control

### System Administrator
- **Full Access** - Can create challenges for all schools, classes, and teams
- **Global View** - Can see all challenges and progress across the system
- **Complete Management** - Can manage all aspects of challenge system

### School Administrator
- **School Scope** - Can create challenges for their schools and classes
- **School View** - Can see challenges and progress for their schools
- **Limited Management** - Can manage challenges within their school scope

### Teachers
- **Class Scope** - Can create challenges for their assigned classes
- **Class View** - Can see challenges and progress for their students
- **Student Management** - Can track individual student progress in challenges

### Students
- **Personal View** - Can see only their own challenge assignments and progress
- **No Management** - Cannot create or manage challenges
- **Progress Tracking** - Can view their own completion status and progress

## Business Rules and Validation

### Challenge Validation
- **Date Range** - End date must be after start date
- **Task Dates** - Challenge task dates must be within challenge date range
- **Unique Tasks** - Same task cannot be assigned multiple times with same dates
- **Active Status** - Only active challenges can have new assignments

### Assignment Validation
- **Duplicate Prevention** - Same user cannot be assigned same challenge task multiple times
- **Role Permissions** - Users can only assign challenges within their scope
- **Date Consistency** - Assignment dates must be logical and within challenge period

### Progress Validation
- **Completion Logic** - Tasks marked complete automatically set completion timestamp
- **Overdue Detection** - System identifies overdue tasks based on end dates
- **Progress Calculation** - Progress percentages calculated from actual activity data

## Integration with Existing Systems

### Task Management System
- **Reuses Existing Tasks** - Challenges use the same task definitions
- **Task Types Supported** - All existing task types (READ_BOOKS, READ_PAGES, etc.)
- **Task Cycles** - Supports daily, weekly, monthly task cycles

### Reading Tracking System
- **Reading Logs** - Automatically linked to challenge tasks when within date range
- **Book Completion** - Book reading sessions linked to relevant challenge tasks
- **Activity Completion** - Reading activities linked to challenge tasks

### Gamification System
- **Badge Integration** - Challenge completion can trigger badge awards
- **Point System** - Challenge activities contribute to user points
- **Team Competition** - Team-based challenges support team gamification

### User Management System
- **Role-based Access** - Integrates with existing role system
- **School Hierarchy** - Respects existing school-class-user relationships
- **Permission System** - Uses existing permission framework

## Menu Structure

**Challenge Management** menu group added to MoonShine admin:
- **Challenges** - Main challenge management
- **Challenge Tasks** - Challenge-task relationships
- **User Challenge Tasks** - Individual progress tracking

Located after Goal Management in the admin menu structure.

## Translation Support

**Complete English and Turkish translations** for:
- Challenge management interface
- Form labels and hints
- Status messages and displays
- Validation messages
- Menu items and navigation

## Future Enhancement Opportunities

### Advanced Features
- **Challenge Templates** - Reusable challenge configurations
- **Automated Scheduling** - Recurring challenges with automatic setup
- **Advanced Analytics** - Detailed progress and participation analytics
- **Leaderboards** - Real-time competition rankings
- **Notification System** - Challenge start/end and progress notifications

### Integration Enhancements
- **Calendar Integration** - Challenge deadlines in calendar views
- **Report Generation** - Automated challenge progress reports
- **Parent Portal** - Parent access to student challenge progress
- **Mobile App** - Dedicated mobile interface for challenge participation

### Gamification Enhancements
- **Challenge Badges** - Special badges for challenge completion
- **Achievement Levels** - Bronze, silver, gold challenge achievements
- **Team Rewards** - Team-based reward systems
- **Challenge Streaks** - Consecutive challenge participation tracking

The Reading Challenges Management System provides a comprehensive platform for organizing and tracking reading competitions while seamlessly integrating with the existing task management and reading tracking infrastructure.
