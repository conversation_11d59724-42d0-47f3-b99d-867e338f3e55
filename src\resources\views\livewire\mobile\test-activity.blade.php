<div class="min-h-screen bg-base-200">
    <x-mobile-page-header route="{{ route('mobile.books.activities', $book->id) }}" header="{{ $activity->name }}" />

    <div class="p-4">
        <!-- Activity Info -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center">
                    @if($activity->isQuiz())
                        <span class="text-2xl">📝</span>
                    @else
                        <span class="text-2xl">📚</span>
                    @endif
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-1">{{ $activity->name }}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ $book->name }}</p>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="mobile-badge">{{ $activity->points }} {{ __('mobile.points') }}</span>
                        @if($activity->required)
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">{{ __('mobile.required') }}</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        @if($isCompleted && $testResults)
            <!-- Test Results -->
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-sm">
                <div class="text-center">
                    @if($testResults['passed'])
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-3xl">✅</span>
                        </div>
                        <h3 class="text-xl font-bold text-green-600 mb-2">{{ __('mobile.test_passed_successfully') }}</h3>
                    @else
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-3xl">❌</span>
                        </div>
                        <h3 class="text-xl font-bold text-red-600 mb-2">{{ __('mobile.test_failed') }}</h3>
                    @endif
                    
                    <div class="bg-gray-50 rounded-xl p-4 mb-4">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold text-gray-900">{{ $testResults['score_percentage'] }}%</div>
                                <div class="text-sm text-gray-600">{{ __('mobile.score') }}</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-gray-900">{{ $testResults['correct_answers'] }}/{{ $testResults['total_questions'] }}</div>
                                <div class="text-sm text-gray-600">{{ __('mobile.correct_answers') }}</div>
                            </div>
                        </div>
                    </div>

                    @if($testResults['min_grade_required'] > 0)
                        <p class="text-sm text-gray-600 mb-4">
                            {{ __('mobile.minimum_grade_required') }}: {{ $testResults['min_grade_required'] }}%
                        </p>
                    @endif

                    @if(!$testResults['passed'] && $testResults['remaining_attempts'] > 0)
                        <div class="mb-4">
                            <p class="text-sm text-gray-600 mb-2">
                                {{ __('mobile.remaining_attempts') }}: {{ $testResults['remaining_attempts'] }}
                            </p>
                            <button wire:click="retakeTest" class="mobile-button bg-orange-500 hover:bg-orange-600">
                                {{ __('mobile.retake_test') }}
                            </button>
                        </div>
                    @endif

                    <button wire:click="goBackToActivities" class="mobile-button-secondary">
                        {{ __('mobile.back_to_activities') }}
                    </button>
                </div>
            </div>
        @elseif($quiz && !$isCompleted)
            <!-- Test Interface -->
            <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
                <!-- Progress Bar -->
                <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span>{{ __('mobile.progress') }}</span>
                        <span>{{ $currentQuestionIndex + 1 }} / {{ $totalQuestions }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full transition-all duration-300" 
                             style="width: {{ (($currentQuestionIndex + 1) / $totalQuestions) * 100 }}%"></div>
                    </div>
                </div>

                <!-- Question Navigation -->
                <div class="flex flex-wrap gap-2 mb-6">
                    @for($i = 0; $i < $totalQuestions; $i++)
                        <button
                            wire:click="goToQuestion({{ $i }})"
                            type="button"
                            class="w-8 h-8 rounded-full text-sm font-semibold transition-colors
                                @if($i === $currentQuestionIndex)
                                    bg-primary text-white
                                @elseif($this->getUserAnswer($i) !== null)
                                    bg-green-100 text-green-600
                                @else
                                    bg-gray-100 text-gray-600
                                @endif"
                        >
                            {{ $i + 1 }}
                        </button>
                    @endfor
                </div>

                @if($currentQuestion)
                    <!-- Current Question -->
                    <div class="mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">
                            {!! $currentQuestion['question'] !!}
                        </h4>

                        @if(isset($currentQuestion['question_image_url']) && $currentQuestion['question_image_url'])
                            <div class="mb-4">
                                <img src="{{ $currentQuestion['question_image_url'] }}" 
                                     alt="Question Image" 
                                     class="max-w-full h-auto rounded-lg">
                            </div>
                        @endif

                        <!-- Answer Choices -->
                        <div class="space-y-3">
                            @foreach($currentQuestion['choices'] as $index => $choice)
                                <button
                                    wire:click="selectAnswer({{ $currentQuestionIndex }}, {{ $index }})"
                                    type="button"
                                    class="w-full text-left p-4 rounded-xl border-2 transition-colors
                                        @if($this->isAnswerSelected($currentQuestionIndex, $index))
                                            border-primary bg-primary-300 bg-opacity-10
                                        @else
                                            border-gray-200 hover:border-gray-300
                                        @endif"
                                >
                                    <div class="flex items-center space-x-3">
                                        <div class="w-6 h-6 rounded-full border-2 flex items-center justify-center
                                            @if($this->isAnswerSelected($currentQuestionIndex, $index))
                                                border-primary bg-primary
                                            @else
                                                border-gray-300
                                            @endif">
                                            @if($this->isAnswerSelected($currentQuestionIndex, $index))
                                                <span class="text-white text-sm">✓</span>
                                            @endif
                                        </div>
                                        <span class="flex-1">{!! $choice !!}</span>
                                    </div>
                                </button>
                            @endforeach
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between">
                        <button
                            wire:click="previousQuestion"
                            type="button"
                            @if($currentQuestionIndex === 0) disabled @endif
                            class="mobile-button-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {{ __('mobile.previous') }}
                        </button>

                        @if($currentQuestionIndex === $totalQuestions - 1)
                            <button
                                wire:click="submitTest"
                                type="button"
                                class="mobile-button bg-green-500 hover:bg-green-600"
                            >
                                {{ __('mobile.submit_test') }}
                            </button>
                        @else
                            <button
                                wire:click="nextQuestion"
                                type="button"
                                class="mobile-button"
                            >
                                {{ __('mobile.next') }}
                            </button>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Test Info -->
            <div class="bg-blue-50 rounded-2xl p-4 shadow-sm">
                <div class="flex items-start space-x-3">
                    <span class="text-2xl">ℹ️</span>
                    <div class="flex-1">
                        <h4 class="font-semibold text-blue-900 mb-2">{{ __('mobile.test_instructions') }}</h4>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li>• {{ __('mobile.answer_all_questions') }}</li>
                            <li>• {{ __('mobile.you_can_navigate_between_questions') }}</li>
                            @if($activity->min_grade)
                                <li>• {{ __('mobile.minimum_score_required', ['score' => $activity->min_grade]) }}%</li>
                            @endif
                            @if($activity->allowed_tries > 1)
                                <li>• {{ __('mobile.you_have_attempts', ['attempts' => $activity->allowed_tries]) }}</li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
