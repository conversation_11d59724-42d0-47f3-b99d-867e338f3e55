<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\SchoolClass;
use App\Models\User;

class SchoolClassPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher();
    }

    public function view(User $user, SchoolClass $item): bool
    {
        // System admin can view all
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admin can view classes in their assigned schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();
            return in_array($item->school_id, $userSchoolIds);
        }

        // Teacher can view their assigned classes
        if ($user->isTeacher()) {
            $userClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();
            return in_array($item->id, $userClassIds);
        }

        return false;
    }

    public function create(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin();
    }

    public function update(User $user, SchoolClass $item): bool
    {
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admin can update classes in their assigned schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();
            return in_array($item->school_id, $userSchoolIds);
        }

        return false;
    }

    public function delete(User $user, SchoolClass $item): bool
    {
        return $this->update($user, $item);
    }

    public function restore(User $user, SchoolClass $item): bool
    {
        return $this->update($user, $item);
    }

    public function forceDelete(User $user, SchoolClass $item): bool
    {
        return $this->update($user, $item);
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin() || $user->isSchoolAdmin();
    }
}
