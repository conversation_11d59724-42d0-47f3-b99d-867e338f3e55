<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\FCMService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class SendInactivityNotificationJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $fcmService = app(FCMService::class);
        $inactivityThreshold = config('fcm.inactivity_days_threshold', 3);

        if (!config('fcm.inactivity_reminder_enabled', true)) {
            Log::info('Inactivity reminders are disabled');
            return;
        }

        // Get students who haven't had reading activity in X days
        $cutoffDate = Carbon::now()->subDays($inactivityThreshold);

        $inactiveStudents = User::whereHas('roles', function ($query) {
                $query->where('name', 'student');
            })
            ->whereNotNull('fcm_token')
            ->whereDoesntHave('readingLogs', function ($query) use ($cutoffDate) {
                $query->where('log_date', '>=', $cutoffDate);
            })
            ->get();

        Log::info("Found {$inactiveStudents->count()} inactive students for notification");

        $successCount = 0;
        foreach ($inactiveStudents as $student) {
            try {
                if ($fcmService->sendInactivityReminder($student)) {
                    $successCount++;
                }
            } catch (\Exception $e) {
                Log::error("Failed to send inactivity reminder to user {$student->id}: " . $e->getMessage());
            }
        }

        Log::info("Sent {$successCount} inactivity reminder notifications out of {$inactiveStudents->count()} inactive students");
    }
}
