<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('page_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('book_type_id')->constrained('book_types')->onDelete('cascade');
            $table->foreignId('class_level_id')->constrained('enum_class_levels')->onDelete('cascade');
            $table->decimal('point', 8, 2)->default(0.00);

            $table->unique(['book_type_id', 'class_level_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('page_points');
    }
};
