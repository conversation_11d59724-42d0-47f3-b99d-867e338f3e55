<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\User;
use App\Models\Level;
use App\Models\UserLevel;
use App\Models\UserReadingLog;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Text;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('star')]
class UserLevelResource extends BaseResource
{
    protected string $model = UserLevel::class;

    protected array $with = ['user', 'level', 'readingLog'];

    public function getTitle(): string
    {
        return __('admin.user_levels');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.level'),
                'level',
                formatted: fn(Level $level) => $level->display_name,
                resource: LevelResource::class
            )
                ->sortable(),

            Date::make(__('admin.achievement_date'), 'level_date')
                ->format('d.m.Y H:i')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.user'),
                    'user',
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class
                )
                    ->required(),

                BelongsTo::make(
                    __('admin.level'),
                    'level',
                    formatted: fn(Level $level) => $level->display_name,
                    resource: LevelResource::class
                )
                    ->required(),

                Date::make(__('admin.achievement_date'), 'level_date')
                    ->format('d.m.Y H:i')
                    ->required()
                    ->default(now()),

                BelongsTo::make(
                    __('admin.triggered_by_reading'),
                    'readingLog',
                    formatted: fn(?UserReadingLog $log) => $log ? $log->book->name . ' (' . $log->log_date->format('d.m.Y') . ')' : null,
                    resource: UserReadingLogResource::class
                )
                    ->nullable()
                    ->searchable()
                    ->asyncSearch('book.name',  formatted: fn(UserReadingLog $log) => $log->book->name . ' (' . $log->log_date->format('d.m.Y') . ')')
                    ->hint(__('admin.reading_log_trigger_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.level'),
                'level',
                formatted: fn(Level $level) => $level->display_name,
                resource: LevelResource::class
            ),

            Date::make(__('admin.achievement_date'), 'level_date')
                ->format('d.m.Y H:i'),

            BelongsTo::make(
                __('admin.triggered_by_reading'),
                'readingLog',
                formatted: fn(?UserReadingLog $log) => $log ? $log->book->name . ' (' . $log->log_date->format('d.m.Y') . ')' : __('admin.manual'),
                resource: UserReadingLogResource::class
            ),

            Text::make(__('admin.summary'), 'summary'),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'level_id' => ['required', 'exists:levels,id'],
            'level_date' => ['required', 'date'],
            'reading_log_id' => ['nullable', 'exists:user_reading_logs,id'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['user.name', 'level.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['level_date' => 'desc'];
    }

    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        return $builder->forCurrentUser();
    }
}
