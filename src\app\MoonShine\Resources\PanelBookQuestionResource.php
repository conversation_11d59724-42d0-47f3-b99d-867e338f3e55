<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Book;
use App\MoonShine\Resources\PanelBookResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use MoonShine\UI\Components\{Layout\Box, Layout\Flex, Layout\LineBreak, Tabs\Tab, Components, Tabs};
use MoonShine\UI\Fields\{File, Image, Number, Switcher, Text, Textarea, Url};

#[Icon('question-mark-circle')]
class PanelBookQuestionResource extends BookQuestionResource
{
    use WithRolePermissions;
    protected bool $editInModal = true;
    protected bool $detailInModal = true;     

    protected function indexFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.books'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            )
                ->sortable(),

            Text::make(__('admin.question_text'), 'question_text')
                ->sortable(),

            Switcher::make(__('admin.is_active'), 'is_active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make( __('admin.books'), 'book',
                            formatted: fn(Book $book) => html_entity_decode($book->name),
                            resource: PanelBookResource::class )
                            ->required()
                            ->placeholder(__('admin.select_book')),

                        Textarea::make(__('admin.question_text'), 'question_text')
                            ->required()
                            ->placeholder(__('admin.enter_question_text')),

                        Text::make(__('admin.correct_answer'), 'correct_answer')
                            ->required()
                            ->placeholder(__('admin.enter_correct_answer')),

                        Switcher::make(__('admin.is_active'), 'is_active')
                            ->default(true),
                    ]),

                    Tab::make(__('admin.answer_options'), [
                        Text::make(__('admin.incorrect_answer') . ' 1', 'incorrect_answer_1')
                            ->placeholder(__('admin.enter_incorrect_answer')),

                        Text::make(__('admin.incorrect_answer') . ' 2', 'incorrect_answer_2')
                            ->placeholder(__('admin.enter_incorrect_answer')),

                        Text::make(__('admin.incorrect_answer') . ' 3', 'incorrect_answer_3')
                            ->placeholder(__('admin.enter_incorrect_answer')),

                        Text::make(__('admin.incorrect_answer') . ' 4', 'incorrect_answer_4')
                            ->placeholder(__('admin.enter_incorrect_answer')),

                        Text::make(__('admin.incorrect_answer') . ' 5', 'incorrect_answer_5')
                            ->placeholder(__('admin.enter_incorrect_answer')),
                    ]),

                    Tab::make(__('admin.page_reference'), [
                        Flex::make([
                            Number::make(__('admin.page_start'), 'page_start')
                                ->min(1)
                                ->placeholder(__('admin.enter_page_start')),

                            Number::make(__('admin.page_end'), 'page_end')
                                ->min(1)
                                ->placeholder(__('admin.enter_page_end')),
                        ]),
                    ]),
/*
// Disabled for now. It will come as an update if needed 

                    Tab::make(__('admin.media_content'), [
                        Image::make(__('admin.question_image_url'), 'question_image_url')
                            ->dir('book_questions')
                            ->removable()
                            ->hint(__('admin.enter_image_url')),

                         File::make(__('admin.question_audio_url'), 'question_audio_url')
                            ->dir('book_questions')
                            ->removable()
                            ->hint(__('admin.enter_audio_url')),


                        File::make(__('admin.question_video_url'), 'question_video_url')
                            ->dir('book_questions')
                            ->removable()
                            ->hint(__('admin.enter_video_url')),
                    ]),
*/
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
/*        
        $imageField = $this->item->media_type === 'image' ? [ Image::make(__('admin.question_image_url'), 'question_image_url') ] : [];
        $audioField = $this->item->media_type === 'audio' ? [ File::make(__('admin.media_content'), 'media_url')
            ->changePreview(fn(?string $value) => "<audio width='300px' controls><source src='" . asset('storage/' . $value) . "' type='audio/mpeg'></audio>")
            ->changeRender(fn(?string $value, File $ctx) => (string) Components::make([
                    File::make($ctx->getLabel())->withoutWrapper(),
                    LineBreak::make(),
                    $ctx->preview()
                ])) ] : [];
        $videoField = $this->item->media_type === 'video' ? [ File::make(__('admin.question_video_url'), 'question_video_url')
                ->changePreview(fn(?string $value) => "<video width='300px' controls><source src='" . asset('storage/' . $value) . "' type='video/mp4'></video>")                
                ->changeRender(fn(?string $value, File $ctx) => (string) Components::make([
                    File::make($ctx->getLabel())->withoutWrapper(),
                    LineBreak::make(),
                    $ctx->preview()
                ])) ] : [];
*/

        return [
            Text::make(__('admin.question_text'), 'question_text'),
            BelongsTo::make(
                __('admin.books'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            ),
            Text::make(__('admin.correct_answer'), 'correct_answer')
                ->badge('green'),
            Text::make(__('admin.incorrect_answer') . ' 1', 'incorrect_answer_1')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 2', 'incorrect_answer_2')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 3', 'incorrect_answer_3')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 4', 'incorrect_answer_4')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 5', 'incorrect_answer_5')
                ->badge('red'),
            Text::make(__('admin.page_range'), 'page_range')
                ->badge('blue'),
            Switcher::make(__('admin.is_active'), 'is_active')
                ->disabled(),                
/*                
            Text::make(__('admin.media_type'), 'media_type')
                ->badge(fn($mediaType) => match ($mediaType) {
                    'video' => 'red',
                    'audio' => 'yellow',
                    'image' => 'green',
                    default => 'gray'
                }),
            ...$imageField,
            ...$audioField,
            ...$videoField,
*/            
        ];
    }

    protected function filters(): iterable
    {
        return [
        ];
    }
}
