<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\EnumSchoolType;
use App\Models\User;

class EnumSchoolTypePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, EnumSchoolType $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return $user->isSystemAdmin();
    }

    public function update(User $user, EnumSchoolType $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function delete(User $user, EnumSchoolType $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function restore(User $user, EnumSchoolType $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function forceDelete(User $user, EnumSchoolType $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin();
    }
}
