<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Publisher extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'created_by',
    ];

    /**
     * Get books published by this publisher.
     */
    public function books(): HasMany
    {
        return $this->hasMany(Book::class);
    }

    /**
     * Scope to search publishers by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%');
    }

    /**
     * Get book count for this publisher.
     */
    public function getBookCountAttribute(): int
    {
        return $this->books()->count();
    }

    /**
     * Check if publisher has books.
     */
    public function hasBooks(): bool
    {
        return $this->book_count > 0;
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the most recent book by this publisher.
     */
    public function getLatestBookAttribute(): ?Book
    {
        return $this->books()
                    ->orderBy('year_of_publish', 'desc')
                    ->first();
    }

    /**
     * Get books published in a specific year.
     */
    public function booksInYear(int $year)
    {
        return $this->books()->where('year_of_publish', $year);
    }

    /**
     * Get unique authors this publisher has worked with.
     */
    public function getAuthorsAttribute()
    {
        return Author::whereIn('id', 
            BookAuthor::whereIn('book_id', $this->books()->pluck('id'))
                     ->distinct()
                     ->pluck('author_id')
        )->get();
    }

    /**
     * Get total pages published by this publisher.
     */
    public function getTotalPagesAttribute(): int
    {
        return $this->books()->sum('page_count');
    }

    /**
     * Get average pages per book for this publisher.
     */
    public function getAveragePagesAttribute(): float
    {
        return $this->books()->avg('page_count') ?? 0;
    }
}
