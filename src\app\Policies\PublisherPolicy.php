<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Publisher;
use App\Models\User;

class PublisherPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, Publisher $item): bool
    {
        return true;
    }

    public function create(User $user): bool
    {
        return $user->isSystemAdmin();
    }

    public function update(User $user, Publisher $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function delete(User $user, Publisher $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function restore(User $user, Publisher $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function forceDelete(User $user, Publisher $item): bool
    {
        return $user->isSystemAdmin();
    }

    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin();
    }
}
