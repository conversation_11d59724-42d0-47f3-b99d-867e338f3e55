<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\Book;
use App\Models\User;

class BookPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        // All users can view books for selection purposes
        return true;
    }

    public function view(User $user, Book $item): bool
    {
        // All users can view individual books
        return true;
    }

    public function create(User $user): bool
    {
        // Only system admin can create books (automatic creation via backend find book service)
        return $user->isSystemAdmin();
    }

    public function update(User $user, Book $item): bool
    {
        // Only system admin can update books
        return $user->isSystemAdmin();
    }

    public function delete(User $user, Book $item): bool
    {
        // Only system admin can delete books
        return $user->isSystemAdmin();
    }

    public function restore(User $user, Book $item): bool
    {
        // Only system admin can restore books
        return $user->isSystemAdmin();
    }

    public function forceDelete(User $user, Book $item): bool
    {
        // Only system admin can force delete books
        return $user->isSystemAdmin();
    }

    public function massDelete(User $user): bool
    {
        // Only system admin can mass delete books
        return $user->isSystemAdmin();
    }
}
