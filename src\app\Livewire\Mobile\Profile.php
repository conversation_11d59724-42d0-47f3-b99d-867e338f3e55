<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserBook;
use App\Services\StatisticsService;

class Profile extends Component
{
    public $user;
    public $stats = [];
    public $readingStreak = [];
    public $motivationalMessage;
    public $recentRewards = [];
    public $hasReadingHistory = false;

    // Avatar modal properties
    public $showAvatarModal = false;
    public $currentAvatar = null;

    public function mount()
    {
        $this->user = Auth::user();
        $this->currentAvatar = $this->user->getCurrentAvatar();
        $this->loadUserStats();
        $this->loadReadingStreak();
        $this->loadRecentRewards();
        $this->checkReadingHistory();
    }

    private function loadUserStats()
    {
        $this->stats = StatisticsService::getStudentProfileStats($this->user);
    }

    private function loadReadingStreak()
    {
        // Get reading pattern analysis with motivational message
        $analysis = $this->user->getReadingPatternAnalysis();
        
        $this->readingStreak = $analysis['reading_data'];
        $this->motivationalMessage = $analysis['message'];
    }

    private function loadRecentRewards()
    {
        $this->recentRewards = $this->user->getRecentRewardsByLimit(5);
    }

    private function checkReadingHistory()
    {
        $userId = $this->user->id;
        
        $hasCurrentBooks = UserBook::where('user_id', $userId)
            ->inProgress()
            ->exists();
            
        $hasCompletedBooks = UserBook::where('user_id', $userId)
            ->completed()
            ->exists();

        $this->hasReadingHistory = $hasCurrentBooks || $hasCompletedBooks;
    }

    public function navigateToAvatarSelection()
    {
        return redirect()->route('mobile.choose-avatar');
    }

    public function navigateToMyRewards()
    {
        return redirect()->route('mobile.my-rewards');
    }

    /**
     * Show avatar detail modal
     */
    public function showAvatarDetail()
    {
        $this->currentAvatar = $this->user->getCurrentAvatar();
        $this->showAvatarModal = true;
    }

    /**
     * Close avatar detail modal
     */
    public function closeAvatarModal()
    {
        $this->showAvatarModal = false;
    }

    public function render()
    {
        return view('livewire.mobile.profile');
    }
}
