<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\DB;

class ClassActivityResolutionScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $user = $this->getCurrentUser();
        if (!$user || $user->isSystemAdmin()) {
            return;
        }
        
        $defaultClass = $user->getDefaultClass();
        if (!$defaultClass) {
            return;
        }
        
        $builder->leftJoin('class_activities', function ($join) use ($defaultClass) {
            $join->on('activities.id', '=', 'class_activities.activity_id')
                 ->where('class_activities.class_id', '=', $defaultClass->class_id);
        })
        ->select([
            'activities.id',
            'activities.category_id',
            'activities.activity_type',
            'activities.name',
            'activities.description',
            'activities.choices_count',
            'activities.min_rating',
            'activities.max_rating',
            'activities.media_url',
            'activities.media_type',
            // Use COALESCE to prefer ClassActivity values over Activity values
            DB::raw('COALESCE(class_activities.question_count, activities.question_count) as question_count'),
            DB::raw('COALESCE(class_activities.min_grade, activities.min_grade) as min_grade'),
            DB::raw('COALESCE(class_activities.allowed_tries, activities.allowed_tries) as allowed_tries'),
            DB::raw('COALESCE(class_activities.min_word_count, activities.min_word_count) as min_word_count'),
            DB::raw('COALESCE(class_activities.points, activities.points) as points'),
            DB::raw('COALESCE(class_activities.required, activities.required) as required'),
            DB::raw('COALESCE(class_activities.need_approval, activities.need_approval) as need_approval'),
            DB::raw('COALESCE(class_activities.active, activities.active) as active'),
        ]);
    }

    public function getCurrentUser(): ?User
    {
        $user = auth('moonshine')->user();
        if (!$user) {
            $user = auth('web')->user();
        }
        
        return $user instanceof User ? $user : null;
    }

}
