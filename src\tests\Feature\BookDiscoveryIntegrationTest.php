<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Book;
use App\Models\BookType;
use App\Models\Publisher;
use App\Models\User;
use App\Services\BookDiscovery\BookDiscoveryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class BookDiscoveryIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected BookType $bookType;
    protected Publisher $publisher;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'username' => 'testuser',
            'password' => bcrypt('password'),
        ]);

        // Create test book type
        $this->bookType = BookType::create([
            'name' => 'Test Book Type',
            'description' => 'Test book type description',
            'created_by' => $this->user->id,
        ]);

        // Create test publisher
        $this->publisher = Publisher::create([
            'name' => 'Test Publisher',
            'created_by' => $this->user->id,
        ]);
    }

    /** @test */
    public function book_discovery_service_can_be_instantiated()
    {
        $service = app(BookDiscoveryService::class);
        
        $this->assertInstanceOf(BookDiscoveryService::class, $service);
    }

    /** @test */
    public function book_discovery_service_returns_null_for_invalid_isbn()
    {
        $service = app(BookDiscoveryService::class);
        
        $result = $service->searchByIsbn('invalid-isbn');
        
        $this->assertNull($result);
    }

    /** @test */
    public function book_discovery_service_finds_existing_local_book()
    {
        // Create a book in the database
        $book = Book::create([
            'name' => 'Test Book',
            'isbn' => '9781234567890',
            'publisher_id' => $this->publisher->id,
            'book_type_id' => $this->bookType->id,
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->user->id,
        ]);

        $service = app(BookDiscoveryService::class);
        
        $result = $service->searchByIsbn('9781234567890');
        
        $this->assertNotNull($result);
        $this->assertEquals('Test Book', $result['name']);
        $this->assertEquals('9781234567890', $result['isbn']);
        $this->assertTrue($result['exists_locally']);
        $this->assertEquals($book->id, $result['id']);
    }

    /** @test */
    public function book_discovery_service_can_create_book_from_data()
    {
        $bookData = [
            'name' => 'New Test Book',
            'isbn' => '9780987654321',
            'author' => ['Test Author'],
            'publisher' => 'New Publisher',
            'year' => 2024,
            'page_count' => 300,
        ];

        $service = app(BookDiscoveryService::class);
        
        $book = $service->createBookFromData($bookData, $this->user->id);
        
        $this->assertNotNull($book);
        $this->assertEquals('New Test Book', $book->name);
        $this->assertEquals('9780987654321', $book->isbn);
        $this->assertEquals(300, $book->page_count);
        $this->assertEquals(2024, $book->year_of_publish);
        $this->assertEquals($this->user->id, $book->created_by);
        
        // Check that publisher was created
        $this->assertNotNull($book->publisher);
        $this->assertEquals('New Publisher', $book->publisher->name);
        
        // Check that author was created and attached
        $this->assertCount(1, $book->authors);
        $this->assertEquals('Test Author', $book->authors->first()->name);
    }

    /** @test */
    public function mobile_add_book_component_uses_discovery_service()
    {
        $this->actingAs($this->user);

        // Mock the discovery service to return test data
        $mockBookData = [
            'name' => 'Discovered Book',
            'isbn' => '9781111111111',
            'author' => ['Discovered Author'],
            'publisher' => 'Discovered Publisher',
            'year' => 2023,
            'page_count' => 250,
            'source' => 'Test Source',
        ];

        // Clear cache to ensure fresh search
        Cache::flush();

        // Mock HTTP responses for external sources (since they're disabled in config)
        Http::fake([
            'https://www.googleapis.com/books/v1/volumes*' => Http::response([
                'items' => [
                    [
                        'volumeInfo' => [
                            'name' => 'Discovered Book',
                            'authors' => ['Discovered Author'],
                            'publisher' => 'Discovered Publisher',
                            'publishedDate' => '2023',
                            'pageCount' => 250,
                        ]
                    ]
                ]
            ])
        ]);

        // Test the mobile component
        $component = \Livewire\Livewire::test(\App\Livewire\Mobile\AddBook::class)
            ->set('isbn', '9781111111111')
            ->call('searchByIsbn');

        // Since external sources are disabled, it should fall back to Google Books
        // and show book type selection for discovered books
        $component->assertSet('showBookTypeSelection', true)
                  ->assertSet('discoveredBookData.title', 'Discovered Book');
    }

    /** @test */
    public function book_type_selection_component_can_create_book()
    {
        $this->actingAs($this->user);

        $bookData = [
            'name' => 'Book for Type Selection',
            'isbn' => '9782222222222',
            'author' => ['Selection Author'],
            'publisher' => 'Selection Publisher',
            'year' => 2023,
            'page_count' => 180,
            'source' => 'Test Source',
        ];

        $component = \Livewire\Livewire::test(\App\Livewire\Mobile\BookTypeSelection::class, [
            'bookData' => $bookData
        ])
            ->set('selectedBookTypeId', $this->bookType->id)
            ->call('createBook');

        $component->assertHasNoErrors()
                  ->assertSet('successMessage', __('mobile.book_created_success'));

        // Verify book was created in database
        $this->assertDatabaseHas('books', [
            'name' => 'Book for Type Selection',
            'isbn' => '9782222222222',
            'book_type_id' => $this->bookType->id,
            'page_count' => 180,
            'year_of_publish' => 2023,
        ]);
    }

    /** @test */
    public function isbn_validation_works_correctly()
    {
        $service = app(BookDiscoveryService::class);
        
        // Test valid ISBNs
        $validResult = $service->searchByIsbn('9781234567890'); // 13-digit
        // Should not throw exception for valid format
        
        $validResult2 = $service->searchByIsbn('123456789X'); // 10-digit with X
        // Should not throw exception for valid format
        
        // Test invalid ISBNs
        $invalidResult = $service->searchByIsbn('123'); // Too short
        $this->assertNull($invalidResult);
        
        $invalidResult2 = $service->searchByIsbn('12345678901234'); // Too long
        $this->assertNull($invalidResult2);
        
        $invalidResult3 = $service->searchByIsbn('abcdefghij'); // Non-numeric
        $this->assertNull($invalidResult3);
    }

    /** @test */
    public function configuration_is_loaded_correctly()
    {
        $config = config('book_discovery');
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('sources', $config);
        $this->assertArrayHasKey('settings', $config);
        $this->assertArrayHasKey('validation', $config);
        
        // Check that sources are configured
        $this->assertArrayHasKey('dr', $config['sources']);
    }

    /** @test */
    public function cache_is_used_for_repeated_searches()
    {
        Cache::flush();
        
        $service = app(BookDiscoveryService::class);
        
        // First search - should hit external sources/cache miss
        $result1 = $service->searchByIsbn('9783333333333');
        
        // Second search - should hit cache
        $result2 = $service->searchByIsbn('9783333333333');
        
        // Results should be identical (both null in this case since book doesn't exist)
        $this->assertEquals($result1, $result2);
    }
}
