<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Reward;
use App\Models\Task;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Image;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Laravel\Fields\Relationships\RelationRepeater;
use MoonShine\Support\Attributes\Icon;

#[Icon('star')]
class RewardResource extends BaseResource
{
    protected string $model = Reward::class;

    protected string $column = 'name';

    protected array $with = ['tasks', 'userRewards', 'teamRewards'];

    public function getTitle(): string
    {
        return __('admin.rewards');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Image::make(__('admin.image'), 'image'),

            Text::make(__('admin.reward_name'), 'name')
                ->sortable(),

            Select::make(__('admin.reward_type'), 'reward_type')
                ->options(Reward::getRewardTypes())
                ->sortable(),

            Text::make(__('admin.reward_type_display'), 'reward_type_display'),

            Number::make(__('admin.tasks_count'), 'tasks_count')
                ->sortable(),

            Number::make(__('admin.users_earned_count'), 'users_earned_count')
                ->sortable(),

            Number::make(__('admin.teams_earned_count'), 'teams_earned_count')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make(__('admin.basic_info'), [
                Text::make(__('admin.reward_name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_reward_name')),

                Select::make(__('admin.reward_type'), 'reward_type')
                    ->options(Reward::getRewardTypes())
                    ->required()
                    ->default(Reward::TYPE_BADGE),

                Textarea::make(__('admin.reward_description'), 'description')
                    ->placeholder(__('admin.enter_reward_description')),

                Image::make(__('admin.reward_image'), 'image')
                    ->dir('rewards')
                    ->removable()
                    ->hint(__('admin.reward_image_hint')),

                Switcher::make(__('admin.active'), 'active')
                    ->default(true),

                Switcher::make(__('admin.repeatable'), 'repeatable')
                    ->default(false)
                    ->hint(__('admin.repeatable_reward_hint')),
            ]),

            Box::make(__('admin.reward_tasks'), [
                BelongsToMany::make(
                    __('admin.associated_tasks'),
                    'tasks',
                    formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                    resource: TaskResource::class
                )
                    ->selectMode()
                    ->asyncSearch('name',
                        formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')')
                    ->creatable()
                    ->hint(__('admin.select_tasks_for_reward')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Image::make(__('admin.reward_image'), 'image'),

            Text::make(__('admin.reward_name'), 'name'),
            Text::make(__('admin.reward_type_display'), 'reward_type_display'),
            Textarea::make(__('admin.reward_description'), 'description'),
            Switcher::make(__('admin.active'), 'active'),
            Switcher::make(__('admin.repeatable'), 'repeatable'),
            
            Number::make(__('admin.tasks_count'), 'tasks_count'),
            Number::make(__('admin.users_earned_count'), 'users_earned_count'),
            Number::make(__('admin.teams_earned_count'), 'teams_earned_count'),
            Text::make(__('admin.summary'), 'summary'),

            HasMany::make(__('admin.associated_tasks'), 'tasks', TaskResource::class),
            HasMany::make(__('admin.user_rewards'), 'userRewards', UserRewardResource::class),
            HasMany::make(__('admin.team_rewards'), 'teamRewards', TeamRewardResource::class),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'reward_type' => ['required', 'integer', 'in:1,2,3,4'],
            'description' => ['nullable', 'string'],
            'image' => ['image', 'mimes:jpeg,jpg,png,webp,gif', 'max:2048'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];

        return $rules;
    }

    protected function search(): array
    {
        return ['name', 'description'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
