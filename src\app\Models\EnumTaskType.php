<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class EnumTaskType extends BaseModel
{
    /**
     * Task type constants.
     */
    const READ_PAGES = 1;
    const READ_BOOKS = 2;
    const READ_MINUTES = 3;
    const READ_DAYS = 4;
    const READ_STREAK = 5;
    const EARN_READING_POINTS = 6;
    const EARN_ACTIVITY_POINTS = 7;
    const COMPLETE_BOOK_ACTIVITY = 8;
    const COMPLETE_BOOK_LIST = 9;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'nr',
        'name',
    ];

    /**
     * Get the tasks for this task type.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class, 'task_type_id');
    }

    /**
     * Scope to get task type by number.
     */
    public function scopeByNumber($query, $nr)
    {
        return $query->where('nr', $nr);
    }

    /**
     * Check if this task type is quantitative (requires task_value).
     */
    public function isQuantitative(): bool
    {
        return in_array($this->nr, [
            self::READ_PAGES,
            self::READ_BOOKS,
            self::READ_MINUTES,
            self::READ_DAYS,
            self::READ_STREAK,
            self::EARN_READING_POINTS,
            self::EARN_ACTIVITY_POINTS,
            self::COMPLETE_BOOK_ACTIVITY,
        ]);
    }

    /**
     * Check if this task type is qualitative (book list completion).
     */
    public function isQualitative(): bool
    {
        return in_array($this->nr, [
            self::COMPLETE_BOOK_LIST
        ]);
    }

    /**
     * Check if this task type requires book list selection.
     */
    public function requiresBookList(): bool
    {
        return $this->nr === self::COMPLETE_BOOK_LIST;
    }

    /**
     * Check if this task type requires book categories selection.
     */
    public function requiresBookCategories(): bool
    {
        // Can be extended for future task types that need category filtering
        return false;
    }

    /**
     * Check if this task type requires activity selection.
     */
    public function requiresActivity(): bool
    {
        return $this->nr === self::COMPLETE_BOOK_ACTIVITY;
    }

    /**
     * Check if this task type is measurable (has numeric progress).
     */
    public function isMeasurable(): bool
    {
        return $this->isQuantitative();
    }

    /**
     * Check if this task type is binary (achieved/not achieved only).
     */
    public function isBinary(): bool
    {
        return $this->isQualitative();
    }

    /**
     * Get the unit for this task type.
     */
    public function getUnitAttribute(): string
    {
        return match($this->nr) {
            self::READ_PAGES => __('admin.pages_localizable'),
            self::READ_BOOKS => __('admin.books_localizable'),
            self::READ_MINUTES => __('admin.minutes_localizable'),
            self::READ_DAYS => __('admin.days_localizable'),
            self::READ_STREAK => __('admin.days_localizable'),
            self::EARN_READING_POINTS => __('admin.points_localizable'),
            self::EARN_ACTIVITY_POINTS => __('admin.points_localizable'),
            self::COMPLETE_BOOK_ACTIVITY => __('admin.activities_localizable'),
            self::COMPLETE_BOOK_LIST => __('admin.books_localizable'),
            default => '',
        };
    }

    /**
     * Get the description for this task type.
     */
    public function getDescriptionAttribute(): string
    {
        return match($this->nr) {
            self::READ_PAGES => __('admin.task_type_read_pages_desc'),
            self::READ_BOOKS => __('admin.task_type_read_books_desc'),
            self::READ_MINUTES => __('admin.task_type_read_minutes_desc'),
            self::READ_DAYS => __('admin.task_type_read_days_desc'),
            self::READ_STREAK => __('admin.task_type_read_streak_desc'),
            self::EARN_READING_POINTS => __('admin.task_type_earn_reading_points_desc'),
            self::EARN_ACTIVITY_POINTS => __('admin.task_type_earn_activity_points_desc'),
            self::COMPLETE_BOOK_ACTIVITY => __('admin.task_type_complete_book_activity_desc'),
            self::COMPLETE_BOOK_LIST => __('admin.task_type_complete_book_list_desc'),
            default => $this->name,
        };
    }

    /**
     * Get all quantitative task types.
     */
    public static function getQuantitativeTypes(): array
    {
        return [
            self::READ_PAGES,
            self::READ_BOOKS,
            self::READ_MINUTES,
            self::READ_DAYS,
            self::READ_STREAK,
            self::EARN_READING_POINTS,
            self::EARN_ACTIVITY_POINTS,
            self::COMPLETE_BOOK_ACTIVITY,
        ];
    }

    /**
     * Get all qualitative task types.
     */
    public static function getQualitativeTypes(): array
    {
        return [
            self::COMPLETE_BOOK_LIST,
        ];
    }

    /**
     * Get task types that require book selection.
     */
    public static function getBookListTypes(): array
    {
        return [
            self::COMPLETE_BOOK_LIST,
        ];
    }

    /**
     * Get task types that require activity selection.
     */
    public static function getActivityTypes(): array
    {
        return [
            self::COMPLETE_BOOK_ACTIVITY,
        ];
    }

    /**
     * Get the display name with description.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' - ' . $this->description;
    }

    /**
     * Get the summary of this task type.
     */
    public function getSummaryAttribute(): string
    {
        $type = $this->isQuantitative() ? __('admin.quantitative') : __('admin.qualitative');
        return sprintf('%s (%s)', $this->name, $type);
    }

    // get all defined task types in db into an array with types are keys
    public static function getTaskTypes(): array
    {
        return self::all()->keyBy('nr')->toArray();
    }
}
