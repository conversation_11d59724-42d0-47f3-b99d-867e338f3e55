<div class="min-h-screen bg-base-200">
     <x-mobile-page-header route="{{ route('mobile.me') }}" header="{{ __('mobile.my_rewards') . ' (' . $totalRewards . ')' }}" />

     <div class="p-4">

        @if($totalRewards > 0)
            @foreach($rewardsByType as $typeGroup)
                <!-- Reward Type Section -->
                <div class="mb-6">
                    <!-- Section Header -->
                    <div class="flex items-center mb-4">
                        <span class="text-2xl mr-2">{{ $typeGroup['type_icon'] }}</span>
                        <h2 class="text-lg font-bold text-gray-900">
                            {{ $typeGroup['type_name'] }} ({{ $typeGroup['count'] }})
                        </h2>
                    </div>

                    <!-- Rewards Grid for this type -->
                    <div class="grid grid-cols-2 gap-4">
                        @foreach($typeGroup['rewards'] as $userReward)
                            <div class="bg-white rounded-2xl p-4 shadow-sm text-center">
                                <!-- Reward Image -->
                                <div class="w-20 h-20 mx-auto mb-3 rounded-full overflow-hidden border-2 border-gray-200 relative">
                                    @if($userReward->reward && $userReward->reward->image)
                                        <img src="{{ asset('storage/' . $userReward->reward->image) }}" 
                                             alt="{{ $userReward->reward->name }}" 
                                             class="w-full h-full object-cover">
                                    @else
                                        @php
                                            $bgColor = match($userReward->reward->reward_type) {
                                                1 => 'bg-yellow-400', // Badge
                                                2 => 'bg-green-400',  // Gift
                                                3 => 'bg-purple-400', // Trophy
                                                4 => 'bg-blue-400',   // Item
                                                default => 'bg-gray-400'
                                            };
                                        @endphp
                                        <div class="w-full h-full {{ $bgColor }} flex items-center justify-center">
                                            <span class="text-3xl">{{ $typeGroup['type_icon'] }}</span>
                                        </div>
                                    @endif
                                    
                                    <!-- Type indicator badge -->
                                    <div class="absolute -bottom-1 -right-1 w-6 h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center">
                                        <span class="text-sm">{{ $typeGroup['type_icon'] }}</span>
                                    </div>
                                </div>

                                <!-- Reward Name -->
                                <h3 class="font-bold text-gray-900 text-sm mb-2">
                                    {{ $userReward->reward->name ?? __('mobile.rewards') }}
                                </h3>

                                <!-- Reward Description (if available) -->
                                @if($userReward->reward && $userReward->reward->description)
                                    <p class="text-xs text-gray-600 mb-2">
                                        {{ $userReward->reward->description }}
                                    </p>
                                @endif

                                <!-- Earned Date -->
                                <p class="text-xs text-gray-500">
                                    {{ $userReward->awarded_date->format('d.m.Y') }}
                                </p>

                                <!-- Source Information -->
                                 {{--
                                @if($userReward->readingLog && $userReward->readingLog->book)
                                    <p class="text-xs text-violet-600 mt-1">
                                        {{ __('mobile.from_book') }}: {!! Str::limit($userReward->readingLog->book->name, 20) !!}
                                    </p>
                                @elseif($userReward->userActivity && $userReward->userActivity->activity)
                                    <p class="text-xs text-violet-600 mt-1">
                                        {{ __('mobile.from_activity') }}: {{ Str::limit($userReward->userActivity->activity->name, 20) }}
                                    </p>
                                @endif
                                --}}
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        @else
            <!-- No Rewards Message -->
            <div class="bg-white rounded-2xl p-8 shadow-sm text-center">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('mobile.no_rewards_yet') }}</h3>
                <p class="text-gray-500 text-sm mb-4">{{ __('mobile.complete_activities_to_earn_rewards') }}</p>
                
                <!-- Motivational Tips -->
                <div class="bg-violet-50 rounded-xl p-4 text-left">
                    <h4 class="font-semibold text-violet-800 mb-2">{{ __('mobile.how_to_earn_rewards') }}:</h4>
                    <ul class="text-sm text-violet-700 space-y-1">
                        <li>• {{ __('mobile.complete_reading_activities') }}</li>
                        <li>• {{ __('mobile.finish_books') }}</li>
                        <li>• {{ __('mobile.maintain_reading_streaks') }}</li>
                        <li>• {{ __('mobile.participate_in_challenges') }}</li>
                    </ul>
                </div>
            </div>
        @endif

    </div>
</div>
