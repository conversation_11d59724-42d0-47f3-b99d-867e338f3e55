<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\TeamReward;
use App\Models\Reward;
use App\Models\Team;
use App\Models\User;
use MoonShine\UI\Fields\Text;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;

#[Icon('star')]
class TeamRewardResource extends BaseResource
{
    protected string $model = TeamReward::class;

    protected string $column = 'id';

    protected array $with = [
        'team', 
        'reward', 
        'awarder', 
        'readingLog', 
        'userActivity'
    ];

    public function getTitle(): string
    {
        return __('admin.team_rewards');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.team'),
                'team',
                formatted: fn(Team $team) => $team->name,
                resource: TeamResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: RewardResource::class
            )
                ->sortable(),

            Text::make(__('admin.reward_type'), 'reward.reward_type_display')
                ->sortable(),

            BelongsTo::make(
                __('admin.awarded_by'),
                'awarder',
                formatted: fn(?User $user) => $user ? $user->name : __('admin.system'),
                resource: UserResource::class
            )
                ->sortable(),

            Date::make(__('admin.awarded_date'), 'awarded_date')
                ->sortable(),

            Text::make(__('admin.award_type'), 'award_type'),

            Text::make(__('admin.trigger_source'), 'trigger_source'),

            Text::make(__('admin.award_age'), 'award_age_text'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.team'),
                'team',
                formatted: fn(Team $team) => $team->name,
                resource: TeamResource::class
            )
                ->required()
                ->searchable(),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name . ' (' . $reward->reward_type_display . ')',
                resource: RewardResource::class
            )
                ->required()
                ->searchable(),

            BelongsTo::make(
                __('admin.awarded_by'),
                'awarder',
                formatted: fn(?User $user) => $user ? $user->name : null,
                resource: UserResource::class
            )
                ->nullable()
                ->searchable()
                ->default(auth('moonshine')->id())
                ->hint(__('admin.leave_empty_for_automatic_award')),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.team'),
                'team',
                formatted: fn(Team $team) => $team->name,
                resource: TeamResource::class
            ),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: RewardResource::class
            ),

            Text::make(__('admin.reward_type'), 'reward.reward_type_display'),
            Text::make(__('admin.reward_description'), 'reward.description'),

            BelongsTo::make(
                __('admin.awarded_by'),
                'awarder',
                formatted: fn(?User $user) => $user ? $user->name : __('admin.system'),
                resource: UserResource::class
            ),

            Text::make(__('admin.awarded_date'), 'awarded_date'),
            Text::make(__('admin.award_type'), 'award_type'),
            Text::make(__('admin.trigger_source'), 'trigger_source'),
            Text::make(__('admin.award_age'), 'award_age_text'),

            BelongsTo::make(
                __('admin.reading_log'),
                'readingLog',
                formatted: fn($log) => $log ? 'Reading Log #' . $log->id : '-',
                resource: UserReadingLogResource::class
            ),

            BelongsTo::make(
                __('admin.user_activity'),
                'userActivity',
                formatted: fn($activity) => $activity ? 'Activity #' . $activity->id : '-',
                resource: UserActivityResource::class
            ),

            Text::make(__('admin.summary'), 'summary'),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'team_id' => ['required', 'exists:teams,id'],
            'reward_id' => ['required', 'exists:rewards,id'],
            'awarded_by' => ['nullable', 'exists:users,id'],
            ...parent::getCommonRules($item),
        ];

        return $rules;
    }

    protected function search(): array
    {
        return [
            'team.name', 
            'reward.name', 
            'awarder.name'
        ];
    }

    protected function getDefaultSort(): array
    {
        return ['awarded_date' => 'desc'];
    }
}
