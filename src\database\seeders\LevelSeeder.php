<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Level;

class LevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $levels = [
            [
                'nr' => 1,
                'name' => '<PERSON><PERSON>',
                'books_count' => 1,
                'page_points' => 50,
                'all_required' => false, // Either 1 book OR 50 points
            ],
            [
                'nr' => 2,
                'name' => 'Okuma Meraklısı',
                'books_count' => 3,
                'page_points' => 150,
                'all_required' => false, // Either 3 books OR 150 points
            ],
            [
                'nr' => 3,
                'name' => 'Kitap Kurdu',
                'books_count' => 5,
                'page_points' => 300,
                'all_required' => false, // Either 5 books OR 300 points
            ],
            [
                'nr' => 4,
                'name' => 'Okuma Ustası',
                'books_count' => 10,
                'page_points' => 600,
                'all_required' => true, // Both 10 books AND 600 points
            ],
            [
                'nr' => 5,
                'name' => '<PERSON><PERSON><PERSON>',
                'books_count' => 15,
                'page_points' => 1000,
                'all_required' => true, // Both 15 books AND 1000 points
            ],
            [
                'nr' => 6,
                'name' => '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>hramanı',
                'books_count' => 25,
                'page_points' => 1500,
                'all_required' => true, // Both 25 books AND 1500 points
            ],
            [
                'nr' => 7,
                'name' => 'Okuma Şampiyonu',
                'books_count' => 40,
                'page_points' => 2500,
                'all_required' => true, // Both 40 books AND 2500 points
            ],
            [
                'nr' => 8,
                'name' => 'Efsanevi Okuyucu',
                'books_count' => 60,
                'page_points' => 4000,
                'all_required' => true, // Both 60 books AND 4000 points
            ],
            [
                'nr' => 9,
                'name' => 'Okuma Efendisi',
                'books_count' => 80,
                'page_points' => 6000,
                'all_required' => true, // Both 80 books AND 6000 points
            ],
            [
                'nr' => 10,
                'name' => 'Büyük Usta',
                'books_count' => 100,
                'page_points' => 10000,
                'all_required' => true, // Both 100 books AND 10000 points
            ],
        ];

        foreach ($levels as $levelData) {
            Level::create($levelData);
        }
    }
}
