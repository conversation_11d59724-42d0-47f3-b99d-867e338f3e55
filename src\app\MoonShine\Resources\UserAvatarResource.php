<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserAvatar;
use App\Models\User;
use App\Models\Avatar;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Image;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Date;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;

#[Icon('user-circle')]
class UserAvatarResource extends BaseResource
{
    protected string $model = UserAvatar::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'avatar'];

    public function getTitle(): string
    {
        return __('admin.user_avatars');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.avatar'),
                'avatar',
                formatted: fn(Avatar $avatar) => $avatar->name,
                resource: AvatarResource::class
            )
                ->sortable(),

            Image::make(__('admin.current_avatar_image'), 'current_image_url'),

            Number::make(__('admin.user_activity_points'), 'user.activity_points')
                ->sortable(),

            Number::make(__('admin.avatar_required_points'), 'avatar.required_points')
                ->sortable(),

            Date::make(__('admin.selected_at'), 'selected_at')
                ->sortable(),

            Text::make(__('admin.selection_age'), 'selection_age_text')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name . ' (' . $user->activity_points . ' activity pts)',
                        resource: UserResource::class
                    )
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.avatar'),
                        'avatar',
                        formatted: fn(Avatar $avatar) => $avatar->name . ' (' . $avatar->required_points . ' pts required)',
                        resource: AvatarResource::class
                    )
                        ->required()
                        ->searchable(),
                ]),

                Date::make(__('admin.selected_at'), 'selected_at')
                    ->default(now()->format('Y-m-d H:i:s'))
                    ->hint(__('admin.selected_at_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.avatar'),
                'avatar',
                formatted: fn(Avatar $avatar) => $avatar->name,
                resource: AvatarResource::class
            ),

            Image::make(__('admin.current_avatar_image'), 'current_image_url'),

            Number::make(__('admin.user_activity_points'), 'user.activity_points'),
            Number::make(__('admin.avatar_required_points'), 'avatar.required_points'),
            Date::make(__('admin.selected_at'), 'selected_at'),
            Text::make(__('admin.selection_age'), 'selection_age_text'),
            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'user_id' => ['required', 'exists:users,id'],
            'avatar_id' => ['required', 'exists:avatars,id'],
            'selected_at' => ['required', 'date'],
            ...parent::getCommonRules($item),
        ];

        // Add custom validation for avatar selection
        $rules['avatar_id'][] = function ($attribute, $value, $fail) {
            $userId = request('user_id');
            if ($userId && $value) {
                $user = User::find($userId);
                if ($user && !$user->canSelectAvatar($value)) {
                    $avatar = Avatar::find($value);
                    $userActivityPoints = $user->getActivityPoints();
                    $fail(__('admin.insufficient_activity_points_for_avatar', [
                        'required' => $avatar->required_points,
                        'current' => $userActivityPoints
                    ]));
                }
            }
        };

        return $rules;
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'avatar.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['selected_at' => 'desc', 'user.name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all user avatar selections
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see avatar selections for students in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            });
        }

        // Teachers can see avatar selections for students in their assigned classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            });
        }

        // Students can only see their own avatar selection
        if ($user->isStudent()) {
            return $builder->where('user_id', $user->id);
        }

        // Default: no access
        return $builder->where('id', 0);
    }
}
