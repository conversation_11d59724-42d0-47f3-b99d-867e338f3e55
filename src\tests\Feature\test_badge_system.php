<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Badge;
use App\Models\BadgeRule;
use App\Models\UserBadge;
use App\Models\UserPoint;
use App\Models\UserReadingLog;
use App\Models\EnumBadgeRuleType;
use App\Models\Book;
use App\Models\Category;

try {
    echo "=== TESTING COMPREHENSIVE BADGE SYSTEM ===\n\n";
    
    // Get test data
    $user = User::first();
    $book = Book::first();
    $category = Category::first();
    
    if (!$user || !$book || !$category) {
        echo "❌ Missing test data. Need user, book, and category\n";
        exit;
    }
    
    echo "Test User: {$user->name}\n";
    echo "Test Book: {$book->name}\n";
    echo "Test Category: {$category->name}\n\n";
    
    // Clean up existing data
    UserBadge::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserReadingLog::where('user_id', $user->id)->delete();
    Badge::where('name', 'LIKE', 'Test%')->delete();
    
    // Test Case 1: Create test badges with different rule types
    echo "TEST CASE 1: Create test badges with different rule types\n";
    
    // Reading Points Badge (50 points)
    $readingPointsBadge = Badge::create([
        'name' => 'Test Reading Points Badge',
        'description' => 'Earn 50 reading points',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $readingPointsBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::READING_POINTS)->first()->id,
        'rule_value' => 50,
        'active' => true,
    ]);
    
    // Reading Days Badge (5 days)
    $readingDaysBadge = Badge::create([
        'name' => 'Test Reading Days Badge',
        'description' => 'Read for 5 different days',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $readingDaysBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::TOTAL_READING_DAYS)->first()->id,
        'rule_value' => 5,
        'active' => true,
    ]);
    
    // Books Completed Badge (2 books in specific category)
    $booksCompletedBadge = Badge::create([
        'name' => 'Test Books Completed Badge',
        'description' => 'Complete 2 books in category',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $booksCompletedBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::TOTAL_BOOKS_COMPLETED)->first()->id,
        'rule_value' => 2,
        'category_id' => $category->id,
        'active' => true,
    ]);
    
    // Manual Badge
    $manualBadge = Badge::create([
        'name' => 'Test Manual Badge',
        'description' => 'Manually awarded badge',
        'manual' => true,
        'active' => true,
    ]);
    
    echo "✅ Created 4 test badges with different rule types\n\n";
    
    // Test Case 2: Check initial badge eligibility (should be none)
    echo "TEST CASE 2: Check initial badge eligibility\n";
    $earnableBadges = Badge::getEarnableBadgesForUser($user->id);
    $userBadges = $user->getEarnedBadges();
    
    echo "- Earnable Badges: {$earnableBadges->count()}\n";
    echo "- User Badges: {$userBadges->count()}\n";
    echo "✅ " . ($earnableBadges->count() === 0 && $userBadges->count() === 0 ? "PASS" : "FAIL") . " - No badges should be earnable initially\n\n";
    
    // Test Case 3: Add reading logs to trigger badge evaluation
    echo "TEST CASE 3: Add reading logs to trigger badge evaluation\n";
    
    // Add reading logs for different days
    $dates = [
        now()->subDays(4)->toDateString(),
        now()->subDays(3)->toDateString(),
        now()->subDays(2)->toDateString(),
        now()->subDays(1)->toDateString(),
        now()->toDateString(),
    ];
    
    foreach ($dates as $index => $date) {
        UserReadingLog::create([
            'user_id' => $user->id,
            'book_id' => $book->id,
            'log_date' => $date,
            'start_page' => $index * 10 + 1,
            'end_page' => ($index + 1) * 10,
            'pages_read' => 10,
            'reading_minutes' => 30,
            'book_completed' => false,
        ]);
    }
    
    $user->refresh();
    $userBadges = $user->getEarnedBadges();
    $badgeStats = $user->getBadgeStats();
    
    echo "- User Badges After Reading Logs: {$userBadges->count()}\n";
    echo "- Badge Stats: Total={$badgeStats['total']}, Automatic={$badgeStats['automatic']}, Manual={$badgeStats['manual']}\n";
    
    foreach ($userBadges as $userBadge) {
        echo "  - Earned: {$userBadge->badge->name} ({$userBadge->award_type})\n";
    }
    
    echo "✅ " . ($userBadges->count() >= 2 ? "PASS" : "FAIL") . " - Should earn reading points and reading days badges\n\n";
    
    // Test Case 4: Complete books to trigger book completion badge
    echo "TEST CASE 4: Complete books to trigger book completion badge\n";
    
    // Update book category to match our test category
    $book->update(['category_id' => $category->id]);
    
    // Mark first book as completed
    UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book->id,
        'log_date' => now()->toDateString(),
        'start_page' => 1,
        'end_page' => $book->page_count ?? 100,
        'pages_read' => $book->page_count ?? 100,
        'reading_minutes' => 60,
        'book_completed' => true,
    ]);
    
    // Create another book in same category and complete it
    $book2 = Book::create([
        'title' => 'Test Book 2',
        'author_id' => $book->author_id,
        'publisher_id' => $book->publisher_id,
        'category_id' => $category->id,
        'page_count' => 50,
        'active' => true,
    ]);
    
    UserReadingLog::create([
        'user_id' => $user->id,
        'book_id' => $book2->id,
        'log_date' => now()->toDateString(),
        'start_page' => 1,
        'end_page' => 50,
        'pages_read' => 50,
        'reading_minutes' => 45,
        'book_completed' => true,
    ]);
    
    $user->refresh();
    $userBadges = $user->getEarnedBadges();
    $badgeStats = $user->getBadgeStats();
    
    echo "- User Badges After Book Completion: {$userBadges->count()}\n";
    echo "- Badge Stats: Total={$badgeStats['total']}, Automatic={$badgeStats['automatic']}, Manual={$badgeStats['manual']}\n";
    
    foreach ($userBadges as $userBadge) {
        echo "  - Earned: {$userBadge->badge->name} ({$userBadge->award_type})\n";
    }
    
    echo "✅ " . ($userBadges->count() >= 3 ? "PASS" : "FAIL") . " - Should earn books completed badge\n\n";
    
    // Test Case 5: Test manual badge awarding
    echo "TEST CASE 5: Test manual badge awarding\n";
    
    $teacher = User::where('id', '!=', $user->id)->first(); // Get another user as teacher
    $manualAward = UserBadge::awardBadgeToUser($user->id, $manualBadge->id, $teacher->id);
    
    $user->refresh();
    $userBadges = $user->getEarnedBadges();
    $manualBadges = $userBadges->filter(function($ub) { return $ub->awarded_by !== null; });
    
    echo "- Manual Award Result: " . ($manualAward ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Total Badges: {$userBadges->count()}\n";
    echo "- Manual Badges: {$manualBadges->count()}\n";
    echo "✅ " . ($manualAward && $manualBadges->count() === 1 ? "PASS" : "FAIL") . " - Manual badge should be awarded\n\n";
    
    // Test Case 6: Test duplicate badge prevention
    echo "TEST CASE 6: Test duplicate badge prevention\n";
    
    $duplicateAward = UserBadge::awardBadgeToUser($user->id, $manualBadge->id, $teacher->id);
    $userBadgesAfter = $user->getEarnedBadges();
    
    echo "- Duplicate Award Result: " . ($duplicateAward ? 'SUCCESS' : 'FAILED') . "\n";
    echo "- Badges Count After Duplicate Attempt: {$userBadgesAfter->count()}\n";
    echo "✅ " . (!$duplicateAward && $userBadgesAfter->count() === $userBadges->count() ? "PASS" : "FAIL") . " - Duplicate awards should be prevented\n\n";
    
    // Test Case 7: Test badge rule evaluation
    echo "TEST CASE 7: Test badge rule evaluation\n";
    
    foreach ($userBadges as $userBadge) {
        $badge = $userBadge->badge;
        $canEarn = $badge->canBeEarnedByUser($user->id);
        echo "- Badge: {$badge->name} - Can Earn Again: " . ($canEarn ? 'YES' : 'NO') . "\n";
        
        foreach ($badge->activeBadgeRules as $rule) {
            $userValue = $rule->getUserValueForRule($user);
            $satisfied = $rule->isSatisfiedByUser($user);
            echo "  - Rule: {$rule->summary} - User Value: {$userValue} - Satisfied: " . ($satisfied ? 'YES' : 'NO') . "\n";
        }
    }
    
    echo "✅ PASS - Badge rule evaluation working\n\n";
    
    // Test Case 8: Test reading streak calculation
    echo "TEST CASE 8: Test reading streak calculation\n";
    
    $streakBadge = Badge::create([
        'name' => 'Test Streak Badge',
        'description' => 'Read for 3 consecutive days',
        'manual' => false,
        'active' => true,
    ]);
    
    BadgeRule::create([
        'badge_id' => $streakBadge->id,
        'rule_type_id' => EnumBadgeRuleType::where('nr', EnumBadgeRuleType::READING_STREAK_DAYS)->first()->id,
        'rule_value' => 3,
        'active' => true,
    ]);
    
    // Check and award badges
    $awardedBadges = $user->checkAndAwardBadges();
    $user->refresh();
    $finalBadges = $user->getEarnedBadges();
    
    echo "- Newly Awarded Badges: " . count($awardedBadges) . "\n";
    echo "- Final Badge Count: {$finalBadges->count()}\n";
    
    foreach ($awardedBadges as $newBadge) {
        echo "  - New Badge: {$newBadge->badge->name}\n";
    }
    
    echo "✅ PASS - Streak badge evaluation completed\n\n";
    
    // Cleanup
    echo "CLEANUP: Removing test data\n";
    UserBadge::where('user_id', $user->id)->delete();
    UserPoint::where('user_id', $user->id)->delete();
    UserReadingLog::where('user_id', $user->id)->delete();
    Badge::where('name', 'LIKE', 'Test%')->delete();
    $book2->delete();
    echo "✅ Test data cleaned up\n\n";
    
    echo "✅ Comprehensive badge system tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
