<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('avatars', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('base_image'); // Path to base avatar image
            $table->string('happy_image'); // Path to happy avatar image
            $table->string('sad_image'); // Path to sad avatar image
            $table->string('sleepy_image'); // Path to sleepy avatar image
            $table->integer('required_points')->default(0);
            $table->boolean('active')->default(true);

            // Add indexes
            $table->index('name');
            $table->index('required_points');
            $table->index('active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('avatars');
    }
};
