# Define the file with strings to search and the target file where you want to remove lines
$searchFile =  "remove.txt" # File with strings to search for
$targetFile = "D:\ba\calisma\web\kitapokuma\okumobil\src\lang\tr\admin.php"  # File from which to remove matching lines

$stringsToRemove = Get-Content $searchFile | ForEach-Object { "'$_'" }

# Read the target file and filter out any line that contains a key from the search file
$filteredContent = Get-Content $targetFile | Where-Object {
    $line = $_.Trim()  # Remove extra spaces/newlines
    $matchFound = $false

    # Check if the line contains any of the search strings surrounded by single quotes
    foreach ($string in $stringsToRemove) {
        
        # Use -like to allow for partial matching
        if ($line -like "*$string*") {
            Write-Host "Match found: $line"
            $matchFound = $true
            break
        }
    }

    # Exclude line if a match is found
    -not $matchFound
}

# Write the filtered content back to the target file
$filteredContent | Set-Content $targetFile