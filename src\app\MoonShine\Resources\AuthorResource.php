<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Author;
use App\Models\Role;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;

use MoonShine\Support\Attributes\Icon;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

#[Icon('user')]

class AuthorResource extends BaseResource
{
    use WithRolePermissions;
    
    protected string $model = Author::class;

    protected string $column = 'name';

    public function getTitle(): string
    {
        return __('admin.authors');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Number::make(__('admin.books_count'), 'book_count'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),
            ]),


        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Number::make(__('admin.books_count'), 'book_count'),
            //Text::make(__('admin.books_list'), 'books'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
