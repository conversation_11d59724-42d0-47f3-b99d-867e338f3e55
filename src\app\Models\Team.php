<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Team extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'logo',
        'leader_user_id',
        'active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
        ];
    }

    /**
     * Get the team leader.
     */
    public function leader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'leader_user_id');
    }

    /**
     * Get the team members through user_teams pivot table.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_teams');
    }

    /**
     * Get the user team pivot records.
     */
    public function userTeams(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(UserTeam::class);
    }

    /**
     * Get the team's earned rewards.
     */
    public function teamRewards(): Has<PERSON>any
    {
        return $this->hasMany(TeamReward::class);
    }

    /**
     * Check if team has earned a specific reward.
     */
    public function hasEarnedReward($rewardId): bool
    {
        return $this->teamRewards()->where('reward_id', $rewardId)->exists();
    }

    /**
     * Get all rewards earned by this team.
     */
    public function getEarnedRewards()
    {
        return $this->teamRewards()
            ->with(['reward', 'awarder', 'readingLog', 'userActivity'])
            ->orderBy('awarded_date', 'desc')
            ->get();
    }

    /**
     * Get reward statistics for this team.
     */
    public function getRewardStats(): array
    {
        return TeamReward::getRewardStatsForTeam($this->id);
    }



    /**
     * Scope to get active teams.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to get teams for the current user.
     */
    public function scopeForCurrentUser($query)
    {
        return $query->whereHas('users', function ($q) {
            $q->forCurrentUser();
        });
    }

    /**
     * Scope to get teams created by a specific user.
     */
    public function scopeCreatedBy($query, $userId)
    {
        return $query->where('created_by', $userId);
    }
    

    /**
     * Get the display logo for this team.
     */
    public function getDisplayLogoAttribute(): string
    {
        return $this->logo ?: '/images/default-team-logo.png';
    }

    /**
     * Get the member count for this team.
     */
    public function getMemberCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * Get the leader name.
     */
    public function getLeaderNameAttribute(): string
    {
        return $this->leader ? $this->leader->name : __('admin.no_leader');
    }

    /**
     * Get the total reading points for this team.
     */
    public function getTotalPoints(): int
    {
        $memberIds = $this->users()->pluck('users.id')->toArray();
        
        if (empty($memberIds)) {
            return 0;
        }

        return UserPoint::whereIn('user_id', $memberIds)
            ->where('point_type', UserPoint::POINT_TYPE_PAGE)
            ->sum('points');
    }

    /**
     * Get the total reading points as an attribute.
     */
    public function getTotalPointsAttribute(): int
    {
        return $this->getTotalPoints();
    }

    /**
     * Get the summary of this team.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            '%s (%d %s, %d %s)',
            $this->name,
            $this->member_count,
            __('admin.members'),
            $this->total_points,
            __('admin.points_rule')
        );
    }


}
