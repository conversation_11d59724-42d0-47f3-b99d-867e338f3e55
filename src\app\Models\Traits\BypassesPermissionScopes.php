<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;

/**
 * Trait for models that need to bypass permission scopes in certain operations.
 * This is useful for system operations, automatic triggers, and administrative functions.
 */
trait BypassesPermissionScopes
{
    /**
     * Create a new model instance without applying permission scopes.
     * Use this for system operations that should bypass permission filtering.
     */
    public static function createWithoutScopes(array $attributes = [])
    {
        return static::withoutGlobalScopes()->create($attributes);
    }

    /**
     * Update a model instance without applying permission scopes.
     * Use this for system operations that should bypass permission filtering.
     */
    public function updateWithoutScopes(array $attributes = [])
    {
        return static::withoutGlobalScopes()->where('id', $this->id)->update($attributes);
    }

    /**
     * Find a model by ID without applying permission scopes.
     * Use this for system operations that should bypass permission filtering.
     */
    public static function findWithoutScopes($id, $columns = ['*'])
    {
        return static::withoutGlobalScopes()->find($id, $columns);
    }

    /**
     * Get all models without applying permission scopes.
     * Use this for system operations that should bypass permission filtering.
     */
    public static function allWithoutScopes($columns = ['*'])
    {
        return static::withoutGlobalScopes()->get($columns);
    }

    /**
     * Get a query builder without permission scopes.
     * Use this for complex system operations that need to bypass permission filtering.
     */
    public static function queryWithoutScopes(): Builder
    {
        return static::withoutGlobalScopes();
    }

    /**
     * Check if a record exists without applying permission scopes.
     * Use this for system operations that should bypass permission filtering.
     */
    public static function existsWithoutScopes($id): bool
    {
        return static::withoutGlobalScopes()->where('id', $id)->exists();
    }

    /**
     * Count records without applying permission scopes.
     * Use this for system operations that should bypass permission filtering.
     */
    public static function countWithoutScopes(): int
    {
        return static::withoutGlobalScopes()->count();
    }
}
