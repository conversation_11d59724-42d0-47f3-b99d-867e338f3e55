| EnumTaskCycle | EnumTaskType           | user_tasks progress calculation                                                                                                                        |
| ------------- | ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| TOTAL         | READ_PAGES             | Number of pages read since the start date / Number of pages to be read between the start and end dates.                                                |
| DAILY         | READ_PAGES             | Number of days the daily target pages were completed since the start date / Number of days between the start and end dates.                            |
| WEEKLY        | READ_PAGES             | Number of weeks the weekly target pages were completed since the start date / Number of weeks between the start and end dates.                         |
| MONTHLY       | READ_PAGES             | Number of months the monthly target pages were completed since the start date / Number of months between the start and end dates.                      |
| TOTAL         | READ_BOOKS             | Number of books read since the start date / Number of books to be read between the start and end dates.                                                |
| DAILY         | READ_BOOKS             | Number of days the daily target books were completed since the start date / Number of days between the start and end dates.                            |
| WEEKLY        | READ_BOOKS             | Number of weeks the weekly target books were completed since the start date / Number of weeks between the start and end dates.                         |
| MONTHLY       | READ_BOOKS             | Number of months the monthly target books were completed since the start date / Number of months between the start and end dates.                      |
| TOTAL         | READ_MINUTES           | Number of minutes read since the start date / Number of minutes to be read between the start and end dates.                                            |
| DAILY         | READ_MINUTES           | Number of days the daily target minutes were completed since the start date / Number of days between the start and end dates.                          |
| WEEKLY        | READ_MINUTES           | Number of weeks the weekly target minutes were completed since the start date / Number of weeks between the start and end dates.                       |
| MONTHLY       | READ_MINUTES           | Number of months the monthly target minutes were completed since the start date / Number of months between the start and end dates.                    |
| TOTAL         | READ_DAYS              | Number of days the book was read since the start date / Number of days required to read between the start and end dates.                               |
| DAILY         | READ_DAYS              | Number of days the book was read since the start date / Number of days between the start and end dates.                                                |
| WEEKLY        | READ_DAYS              | Number of weeks the weekly target days for reading were completed since the start date / Number of weeks between the start and end dates.              |
| MONTHLY       | READ_DAYS              | Number of months the monthly target days for reading were completed since the start date / Number of months between the start and end dates.           |
| TOTAL         | READ_STREAK            | Number of consecutive reading days from the start date to today / Number of consecutive reading days required between the start and end dates.         |
| DAILY         | READ_STREAK            | Number of days the book was read since the start date / Number of days between the start and end dates.                                                |
| WEEKLY        | READ_STREAK            | Number of weeks the weekly target of consecutive reading days was completed since the start date / Number of weeks between the start and end dates.    |
| MONTHLY       | READ_STREAK            | Number of months the monthly target of consecutive reading days was completed since the start date / Number of months between the start and end dates. |
| TOTAL         | EARN_READING_POINTS    | Number of page points earned since the start date / Number of page points to be earned between the start and end dates.                                |
| DAILY         | EARN_READING_POINTS    | Number of days the daily target page points were completed since the start date / Number of days between the start and end dates.                      |
| WEEKLY        | EARN_READING_POINTS    | Number of weeks the weekly target page points were completed since the start date / Number of weeks between the start and end dates.                   |
| MONTHLY       | EARN_READING_POINTS    | Number of months the monthly target page points were completed since the start date / Number of months between the start and end dates.                |
| TOTAL         | EARN_ACTIVITY_POINTS   | Number of activity points earned since the start date / Number of activity points to be earned between the start and end dates.                        |
| DAILY         | EARN_ACTIVITY_POINTS   | Number of days the daily target activity points were completed since the start date / Number of days between the start and end dates.                  |
| WEEKLY        | EARN_ACTIVITY_POINTS   | Number of weeks the weekly target activity points were completed since the start date / Number of weeks between the start and end dates.               |
| MONTHLY       | EARN_ACTIVITY_POINTS   | Number of months the monthly target activity points were completed since the start date / Number of months between the start and end dates.            |
| TOTAL         | COMPLETE_BOOK_ACTIVITY | Number of activities completed since the start date / Number of activities to be completed between the start and end dates.                            |
| DAILY         | COMPLETE_BOOK_ACTIVITY | Number of days the daily target activities were completed since the start date / Number of days between the start and end dates.                       |
| WEEKLY        | COMPLETE_BOOK_ACTIVITY | Number of weeks the weekly target activities were completed since the start date / Number of weeks between the start and end dates.                    |
| MONTHLY       | COMPLETE_BOOK_ACTIVITY | Number of months the monthly target activities were completed since the start date / Number of months between the start and end dates.                 |
| TOTAL         | COMPLETE_BOOK_LIST     | Number of books from the reading list completed since the start date / Number of books in the provided list.                                           |
| DAILY         | COMPLETE_BOOK_LIST     | Number of days the listed books were completed daily since the start date / Number of days between the start and end dates.                            |
| WEEKLY        | COMPLETE_BOOK_LIST     | Number of weeks the listed books were completed weekly since the start date / Number of weeks between the start and end dates.                         |
| MONTHLY       | COMPLETE_BOOK_LIST     | Number of months the listed books were completed monthly since the start date / Number of months between the start and end dates.                      |
