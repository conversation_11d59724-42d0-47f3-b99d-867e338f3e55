<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Role;
use MoonShine\Laravel\Resources\ModelResource;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\ID;
use Moon<PERSON>hine\UI\Fields\Text;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;
use Sweet1s\MoonshineRBAC\Traits\WithPermissionsFormComponent;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;


#[Icon('shield-check')]
class RoleResource extends ModelResource
{
    use WithPermissionsFormComponent;
    use WithRolePermissions;

    protected string $model = Role::class;

    protected string $column = 'name';

    public function getTitle(): string
    {
        return __('admin.roles');
    }

    protected function indexFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Text::make(__('admin.description'), 'description')
                ->sortable(),
            Text::make(__('admin.guard_name'), 'guard_name')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),
                ]),

                Text::make(__('admin.description'), 'description')
                    ->placeholder(__('admin.enter_description')),
                Select::make(__('admin.guard_name'), 'guard_name')
                    ->options(Role::getGuardOptions())
                    ->default(Role::DEFAULT_GUARD),
            ]),


        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.description'), 'description'),
            Text::make(__('admin.guard_name'), 'guard_name'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:255'],
        ];
    }

    protected function search(): array
    {
        return ['name', 'description'];
    }
}
