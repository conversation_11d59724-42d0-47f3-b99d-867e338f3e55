
CREATE TABLE organizations (
  id INT PRIMARY KEY,
  parent_id INT NULL, -- NULL for top-level (e.g., group)
  name VA<PERSON>HA<PERSON>(255),
  type VARCHAR(50) -- e.g., 'group', 'school'
);

CREATE TABLE academic_terms (
  id INT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(100),
  start_date DATE,
  end_date DATE
);

CREATE TABLE grade_levels (
  id INT PRIMARY KEY,
  code VARCHAR(10),  -- e.g., "1", "2", ..., "12"
  label VARCHAR(100)
);

CREATE TABLE school_grade_levels (
  id INT PRIMARY KEY,
  organization_id INT, -- refers to a school
  grade_level_id INT   -- refers to global grade_levels table
);

CREATE TABLE classes (
  id INT PRIMARY KEY,
  organization_id INT, -- refers to a school
  grade_level_id INT,
  section VARCHAR(10)  -- e.g., "A", "B"
);

-- USERS AND ROLES
CREATE TABLE users (
  id INT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(100),
  email VARCHAR(100) UNIQUE,
  password_hash VARCHAR(255)
);

CREATE TABLE roles (
  id INT PRIMARY KEY,
  code VARCHAR(50) UNIQUE,  -- e.g., "system_admin", "group_admin", "school_admin", "teacher", "counselor"
  label VARCHAR(100)
);

CREATE TABLE term_users (
  id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  user_id BIGINT(20) UNSIGNED NOT NULL,
  role_id BIGINT(20) UNSIGNED NOT NULL,
  organization_id BIGINT(20) UNSIGNED NULL, -- örn: öğretmen, counselor, öğrenci vs.
  class_id BIGINT(20) UNSIGNED NULL,        -- eğer sınıfa atanmışsa
  term_id BIGINT(20) UNSIGNED NOT NULL,         -- hangi öğretim dönemi
  active BOOLEAN DEFAULT TRUE,
  created_by BIGINT(20) UNSIGNED NULL DEFAULT NULL,
  updated_by BIGINT(20) UNSIGNED NULL DEFAULT NULL,
  created_at TIMESTAMP NULL DEFAULT NULL,
  updated_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (user_id, role_id, organization_id, class_id, term_id)
);

CREATE TABLE authors (
  id INT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  bio TEXT,
  photo_url VARCHAR(255)
);

CREATE TABLE publishers (
  id INT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  website_url VARCHAR(255),
  logo_url VARCHAR(255)
);

CREATE TABLE books (
id INT PK
title VARCHAR(255)
publisher_id INT,
pages INT
FOREIGN KEY (publisher_id) REFERENCES publishers(id);
);

CREATE TABLE book_authors (
  book_id INT,
  author_id INT,
  PRIMARY KEY (book_id, author_id),
  FOREIGN KEY (book_id) REFERENCES books(id),
  FOREIGN KEY (author_id) REFERENCES authors(id)
);


-- READING PROGRAMS
CREATE TABLE programs (
  id INT PRIMARY KEY,
  title VARCHAR(255),
  story_id INT,
  start_date DATE
  end_date DATE
  created_by_user_id INT,
  created_at DATETIME
);

CREATE TABLE program_schools (
  id INT PRIMARY KEY,
  program_id INT,
  school_id INT,
  invited_at DATETIME,
  invited_by_user_id INT
);

CREATE TABLE program_classes (
  id INT PRIMARY KEY,
  program_id INT,
  class_term_id INT,
  added_by_user_id INT,
  added_at DATETIME
);

CREATE TABLE program_books (
program_id INT FK -> programs.id
book_id INT FK -> books.id
);

CREATE TABLE program_teams (
id INT PK
program_id INT FK -> programs.id
name VARCHAR(100)
);

CREATE TABLE program_team_members (
program_team_id INT FK -> program_teams.id
student_id INT FK -> users.id
);


CREATE TABLE tasks (
id INT PK
program_id INT FK -> programs.id
book_id INT FK -> books.id
title VARCHAR(255)
description TEXT
points INT
is_recurring BOOLEAN
recurrence_type ENUM('none', 'daily', 'weekly') NULL
assigned_to_team_id INT FK → program_teams.id
assigned_to_student_id INT NULL
CHECK (assigned_to_team_id IS NOT NULL OR assigned_to_student_id IS NOT NULL)
);

CREATE TABLE task_instances (
id INT PK
task_id INT FK -> tasks.id
student_id INT FK -> users.id
due_date DATE
attempt INT DEFAULT 1
);

CREATE TABLE task_completions (
id INT PK
task_instance_id INT FK
completed_at DATETIME
status ENUM('completed', 'missed', 'redo_requested', 'redo_approved')
points_awarded INT
);

CREATE TABLE badges (
id INT PK
name VARCHAR(100)
description TEXT
rule_type ENUM('streak', 'points', 'books_completed')
rule_value INT
);

CREATE TABLE user_badges (
badge_id INT FK
user_id INT FK
awarded_at DATETIME
);

CREATE TABLE achievements (
id INT PK
name VARCHAR(100)
rule_type ENUM('points', 'books', 'tasks')
rule_value INT
map_display BOOLEAN DEFAULT FALSE, --show on the map
map_image_url VARCHAR(255),         -- icon or sprite
map_category ENUM('plant', 'decoration', 'structure'),
placeable BOOLEAN DEFAULT FALSE;    -- if students can place it
);

CREATE TABLE user_achievements (
achievement_id INT FK
user_id INT FK
awarded_at DATETIME
);

CREATE TABLE levels (
id INT PK
name VARCHAR(100)
required_points INT
required_books INT
);

CREATE TABLE user_levels (
user_id INT FK
level_id INT FK
reached_at DATETIME
);

CREATE TABLE rewards (
id INT PK
name VARCHAR(100)
description TEXT
points_required INT
rule_type ENUM('none', 'badge', 'achievement') NULL
rule_reference_id INT NULL -- FK-like, context-specific
);

CREATE TABLE user_rewards (
reward_id INT FK
user_id INT FK
claimed_at DATETIME
);

CREATE TABLE inventory (
id INT PK
user_id INT FK
item_type ENUM('badge', 'achievement', 'reward')
item_id INT
obtained_at DATETIME
);

CREATE TABLE extra_task_requests (
id INT PK
student_id INT FK
program_id INT FK
reason TEXT
status ENUM('pending', 'approved', 'denied')
requested_at DATETIME
);

CREATE TABLE redo_requests (
id INT PK
task_instance_id INT FK
student_id INT FK
reason TEXT
status ENUM('pending', 'approved', 'denied')
);

CREATE TABLE daily_reading_log (
student_id INT FK -> users.id
date DATE
task_instance_id INT FK -> task_instances.id
PRIMARY KEY (student_id, date)
);

CREATE TABLE streaks (
  student_id INT PRIMARY KEY,
  current_streak INT DEFAULT 0,
  last_reading_date DATE
);

SELECT COUNT(*) FROM daily_reading_log
WHERE student_id = :sid
  AND date BETWEEN CURRENT_DATE - INTERVAL 4 DAY AND CURRENT_DATE;

INSERT INTO user_badges (badge_id, user_id, awarded_at)
SELECT :badge_id, :student_id, NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM user_badges WHERE badge_id = :badge_id AND user_id = :student_id
);




CREATE TABLE stories  (
id INT PK
title VARCHAR(255)
description TEXT
);

CREATE TABLE  story_chapters (
id INT PK
story_id INT FK -> stories.id
chapter_number INT NOT NULL
title VARCHAR(255)
content TEXT
unlock_rule_type ENUM('points', 'achievement')
unlock_rule_value INT  -- e.g., 200 points OR achievement_id
map_startx INT                -- visual position in story map (optional)
map_endx INT                
map_starty INT                
map_endy INT                
);


CREATE TABLE  story_characters (
id INT PK
story_id INT FK -> stories.id
name VARCHAR(100)
avatar_url VARCHAR(255)
description TEXT
attributes JSON  -- optional: power, traits, etc.
);


-- Let students' story characters evolve (e.g., level up, change appearance, unlock abilities) based on simple rules like points, progress, or achievements.
CREATE TABLE  story_character_stages  (
id INT PK
story_character_id INT FK -> story_characters.id
stage_number INT  -- 1 = base, 2 = evolved form, etc.
name VARCHAR(100)
avatar_url VARCHAR(255)
unlock_rule_type ENUM('points', 'chapter', 'achievement')  -- optional
unlock_rule_value INT  -- value depends on rule type
description TEXT
);


CREATE TABLE  student_story_progress (
student_id INT FK
story_id INT FK
chapter_id INT FK -> story_chapters.id
unlocked_at DATETIME
PRIMARY KEY (student_id, chapter_id)
);


CREATE TABLE student_characters  (
student_id INT FK
program_id INT FK
story_character_id INT FK -> story_characters.id
current_stage_id INT FK -> story_character_stages.id
selected_at DATETIME
PRIMARY KEY (student_id, program_id)
);

-- Unlock Logic (High-Level) For points-based chapter unlocks
SELECT c.id
FROM story_chapters c
LEFT JOIN student_story_progress p ON c.id = p.chapter_id AND p.student_id = :sid
WHERE c.unlock_rule_type = 'points'
  AND :student_total_points >= c.unlock_rule_value
  AND p.chapter_id IS NULL

-- Evolution Logic (in simple terms)
-- On point/achievement/chapter update:
--Look up current current_stage_id
-- Check if a higher stage_number for same character matches unlock rule
-- If so, set current_stage_id to next stage
SELECT s.id
FROM story_character_stages s
WHERE s.story_character_id = :character_id
  AND s.unlock_rule_type = 'points'
  AND s.unlock_rule_value <= :student_total_points
ORDER BY s.stage_number DESC
LIMIT 1

UPDATE student_characters
SET current_stage_id = :new_stage_id
WHERE student_id = :sid AND program_id = :pid



CREATE TABLE story_maps  (
id INT PK
story_id INT FK -> stories.id
name VARCHAR(100)
grid_width INT DEFAULT 10
grid_height INT DEFAULT 10
background_image_url VARCHAR(255)  -- optional
);

--Define collectible/placeable things (e.g., trees, flowers)
CREATE TABLE  map_items (
id INT PK
story_id INT FK
name VARCHAR(100)
type ENUM('plant', 'decoration', 'structure')
image_url VARCHAR(255)
unlock_rule_type ENUM('points', 'achievement', 'chapter')
unlock_rule_value INT
x INT -- optional static place on map. 
y INT
description TEXT
);

--Track which items each student has unlocked
CREATE TABLE student_map_items   (
id INT PK
student_id INT FK
program_id INT FK
map_item_id INT FK -> map_items.id
quantity INT DEFAULT 1
unlocked_at DATETIME
);


--Where items are placed on the map
CREATE TABLE student_map_placements (
id INT PK
student_id INT FK
program_id INT FK
achievement_id INT FK -> achievements.id
creation_id INT NULL FK
x INT
y INT
placed_at DATETIME
);

--–visual models the student unlocks, like “Red House” or “Lego Tree.”
CREATE TABLE creations ( 
id INT PK
story_id INT FK
name VARCHAR(100)
image_url VARCHAR(255)
description TEXT
placeable BOOLEAN DEFAULT TRUE
);

--– What’s needed to build each creation
CREATE TABLE creation_requirements ( 
id INT PK
creation_id INT FK -> creations.id
required_achievement_id INT FK -> achievements.id
required_quantity INT
);


CREATE TABLE student_unlocked_creations (
id INT PK
student_id INT FK
program_id INT FK
creation_id INT FK
unlocked_at DATETIME
);

---When a student earns a new brick (achievement), check and insert into student_unlocked_creations.
SELECT c.id
FROM creations c
JOIN creation_requirements r ON c.id = r.creation_id
LEFT JOIN user_achievements ua 
  ON ua.achievement_id = r.required_achievement_id 
 AND ua.user_id = :student_id
GROUP BY c.id
HAVING COUNT(*) = COUNT(r.id)  -- all required parts achieved
   AND MIN(ua_count) >= r.required_quantity



CREATE TABLE assessments   ( –--a set of questions for a book or task
id INT PK
title VARCHAR(255)
type ENUM('quiz', 'activity', 'game') DEFAULT 'quiz'
target_type ENUM('book', 'task', 'chapter')  -- what it's for
target_id INT  -- FK to books, tasks, chapters
min_score_percent INT DEFAULT 60
cooldown_minutes INT DEFAULT 1440  -- retry lock period
);


CREATE TABLE  assessment_questions (
id INT PK
assessment_id INT FK
question_type ENUM('multiple_choice', 'text', 'ordering', 'matching', 'sentence_building')
question_text TEXT
media_url VARCHAR(255)  -- optional image/audio
points INT DEFAULT 1
);


CREATE TABLE  assessment_question_options ( --(for multiple choice, etc.)
id INT PK
question_id INT FK
option_text TEXT
is_correct BOOLEAN DEFAULT FALSE
sort_order INT
);


CREATE TABLE  student_assessment_attempts (
id INT PK
student_id INT FK
assessment_id INT FK
started_at DATETIME
completed_at DATETIME
score_percent FLOAT
is_passed BOOLEAN
retry_available_at DATETIME  -- if failed, next try allowed time
);


CREATE TABLE student_assessment_answers  (
id INT PK
attempt_id INT FK
question_id INT FK
answer_text TEXT
selected_option_id INT NULL  -- if applicable
is_correct BOOLEAN
);


CREATE TABLE   (
);






proposed structure for your game-based web application,

Core Models/Tables:

    Users

        User ID

        Name

        Email

        Profile Picture

        Password (hashed)

        Role (Reader, Admin, etc.)

        Date Joined

        Total Points

        Total XP

    Books

        Book ID

        Title

        Author

        Genre

        ISBN

        Description

        Cover Image

        Date Added

    Reading Logs

        Log ID

        User ID (FK)

        Book ID (FK)

        Start Date

        End Date

        Progress Percentage

        Date Updated

        Challenge ID (FK, nullable) (to relate to a challenge, if applicable)

    Tasks

        Task ID

        Task Name

        Task Description

        Points Awarded

        Type (e.g., Read Chapter, Write Review, Share with Friends)

        Related Book ID (optional)

        Challenge ID (FK, nullable) (to relate to a challenge, if applicable)

        Status (Active, Completed, etc.)

    Challenges

        Challenge ID

        Challenge Name

        Description

        Creator (User ID FK)

        Points for Completion

        Start Date

        End Date

        Status (Active, Completed, Inactive)

        Reward Description (optional)

        Team Mode (True/False, indicates whether teams are allowed or not)

    Teams

        Team ID

        Team Name

        Leader (User ID FK)

        Date Created

        Status (Active, Completed, etc.)

        Challenge ID (FK, nullable) (if the team is associated with a challenge)

    Team Members

        Team ID (FK)

        User ID (FK)

        Role (Leader, Member)

        Date Joined

    User Challenges

        User ID (FK)

        Challenge ID (FK)

        Challenge Progress (percentage or steps completed)

        Points Earned

        Rewards Earned (JSON or List)

        Status (Active, Completed, Failed)

        Date Joined

    Challenge Points History

        User ID (FK)

        Challenge ID (FK)

        Points Earned (incremental points)

        Date Awarded

        Task/Activity Related to Points (FK to Task/Reading Log/Other)

    Challenge Rewards

        Reward ID

        Challenge ID (FK)

        Reward Type (Points, Badge, Achievement, etc.)

        Reward Description

        Quantity (if applicable)

        Condition (e.g., individual performance, team performance)

    Badges

        Badge ID

        Badge Name

        Description

        Points Threshold (optional)

        Image

    User Badges

        User ID (FK)

        Badge ID (FK)

        Date Awarded

    Achievements

        Achievement ID

        Name

        Description

        Points

        Unlock Criteria (e.g., read 5 books, complete 10 tasks)

    User Achievements

        User ID (FK)

        Achievement ID (FK)

        Date Unlocked

    Levels

        Level ID

        Level Name

        Points Required for Level

        XP Range

    User Levels

        User ID (FK)

        Level ID (FK)

        XP Earned

        Level Progress (points, %)

    Game Map

        Map ID

        Map Name

        Description

        Image

        Associated Levels

    User Map Progress

        User ID (FK)

        Map ID (FK)

        Map Progress (percentage or completed tasks)

        Date Updated

Key Changes and Additions:

    Flexible Challenge Relation:

        Both tasks and reading logs can optionally be linked to a challenge using a nullable Challenge ID. If an activity is part of a challenge, this relationship can be used to track progress within that specific challenge.

    Tasks and Logs:

        Tasks and reading logs now have an optional Challenge ID field. This way, activities can be either part of a challenge (if Challenge ID is set) or just regular activities independent of any challenge (if Challenge ID is NULL).

    Challenge Flexibility:

        Challenges can have optional teams (controlled by the Team Mode field), and any activity or task can optionally relate to a challenge, enabling flexibility. This means you can have tasks or logs that are part of a challenge, or just standalone tasks or logs not associated with any challenge.

    Points and Rewards System:

        Points and rewards continue to be tracked in the same way, but now the system allows for granular tracking of whether those points are earned through a challenge or through non-challenge activities.

        The Challenge Points History table tracks points earned from both challenge-related tasks and non-challenge activities, ensuring consistency in point tracking.

Example Use Cases:

    Independent Tasks and Reading Logs:

        A user can read a book and log it in the system, earning points for completing the book. This task is independent of any challenge and does not involve teams.

    Challenge-Linked Tasks and Logs:

        A user might read a specific book as part of a "Read 3 Books" Challenge, and their progress is tracked within that challenge.

        Tasks like "Write a review" or "Share the book with a friend" could be tied to the challenge, and when they are completed, the user earns points within that challenge.

    Teams in Challenges:

        Teams can be formed within a challenge, and team members can contribute to completing tasks together. Points for completing challenge-related tasks are recorded in the Challenge Points History, and team progress is tracked at the challenge level.

    Reward Unlocks:

        Rewards (e.g., badges, achievements) can be earned based on completing tasks in both challenge-related and non-challenge-related activities. For example, completing certain tasks in the system (whether tied to a challenge or not) might unlock a badge like "Bookworm" for reading a set number of books.
		
		