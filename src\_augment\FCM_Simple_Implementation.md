# Simplified FCM Push Notifications Implementation - Firebase SDK 12.4.0

## Overview

This is a minimal Firebase Cloud Messaging (FCM) implementation for the Laravel PWA mobile application. It uses **Firebase SDK 12.4.0 with modular API** on the frontend and **direct HTTP API calls** to Firebase Cloud Messaging REST API on the backend without external Firebase SDK dependencies.

## Architecture

### Backend Components

1. **FCMService** (`app/Services/FCMService.php`)
   - Uses direct HTTP API calls to Firebase FCM v1 API
   - Handles OAuth2 authentication with Firebase
   - Creates JWT tokens for authentication
   - Sends notifications to individual users, classes, and teams

2. **Database Migration** (`database/migrations/2025_10_22_180617_add_fcm_token_to_users_table.php`)
   - Adds `fcm_token` TEXT field to users table

3. **Model Updates**
   - `User.php`: Added 'fcm_token' to fillable array
   - `UserActivity.php`: Added FCM notification on activity submission
   - `UserActivityReview.php`: Added FCM notifications on approval/rejection

4. **Jobs & Commands**
   - `SendInactivityNotificationJob`: Scheduled job for inactivity reminders
   - `SendFCMNotification`: Console command for manual notifications

5. **Configuration** (`config/fcm.php`)
   - Firebase credentials path
   - Project ID and API keys
   - Notification types and default messages
   - Activity logging settings

### Frontend Components

1. **Firebase Configuration** (`resources/js/firebase.js`)
   - Firebase SDK 12.4.0 modular API integration
   - Complete FCM initialization and token management
   - Foreground message handling with notification actions
   - Service worker registration and token refresh handling

2. **JavaScript App** (`resources/js/app.js`)
   - Simplified FCM initialization that delegates to firebase.js
   - Retry mechanism for Firebase module loading

3. **Service Worker** (`public/sw.js`)
   - Firebase SDK 12.4.0 modular API for background messages
   - Static Firebase configuration (no dynamic injection)
   - Deep linking support with notification actions
   - Proper FCM background message processing

4. **Mobile Layout** (`resources/views/components/layouts/mobile.blade.php`)
   - Vite compilation of firebase.js module
   - Clean integration without inline scripts

3. **MoonShine Admin** (`app/MoonShine/Pages/SendNotificationPage.php`)
   - Admin interface for sending notifications
   - Support for individual users, classes, teams, or all users

## Configuration

### Environment Variables

```env
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CREDENTIALS_PATH=/path/to/service-account.json
FIREBASE_WEB_API_KEY=your-web-api-key
FIREBASE_VAPID_KEY=your-vapid-key
FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
FIREBASE_APP_ID=your-app-id
FCM_DEFAULT_ICON=/images/icon-192x192.png
```

### Firebase Setup

1. Create Firebase project
2. Generate service account JSON file
3. Place in `storage/app/private/firebase/service-account.json`
4. Configure environment variables

## Usage

### Automatic Notifications

- **Activity Submission**: When student submits activity requiring approval
- **Activity Approval**: When teacher approves student activity
- **Activity Rejection**: When teacher rejects student activity
- **Inactivity Reminders**: Daily job for inactive students

### Manual Notifications

#### Console Command
```bash
php artisan fcm:send {user-id} "Title" "Message" --type=general --url=/books
php artisan fcm:send class:123 "Title" "Message"
php artisan fcm:send team:456 "Title" "Message"
php artisan fcm:send all "Title" "Message"
```

#### Admin Interface
- Navigate to MoonShine admin panel
- Go to Communication > Send Push Notification
- Select recipient type and fill form

## API Endpoints

- `POST /api/fcm/register-token` - Register FCM token for authenticated user

## Logging

All FCM activities are logged using Spatie ActivityLog with log name 'fcm_notifications':
- Successful sends
- Failed sends
- Token registrations
- Batch operations

## Deep Linking

Notifications support deep linking to specific app sections:
- `/books` - Books section
- `/activities` - Activities section
- `/home` - Home page
- Custom URLs as needed

## Scheduled Tasks

Inactivity reminder job runs daily via Laravel scheduler:
```php
Schedule::job(new SendInactivityNotificationJob())->daily();
```

## Security

- Firebase service account credentials stored securely
- OAuth2 JWT authentication with Firebase
- Token validation and error handling
- Activity logging for audit trail

## Testing

1. Register FCM token via mobile app
2. Test notifications through admin interface
3. Verify deep linking works correctly
4. Check activity logs for successful operations

## Troubleshooting

- Check Laravel logs for FCM errors
- Verify Firebase credentials are valid
- Ensure users have FCM tokens registered
- Check activity log table for notification history
