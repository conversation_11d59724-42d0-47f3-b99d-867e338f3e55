<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('challenge_tasks', function (Blueprint $table) {
            $table->foreignId('reward_id')->nullable()->after('end_date')->constrained('rewards')->onDelete('set null');
            
            // Add index for performance
            $table->index(['challenge_id', 'reward_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('challenge_tasks', function (Blueprint $table) {
            $table->dropForeign(['reward_id']);
            $table->dropIndex(['challenge_id', 'reward_id']);
            $table->dropColumn('reward_id');
        });
    }
};
