<div class="min-h-screen bg-gray-50 pb-20">
    <x-mobile-page-header route="{{ route('mobile.messages') }}" header="{{ __('mobile.message') }}" />

    <!-- Message Content -->
    <div class="p-4">
        <div class="bg-white rounded-lg shadow-md p-6">
            <!-- Message Title -->
            <h2 class="text-2xl font-bold text-gray-800 mb-4">
                {!! $message->title !!}
            </h2>

            <!-- Message Meta Info -->
            <div class="flex items-center text-sm text-gray-500 mb-6 pb-4 border-b border-gray-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                <span>{{ __('mobile.sent_on') }}: {{ $messageRecipient->sent_date->format('d.m.Y H:i') }}</span>
            </div>

            <!-- Message Body -->
            <div class="prose prose-sm max-w-none">
                {!! $message->message !!}
            </div>
        </div>

        <!-- Back Button -->
        <div class="mt-6">
            <button wire:click="backToMessages" 
                    class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-6 rounded-lg shadow-lg transition-colors">
                {{ __('mobile.back_to_messages') }}
            </button>
        </div>
    </div>
</div>

