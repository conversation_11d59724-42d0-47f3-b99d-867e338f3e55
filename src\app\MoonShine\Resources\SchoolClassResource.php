<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\EnumClassLevel;
use App\Models\SchoolClass;
use App\Models\School;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\Switcher;

#[Icon('user-group')]

class SchoolClassResource extends BaseResource
{
    //    use WithRolePermissions;

    protected string $model = SchoolClass::class;

    protected string $column = 'name';

    protected array $with = ['school'];

    public function getTitle(): string
    {
        return __('admin.school_classes');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.school'),
                'school',
                formatted: fn(School $org) => $org->name,
                resource: SchoolResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.class_level'),
                'classLevel',
                formatted: fn(EnumClassLevel $org) => $org->name,
                resource: EnumClassLevelResource::class
            )
                ->sortable(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active'),
            
            Number::make(__('admin.student_count'), 'student_count'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.school'),
                    'school',
                    formatted: fn(School $org) => $org->name,
                    resource: SchoolResource::class
                )
                    ->required()
                    ->placeholder(__('admin.select_school'))
                    ->valuesQuery(function ($query) {
                        return $query->where('active', true);
                    }),

                BelongsTo::make(
                    __('admin.class_level'),
                    'classLevel',
                    formatted: fn(enumClassLevel $org) => $org->name,
                    resource: EnumClassLevelResource::class
                )
                    ->placeholder(__('admin.select_class_level')),

                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),
                Switcher::make(__('admin.active'), 'active')
                    ->default(true),
            ]),


        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.school'),
                'school',
                formatted: fn(School $org) => $org->name,
                resource: SchoolResource::class
            ),

            BelongsTo::make(
                __('admin.class_level'),
                'classLevel',
                formatted: fn(EnumClassLevel $org) => $org->name,
                resource: EnumClassLevelResource::class
            ),

            Text::make(__('admin.name'), 'name'),

            Switcher::make(__('admin.active'), 'active'),

            Number::make(__('admin.student_count'), 'student_count'),
            HasMany::make(
                __('admin.users'),
                'users',
                resource: UserResource::class
            ),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'school_id' => ['required', 'exists:schools,id'],
            'class_level_id' => ['required', 'exists:enum_class_levels,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['school_id' => 'asc', 'name' => 'asc'];
    }
}
