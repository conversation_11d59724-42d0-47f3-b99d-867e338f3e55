# Reward Calculation Date_Only Column Fix

## Issue Description

The reward task completion system was failing with the following error:
```
Error checking reward task completion {"reward_task_id":33,"task_id":33,"user_id":33,"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'date_only' in 'SELECT' (Connection: mysql, SQL: select count(distinct `date_only`) as aggregate from `user_reading_logs` where `user_id` = 33)"}
```

## Root Cause

The issue was in the `calculateReadDaysProgress()` method in `RewardCalculationService.php` and similar methods in `TaskProgressCalculationService.php`. The code was using:

```php
return (int) $query->selectRaw('DATE(log_date) as date_only')
    ->distinct()
    ->count('date_only');
```

The problem is that `date_only` is just an alias created by `selectRaw()`, but when using `count('date_only')`, MySQL expects `date_only` to be an actual column in the table, which it isn't.

## Solution Implemented

### Files Modified

1. **src/app/Services/RewardCalculationService.php** (line 568-571)
2. **src/app/Services/TaskProgressCalculationService.php** (lines 301-307 and 422-423)

### Fix Applied

**Before (Incorrect):**
```php
return (int) $query->selectRaw('DATE(log_date) as date_only')
    ->distinct()
    ->count('date_only');
```

**After (Correct):**
```php
return (int) $query->selectRaw('COUNT(DISTINCT DATE(log_date)) as count')
    ->value('count') ?? 0;
```

## Technical Details

The fix uses MySQL's `COUNT(DISTINCT DATE(log_date))` function directly in the SQL query, which:

1. **Extracts the date part** from the `log_date` timestamp using `DATE()`
2. **Counts distinct dates** using `COUNT(DISTINCT ...)`
3. **Returns the result** using Laravel's `value()` method with null coalescing

This approach is more efficient and avoids the column alias issue entirely.

## Testing

The fix was tested with:
- Direct database query test for user ID 33 (the failing case)
- Full service test through `RewardCalculationService::checkAndAwardUserRewards()`
- Both tests passed successfully without errors

## Impact

This fix resolves the reward task completion errors and ensures that:
- READ_DAYS task type calculations work correctly
- Reward awarding system functions properly
- No more "Column not found: date_only" errors occur

## Related Files

The User model's `getCurrentReadingStreak()` method uses a different approach with `pluck()` which doesn't have this issue, so no changes were needed there.
