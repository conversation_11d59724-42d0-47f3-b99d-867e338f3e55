<?php

namespace Database\Factories;

use App\Models\Task;
use App\Models\EnumTaskType;
use App\Models\EnumTaskCycle;
use App\Models\Activity;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Task>
 */
class TaskFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Task::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $taskType = EnumTaskType::inRandomOrder()->first();
        $taskCycle = EnumTaskCycle::inRandomOrder()->first();
        
        return [
            'name' => $this->faker->sentence(3),
            'task_type_id' => $taskType->id,
            'task_cycle_id' => $taskCycle->id,
            'task_value' => $taskType->isQuantitative() ? $this->faker->numberBetween(1, 100) : null,
            'activity_id' => $taskType->requiresActivity() ? Activity::factory() : null,
            'active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Create a quantitative task.
     */
    public function quantitative(): static
    {
        return $this->state(function (array $attributes) {
            $quantitativeTypes = EnumTaskType::getQuantitativeTypes();
            $taskType = EnumTaskType::whereIn('nr', $quantitativeTypes)->inRandomOrder()->first();
            
            return [
                'task_type_id' => $taskType->id,
                'task_value' => $this->faker->numberBetween(1, 50),
            ];
        });
    }

    /**
     * Create a qualitative task.
     */
    public function qualitative(): static
    {
        return $this->state(function (array $attributes) {
            $qualitativeTypes = EnumTaskType::getQualitativeTypes();
            $taskType = EnumTaskType::whereIn('nr', $qualitativeTypes)->inRandomOrder()->first();
            
            return [
                'task_type_id' => $taskType->id,
                'task_value' => null,
            ];
        });
    }

    /**
     * Create a daily task.
     */
    public function daily(): static
    {
        return $this->state(function (array $attributes) {
            $dailyCycle = EnumTaskCycle::where('nr', EnumTaskCycle::DAILY)->first();
            
            return [
                'task_cycle_id' => $dailyCycle->id,
            ];
        });
    }

    /**
     * Create a weekly task.
     */
    public function weekly(): static
    {
        return $this->state(function (array $attributes) {
            $weeklyCycle = EnumTaskCycle::where('nr', EnumTaskCycle::WEEKLY)->first();
            
            return [
                'task_cycle_id' => $weeklyCycle->id,
            ];
        });
    }

    /**
     * Create a monthly task.
     */
    public function monthly(): static
    {
        return $this->state(function (array $attributes) {
            $monthlyCycle = EnumTaskCycle::where('nr', EnumTaskCycle::MONTHLY)->first();
            
            return [
                'task_cycle_id' => $monthlyCycle->id,
            ];
        });
    }

    /**
     * Create a total (cumulative) task.
     */
    public function total(): static
    {
        return $this->state(function (array $attributes) {
            $totalCycle = EnumTaskCycle::where('nr', EnumTaskCycle::TOTAL)->first();
            
            return [
                'task_cycle_id' => $totalCycle->id,
            ];
        });
    }

    /**
     * Create a read pages task.
     */
    public function readPages(): static
    {
        return $this->state(function (array $attributes) {
            $taskType = EnumTaskType::where('nr', EnumTaskType::READ_PAGES)->first();
            
            return [
                'task_type_id' => $taskType->id,
                'task_value' => $this->faker->numberBetween(5, 50),
                'name' => 'Read ' . $attributes['task_value'] . ' Pages',
            ];
        });
    }

    /**
     * Create a read books task.
     */
    public function readBooks(): static
    {
        return $this->state(function (array $attributes) {
            $taskType = EnumTaskType::where('nr', EnumTaskType::READ_BOOKS)->first();
            
            return [
                'task_type_id' => $taskType->id,
                'task_value' => $this->faker->numberBetween(1, 10),
                'name' => 'Read ' . $attributes['task_value'] . ' Books',
            ];
        });
    }

    /**
     * Create a read minutes task.
     */
    public function readMinutes(): static
    {
        return $this->state(function (array $attributes) {
            $taskType = EnumTaskType::where('nr', EnumTaskType::READ_MINUTES)->first();
            
            return [
                'task_type_id' => $taskType->id,
                'task_value' => $this->faker->numberBetween(15, 120),
                'name' => 'Read for ' . $attributes['task_value'] . ' Minutes',
            ];
        });
    }

    /**
     * Create a book list task.
     */
    public function bookList(): static
    {
        return $this->state(function (array $attributes) {
            $taskType = EnumTaskType::where('nr', EnumTaskType::COMPLETE_BOOK_LIST)->first();
            
            return [
                'task_type_id' => $taskType->id,
                'task_value' => null,
                'name' => 'Complete Book List: ' . $this->faker->words(3, true),
            ];
        });
    }

    /**
     * Create an activity-based task.
     */
    public function activityBased(): static
    {
        return $this->state(function (array $attributes) {
            $taskType = EnumTaskType::where('nr', EnumTaskType::COMPLETE_BOOK_ACTIVITY)->first();
            
            return [
                'task_type_id' => $taskType->id,
                'task_value' => $this->faker->numberBetween(1, 5),
                'activity_id' => Activity::factory(),
                'name' => 'Complete Activity: ' . $this->faker->words(2, true),
            ];
        });
    }

    /**
     * Create an active task.
     */
    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => true,
            ];
        });
    }

    /**
     * Create an inactive task.
     */
    public function inactive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => false,
            ];
        });
    }
}
