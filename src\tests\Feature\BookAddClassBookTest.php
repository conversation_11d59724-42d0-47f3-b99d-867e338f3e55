<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\{Book, User, Publisher, BookType, School, SchoolClass, UserClass, ClassBook};

class BookAddClassBookTest extends TestCase
{
    use RefreshDatabase;

    private User $teacher;
    private User $systemAdmin;
    private Book $book;
    private School $school;
    private SchoolClass $class1;
    private SchoolClass $class2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create users first
        $this->teacher = User::factory()->create();
        $this->systemAdmin = User::factory()->create();

        // Create test data
        $this->school = School::factory()->create();

        $publisher = Publisher::create([
            'name' => 'Test Publisher',
            'created_by' => $this->teacher->id,
        ]);

        $bookType = BookType::create([
            'name' => 'Test Book Type',
            'description' => 'Test book type description',
            'thumbnail' => 'test-thumbnail.jpg',
            'created_by' => $this->teacher->id,
        ]);

        // Create roles first
        \Spatie\Permission\Models\Role::create(['name' => 'teacher', 'guard_name' => 'moonshine']);
        \Spatie\Permission\Models\Role::create(['name' => 'system_admin', 'guard_name' => 'moonshine']);

        // Assign teacher role
        $this->teacher->assignRole('teacher');
        $this->systemAdmin->assignRole('system_admin');

        $this->book = Book::create([
            'name' => 'Test Book',
            'isbn' => '9781234567890',
            'publisher_id' => $publisher->id,
            'book_type_id' => $bookType->id,
            'page_count' => 200,
            'year_of_publish' => 2023,
            'created_by' => $this->teacher->id,
        ]);

        // Create classes
        $this->class1 = SchoolClass::factory()->create([
            'school_id' => $this->school->id,
            'name' => 'Class 1A',
        ]);

        $this->class2 = SchoolClass::factory()->create([
            'school_id' => $this->school->id,
            'name' => 'Class 1B',
        ]);

        // Assign teacher to classes
        UserClass::create([
            'user_id' => $this->teacher->id,
            'class_id' => $this->class1->id,
            'school_id' => $this->school->id,
            'active' => true,
        ]);

        UserClass::create([
            'user_id' => $this->teacher->id,
            'class_id' => $this->class2->id,
            'school_id' => $this->school->id,
            'active' => true,
        ]);
    }

    /** @test */
    public function addClassBook_creates_class_book_assignments_for_teacher_classes()
    {
        // Act
        $result = $this->book->addClassBook($this->teacher->id);

        // Assert
        $this->assertCount(2, $result);
        
        // Check that ClassBook records were created
        $this->assertDatabaseHas('class_books', [
            'class_id' => $this->class1->id,
            'book_id' => $this->book->id,
            'created_by' => $this->teacher->id,
        ]);
        
        $this->assertDatabaseHas('class_books', [
            'class_id' => $this->class2->id,
            'book_id' => $this->book->id,
            'created_by' => $this->teacher->id,
        ]);
    }

    /** @test */
    public function addClassBook_does_not_create_duplicate_assignments()
    {
        // Create existing assignment
        ClassBook::create([
            'class_id' => $this->class1->id,
            'book_id' => $this->book->id,
            'created_by' => $this->teacher->id,
        ]);

        // Act
        $result = $this->book->addClassBook($this->teacher->id);

        // Assert - should only create one new assignment (for class2)
        $this->assertCount(1, $result);
        
        // Check that only one ClassBook record exists for class1
        $this->assertEquals(1, ClassBook::where('class_id', $this->class1->id)
            ->where('book_id', $this->book->id)
            ->count());
    }

    /** @test */
    public function addClassBook_returns_empty_array_for_nonexistent_user()
    {
        // Act
        $result = $this->book->addClassBook(99999);

        // Assert
        $this->assertEmpty($result);
        $this->assertEquals(0, ClassBook::where('book_id', $this->book->id)->count());
    }

    /** @test */
    public function addClassBook_returns_empty_array_for_user_with_no_classes()
    {
        $userWithoutClasses = User::factory()->create();
        $userWithoutClasses->assignRole('teacher');

        // Act
        $result = $this->book->addClassBook($userWithoutClasses->id);

        // Assert
        $this->assertEmpty($result);
        $this->assertEquals(0, ClassBook::where('book_id', $this->book->id)->count());
    }

    /** @test */
    public function addClassBook_only_considers_active_class_assignments()
    {
        // Make one class assignment inactive
        UserClass::where('user_id', $this->teacher->id)
            ->where('class_id', $this->class2->id)
            ->update(['active' => false]);

        // Act
        $result = $this->book->addClassBook($this->teacher->id);

        // Assert - should only create assignment for active class
        $this->assertCount(1, $result);
        
        $this->assertDatabaseHas('class_books', [
            'class_id' => $this->class1->id,
            'book_id' => $this->book->id,
        ]);
        
        $this->assertDatabaseMissing('class_books', [
            'class_id' => $this->class2->id,
            'book_id' => $this->book->id,
        ]);
    }

    /** @test */
    public function book_has_classBooks_relationship()
    {
        // Create a class book assignment
        ClassBook::create([
            'class_id' => $this->class1->id,
            'book_id' => $this->book->id,
            'created_by' => $this->teacher->id,
        ]);

        // Act & Assert
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class, $this->book->classBooks());
        $this->assertCount(1, $this->book->classBooks);
        $this->assertEquals($this->class1->id, $this->book->classBooks->first()->class_id);
    }
}
