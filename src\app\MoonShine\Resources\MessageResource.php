<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Message;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Number;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\QueryTags\QueryTag;
use Illuminate\Database\Eloquent\Builder;

#[Icon('envelope')]
class MessageResource extends BaseResource
{
    protected string $model = Message::class;

    protected string $column = 'title';

    protected array $with = ['messageRecipients', 'recipients'];

    public function getTitle(): string
    {
        return __('admin.messages');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Text::make(__('admin.title'), 'title')
                ->unescape()
                ->sortable(),

            Date::make(__('admin.message_date'), 'message_date')
                ->format('d.m.Y H:i')
                ->sortable(),

            Switcher::make(__('admin.default'), 'default')
                ->sortable(),

            Number::make(__('admin.total_recipients'), 'total_recipients')
                ->sortable(),

            Number::make(__('admin.read_count'), 'read_count')
                ->sortable(),

            Number::make(__('admin.unread_count'), 'unread_count')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make(__('admin.message_info'), [
                Text::make(__('admin.title'), 'title')
                    ->required()
                    ->unescape()
                    ->placeholder(__('admin.enter_message_title')),

                Textarea::make(__('admin.message'), 'message')
                    ->required()
                    ->unescape()
                    ->placeholder(__('admin.enter_message_content')),

                Date::make(__('admin.message_date'), 'message_date')
                    ->format('d.m.Y H:i')
                    ->default(now()),

                Switcher::make(__('admin.default'), 'default')
                    ->hint(__('admin.default_message_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.title'), 'title')
                ->unescape(),
            
            Textarea::make(__('admin.message'), 'message')
                ->unescape(),

            Date::make(__('admin.message_date'), 'message_date')
                ->format('d.m.Y H:i'),

            Switcher::make(__('admin.default'), 'default'),

            Number::make(__('admin.total_recipients'), 'total_recipients'),
            Number::make(__('admin.read_count'), 'read_count'),
            Number::make(__('admin.unread_count'), 'unread_count'),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'message' => ['required', 'string'],
            'message_date' => ['required', 'date'],
            'default' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['title', 'message'];
    }

    protected function getDefaultSort(): array
    {
        return ['message_date' => 'desc'];
    }

    /**
     * After creating a message, assign it to selected recipients.
     */
    protected function afterCreated(mixed $item): mixed
    {
        if ($item instanceof Message) {
            // If default message, assign to all existing users
            if ($item->default) {
                $allUserIds = User::pluck('id')->toArray();
                $item->assignToUsers($allUserIds);
            }
        }

        return $item;
    }

    /**
     * After updating a message, handle default status changes.
     */
    protected function afterUpdated(mixed $item): mixed
    {
        if ($item instanceof Message) {
            // If changed to default, assign to all users who don't have it yet
            if ($item->default && $item->wasChanged('default')) {
                $existingRecipientIds = $item->messageRecipients()->pluck('user_id')->toArray();
                $allUserIds = User::pluck('id')->toArray();
                $newRecipientIds = array_diff($allUserIds, $existingRecipientIds);
                
                if (!empty($newRecipientIds)) {
                    $item->assignToUsers($newRecipientIds);
                }
            }
        }

        return $item;
    }
}

