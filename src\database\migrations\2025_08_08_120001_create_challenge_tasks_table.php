<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('challenge_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('challenge_id')->constrained('challenges')->onDelete('cascade');
            $table->foreignId('task_id')->constrained('tasks')->onDelete('cascade');
            $table->date('start_date');
            $table->date('end_date');

            // Indexes for performance
            $table->index(['challenge_id', 'task_id']);
            $table->index(['start_date', 'end_date']);
            $table->index(['challenge_id', 'start_date', 'end_date']);
            
            // Unique constraint to prevent duplicate task assignments to same challenge
            $table->unique(['challenge_id', 'task_id', 'start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('challenge_tasks');
    }
};
