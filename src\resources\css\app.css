@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap');
@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source "../**/*.blade.php";
@source "../**/*.js";
@source "../**/*.vue";
@source "../../app/Livewire/**/*.php";


@theme {
    --font-sans: 'Quicksand', 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

@supports selector(:has(*)) {
  @media (min-width: 1024px) {
    .layout-wrapper:has(> .layout-menu) {
      padding-top: 0;
      padding-right: 0;
      padding-bottom: 0;
    }
  }
}

.layout-page {
  border-radius: 0;
}

.card-category .card-photo img {
	-o-object-fit: contain;
	object-fit: contain;
}

.choices {
	margin-bottom: 0;
}

.authentication-logo {
    max-width: 420px;
}

/* Mobile App Specific Styles */
.mobile-container {
  @apply max-w-2xl mx-auto bg-gray-50 min-h-screen relative;
}

.mobile-header {
  @apply flex flex-col p-4 bg-gradient-to-r from-violet-600 to-purple-600 text-white bg-[url(/images/header-bg.jpg)] bg-cover bg-center bg-no-repeat;
}

.mobile-teacher-header {
  @apply flex flex-col p-4 bg-gradient-to-r from-violet-600 to-purple-600 text-white bg-[url(/images/header-bg-teacher.webp)] bg-cover bg-center bg-no-repeat;
}

.mobile-page-header {
  @apply flex flex-col p-4 bg-violet-200;
}

.mobile-card {
  @apply bg-white rounded-2xl shadow-lg p-4 m-4;
}

.mobile-button {
  @apply bg-violet-600 hover:bg-violet-700 text-white w-full text-lg font-semibold py-3 rounded-xl transition-colors;
}

.mobile-button-secondary {
  @apply border-2 border-violet-600 text-violet-600 hover:bg-violet-600 hover:text-white w-full text-lg font-semibold py-3 rounded-xl transition-colors;
}

.mobile-input {
  @apply border border-gray-300 w-full text-lg p-4 rounded-xl focus:border-violet-600 focus:ring-2 focus:ring-violet-600/50;
}

.mobile-avatar {
  @apply w-16 h-16 rounded-full border-4 border-white shadow-lg;
}

.mobile-book-cover {
  @apply w-20 h-28 rounded-lg shadow-md object-cover;
}

.mobile-badge {
  @apply bg-violet-600 text-white text-xs px-2 py-1 rounded-full font-semibold;
}

.mobile-stats-box {
  @apply text-center w-12 h-6;
}

.mobile-stats-text {
  @apply text-black font-bold;
}

.mobile-tab {
  @apply border-b-2 border-transparent text-lg font-semibold px-4 py-2 cursor-pointer;
}

.mobile-tab-active {
  @apply border-violet-600 text-violet-600;
}

.mobile-bottom-nav {
  @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2;
  @apply flex justify-around items-center max-w-2xl mx-auto;
}

.mobile-nav-item {
  @apply flex flex-col items-center p-2 text-gray-500 hover:text-violet-600 transition-colors;
}

.mobile-nav-item.active {
  @apply text-violet-600;
}

.mobile-streak-day {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold;
}

.mobile-streak-day.completed {
  @apply bg-green-500 text-white;
}

.mobile-streak-day.missed {
  @apply bg-gray-300 text-gray-600;
}

.mobile-streak-day.today {
  @apply bg-violet-600 text-white ring-2 ring-violet-600 ring-offset-2;
}

.mobile-progress-bar {
  @apply w-full bg-gray-200 rounded-full h-3;
}

.mobile-progress-fill {
  @apply bg-gradient-to-r from-violet-600 to-purple-600 h-3 rounded-full transition-all duration-500;
}

.mobile-confetti {
  @apply absolute inset-0 pointer-events-none overflow-hidden;
}

.mobile-modal {
  @apply fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50;
}

.mobile-modal-content {
  @apply bg-white rounded-2xl p-6 w-full max-w-sm mx-auto;
}

.mobile-loading {
  @apply flex items-center justify-center p-8;
}

.mobile-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600;
}

/* Text truncation utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}