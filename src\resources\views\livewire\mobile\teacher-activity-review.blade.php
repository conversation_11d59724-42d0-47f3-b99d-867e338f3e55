<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <x-mobile-page-header route="{{ route('mobile.teacher.home') }}" header="{{ __('mobile.activity_review') }}" />

    <!-- Main Content -->
    <div class="p-4 space-y-4">
        <!-- Student Information -->
        <div class="bg-white rounded-xl p-4 shadow-sm">
            <div class="flex items-center space-x-3">
                <!-- Student Avatar -->
                <div class="w-16 h-16 rounded-full overflow-hidden">
                    @if($student->getAvatarDisplayImage())
                        <img src="{{ asset('storage/' . $student->getAvatarDisplayImage()) }}" alt="Avatar" class="w-full h-full object-cover">
                    @else
                        <div class="w-full h-full bg-orange-400 rounded-full flex items-center justify-center">
                            <span class="text-white text-lg font-bold">{{ substr($student->name, 0, 1) }}</span>
                        </div>
                    @endif
                </div>
                
                <!-- Student Info -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $student->name }}</h3>
                </div>
            </div>
        </div>

        <!-- Book Information -->
        <div class="bg-white rounded-xl p-4 shadow-sm">
            <div class="flex items-start space-x-4">
                <!-- Book Cover -->
                @if($book->cover_image)
                    <div class="w-16 h-20 rounded overflow-hidden flex-shrink-0">
                        <img src="{{ asset('storage/' . $book->cover_image) }}" alt="Book Cover" class="w-full h-full object-cover">
                    </div>
                @endif
                
                <!-- Book Info -->
                <div class="flex-1 min-w-0">
                    <h4 class="text-lg font-semibold text-gray-900 mb-1">{{ $book->name }}</h4>
                    @if($book->primary_author)
                        <p class="text-sm text-gray-600 mb-2">{{ $book->primary_author->name }}</p>
                    @endif
                    <p class="text-gray-700">{{ $activity->name }}</p>
                    @if($activity->points > 0)
                        <span class="inline-block mt-2 text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">
                            {{ $activity->points }} {{ __('mobile.points') }}
                        </span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Activity Content Preview -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">{{ __('mobile.activity_content') }}</h4>
            
            @if($this->isWriting)
                <!-- Writing Activity Content -->
                <div class="p-4">
                    @if(is_array($this->activityContent))
                        @foreach($this->activityContent as $key => $value)
                            @if(is_string($value) && !empty($value))
                                <div class="mb-3">
                                    <p class="text-sm font-medium text-gray-700 mb-1">{{ ucfirst(str_replace('_', ' ', $key)) }}:</p>
                                    <p class="text-gray-900">{{ $value }}</p>
                                </div>
                            @endif
                        @endforeach
                    @else
                        <p class="text-gray-900">{{ $this->activityContent }}</p>
                    @endif
                </div>
                
            @elseif($this->isRating)
                <!-- Rating Activity Content -->
                <div class="text-center py-6">
                    <div class="flex justify-center items-center space-x-1 mb-2">
                        @for($i = 1; $i <= $this->activity->max_rating; $i++)
                            <svg class="w-8 h-8 {{ $i <= $this->activityContent ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        @endfor
                    </div>
                    <p class="text-lg font-semibold text-gray-900">{{ $this->activityContent }} / {{ $this->activity->max_rating }}</p>
                    <p class="text-sm text-gray-600">{{ __('mobile.student_rating') }}</p>
                </div>
                
            @elseif($this->hasMedia)
                <!-- Media Activity Content -->
                <div class="text-center">
                    @if($this->mediaType === 'image')
                        <!-- Image Upload -->
                        <div class="mb-4">
                            <img src="{{ asset('storage/' . $this->activityContent) }}" alt="Student Upload" class="max-w-full h-auto rounded-lg mx-auto">
                        </div>
                        <p class="text-sm text-gray-600">{{ __('mobile.student_uploaded_image') }}</p>
                        
                    @elseif($this->mediaType === 'audio')
                        <!-- Audio Upload -->
                        <div class="mb-4">
                            <div class="bg-gray-100 rounded-lg p-2">
                                <audio controls class="w-full">
                                    <source src="{{ asset('storage/' . $this->activityContent) }}" type="audio/mpeg">
                                    {{ __('mobile.audio_not_supported') }}
                                </audio>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600">{{ __('mobile.student_uploaded_audio') }}</p>
                    @endif
                </div>
            @endif
        </div>

        <!-- Feedback Section (Optional) -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <label for="feedback" class="block text-sm font-medium text-gray-700 mb-2">
                {{ __('mobile.feedback_optional') }}
            </label>
            <textarea 
                wire:model="feedback" 
                id="feedback"
                rows="3" 
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
                placeholder="{{ __('mobile.add_feedback_placeholder') }}"
            ></textarea>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-3 pb-6">
            <!-- Accept Button -->
            <button 
                wire:click="acceptActivity"
                wire:loading.attr="disabled"
                class="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
                <span wire:loading.remove wire:target="acceptActivity">✓ {{ __('mobile.accept') }}</span>
                <span wire:loading wire:target="acceptActivity">{{ __('mobile.processing') }}...</span>
            </button>

            <!-- Reject Button -->
            <button 
                wire:click="rejectActivity"
                wire:loading.attr="disabled"
                class="flex-1 bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
                <span wire:loading.remove wire:target="rejectActivity">✗ {{ __('mobile.reject') }}</span>
                <span wire:loading wire:target="rejectActivity">{{ __('mobile.processing') }}...</span>
            </button>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div wire:loading.flex wire:target="acceptActivity,rejectActivity" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600 mx-auto mb-3"></div>
            <p class="text-gray-700">{{ __('mobile.processing_request') }}...</p>
        </div>
    </div>
</div>
