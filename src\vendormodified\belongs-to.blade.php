@props([
    'value' => '',
    'values' => [],
    'isNullable' => false,
    'isSearchable' => false,
    'isAsyncSearch' => false,
    'asyncSearchUrl' => '',
    'isCreatable' => false,
    'createButton' => '',
    'fragmentUrl' => '',
    'relationName' => '',
    'isNative' => false,
])
@if($isCreatable)
@fragment($relationName)
<div style="display: flex; gap: 5px;"
    x-data="fragment('{{ $fragmentUrl }}')"
    @defineEvent('fragment_updated', $relationName, 'fragmentUpdate')
>
@endif

<x-moonshine::form.select
    :attributes="$attributes"
    :nullable="$isNullable"
    :searchable="$isSearchable"
    :value="$value"
    :values="$values"
    :asyncRoute="$isAsyncSearch ? $asyncSearchUrl : null"
    :native="$isNative"
>
</x-moonshine::form.select>
@if($isCreatable)
{!! $createButton !!}
</div>
@endfragment
@endif
