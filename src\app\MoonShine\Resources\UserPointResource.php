<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserPoint;
use App\Models\User;
use App\Models\Book;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\DateTime;
use MoonShine\UI\Fields\Select;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\UI\Fields\Date;

#[Icon('star')]
class UserPointResource extends BaseResource
{
    protected string $model = UserPoint::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'book'];

    public function getTitle(): string
    {
        return __('admin.user_points');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(?Book $book) => $book?->name ?? 'N/A',
                resource: BookResource::class
            )
                ->sortable(),

            Date::make(__('admin.point_date'), 'point_date')
                ->sortable(),

            Select::make(__('admin.point_type'), 'point_type')
                ->options(UserPoint::getPointTypeOptions())
                ->sortable(),

            Number::make(__('admin.points'), 'points')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class
                    )
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.book'),
                        'book',
                        formatted: fn(?Book $book) => $book ? html_entity_decode($book->name) . ' - ' . $book->isbn : 'N/A',
                        resource: BookResource::class
                    )
                        ->searchable(),
                ]),

                Date::make(__('admin.point_date'), 'point_date')
                    ->required()
                    ->default(now()),

                Flex::make([
                    Select::make(__('admin.point_type'), 'point_type')
                        ->required()
                        ->options(UserPoint::getPointTypeOptions())
                        ->default(UserPoint::POINT_TYPE_MANUAL),

                    Number::make(__('admin.points'), 'points')
                        ->required()
                        ->min(1),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(?Book $book) => $book?->name ?? 'N/A',
                resource: BookResource::class
            ),

            Date::make(__('admin.point_date'), 'point_date'),
            
            Text::make(__('admin.point_type'), 'localized_point_type_name'),
            
            Number::make(__('admin.points'), 'points'),

            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'book_id' => ['nullable', 'exists:books,id'],
            'point_date' => ['required', 'date'],
            'point_type' => ['required', 'integer', 'in:1,2,3,4'],
            'points' => ['required', 'integer', 'min:1'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['user.name', 'user.email', 'book.name', 'book.isbn'];
    }

    protected function getDefaultSort(): array
    {
        return ['point_date' => 'desc', 'user.name' => 'asc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all user points
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see points for students in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            });
        }

        // Teachers can see points for students in their assigned classes
        if ($user->isTeacher()) {
            $teacherClassIds = $user->activeUserClasses()->pluck('class_id')->toArray();

            if (empty($teacherClassIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            });
        }

        // Students can only see their own points
        if ($user->isStudent()) {
            return $builder->where('user_id', $user->id);
        }

        // Default: no access
        return $builder->where('id', 0);
    }
}
