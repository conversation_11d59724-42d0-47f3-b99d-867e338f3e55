<?php

namespace App\Models;

use Sweet1s\MoonshineRBAC\Traits\HasMoonShineRolePermissions;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    use HasMoonShineRolePermissions;

    // TODO: Restrict admin panel login by role: https://moonshine-laravel.com/en/docs/3.x/security/authentication#role-based-access

    const DEFAULT_GUARD = 'moonshine';

    protected $with = ['permissions'];

    const SYSTEM_ADMIN = 'system_admin';
    const SCHOOL_ADMIN = 'school_admin';
    const TEACHER = 'teacher';
    const PARENT = 'parent';
    const STUDENT = 'student';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'guard_name',
    ];

    public static function getGuardOptions(): array
    {
        return [
            'moonshine' => 'Moonshine',
            'web' => 'Web',
        ];
    }
}
