<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class Friends extends Component
{
    public $user;
    public $friends;
    public $activeTab = 'all';
    public $sortBy = 'name';
    
    public $sortOptions = [
        'name_asc' => 'Name',
        'books_desc' => 'Books Read',
        'badges_desc' => 'Badges',
        'points_desc' => 'Activity Points',
    ];

    public function mount()
    {
        $this->user = Auth::user();
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function setSortBy($sortBy)
    {
        $this->sortBy = $sortBy;
    }

    public function loadFriends()
    {
        $friends = collect();

        switch ($this->activeTab) {
            case 'class':
                $friends = $this->user->getClassmates();
                break;
            case 'teams':
                $friends = $this->user->getTeammates();
                break;
            default: // 'all'
                $friends = $this->user->getFriends();
                break;
        }

        // Add statistics to each friend
        $friends = $friends->map(function ($friend) {
            $friend->avatar = $friend->getAvatarDisplayImage();
            $friend->total_books = $friend->getTotalBooksCompleted();
            $friend->total_badges = $friend->getTotalBadges();
            $friend->total_points = $friend->getTotalActivityPoints();
            $friend->primary_class = $friend->getPrimaryClassName();
            $friend->currently_reading = $friend->getCurrentlyReadingBook();
            $friend->shared_teams = $this->user->getSharedTeams($friend);
            return $friend;
        });

        // Sort friends
        switch ($this->sortBy) {
            case 'books_desc':
                $friends = $friends->sortByDesc('total_books');
                break;
            case 'badges_desc':
                $friends = $friends->sortByDesc('total_badges');
                break;
            case 'points_desc':
                $friends = $friends->sortByDesc('total_points');
                break;
            default: // 'name'
                $friends = $friends->sortBy('name');
                break;
        }

        $this->friends = $friends->values();
    }

    public function render()
    {
        $this->loadFriends();
        return view('livewire.mobile.friends');
    }
}
