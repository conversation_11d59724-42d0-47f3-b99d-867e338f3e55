# Define the folder path where the files are located
$folderPath = "D:\ba\calisma\web\kitapokuma\okumobil\src\*.php"  # Change this to your target folder

# Define the path to the file containing the list of strings
$stringFilePath = "D:\ba\calisma\web\kitapokuma\okumobil\tools\strings.txt"  # Change this to your string list file path

# Read all strings from the file
$searchStrings = Get-Content -Path $stringFilePath

# Create an output array to store the results
$output = @()

# Loop through each string in the list
foreach ($string in $searchStrings) {
    # Initialize a counter for occurrences
    $count = 0
    Write-Host $string
    # Search all files in the folder (including subfolders) for the string
    Get-ChildItem -Path $folderPath -Recurse -File | ForEach-Object {
        $fileContent = Get-Content $_.FullName -Raw
        # Count occurrences of the string in the file content
        $count += [regex]::Matches($fileContent, [regex]::Escape($string)).Count
    }
    
    # Add the result to the output array
    $output += "$string : $count"
}

# Output the results to a file or display on console
$output | Out-File "strings-count.txt"  # Change this to your desired output file path
