SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;


CREATE TABLE `activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL,
  `activity_type` int(11) NOT NULL DEFAULT 1 COMMENT '1-Writing, 2-Rating, 3-Media, 4-Physical, 5-Game',
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `question_count` int(11) DEFAULT NULL COMMENT 'Number of questions for quiz/vocabulary tests (1-10)',
  `choices_count` int(11) DEFAULT NULL COMMENT 'Number of answer choices per question (1-6)',
  `min_grade` int(11) DEFAULT NULL COMMENT 'Minimum passing grade out of 100',
  `required` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether activity is mandatory for book completion',
  `allowed_tries` int(11) NOT NULL DEFAULT 1 COMMENT 'Number of retry attempts allowed',
  `min_word_count` int(11) DEFAULT NULL,
  `min_rating` int(11) DEFAULT NULL,
  `max_rating` int(11) DEFAULT NULL,
  `media_url` varchar(255) DEFAULT NULL COMMENT 'Playable game URL or media content',
  `points` int(11) NOT NULL DEFAULT 0,
  `need_approval` tinyint(1) NOT NULL DEFAULT 0,
  `media_type` int(11) DEFAULT NULL COMMENT '1-image, 2-audio',
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `activity_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `activity_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `log_name` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `subject_type` varchar(255) DEFAULT NULL,
  `event` varchar(255) DEFAULT NULL,
  `subject_id` bigint(20) UNSIGNED DEFAULT NULL,
  `causer_type` varchar(255) DEFAULT NULL,
  `causer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`properties`)),
  `batch_uuid` char(36) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `avatars` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `base_image` varchar(255) NOT NULL,
  `happy_image` varchar(255) NOT NULL,
  `sad_image` varchar(255) NOT NULL,
  `sleepy_image` varchar(255) NOT NULL,
  `required_points` int(11) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `isbn` varchar(255) NOT NULL,
  `publisher_id` bigint(20) UNSIGNED DEFAULT NULL,
  `book_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `page_count` int(11) DEFAULT NULL,
  `year_of_publish` year(4) DEFAULT NULL,
  `cover_image` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_authors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `author_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_questions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `question_text` text NOT NULL COMMENT 'The question content',
  `question_image_url` varchar(255) DEFAULT NULL COMMENT 'Optional image URL',
  `question_audio_url` varchar(255) DEFAULT NULL COMMENT 'Optional audio URL',
  `question_video_url` varchar(255) DEFAULT NULL COMMENT 'Optional video URL',
  `correct_answer` varchar(255) NOT NULL COMMENT 'The correct answer text',
  `incorrect_answer_1` varchar(255) DEFAULT NULL COMMENT 'First incorrect option',
  `incorrect_answer_2` varchar(255) DEFAULT NULL COMMENT 'Second incorrect option',
  `incorrect_answer_3` varchar(255) DEFAULT NULL COMMENT 'Third incorrect option',
  `incorrect_answer_4` varchar(255) DEFAULT NULL COMMENT 'Fourth incorrect option',
  `incorrect_answer_5` varchar(255) DEFAULT NULL COMMENT 'Fifth incorrect option',
  `page_start` int(11) DEFAULT NULL COMMENT 'Starting page reference',
  `page_end` int(11) DEFAULT NULL COMMENT 'Ending page reference',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether question is available for use'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `thumbnail` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `book_words` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `word` varchar(255) NOT NULL COMMENT 'The vocabulary word',
  `definition` text DEFAULT NULL COMMENT 'Word definition',
  `synonym` varchar(255) DEFAULT NULL COMMENT 'Synonym of the word',
  `antonym` varchar(255) DEFAULT NULL COMMENT 'Antonym of the word',
  `page_reference` int(11) DEFAULT NULL COMMENT 'Page where word appears',
  `difficulty_level` enum('easy','medium','hard') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenges` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL COMMENT 'Challenge banner/flyer image path',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `prize` text DEFAULT NULL COMMENT 'Challenge reward description',
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `school_class_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `reward_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `challenge_teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `challenge_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `class_activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `question_count` int(11) DEFAULT NULL COMMENT 'Number of questions for quiz/vocabulary tests (1-50)',
  `min_grade` int(11) DEFAULT NULL COMMENT 'Minimum passing grade 0-100 for tests',
  `allowed_tries` int(11) DEFAULT NULL COMMENT 'Maximum retry attempts for test activities',
  `min_word_count` int(11) DEFAULT NULL COMMENT 'Minimum word count for writing activities',
  `points` int(11) DEFAULT NULL COMMENT 'Activity points for this class',
  `required` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether activity is required for book completion',
  `need_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether activity needs teacher approval',
  `active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether activity is enabled for this class'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `class_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_class_levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_school_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_task_cycles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `enum_task_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nr` int(11) NOT NULL COMMENT 'Level number, incremental starting from 1',
  `name` varchar(255) NOT NULL COMMENT 'Level name/title',
  `image` varchar(255) DEFAULT NULL COMMENT 'Optional level badge/icon image path',
  `books_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Minimum books required for this level',
  `page_points` int(11) NOT NULL DEFAULT 0 COMMENT 'Minimum page points required for this level',
  `all_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'true = both books_count AND page_points must be met, false = either condition can trigger level up'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `messages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `message_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `default` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `message_recipients` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `message_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `read` tinyint(1) NOT NULL DEFAULT 0,
  `sent_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `moonshine_users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `moonshine_user_role_id` bigint(20) UNSIGNED NOT NULL DEFAULT 1,
  `email` varchar(190) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `moonshine_user_roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `page_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `book_type_id` bigint(20) UNSIGNED NOT NULL,
  `class_level_id` bigint(20) UNSIGNED NOT NULL,
  `point` decimal(8,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `publishers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `pulse_aggregates` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `bucket` int(10) UNSIGNED NOT NULL,
  `period` mediumint(8) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL,
  `key` mediumtext NOT NULL,
  `key_hash` binary(16) GENERATED ALWAYS AS (unhex(md5(`key`))) VIRTUAL,
  `aggregate` varchar(255) NOT NULL,
  `value` decimal(20,2) NOT NULL,
  `count` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `pulse_entries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `timestamp` int(10) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL,
  `key` mediumtext NOT NULL,
  `key_hash` binary(16) GENERATED ALWAYS AS (unhex(md5(`key`))) VIRTUAL,
  `value` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `pulse_values` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `timestamp` int(10) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL,
  `key` mediumtext NOT NULL,
  `key_hash` binary(16) GENERATED ALWAYS AS (unhex(md5(`key`))) VIRTUAL,
  `value` mediumtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reward_type` int(11) NOT NULL DEFAULT 1 COMMENT '1-Badge, 2-Trophy, 3-Card, 4-Item',
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `repeatable` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `reward_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `role_priority` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`role_priority`)),
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `school_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `school_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `class_level_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `task_type_id` bigint(20) UNSIGNED NOT NULL,
  `task_cycle_id` bigint(20) UNSIGNED NOT NULL,
  `task_value` int(11) DEFAULT NULL,
  `activity_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `task_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `task_book_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `task_id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `leader_user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `team_rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `awarded_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `awarded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reading_log_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `activity_date` timestamp NOT NULL,
  `content` text DEFAULT NULL COMMENT 'Written content for writing activities',
  `rating` int(11) DEFAULT NULL COMMENT 'Rating value for rating activities',
  `media_url` varchar(255) DEFAULT NULL COMMENT 'User-submitted media content',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0-pending, 1-approved, 2-rejected, 3-completed (no approval needed)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_activity_reviews` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED NOT NULL,
  `review_date` date NOT NULL,
  `reviewed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0-waiting, 1-approved, 2-rejected',
  `feedback` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_agreements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `agreement_type` varchar(255) NOT NULL DEFAULT 'privacy_policy' COMMENT 'Type of agreement (privacy_policy, terms_of_service, etc.)',
  `version` varchar(255) NOT NULL DEFAULT '1.0' COMMENT 'Version of the agreement accepted',
  `accepted_at` timestamp NOT NULL COMMENT 'When the user accepted the agreement',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the user when accepting'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_avatars` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `avatar_id` bigint(20) UNSIGNED NOT NULL,
  `selected_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_books` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_classes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `class_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `default` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_levels` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `level_id` bigint(20) UNSIGNED NOT NULL,
  `level_date` timestamp NOT NULL COMMENT 'When the level was achieved',
  `reading_log_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `point_date` timestamp NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED DEFAULT NULL,
  `source_id` bigint(20) DEFAULT NULL,
  `point_type` int(11) NOT NULL COMMENT '1-Page, 2-Activity, 3-Task, 4-Manual',
  `points` int(11) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_reading_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `book_id` bigint(20) UNSIGNED NOT NULL,
  `log_date` timestamp NOT NULL,
  `start_page` int(11) DEFAULT NULL COMMENT 'Optional start page information',
  `end_page` int(11) DEFAULT NULL COMMENT 'Optional end page information',
  `pages_read` int(11) NOT NULL,
  `reading_duration` int(11) DEFAULT NULL COMMENT 'Time spent reading in minutes',
  `book_completed` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_rewards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `reward_id` bigint(20) UNSIGNED NOT NULL,
  `awarded_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `awarded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `reading_log_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_activity_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_schools` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `school_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `default` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `task_type` int(11) NOT NULL DEFAULT 0 COMMENT '0-Standalone, 1-Challenge',
  `challenge_task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `task_id` bigint(20) UNSIGNED DEFAULT NULL,
  `team_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `assigned_by` bigint(20) UNSIGNED DEFAULT NULL,
  `assign_date` timestamp NULL DEFAULT NULL,
  `start_date` timestamp NULL DEFAULT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `complete_date` timestamp NULL DEFAULT NULL,
  `due_date` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `class_id` bigint(20) UNSIGNED DEFAULT NULL,
  `reward_id` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


ALTER TABLE `activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activities_category_id_active_index` (`category_id`,`active`),
  ADD KEY `activities_activity_type_index` (`activity_type`),
  ADD KEY `activities_need_approval_index` (`need_approval`),
  ADD KEY `activities_active_index` (`active`),
  ADD KEY `activities_points_index` (`points`),
  ADD KEY `activities_required_index` (`required`),
  ADD KEY `activities_allowed_tries_index` (`allowed_tries`);

ALTER TABLE `activity_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activity_categories_active_index` (`active`),
  ADD KEY `activity_categories_name_index` (`name`);

ALTER TABLE `activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subject` (`subject_type`,`subject_id`),
  ADD KEY `causer` (`causer_type`,`causer_id`),
  ADD KEY `activity_log_log_name_index` (`log_name`);

ALTER TABLE `authors`
  ADD PRIMARY KEY (`id`),
  ADD KEY `authors_name_index` (`name`),
  ADD KEY `authors_created_by_index` (`created_by`);

ALTER TABLE `avatars`
  ADD PRIMARY KEY (`id`),
  ADD KEY `avatars_name_index` (`name`),
  ADD KEY `avatars_required_points_index` (`required_points`),
  ADD KEY `avatars_active_index` (`active`);

ALTER TABLE `books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `books_isbn_unique` (`isbn`),
  ADD KEY `books_name_index` (`name`),
  ADD KEY `books_publisher_id_index` (`publisher_id`),
  ADD KEY `books_book_type_id_index` (`book_type_id`),
  ADD KEY `books_year_of_publish_index` (`year_of_publish`),
  ADD KEY `books_active_index` (`active`),
  ADD KEY `books_created_by_index` (`created_by`);

ALTER TABLE `book_authors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_authors_book_id_author_id_unique` (`book_id`,`author_id`),
  ADD KEY `book_authors_author_id_index` (`author_id`);

ALTER TABLE `book_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_categories_book_id_category_id_unique` (`book_id`,`category_id`),
  ADD KEY `book_categories_category_id_index` (`category_id`);

ALTER TABLE `book_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `book_questions_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_questions_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_questions_page_start_page_end_index` (`page_start`,`page_end`),
  ADD KEY `book_questions_difficulty_level_index` (`difficulty_level`),
  ADD KEY `book_questions_created_by_index` (`created_by`);

ALTER TABLE `book_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `book_types_name_unique` (`name`),
  ADD KEY `book_types_name_index` (`name`);

ALTER TABLE `book_words`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_book_word` (`book_id`,`word`),
  ADD KEY `book_words_book_id_is_active_index` (`book_id`,`is_active`),
  ADD KEY `book_words_book_id_difficulty_level_index` (`book_id`,`difficulty_level`),
  ADD KEY `book_words_word_book_id_index` (`word`,`book_id`),
  ADD KEY `book_words_page_reference_index` (`page_reference`),
  ADD KEY `book_words_difficulty_level_index` (`difficulty_level`),
  ADD KEY `book_words_created_by_index` (`created_by`);

ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `categories_name_unique` (`name`),
  ADD KEY `categories_name_index` (`name`);

ALTER TABLE `challenges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `challenges_active_index` (`active`),
  ADD KEY `challenges_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `challenges_active_start_date_end_date_index` (`active`,`start_date`,`end_date`),
  ADD KEY `challenges_created_by_index` (`created_by`);

ALTER TABLE `challenge_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_classes_challenge_id_school_class_id_unique` (`challenge_id`,`school_class_id`),
  ADD KEY `challenge_classes_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_classes_school_class_id_index` (`school_class_id`);

ALTER TABLE `challenge_schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_schools_challenge_id_school_id_unique` (`challenge_id`,`school_id`),
  ADD KEY `challenge_schools_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_schools_school_id_index` (`school_id`);

ALTER TABLE `challenge_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_tasks_challenge_id_task_id_start_date_end_date_unique` (`challenge_id`,`task_id`,`start_date`,`end_date`),
  ADD KEY `challenge_tasks_task_id_foreign` (`task_id`),
  ADD KEY `challenge_tasks_challenge_id_task_id_index` (`challenge_id`,`task_id`),
  ADD KEY `challenge_tasks_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `challenge_tasks_challenge_id_start_date_end_date_index` (`challenge_id`,`start_date`,`end_date`),
  ADD KEY `challenge_tasks_reward_id_foreign` (`reward_id`),
  ADD KEY `challenge_tasks_challenge_id_reward_id_index` (`challenge_id`,`reward_id`),
  ADD KEY `challenge_tasks_created_by_index` (`created_by`);

ALTER TABLE `challenge_teams`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `challenge_teams_challenge_id_team_id_unique` (`challenge_id`,`team_id`),
  ADD KEY `challenge_teams_challenge_id_index` (`challenge_id`),
  ADD KEY `challenge_teams_team_id_index` (`team_id`);

ALTER TABLE `class_activities`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `class_activities_unique` (`class_id`,`activity_id`),
  ADD KEY `class_activities_class_id_active_index` (`class_id`,`active`),
  ADD KEY `class_activities_activity_id_active_index` (`activity_id`,`active`),
  ADD KEY `class_activities_active_index` (`active`);

ALTER TABLE `class_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `class_books_unique` (`class_id`,`book_id`),
  ADD KEY `class_books_class_id_index` (`class_id`),
  ADD KEY `class_books_book_id_index` (`book_id`),
  ADD KEY `class_books_created_by_index` (`created_by`);

ALTER TABLE `enum_class_levels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_class_levels_name_unique` (`name`),
  ADD KEY `enum_class_levels_name_index` (`name`);

ALTER TABLE `enum_school_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_school_types_name_unique` (`name`),
  ADD KEY `enum_school_types_name_index` (`name`);

ALTER TABLE `enum_task_cycles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_task_cycles_nr_unique` (`nr`),
  ADD KEY `enum_task_cycles_nr_index` (`nr`);

ALTER TABLE `enum_task_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `enum_task_types_nr_unique` (`nr`),
  ADD KEY `enum_task_types_nr_index` (`nr`);

ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `levels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `levels_nr_unique` (`nr`),
  ADD KEY `levels_nr_index` (`nr`),
  ADD KEY `levels_books_count_page_points_index` (`books_count`,`page_points`),
  ADD KEY `levels_all_required_index` (`all_required`);

ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `messages_default_index` (`default`),
  ADD KEY `messages_message_date_index` (`message_date`),
  ADD KEY `messages_created_by_index` (`created_by`);

ALTER TABLE `message_recipients`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `message_recipients_message_id_user_id_unique` (`message_id`,`user_id`),
  ADD KEY `message_recipients_message_id_index` (`message_id`),
  ADD KEY `message_recipients_user_id_index` (`user_id`),
  ADD KEY `message_recipients_read_index` (`read`),
  ADD KEY `message_recipients_user_id_read_index` (`user_id`,`read`);

ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

ALTER TABLE `moonshine_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `moonshine_users_email_unique` (`email`),
  ADD KEY `moonshine_users_moonshine_user_role_id_foreign` (`moonshine_user_role_id`);

ALTER TABLE `moonshine_user_roles`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

ALTER TABLE `page_points`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `page_points_book_type_id_class_level_id_unique` (`book_type_id`,`class_level_id`),
  ADD KEY `page_points_class_level_id_foreign` (`class_level_id`);

ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

ALTER TABLE `publishers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `publishers_name_index` (`name`),
  ADD KEY `publishers_created_by_index` (`created_by`);

ALTER TABLE `pulse_aggregates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `pulse_aggregates_bucket_period_type_aggregate_key_hash_unique` (`bucket`,`period`,`type`,`aggregate`,`key_hash`),
  ADD KEY `pulse_aggregates_period_bucket_index` (`period`,`bucket`),
  ADD KEY `pulse_aggregates_type_index` (`type`),
  ADD KEY `pulse_aggregates_period_type_aggregate_bucket_index` (`period`,`type`,`aggregate`,`bucket`);

ALTER TABLE `pulse_entries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pulse_entries_timestamp_index` (`timestamp`),
  ADD KEY `pulse_entries_type_index` (`type`),
  ADD KEY `pulse_entries_key_hash_index` (`key_hash`),
  ADD KEY `pulse_entries_timestamp_type_key_hash_value_index` (`timestamp`,`type`,`key_hash`,`value`);

ALTER TABLE `pulse_values`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `pulse_values_type_key_hash_unique` (`type`,`key_hash`),
  ADD KEY `pulse_values_timestamp_index` (`timestamp`),
  ADD KEY `pulse_values_type_index` (`type`);

ALTER TABLE `rewards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `rewards_reward_type_index` (`reward_type`),
  ADD KEY `rewards_name_index` (`name`),
  ADD KEY `rewards_active_index` (`active`),
  ADD KEY `rewards_reward_type_active_index` (`reward_type`,`active`),
  ADD KEY `rewards_created_by_index` (`created_by`),
  ADD KEY `rewards_repeatable_index` (`repeatable`),
  ADD KEY `rewards_active_repeatable_index` (`active`,`repeatable`);

ALTER TABLE `reward_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reward_tasks_reward_id_task_id_unique` (`reward_id`,`task_id`),
  ADD KEY `reward_tasks_reward_id_task_id_index` (`reward_id`,`task_id`),
  ADD KEY `reward_tasks_reward_id_index` (`reward_id`),
  ADD KEY `reward_tasks_task_id_index` (`task_id`),
  ADD KEY `reward_tasks_created_by_index` (`created_by`);

ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

ALTER TABLE `schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `schools_name_school_type_id_unique` (`name`,`school_type_id`),
  ADD KEY `schools_school_type_id_active_index` (`school_type_id`,`active`),
  ADD KEY `schools_school_type_id_index` (`school_type_id`);

ALTER TABLE `school_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `school_classes_school_id_name_active_unique` (`school_id`,`name`,`active`),
  ADD KEY `school_classes_school_id_class_level_id_active_index` (`school_id`,`class_level_id`,`active`),
  ADD KEY `school_classes_class_level_id_index` (`class_level_id`),
  ADD KEY `school_classes_created_by_index` (`created_by`);

ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

ALTER TABLE `tasks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tasks_task_type_id_index` (`task_type_id`),
  ADD KEY `tasks_task_cycle_id_index` (`task_cycle_id`),
  ADD KEY `tasks_activity_id_index` (`activity_id`),
  ADD KEY `tasks_active_index` (`active`),
  ADD KEY `tasks_active_task_type_id_index` (`active`,`task_type_id`),
  ADD KEY `tasks_created_by_index` (`created_by`);

ALTER TABLE `task_books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_books_task_id_book_id_unique` (`task_id`,`book_id`),
  ADD KEY `task_books_task_id_index` (`task_id`),
  ADD KEY `task_books_book_id_index` (`book_id`),
  ADD KEY `task_books_created_by_index` (`created_by`);

ALTER TABLE `task_book_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_book_categories_task_id_category_id_unique` (`task_id`,`category_id`),
  ADD KEY `task_book_categories_task_id_index` (`task_id`),
  ADD KEY `task_book_categories_category_id_index` (`category_id`),
  ADD KEY `task_book_categories_created_by_index` (`created_by`);

ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teams_name_index` (`name`),
  ADD KEY `teams_leader_user_id_index` (`leader_user_id`),
  ADD KEY `teams_active_index` (`active`),
  ADD KEY `teams_active_name_index` (`active`,`name`),
  ADD KEY `teams_created_by_index` (`created_by`);

ALTER TABLE `team_rewards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_reward_unique` (`team_id`,`reward_id`),
  ADD KEY `team_rewards_team_id_reward_id_index` (`team_id`,`reward_id`),
  ADD KEY `team_rewards_team_id_awarded_date_index` (`team_id`,`awarded_date`),
  ADD KEY `team_rewards_reward_id_awarded_date_index` (`reward_id`,`awarded_date`),
  ADD KEY `team_rewards_awarded_by_index` (`awarded_by`),
  ADD KEY `team_rewards_reading_log_id_index` (`reading_log_id`),
  ADD KEY `team_rewards_user_activity_id_index` (`user_activity_id`),
  ADD KEY `team_rewards_awarded_date_index` (`awarded_date`),
  ADD KEY `team_rewards_created_by_index` (`created_by`);

ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_username_unique` (`username`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

ALTER TABLE `user_activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_activities_user_id_activity_date_index` (`user_id`,`activity_date`),
  ADD KEY `user_activities_book_id_activity_date_index` (`book_id`,`activity_date`),
  ADD KEY `user_activities_activity_id_status_index` (`activity_id`,`status`),
  ADD KEY `user_activities_status_index` (`status`),
  ADD KEY `user_activities_activity_date_index` (`activity_date`),
  ADD KEY `user_activities_created_by_index` (`created_by`);

ALTER TABLE `user_activity_reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_activity_reviews_user_activity_id_status_index` (`user_activity_id`,`status`),
  ADD KEY `user_activity_reviews_reviewed_by_review_date_index` (`reviewed_by`,`review_date`),
  ADD KEY `user_activity_reviews_status_index` (`status`),
  ADD KEY `user_activity_reviews_review_date_index` (`review_date`),
  ADD KEY `user_activity_reviews_created_by_index` (`created_by`);

ALTER TABLE `user_agreements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_agreements_user_id_agreement_type_index` (`user_id`,`agreement_type`),
  ADD KEY `user_agreements_agreement_type_version_index` (`agreement_type`,`version`),
  ADD KEY `user_agreements_accepted_at_index` (`accepted_at`);

ALTER TABLE `user_avatars`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_avatars_user_id_unique` (`user_id`),
  ADD KEY `user_avatars_avatar_id_index` (`avatar_id`),
  ADD KEY `user_avatars_selected_at_index` (`selected_at`),
  ADD KEY `user_avatars_created_by_index` (`created_by`);

ALTER TABLE `user_books`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_books_user_id_index` (`user_id`),
  ADD KEY `user_books_book_id_index` (`book_id`),
  ADD KEY `user_books_start_date_index` (`start_date`),
  ADD KEY `user_books_end_date_index` (`end_date`),
  ADD KEY `user_books_user_id_book_id_index` (`user_id`,`book_id`),
  ADD KEY `user_book_session_index` (`user_id`,`book_id`,`start_date`),
  ADD KEY `user_books_created_by_index` (`created_by`),
  ADD KEY `user_books_completed_index` (`completed`),
  ADD KEY `user_books_user_id_completed_index` (`user_id`,`completed`),
  ADD KEY `user_books_book_id_completed_index` (`book_id`,`completed`);

ALTER TABLE `user_classes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_classes_unique` (`user_id`,`class_id`,`school_id`),
  ADD KEY `user_classes_class_id_active_index` (`class_id`,`active`),
  ADD KEY `user_classes_school_id_active_index` (`school_id`,`active`),
  ADD KEY `user_classes_user_id_active_index` (`user_id`,`active`),
  ADD KEY `user_classes_active_index` (`active`),
  ADD KEY `user_classes_user_id_default_index` (`user_id`,`default`),
  ADD KEY `user_classes_user_id_active_default_index` (`user_id`,`active`,`default`),
  ADD KEY `user_classes_created_by_index` (`created_by`);

ALTER TABLE `user_levels`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_levels_user_id_level_id_unique` (`user_id`,`level_id`),
  ADD KEY `user_levels_user_id_level_date_index` (`user_id`,`level_date`),
  ADD KEY `user_levels_user_id_level_id_index` (`user_id`,`level_id`),
  ADD KEY `user_levels_level_id_index` (`level_id`),
  ADD KEY `user_levels_reading_log_id_index` (`reading_log_id`),
  ADD KEY `user_levels_level_date_index` (`level_date`);

ALTER TABLE `user_points`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_points_created_by_foreign` (`created_by`),
  ADD KEY `user_points_user_id_point_date_index` (`user_id`,`point_date`),
  ADD KEY `user_points_book_id_point_date_index` (`book_id`,`point_date`),
  ADD KEY `user_points_user_id_point_type_index` (`user_id`,`point_type`),
  ADD KEY `user_points_point_type_index` (`point_type`),
  ADD KEY `user_points_point_date_index` (`point_date`),
  ADD KEY `user_points_point_type_source_id_index` (`point_type`,`source_id`);

ALTER TABLE `user_reading_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_reading_logs_user_id_log_date_index` (`user_id`,`log_date`),
  ADD KEY `user_reading_logs_book_id_log_date_index` (`book_id`,`log_date`),
  ADD KEY `user_reading_logs_user_id_book_id_index` (`user_id`,`book_id`),
  ADD KEY `user_reading_logs_log_date_index` (`log_date`),
  ADD KEY `user_reading_logs_book_completed_index` (`book_completed`),
  ADD KEY `user_reading_logs_created_by_index` (`created_by`);

ALTER TABLE `user_rewards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_rewards_user_id_reward_id_index` (`user_id`,`reward_id`),
  ADD KEY `user_rewards_user_id_awarded_date_index` (`user_id`,`awarded_date`),
  ADD KEY `user_rewards_reward_id_awarded_date_index` (`reward_id`,`awarded_date`),
  ADD KEY `user_rewards_awarded_by_index` (`awarded_by`),
  ADD KEY `user_rewards_reading_log_id_index` (`reading_log_id`),
  ADD KEY `user_rewards_user_activity_id_index` (`user_activity_id`),
  ADD KEY `user_rewards_awarded_date_index` (`awarded_date`),
  ADD KEY `user_rewards_created_by_index` (`created_by`);

ALTER TABLE `user_schools`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_schools_unique` (`user_id`,`school_id`,`role_id`),
  ADD KEY `user_schools_role_id_foreign` (`role_id`),
  ADD KEY `user_schools_school_id_role_id_index` (`school_id`,`role_id`),
  ADD KEY `user_schools_user_id_active_index` (`user_id`,`active`),
  ADD KEY `user_schools_active_index` (`active`),
  ADD KEY `user_schools_user_id_default_index` (`user_id`,`default`),
  ADD KEY `user_schools_user_id_active_default_index` (`user_id`,`active`,`default`),
  ADD KEY `user_schools_created_by_index` (`created_by`);

ALTER TABLE `user_tasks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_challenge_task` (`challenge_task_id`,`user_id`,`team_id`),
  ADD KEY `user_tasks_created_by_foreign` (`created_by`),
  ADD KEY `user_tasks_task_type_user_id_completed_index` (`task_type`,`user_id`,`completed`),
  ADD KEY `user_tasks_challenge_task_id_user_id_index` (`challenge_task_id`,`user_id`),
  ADD KEY `user_tasks_task_id_user_id_index` (`task_id`,`user_id`),
  ADD KEY `user_tasks_user_id_completed_index` (`user_id`,`completed`),
  ADD KEY `user_tasks_team_id_completed_index` (`team_id`,`completed`),
  ADD KEY `user_tasks_assigned_by_assign_date_index` (`assigned_by`,`assign_date`),
  ADD KEY `user_tasks_complete_date_index` (`complete_date`),
  ADD KEY `user_tasks_task_type_completed_index` (`task_type`,`completed`),
  ADD KEY `user_tasks_due_date_index` (`due_date`),
  ADD KEY `user_tasks_class_id_index` (`class_id`),
  ADD KEY `user_tasks_reward_id_index` (`reward_id`),
  ADD KEY `user_tasks_user_id_due_date_index` (`user_id`,`due_date`),
  ADD KEY `user_tasks_class_id_due_date_index` (`class_id`,`due_date`);

ALTER TABLE `user_teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_teams_team_id_index` (`team_id`),
  ADD KEY `user_teams_user_id_index` (`user_id`),
  ADD KEY `user_teams_team_id_user_id_index` (`team_id`,`user_id`),
  ADD KEY `user_teams_created_by_index` (`created_by`);


ALTER TABLE `activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `activity_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `activity_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `avatars`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_authors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_questions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `book_words`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenges`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `challenge_teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `class_activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `class_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_class_levels`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_school_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_task_cycles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `enum_task_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `levels`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `messages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `message_recipients`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `moonshine_users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `moonshine_user_roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `page_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `publishers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `pulse_aggregates`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `pulse_entries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `pulse_values`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `reward_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `school_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `task_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `task_book_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `team_rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_activity_reviews`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_agreements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_avatars`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_books`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_classes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_levels`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_reading_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_rewards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_schools`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_tasks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

ALTER TABLE `user_teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;


ALTER TABLE `activities`
  ADD CONSTRAINT `activities_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `activity_categories` (`id`) ON DELETE CASCADE;

ALTER TABLE `authors`
  ADD CONSTRAINT `authors_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `books`
  ADD CONSTRAINT `books_book_type_id_foreign` FOREIGN KEY (`book_type_id`) REFERENCES `book_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `books_publisher_id_foreign` FOREIGN KEY (`publisher_id`) REFERENCES `publishers` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_authors`
  ADD CONSTRAINT `book_authors_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_authors_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_categories`
  ADD CONSTRAINT `book_categories_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_categories_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

ALTER TABLE `book_questions`
  ADD CONSTRAINT `book_questions_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_questions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `book_words`
  ADD CONSTRAINT `book_words_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `book_words_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `challenges`
  ADD CONSTRAINT `challenges_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `challenge_classes`
  ADD CONSTRAINT `challenge_classes_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_classes_school_class_id_foreign` FOREIGN KEY (`school_class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_schools`
  ADD CONSTRAINT `challenge_schools_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_schools_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_tasks`
  ADD CONSTRAINT `challenge_tasks_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `challenge_tasks_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `challenge_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `challenge_teams`
  ADD CONSTRAINT `challenge_teams_challenge_id_foreign` FOREIGN KEY (`challenge_id`) REFERENCES `challenges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `challenge_teams_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE;

ALTER TABLE `class_activities`
  ADD CONSTRAINT `class_activities_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `class_activities_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE;

ALTER TABLE `class_books`
  ADD CONSTRAINT `class_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `class_books_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `class_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `messages`
  ADD CONSTRAINT `messages_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `message_recipients`
  ADD CONSTRAINT `message_recipients_message_id_foreign` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `message_recipients_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

ALTER TABLE `moonshine_users`
  ADD CONSTRAINT `moonshine_users_moonshine_user_role_id_foreign` FOREIGN KEY (`moonshine_user_role_id`) REFERENCES `moonshine_user_roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `page_points`
  ADD CONSTRAINT `page_points_book_type_id_foreign` FOREIGN KEY (`book_type_id`) REFERENCES `book_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `page_points_class_level_id_foreign` FOREIGN KEY (`class_level_id`) REFERENCES `enum_class_levels` (`id`) ON DELETE CASCADE;

ALTER TABLE `publishers`
  ADD CONSTRAINT `publishers_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `rewards`
  ADD CONSTRAINT `rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `reward_tasks`
  ADD CONSTRAINT `reward_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `reward_tasks_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reward_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

ALTER TABLE `schools`
  ADD CONSTRAINT `schools_school_type_id_foreign` FOREIGN KEY (`school_type_id`) REFERENCES `enum_school_types` (`id`) ON DELETE SET NULL;

ALTER TABLE `school_classes`
  ADD CONSTRAINT `school_classes_class_level_id_foreign` FOREIGN KEY (`class_level_id`) REFERENCES `enum_class_levels` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `school_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `school_classes_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE;

ALTER TABLE `tasks`
  ADD CONSTRAINT `tasks_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `tasks_task_cycle_id_foreign` FOREIGN KEY (`task_cycle_id`) REFERENCES `enum_task_cycles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `tasks_task_type_id_foreign` FOREIGN KEY (`task_type_id`) REFERENCES `enum_task_types` (`id`) ON DELETE CASCADE;

ALTER TABLE `task_books`
  ADD CONSTRAINT `task_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `task_books_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `task_book_categories`
  ADD CONSTRAINT `task_book_categories_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_book_categories_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `task_book_categories_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `teams`
  ADD CONSTRAINT `teams_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `teams_leader_user_id_foreign` FOREIGN KEY (`leader_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `team_rewards`
  ADD CONSTRAINT `team_rewards_awarded_by_foreign` FOREIGN KEY (`awarded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `team_rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `team_rewards_reading_log_id_foreign` FOREIGN KEY (`reading_log_id`) REFERENCES `user_reading_logs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_rewards_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_activities`
  ADD CONSTRAINT `user_activities_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_activities_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_activities_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activities_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_activity_reviews`
  ADD CONSTRAINT `user_activity_reviews_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activity_reviews_reviewed_by_foreign` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_activity_reviews_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_agreements`
  ADD CONSTRAINT `user_agreements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_avatars`
  ADD CONSTRAINT `user_avatars_avatar_id_foreign` FOREIGN KEY (`avatar_id`) REFERENCES `avatars` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_avatars_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_avatars_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_books`
  ADD CONSTRAINT `user_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_books_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_books_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_classes`
  ADD CONSTRAINT `user_classes_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_classes_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_classes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_levels`
  ADD CONSTRAINT `user_levels_level_id_foreign` FOREIGN KEY (`level_id`) REFERENCES `levels` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_levels_reading_log_id_foreign` FOREIGN KEY (`reading_log_id`) REFERENCES `user_reading_logs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_levels_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_points`
  ADD CONSTRAINT `user_points_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_points_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_points_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_reading_logs`
  ADD CONSTRAINT `user_reading_logs_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_reading_logs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_reading_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_rewards`
  ADD CONSTRAINT `user_rewards_awarded_by_foreign` FOREIGN KEY (`awarded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_rewards_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_rewards_reading_log_id_foreign` FOREIGN KEY (`reading_log_id`) REFERENCES `user_reading_logs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_user_activity_id_foreign` FOREIGN KEY (`user_activity_id`) REFERENCES `user_activities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_rewards_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_schools`
  ADD CONSTRAINT `user_schools_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_schools_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_schools_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_schools_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_tasks`
  ADD CONSTRAINT `user_tasks_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_tasks_challenge_task_id_foreign` FOREIGN KEY (`challenge_task_id`) REFERENCES `challenge_tasks` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_tasks_class_id_foreign` FOREIGN KEY (`class_id`) REFERENCES `school_classes` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_tasks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_tasks_reward_id_foreign` FOREIGN KEY (`reward_id`) REFERENCES `rewards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_tasks_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_tasks_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_tasks_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `user_teams`
  ADD CONSTRAINT `user_teams_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `user_teams_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_teams_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
