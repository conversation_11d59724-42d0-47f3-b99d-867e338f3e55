import './bootstrap';

// Import Firebase module to ensure it loads with app.js
import './firebase';

// Initialize MobileApp object immediately to prevent undefined errors
window.MobileApp = window.MobileApp || {
    showConfetti: () => console.log('Confetti not ready yet'),
    checkCameraSupport: () => Promise.resolve(false),
    requestNotificationPermission: () => Promise.resolve(false),
    fcmInitialized: false,
    fcmSessionInitialized: false // Track per-session initialization
};

// Alpine.js extensions for Livewire compatibility
import './alpine-extensions';

// ZXing for QR code scanning
import { BrowserMultiFormatReader } from '@zxing/library';
window.BrowserMultiFormatReader = BrowserMultiFormatReader;

// Canvas Confetti for celebrations
import confetti from 'canvas-confetti';
window.confetti = confetti;

// Ensure MobileApp is available immediately
window.MobileApp = window.MobileApp || {};

// Mobile App Utilities
Object.assign(window.MobileApp, {
    // Show confetti animation
    showConfetti() {

/*        
        confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });
*/

        var duration = 15 * 1000;
        var animationEnd = Date.now() + duration;
        var defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

        function randomInRange(min, max) {
        return Math.random() * (max - min) + min;
        }

        var interval = setInterval(function() {
        var timeLeft = animationEnd - Date.now();

        if (timeLeft <= 0) {
            return clearInterval(interval);
        }

        var particleCount = 50 * (timeLeft / duration);
        confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.5, 0.5), y: Math.random() - 0.2 },shapes: ['star'] });
        }, 250);

    },

    // Check if device supports camera (without requesting permissions)
    async checkCameraSupport() {
        try {
            // Check if MediaDevices API is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                return false;
            }

            // Check if we can enumerate devices (this doesn't require permissions)
            if (navigator.mediaDevices.enumerateDevices) {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const hasVideoInput = devices.some(device => device.kind === 'videoinput');
                    return hasVideoInput;
                } catch (error) {
                    console.log('Could not enumerate devices:', error);
                    // Fallback: assume camera is available if API exists
                    return true;
                }
            }

            // Fallback: assume camera is available if getUserMedia exists
            return true;
        } catch (error) {
            console.log('Camera support check failed:', error);
            return false;
        }
    },

    // Enhanced camera access for barcode scanning
    async startCamera(videoElement) {
        try {
            console.log('Requesting camera access...');

            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment', // Prefer back camera for scanning
                    width: { ideal: 1280, min: 640 },
                    height: { ideal: 720, min: 480 }
                }
            });

            console.log('Camera access granted, setting up video element...');

            // Set the stream to the video element
            videoElement.srcObject = stream;

            // Ensure video plays automatically
            videoElement.setAttribute('autoplay', '');
            videoElement.setAttribute('playsinline', '');
            videoElement.setAttribute('muted', '');

            // Wait for the video to be ready and start playing
            return new Promise((resolve, reject) => {
                videoElement.onloadedmetadata = () => {
                    console.log('Video metadata loaded, starting playback...');
                    videoElement.play()
                        .then(() => {
                            console.log('Video playback started successfully');
                            resolve(stream);
                        })
                        .catch((playError) => {
                            console.error('Video play failed:', playError);
                            // Clean up stream if play fails
                            stream.getTracks().forEach(track => track.stop());
                            reject(playError);
                        });
                };

                videoElement.onerror = (error) => {
                    console.error('Video element error:', error);
                    stream.getTracks().forEach(track => track.stop());
                    reject(error);
                };
            });

        } catch (error) {
            console.error('Error accessing camera:', error);
            throw error;
        }
    },

    // Stop camera stream
    stopCamera(stream) {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
    },

    // Request notification permission - Optimized with session tracking
    async requestNotificationPermission() {
        console.log('requestNotificationPermission called');

        // Prevent multiple initializations per session
        if (this.fcmSessionInitialized) {
            console.log('FCM already initialized this session, skipping...');
            return true;
        }

        if ('Notification' in window) {
            console.log('Requesting notification permission...');
            const permission = await Notification.requestPermission();
            console.log('Notification permission result:', permission);

            if (permission === 'granted') {
                // Initialize FCM if available
                console.log('Permission granted, initializing FCM...');
                this.fcmSessionInitialized = true; // Mark as initialized for this session
                this.initializeFCM();
            }
            return permission === 'granted';
        } else {
            console.log('Notifications not supported in this browser');
        }
        return false;
    },

    // Initialize Firebase Cloud Messaging
    async initializeFCM() {
        try {
            console.log('MobileApp.initializeFCM called');
            console.log('window.initializeFCM type:', typeof window.initializeFCM);

            // Check if Firebase is available (loaded via firebase.js module)
            if (typeof window.initializeFCM === 'function') {
                console.log('Firebase module found, initializing...');
                await window.initializeFCM();
            } else {
                console.log('Firebase module not loaded, retrying in 1 second...');
                // Retry up to 5 times
                if (!this.fcmRetryCount) this.fcmRetryCount = 0;
                if (this.fcmRetryCount < 5) {
                    this.fcmRetryCount++;
                    console.log('Retry attempt:', this.fcmRetryCount);
                    setTimeout(() => this.initializeFCM(), 1000);
                } else {
                    console.error('Firebase module failed to load after 5 retries');
                }
            }
        } catch (error) {
            console.error('FCM initialization failed:', error);
        }
    },

    // Send local notification
    showLocalNotification(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                icon: '/images/icon-192x192.png',
                badge: '/images/icon-72x72.png',
                ...options
            });
        }
    },

    // Vibrate device (if supported)
    vibrate(pattern = [100, 50, 100]) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    },
});

// Optimized FCM Initialization - Single load event with session tracking
window.addEventListener('load', () => {
    console.log('Page loaded, checking FCM initialization status...');
    console.log('FCM session initialized:', window.MobileApp.fcmSessionInitialized);

    // Only initialize if not already done in this session
    if (!window.MobileApp.fcmSessionInitialized) {
        console.log('FCM not initialized this session, starting initialization...');

        // Wait for firebase.js module to load, then initialize FCM
        setTimeout(() => {
            console.log('Auto-initializing FCM...');
            if (typeof window.MobileApp.requestNotificationPermission === 'function') {
                window.MobileApp.requestNotificationPermission();
            } else {
                console.error('window.MobileApp.requestNotificationPermission is not a function');
            }
        }, 1000); // Wait 1 second for firebase.js to load
    } else {
        console.log('FCM already initialized this session, skipping initialization');
    }
});
