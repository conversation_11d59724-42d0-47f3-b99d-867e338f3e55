<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('challenges', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('image')->nullable()->comment('Challenge banner/flyer image path');
            $table->date('start_date');
            $table->date('end_date');
            $table->text('prize')->nullable()->comment('Challenge reward description');
            $table->boolean('active')->default(true);

            // Indexes for performance
            $table->index('active');
            $table->index(['start_date', 'end_date']);
            $table->index(['active', 'start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('challenges');
    }
};
