<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_points', function (Blueprint $table) {
            $table->id();
            $table->timestamp('point_date');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('book_id')->nullable()->constrained('books')->onDelete('cascade');
            $table->bigInteger('source_id')->nullable();
            $table->integer('point_type')->comment('1-Page, 2-Activity, 3-Task, 4-Manual');
            $table->integer('points');

            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
          


            // Add indexes for performance
            $table->index(['user_id', 'point_date']);
            $table->index(['book_id', 'point_date']);
            $table->index(['user_id', 'point_type']);
            $table->index('point_type');
            $table->index('point_date');
            $table->index(['point_type', 'source_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_points');
    }
};
