<?php

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== UserBook Completed Field Simple Test ===\n\n";

try {
    // Test 1: Check if completed field exists
    echo "TEST 1: Check if completed field exists in user_books table\n";
    
    $columns = DB::select("SHOW COLUMNS FROM user_books LIKE 'completed'");
    
    if (count($columns) > 0) {
        echo "✅ PASS - completed field exists in user_books table\n";
        echo "- Field type: {$columns[0]->Type}\n";
        echo "- Default: {$columns[0]->Default}\n\n";
    } else {
        echo "❌ FAIL - completed field does not exist in user_books table\n\n";
    }

    // Test 2: Check UserBook model fillable array
    echo "TEST 2: Check UserBook model configuration\n";
    
    $userBook = new \App\Models\UserBook();
    $fillable = $userBook->getFillable();
    
    if (in_array('completed', $fillable)) {
        echo "✅ PASS - completed field is in fillable array\n";
    } else {
        echo "❌ FAIL - completed field is not in fillable array\n";
    }
    
    // Test casts
    $casts = $userBook->getCasts();
    if (isset($casts['completed']) && $casts['completed'] === 'boolean') {
        echo "✅ PASS - completed field is cast as boolean\n\n";
    } else {
        echo "❌ FAIL - completed field is not properly cast as boolean\n\n";
    }

    // Test 3: Test completed scope
    echo "TEST 3: Test completed() scope\n";
    
    // Get a sample query to test the scope
    $query = \App\Models\UserBook::completed();
    $sql = $query->toSql();
    
    if (strpos($sql, '`completed` = ?') !== false) {
        echo "✅ PASS - completed() scope uses completed field\n";
        echo "- SQL: {$sql}\n\n";
    } else {
        echo "❌ FAIL - completed() scope does not use completed field\n";
        echo "- SQL: {$sql}\n\n";
    }

    // Test 4: Test basic UserBook creation
    echo "TEST 4: Test UserBook creation with completed field\n";
    
    // Find a test user and book
    $user = \App\Models\User::first();
    $book = \App\Models\Book::where('active', true)->first();
    
    if ($user && $book) {
        $testData = [
            'user_id' => $user->id,
            'book_id' => $book->id,
            'start_date' => now()->subDays(1),
            'completed' => false,
        ];
        
        // Test if we can create with completed field
        $userBook = new \App\Models\UserBook($testData);
        
        if ($userBook->completed === false) {
            echo "✅ PASS - UserBook can be created with completed field\n";
            echo "- completed value: " . ($userBook->completed ? 'true' : 'false') . "\n";
            echo "- isCompleted() method: " . ($userBook->isCompleted() ? 'true' : 'false') . "\n\n";
        } else {
            echo "❌ FAIL - UserBook completed field not working correctly\n\n";
        }
    } else {
        echo "❌ SKIP - No test user or book found\n\n";
    }

    echo "=== Simple Test Complete ===\n";

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
